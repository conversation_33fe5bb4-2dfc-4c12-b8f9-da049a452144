<html lang="en">

</html>

<head>
  <style class="vjs-styles-defaults">
    .video-js {
      width: 300px;
      height: 150px;
    }

    .vjs-fluid {
      padding-top: 56.25%;
    }
  </style>
  <meta charset="UTF-8" />
  <meta name="referrer" content="no-referrer" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link href="./static/js/element-ui.css" rel="stylesheet" />
  <link href="static/js/animate.min.css" rel="stylesheet" />
  <link href="static/js/css/video-js.min.css" rel="stylesheet" />
  <link href="static/js/Cesium/Widgets/widgets.css" rel="stylesheet" />
  <link href="static/js/css/common.css" rel="stylesheet" />
  <script type="text/javascript" src="static/js/adapter.min.js"></script>
  <script type="text/javascript" src="static/js/webrtcstreamer.js"></script>
  <script src="static/js/mpegts.js"></script>
  <script src="static/js/h5splayerhelper.js"></script>
  <title>{{sceneName}}</title>
  <style>
    /*!!!!!!!不要删除下面这句话 作用为替换css文件*/

    /* .el-image__inner {
width: 100%;
height: 100%;
} */
    .baseCss {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
    }

    html,
    body,
    p,
    ul {
      margin: 0;
      padding: 0;
    }

    @font-face {
      font-family: "FZHZGBJW";
      src: url(static/js/font/FZHZGBJW.TTF);
    }

    @font-face {
      font-family: "DIGITALDREAMFAT";
      src: url("static/js/font/DIGITALDREAMFAT.woff2") format("woff2"),
        url("static/js/font/DIGITALDREAMFAT.woff") format("woff"),
        url("static/js/font/DIGITALDREAMFAT.ttf") format("truetype");
    }

    #body {
      width: 100%;
      height: 100vh;
      /* background-color: #0D1D31; */
      background-size: 100% 100% !important;
      user-select: none;
      /* background-image: linear-gradient(rgb(41, 40, 50) 13px, transparent 0),
linear-gradient(90deg, rgba(146, 134, 134, 0.623) 2px, transparent 0);
background-size: 15px 15px, 15px 15px; */
    }

    ::-webkit-scrollbar {
      width: 5px;
      height: 5px;
      background-color: aliceblue;
    }

    ::-webkit-scrollbar-track {
      background: rgba(50, 50, 50, 0.01);
    }

    ::-webkit-scrollbar-thumb {
      background-color: rgba(50, 50, 50, 0.6);
    }

    .chart-item {
      width: 600px;
      height: 397px;
      position: absolute;
      background-repeat: no-repeat !important;
      background-size: 100% 100% !important;
    }

    .chart-item .chart-box {
      display: inline-block;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .main {
      width: 100%;
      height: 100%;
    }

    #background {
      height: 100%;
      width: 100%;
      position: absolute;
      z-index: 0;
    }

    li {
      list-style: none;
    }

    .full {
      width: 100% !important;
      height: 100% !important;
    }

    .el-dialog {
      z-index: 9999 !important;

      border: 0;
    }

    .el-dialog {
      margin-top: 0 !important;
    }

    .el-dialog__headerbtn {
      font-size: 40px;
    }

    .willResizeClass {
      width: 90%;
      height: 90%;
      position: absolute;
      left: 50%;
      top: 55%;
      transform: translate(-50%, -50%);
    }

    .fade-enter-active,
    .fade-leave-active {
      transition: opacity 0.5s;
    }

    .fade-enter,
    .fade-leave-to {
      opacity: 0;
    }

    .amap-logo {
      display: none;
      opacity: 0 !important;
    }

    .amap-copyright {
      opacity: 0;
    }

    .l7-right {
      display: none;
    }

    .cesium-viewer-bottom {
      display: none;
    }

    .background {
      width: 100%;
      height: 100%;
      position: absolute;
      background-size: 100% 100% !important;
      top: 0;
      z-index: -1;
    }

    .x-spreadsheet-sheet {
      pointer-events: none;
    }

    .x-spreadsheet-toolbar {
      display: none;
    }

    .el-carousel__container {
      height: 100% !important;
    }

    [v-cloak] {
      display: none !important;
    }

    .l7-left {
      display: none;
    }
  </style>
  <style type="text/css">
    .progress-dialog {
      background-color: #0d1d31 !important;
      border: 1px solid #1e3d68 !important;
    }

    .scroll-list1::-webkit-scrollbar-thumb {
      background-color: #1e3d68;
      /* 淡蓝色 */
      border-radius: 4px;
      /* 圆角 */
    }

    .progress-dialog .el-dialog__header {
      background: url('./static/dialog-header.png') no-repeat;
      background-size: 100% 100%;
      padding: 15px 20px;
      border-bottom: 1px solid #1e3d68;
    }

    .progress-dialog .el-dialog__title {
      color: #fff;
      font-size: 18px;
      font-weight: bold;
    }

    .progress-dialog .el-dialog__headerbtn .el-dialog__close {
      color: #fff;
    }

    .progress-dialog .el-dialog__body {
      padding: 20px;
      color: #fff;
      max-height: 810px;
      overflow-y: auto;
    }

    .progress-dialog .el-dialog__footer {
      border-top: 1px solid #1e3d68;
      padding: 15px 20px;
    }

    .progress-dialog .el-table th.is-leaf {
      text-align: center;
      /* 居中表头 */
    }

    .progress-dialog .el-table .cell {
      text-align: center;
      /* 居中内容 */
    }

    /* 表格样式 */
    .section-title {
      font-size: 23px;
      font-weight: bold;
      color: #fff;
      margin-bottom: 10px;
      padding-left: 10px;
      text-align: center;

    }

    .table-container {
      margin-bottom: 15px;
    }

    .table-container .el-table {
      background-color: transparent !important;
      color: #fff !important;
    }

    .table-container .el-table::before {
      display: none;
    }

    .table-container .el-table th {
      background-color: #0d2c5a !important;
      border-color: #1e3d68 !important;
      color: #fff !important;
      font-weight: normal;
    }

    .table-container .el-table td {
      background-color: #0d1d31 !important;
      border-color: #1e3d68 !important;
      color: #fff !important;
    }

    .table-container .el-table--border {
      border-color: #1e3d68 !important;
    }

    .table-container .el-table--border::after {
      background-color: #1e3d68 !important;
    }

    .table-container .el-table--border th,
    .table-container .el-table--border td {
      border-right-color: #1e3d68 !important;
    }

    .table-container .el-table__row:hover>td {
      background-color: rgba(30, 61, 104, 0.3) !important;
    }

    .table-container .el-button--primary {
      background-color: #188df0;
      border-color: #188df0;
      padding: 5px 10px;
      font-size: 12px;
    }

    .table-container .el-input__inner {
      background-color: rgba(255, 255, 255, 0.1);
      border-color: #1e3d68;
      color: #fff;
      text-align: center;
    }

    /* 滚动条样式 */
    .progress-dialog .el-dialog__body::-webkit-scrollbar {
      width: 6px;
    }

    .progress-dialog .el-dialog__body::-webkit-scrollbar-thumb {
      background-color: #1e3d68;
      border-radius: 3px;
    }

    .progress-dialog .el-dialog__body::-webkit-scrollbar-track {
      background-color: rgba(30, 61, 104, 0.1);
    }

    /* 调整关闭按钮的位置 */
    .progress-dialog .el-dialog__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    /* 隐藏默认的关闭图标 */
    .progress-dialog .el-dialog__headerbtn {
      display: none;
    }

    /* 表格内滚动条样式 */
    .el-table__body-wrapper::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    .el-table__body-wrapper::-webkit-scrollbar-thumb {
      background-color: #1e3d68;
      border-radius: 3px;
    }

    .el-table__body-wrapper::-webkit-scrollbar-track {
      background-color: rgba(30, 61, 104, 0.1);
    }

    .dv-border-box-12 {
      position: relative;
      width: 100%;
      height: 100%;
    }

    .dv-border-box-12 .dv-border-svg-container {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0px;
      left: 0px;
    }

    .dv-border-box-12 .border-box-content {
      position: relative;
      width: 100%;
      height: 100%;
    }

    /* 添加滚动动画样式 */
    .scroll-list {
      position: relative;
    }

    .scroll-content {
      animation: scrollUp linear infinite;
    }

    .scroll-content:hover {
      animation-play-state: paused;
    }

    @keyframes scrollUp {
      0% {
        transform: translateY(0);
      }

      100% {
        transform: translateY(-50%);
      }
    }

    .analysis-dialog {
      background: rgba(0, 19, 52, 0.9) !important;
    }

    .analysis-dialog .el-dialog__title {
      color: #fff;
    }

    .analysis-dialog .el-dialog__header {
      border-bottom: 1px solid rgba(124, 231, 253, 0.3);
    }

    .analysis-dialog .el-dialog__headerbtn .el-dialog__close {
      color: #fff;
    }

    .analysis-dialog .el-button--primary:hover {
      background-color: #409eff !important;
      border-color: #409eff !important;
    }

    .analysis-dialog .el-button--primary:focus {
      background-color: #188df0 !important;
      border-color: #188df0 !important;
    }

    .analysis-dialog .el-date-editor {
      background-color: transparent;
    }

    .analysis-dialog .el-date-editor .el-range-input {
      color: #fff;
      background-color: transparent;
    }

    .analysis-dialog .el-date-editor .el-range-separator {
      color: #fff;
    }

    .analysis-dialog .el-button--primary[loading] {
      background-color: #188df0;
      border-color: #188df0;
      opacity: 0.8;
    }


    .history-dialog {
      background: rgba(0, 19, 52, 0.9) !important;
    }

    .history-dialog .el-dialog__title {
      color: #fff;
    }

    .history-dialog .el-dialog__header {
      border-bottom: 1px solid rgba(124, 231, 253, 0.3);
    }

    .history-dialog .el-dialog__headerbtn .el-dialog__close {
      color: #fff;
    }

    .history-dialog .el-button--primary:hover {
      background-color: #409eff !important;
      border-color: #409eff !important;
    }

    .history-dialog .el-button--primary:focus {
      background-color: #188df0 !important;
      border-color: #188df0 !important;
    }

    .history-dialog .el-date-editor {
      background-color: transparent;
    }

    .history-dialog .el-date-editor .el-range-input {
      color: #fff;
      background-color: transparent;
    }

    .history-dialog .el-date-editor .el-range-separator {
      color: #fff;
    }

    .history-dialog .el-button--primary[loading] {
      background-color: #188df0;
      border-color: #188df0;
      opacity: 0.8;
    }


    .history-dialog .el-table,
    .history-dialog .el-table__header-wrapper,
    .history-dialog .el-table__body-wrapper {
      background-color: transparent !important;
    }

    .history-dialog .el-table::before {
      display: none;
      /* 移除表格底部边框 */
    }

    /* 修复表格背景和文字颜色 */
    .history-dialog .el-table td,
    .history-dialog .el-table th {
      background-color: transparent !important;
    }

    .history-dialog .el-table--striped .el-table__body tr.el-table__row--striped td {
      background-color: rgba(0, 60, 160, 0.3) !important;
    }

    /* 表格内的所有文字颜色 */
    .history-dialog .el-table {
      color: #fff !important;
    }

    .history-dialog .el-table th,
    .history-dialog .el-table td,
    .history-dialog .el-table tr {
      color: #fff !important;
    }

    .history-dialog .el-table th,
    .el-table tr {
      background-color: transparent !important;
    }


    .history-dialog .el-select .el-input__inner {
      background-color: transparent !important;
      color: #fff !important;
      border-color: rgba(124, 231, 253, 0.3) !important;
    }

    /* 下拉面板样式 */
    .el-select-dropdown.el-popper {
      background-color: rgba(0, 19, 52, 0.95) !important;
      border: 1px solid rgba(124, 231, 253, 0.3) !important;
    }

    .el-select-dropdown.el-popper .el-select-dropdown__item {
      background-color: transparent !important;
      color: #fff !important;
    }

    .el-select-dropdown.el-popper .el-select-dropdown__item.hover,
    .el-select-dropdown.el-popper .el-select-dropdown__item:hover {
      background-color: rgba(0, 60, 160, 0.3) !important;
    }

    .el-select-dropdown.el-popper .el-select-dropdown__item.selected {
      color: #188df0 !important;
      background-color: rgba(24, 141, 240, 0.1) !important;
    }



    .history-dialog .el-pagination {
      margin-top: 20px;
    }

    .history-dialog .el-pagination button {
      background-color: transparent !important;
      color: #fff !important;
    }

    .history-dialog .el-pagination .el-pager li {
      background-color: transparent !important;
      color: #fff !important;
      border: 1px solid rgba(124, 231, 253, 0.3);
    }

    .history-dialog .el-pagination .el-pager li.active {
      color: #188df0 !important;
      border-color: #188df0;
    }

    .history-dialog .el-pagination .el-pager li:hover {
      color: #188df0 !important;
    }

    .history-dialog .el-pagination .btn-prev,
    .history-dialog .el-pagination .btn-next {
      background-color: transparent !important;
      color: #fff !important;
      border: 1px solid rgba(124, 231, 253, 0.3);
    }

    .history-dialog .el-pagination .btn-prev:hover,
    .history-dialog .el-pagination .btn-next:hover {
      color: #188df0 !important;
    }

    .history-dialog .el-pagination .el-pagination__total,
    .history-dialog .el-pagination .el-pagination__jump {
      color: #fff !important;
    }

    /* 分页器输入框样式 */
    .history-dialog .el-pagination .el-input__inner {
      background-color: transparent !important;
      color: #fff !important;
      border-color: rgba(124, 231, 253, 0.3) !important;
    }

    .history-dialog .el-pagination .el-input__inner:focus {
      border-color: #188df0 !important;
    }

    /* 下拉框箭头颜色 */
    .history-dialog .el-select .el-input .el-select__caret {
      color: #fff !important;
    }

    /* 确保下拉面板的箭头样式正确 */
    .el-popper .popper__arrow,
    .el-popper .popper__arrow::after {
      border-bottom-color: rgba(0, 19, 52, 0.95) !important;
    }

    /* 禁用状态的样式 */
    .history-dialog .el-pagination button:disabled {
      background-color: transparent !important;
      color: rgba(255, 255, 255, 0.4) !important;
    }

    .history-dialog .el-select .el-input.is-disabled .el-input__inner {
      background-color: transparent !important;
      color: rgba(255, 255, 255, 0.4) !important;
    }

    .el-form-item__label {
      color: #fff !important;
    }

    /*# sourceURL=main.vue */
    /*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm1haW4udnVlIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usa0JBQWtCO0VBQ2xCLFdBQVc7RUFDWCxZQUFZO0FBQ2Q7QUFDQTtFQUNFLGtCQUFrQjtFQUNsQixXQUFXO0VBQ1gsWUFBWTtFQUNaLFFBQVE7RUFDUixTQUFTO0FBQ1g7QUFDQTtFQUNFLGtCQUFrQjtFQUNsQixXQUFXO0VBQ1gsWUFBWTtBQUNkIiwiZmlsZSI6Im1haW4udnVlIiwic291cmNlc0NvbnRlbnQiOlsiLmR2LWJvcmRlci1ib3gtMTIge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG59XG4uZHYtYm9yZGVyLWJveC0xMiAuZHYtYm9yZGVyLXN2Zy1jb250YWluZXIge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG4gIHRvcDogMHB4O1xuICBsZWZ0OiAwcHg7XG59XG4uZHYtYm9yZGVyLWJveC0xMiAuYm9yZGVyLWJveC1jb250ZW50IHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xufVxuIl19 */
  </style>
</head>

<body>
  <div id="body" element-loading-background="rgba(0, 0, 0, 0.8)" element-loading-text="拼命加载中" class="" style="zoom: 1">
    <div class="echartsList">
      <ul :style="ulStyle">
        <!--      <li id="1dc1a6d0-dda6-47f3-8552-2d4e11e8861achart-item" class="chart-item" style="-->
        <!--              zoom: 1;-->
        <!--              will-change: auto;-->
        <!--              width: 389px;-->
        <!--              height: 57px;-->
        <!--              transform: translate3d(1484px, 611px, 0px);-->
        <!--              z-index: 2;-->
        <!--              background-color: transparent;-->
        <!--            ">-->
        <!--        <div class="chart-box animate__animated background selfAnimate_none"-->
        <!--             style="background: url('') no-repeat; transform: rotate(0deg)">-->
        <!--          <div id="1dc1a6d0-dda6-47f3-8552-2d4e11e8861a"-->
        <!--               class="main animate__animated animate__slideInLeft">-->
        <!--            <p class="baseCss" id="1dc1a6d0-dda6-47f3-8552-2d4e11e8861a" style="-->
        <!--                    justify-content: flex-start;-->
        <!--                    font-size: 22px;-->
        <!--                    color: rgb(255, 255, 255);-->
        <!--                    font-family: FZHZGBJW;-->
        <!--                    font-style: normal;-->
        <!--                    font-weight: 500;-->
        <!--                  ">-->
        <!--              <span>| {{video4}}</span>-->
        <!--            </p>-->
        <!--          </div>-->
        <!--        </div>-->
        <!--      </li>-->
        <li id="af1d2c34-95f8-4ade-b67a-5f4df4e9aaeachart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 389px;
height:200px;
              transform: translate3d(1484px, 874px, 0px);
              z-index: 2;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="af1d2c34-95f8-4ade-b67a-5f4df4e9aaea" class="main animate__animated animate__slideInLeft">

              <div class="h5videodiv5">
                <video id="videoElement5" width="90%" height="100%" autoplay controls style="background-color:#000000"
                  muted autoplay></video>
              </div>
            </div>
          </div>
        </li>
        <li id="1dc1a6d0-dda6-47f3-8552-2d4e11e8861achart-item" class="chart-item" style="
              zoom: 1;
              will-change: auto;
              width: 389px;
height:200px;
              transform: translate3d(1484px, 662px, 0px);
              z-index: 2;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="1dc1a6d0-dda6-47f3-8552-2d4e11e8861a" class="main animate__animated animate__slideInLeft">
              <div class="h5videodiv4">
                <video id="videoElement4" width="90%" height="100%" autoplay controls style="background-color:#000000"
                  muted autoplay></video>
              </div>
            </div>
          </div>
        </li>
        <!--      <li id="ba2b9f99-da17-4c03-a040-29c4624350fechart-item" class="chart-item" style="-->
        <!--              zoom: 1;-->
        <!--              will-change: auto;-->
        <!--              width: 389px;-->
        <!--              height: 57px;-->
        <!--              transform: translate3d(1484px, 404px, 0px);-->
        <!--              z-index: 2;-->
        <!--              background-color: transparent;-->
        <!--            ">-->
        <!--        <div class="chart-box animate__animated background selfAnimate_none"-->
        <!--             style="background: url('') no-repeat; transform: rotate(0deg)">-->
        <!--          <div id="ba2b9f99-da17-4c03-a040-29c4624350fe"-->
        <!--               class="main animate__animated animate__slideInLeft">-->
        <!--            <p class="baseCss" id="ba2b9f99-da17-4c03-a040-29c4624350fe" style="-->
        <!--                    justify-content: flex-start;-->
        <!--                    font-size: 22px;-->
        <!--                    color: rgb(255, 255, 255);-->
        <!--                    font-family: FZHZGBJW;-->
        <!--                    font-style: normal;-->
        <!--                    font-weight: 500;-->
        <!--                  ">-->
        <!--              <span>| {{video3}}</span>-->
        <!--            </p>-->
        <!--          </div>-->
        <!--        </div>-->
        <!--      </li>-->

        <li id="ba2b9f99-da17-4c03-a040-29c4624350fechart-item" class="chart-item" style="
              zoom: 1;
              will-change: auto;
              width: 389px;
height:200px;
              transform: translate3d(1484px, 448px, 0px);
              z-index: 2;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="ba2b9f99-da17-4c03-a040-29c4624350fe" class="main animate__animated animate__slideInLeft">

              <div class="h5videodiv3">
                <video id="videoElement3" width="90%" height="100%" autoplay controls style="background-color:#000000"
                  muted autoplay></video>
              </div>

            </div>
          </div>
        </li>
        <!--      <li id="6a474af4-e9c8-4c7f-af83-c0d4c71ce106chart-item" class="chart-item" style="-->
        <!--              zoom: 1;-->
        <!--              will-change: auto;-->
        <!--              width: 389px;-->
        <!--              height: 57px;-->
        <!--              transform: translate3d(1484px, 206px, 0px);-->
        <!--              z-index: 2;-->
        <!--              background-color: transparent;-->
        <!--            ">-->
        <!--        <div class="chart-box animate__animated background selfAnimate_none"-->
        <!--             style="background: url('') no-repeat; transform: rotate(0deg)">-->
        <!--          <div id="6a474af4-e9c8-4c7f-af83-c0d4c71ce106"-->
        <!--               class="main animate__animated animate__slideInLeft">-->
        <!--            <p class="baseCss" id="6a474af4-e9c8-4c7f-af83-c0d4c71ce106" style="-->
        <!--                    justify-content: flex-start;-->
        <!--                    font-size: 22px;-->
        <!--                    color: rgb(255, 255, 255);-->
        <!--                    font-family: FZHZGBJW;-->
        <!--                    font-style: normal;-->
        <!--                    font-weight: 500;-->
        <!--                  ">-->
        <!--              <span>| {{video2}}</span>-->
        <!--            </p>-->
        <!--          </div>-->
        <!--        </div>-->
        <!--      </li>-->

        <li id="6a474af4-e9c8-4c7f-af83-c0d4c71ce106chart-item" class="chart-item" style="
              zoom: 1;
              will-change: auto;
              width: 389px;
height:200px;
              transform: translate3d(1482px, 242px, 0px);
              z-index: 2;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="6a474af4-e9c8-4c7f-af83-c0d4c71ce106" class="main animate__animated animate__slideInLeft">

              <div class="h5videodiv2">
                <video id="videoElement2" width="90%" height="100%" autoplay controls style="background-color:#000000"
                  muted autoplay></video>
              </div>
            </div>
          </div>
        </li>
        <!--      <li id="dd2897d5-da8a-4b9e-8d5e-f02c785a3644chart-item" class="chart-item" style="-->
        <!--              zoom: 1;-->
        <!--              will-change: auto;-->
        <!--              width: 389px;-->
        <!--              height: 57px;-->
        <!--              transform: translate3d(1484px, 11px, 0px);-->
        <!--              z-index: 2;-->
        <!--              background-color: transparent;-->
        <!--            ">-->
        <!--        <div class="chart-box animate__animated background selfAnimate_none"-->
        <!--             style="background: url('') no-repeat; transform: rotate(0deg)">-->
        <!--          <div id="dd2897d5-da8a-4b9e-8d5e-f02c785a3644"-->
        <!--               class="main animate__animated animate__slideInLeft">-->
        <!--            <p class="baseCss" id="dd2897d5-da8a-4b9e-8d5e-f02c785a3644" style="-->
        <!--                    justify-content: flex-start;-->
        <!--                    font-size: 22px;-->
        <!--                    color: rgb(255, 255, 255);-->
        <!--                    font-family: FZHZGBJW;-->
        <!--                    font-style: normal;-->
        <!--                    font-weight: 500;-->
        <!--                  ">-->
        <!--              <span>| {{video1}}</span>-->
        <!--            </p>-->
        <!--          </div>-->
        <!--        </div>-->
        <!--      </li>-->

        <li id="af1d2c34-95f8-4ade-b67a-5f4df4e9aaeachart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 389px;
height:200px;
              transform: translate3d(1484px, 35px, 0px);
              z-index: 2;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="af1d2c34-95f8-4ade-b67a-5f4df4e9aaea" class="main animate__animated animate__slideInLeft">

              <div class="h5videodiv1">
                <video id="videoElement1" width="90%" height="100%" autoplay controls style="background-color:#000000"
                  muted autoplay></video>
              </div>
            </div>
          </div>
        </li>
        <!-- <li
  id="52f2528b-6830-4651-864d-afba8d9eb1f1chart-item"
  class="chart-item"
  style="
    zoom: 1;
    will-change: auto;
    width: 214px;
    height: 56px;
    transform: translate3d(1247px, 891px, 0px);
    z-index: 1016;
    background-color: transparent;
  "
>
  <div
    class="chart-box animate__animated background selfAnimate_none"
    style="background: url('') no-repeat; transform: rotate(0deg)"
  >
    <div
      id="52f2528b-6830-4651-864d-afba8d9eb1f1"
      class="main animate__animated animate__slideInLeft"
    >
      <p
        class="baseCss"
        id="52f2528b-6830-4651-864d-afba8d9eb1f1"
        style="
          justify-content: flex-start;
          font-size: 25px;
          color: rgb(255, 255, 255);
          font-family: FZHZGBJW;
          font-style: normal;
          font-weight: 500;
        "
      >
        <span>| 开机率 75%</span>
      </p>
    </div>
  </div>
</li> -->

        <li id="b76fed82-ac78-4267-96e5-c3cb0845b6cbchart-item" class="chart-item" style="
    zoom: 0.997396;
    will-change: auto;
    width: 389px;
    height: 57px;
    transform: translate3d(34px, 326px, 0px);
    z-index: 1010;
    background-color: transparent;
  ">
          <div class="chart-box animate__animated background selfAnimate_none"
              style="background: url('') no-repeat; transform: rotate(0deg)">
              <div id="b76fed82-ac78-4267-96e5-c3cb0845b6cb"
                  class="main animate__animated animate__slideInLeft">
                  <p class="baseCss" id="b76fed82-ac78-4267-96e5-c3cb0845b6cb" style="
          justify-content: flex-start;
          font-size: 19px;
          color: rgb(255, 255, 255);
          font-family: FZHZGBJW;
          font-style: normal;
          font-weight: 500;
        ">
                      <span>| 锚杆锚索应力</span>
                  </p>
              </div>
          </div>
      </li>
	  
	  
	  <li id="b76fed82-ac78-4267-96e5-c3cb0845b6cbchart-item" class="chart-item" style="
	    zoom: 0.997396;
	    will-change: auto;
	    width: 389px;
	    height: 57px;
	    transform: translate3d(34px, 426px, 0px);
	    z-index: 1010;
	    background-color: transparent;
	  ">
	          <div class="chart-box animate__animated background selfAnimate_none"
	              style="background: url('') no-repeat; transform: rotate(0deg)">
	              <div id="b76fed82-ac78-4267-96e5-c3cb0845b6cb"
	                  class="main animate__animated animate__slideInLeft">
	                  <p class="baseCss" id="b76fed82-ac78-4267-96e5-c3cb0845b6cb" style="
	          justify-content: flex-start;
	          font-size: 19px;
	          color: rgb(255, 255, 255);
	          font-family: FZHZGBJW;
	          font-style: normal;
	          font-weight: 500;
	        ">
	                      <span>| 离层仪数据</span>
	                  </p>
	              </div>
	          </div>
	      </li>

        <li id="b76fed82-ac78-4267-96e5-c3cb0845b6cbchart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 75%;
              transform: translate3d(34px, 348px, 0px);
              z-index: 1010;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none" style="
                   background: url('') no-repeat;
                   transform: rotate(0deg);
                   width: 100%;
                   height: 100%;
               ">
            <div id="b76fed82-ac78-4267-96e5-c3cb0845b6cb" class="main animate__animated animate__slideInLeft">
              <div class="chart-box" style="width: 100%">
				  
                <div class="main" style="display: flex; flex-direction: column;">
                  <!-- 上方图表 -->
				 
                  <div style="width: 100%; margin-bottom: 10px;">
                    <div ref="leftBarChart" style="height: 100px" :style="{
                                       transform: `scale(${zoom})`,
                                       transformOrigin: 'left top'
                                   }"></div>
                  </div>

                  <!-- 下方图表 -->
                  <div style="width: 100%;">
                    <div ref="rightBarChart" style="height: 100px" :style="{
                                       transform: `scale(${zoom})`,
                                       transformOrigin: 'left top'
                                   }"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </li>

        <!-- <li id="1977da78-fbc7-4f2e-93de-1463c9012df3chart-item" class="chart-item" style="
    zoom: 0.997396;
    will-change: auto;
    width: 389px;
    height: 57px;
    transform: translate3d(1004px, 96px, 0px);
    z-index: 1009;
    background-color: transparent;
  ">
          <div class="chart-box animate__animated background selfAnimate_none"
              style="background: url('') no-repeat; transform: rotate(0deg)">
              <div id="1977da78-fbc7-4f2e-93de-1463c9012df3"
                  class="main animate__animated animate__slideInLeft">
                  <p class="baseCss" id="1977da78-fbc7-4f2e-93de-1463c9012df3" style="
          justify-content: flex-start;
          font-size: 25px;
          color: rgb(255, 255, 255);
          font-family: FZHZGBJW;
          font-style: normal;
          -font-weight: 500;
        ">
                      <span>| 安全监控数据</span>
                  </p>
              </div>
          </div>
      </li> -->
        <li id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74achart-item" class="chart-item" style="
                zoom: 0.997396;
                will-change: auto;
                width: 1452px;
      height: 258px;
      transform: translate3d(9px, 325px, 0px);
                z-index: 2;
                background-color: transparent;
              ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74a" class="main animate__animated animate__slideInLeft">
              <div full="" id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74a">
                <img src="static/biankuang.png" style="width: 100%;height: 258px;" />
              </div>
            </div>
        </li>

        <li id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74achart-item" class="chart-item" style="
                zoom: 0.997396;
                will-change: auto;
                width: 1452px;
      height: 258px;
      transform: translate3d(9px, 73px, 0px);
                z-index: 2;
                background-color: transparent;
              ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74a" class="main animate__animated animate__slideInLeft">
              <div full="" id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74a">
                <img src="static/biankuang.png" style="width: 100%;height: 258px;" />
              </div>
            </div>
        </li>


        <li id="1977da78-fbc7-4f2e-93de-1463c9012df3chart-item" class="chart-item"
          style="zoom: 0.997396; will-change: auto; width: 500px;    height: 227px; transform: translate3d(960px, 88px, 0px); z-index: 1009; background-color: transparent;">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg);">
            <div id="1977da78-fbc7-4f2e-93de-1463c9012df3" class="main animate__animated animate__slideInLeft"
              style="display: flex; flex-wrap: wrap; gap: 10px; padding: 10px; text-align: center; font-size: 22px;">
              <!-- 第一行 -->
              <!-- T1 -->
              <div
                style="width: calc((500px - 3 * 10px - 2 * 10px) / 4); box-sizing: border-box; background: #0C2A52; border-radius: 4px; padding: 10px; display: flex; flex-direction: column; justify-content: space-between;">
                <div
                  style="display: flex; align-items: center; gap: 5px; margin-bottom: 18px; justify-content: center;">
                  <i style="color: #ffffff; font-size: 22px;">T1</i>
                </div>
                <div style="color: #ffffff; font-size: 24px; ;">
                  {{t1Amount}}<span style="font-size: 14px; margin-left: 2px;">%</span>
                </div>
              </div>

              <!-- 第二行 -->
              <!-- T2 -->
              <div
                style="width: calc((500px - 3 * 10px - 2 * 10px) / 4); box-sizing: border-box; background: #0C2A52; border-radius: 4px; padding: 10px; display: flex; flex-direction: column; justify-content: space-between;">
                <div
                  style="display: flex; align-items: center; gap: 5px; margin-bottom: 18px; justify-content: center;">
                  <i style="color: #ffffff; font-size: 22px;">T2</i>
                </div>
                <div style="color: #ffffff; font-size: 24px; ;">
                  {{t2Amount}}<span style="font-size: 14px; margin-left: 2px;">%</span>
                </div>
              </div>

              <!-- T进 -->
              <div
                style="width: calc((500px - 3 * 10px - 2 * 10px) / 4); box-sizing: border-box; background: #0C2A52; border-radius: 4px; padding: 10px; display: flex; flex-direction: column; justify-content: space-between;">
                <div
                  style="display: flex; align-items: center; gap: 5px; margin-bottom: 18px; justify-content: center;">
                  <i style="color: #ffffff; font-size: 22px;">T进</i>
                </div>
                <div style="color: #ffffff; font-size: 24px; ;">
                  {{t3Amount}}<span style="font-size: 14px; margin-left: 2px;">%</span>
                </div>
              </div>



              <!-- 新增指标 2（例如光照） -->
              <div
                style="width: calc((500px - 3 * 10px - 2 * 10px) / 4); box-sizing: border-box; background: #0C2A52; border-radius: 4px; padding: 10px; display: flex; flex-direction: column; justify-content: space-between;">
                <div
                  style="display: flex; align-items: center; gap: 5px; margin-bottom: 18px; justify-content: center;">
                  <i style="color: #ffffff; font-size: 22px;">🌞</i>
                  <span style="color: #fff; font-size: 22px;">CO</span>
                </div>
                <div style="color: #ffffff; font-size: 24px; ;">
                  {{t3Amount}}<span style="font-size: 14px; margin-left: 2px;">ppm</span>
                </div>
              </div>

              <!-- 温度 -->
              <div
                style="width: calc((500px - 3 * 10px - 2 * 10px) / 4); box-sizing: border-box; background: #0C2A52; border-radius: 4px; padding: 10px; display: flex; flex-direction: column; justify-content: space-between;">
                <div
                  style="display: flex; align-items: center; gap: 5px; margin-bottom: 18px; justify-content: center;">
                  <i style="color: #7ce7fd; font-size: 22px;"></i>
                  <span style="color: #fff; font-size: 22px;">烟雾</span>
                </div>
                <div style="color: #ffffff; font-size: 24px; ;">
                  {{smoke}}<span style="font-size: 14px; margin-left: 2px;">ppm</span>
                </div>
              </div>

              <!-- 粉尘 -->
              <div
                style="width: calc((500px - 3 * 10px - 2 * 10px) / 4); box-sizing: border-box; background: #0C2A52; border-radius: 4px; padding: 10px; display: flex; flex-direction: column; justify-content: space-between;">
                <div
                  style="display: flex; align-items: center; gap: 5px; margin-bottom: 18px; justify-content: center;">
                  <i style="color: #7ce7fd; font-size: 22px;">🌡️</i>
                  <span style="color: #fff; font-size: 22px;">温度</span>
                </div>
                <div style="color: #ffffff; font-size: 24px; ;">
                  {{temperature}}<span style="font-size: 14px; margin-left: 2px;">℃</span>
                </div>
              </div>

              <!-- 风速 -->
              <div
                style="width: calc((500px - 3 * 10px - 2 * 10px) / 4); box-sizing: border-box; background: #0C2A52; border-radius: 4px; padding: 10px; display: flex; flex-direction: column; justify-content: space-between;">
                <div
                  style="display: flex; align-items: center; gap: 5px; margin-bottom: 18px; justify-content: center;">
                  <i style="color: #7ce7fd; font-size: 22px;">💨</i>
                  <span style="color: #fff; font-size: 22px;">粉尘</span>
                </div>
                <div style="color: #ffffff; font-size: 24px; ">
                  {{dust}}<span style="font-size: 14px; margin-left: 2px;">mg/m³</span>
                </div>
              </div>
              <!-- 新增指标 1（例如湿度） -->
              <div
                style="width: calc((500px - 3 * 10px - 2 * 10px) / 4); box-sizing: border-box; background: #0C2A52; border-radius: 4px; padding: 10px; display: flex; flex-direction: column; justify-content: space-between;">
                <div
                  style="display: flex; align-items: center; gap: 5px; margin-bottom: 18px; justify-content: center;">
                  <i style="color: #7ce7fd; font-size: 22px;">🌪️</i>
                  <span style="color: #fff; font-size: 22px;">风速</span>
                </div>
                <div style="color: #ffffff; font-size: 24px; ">
                  {{windSpeed}}<span style="font-size: 14px; margin-left: 2px;">m/s</span>
                </div>
              </div>


            </div>
          </div>
        </li>

        <!-- <li id="772e7d79-39ac-4f00-81c4-eeb728ff4178chart-item" class="chart-item" style="
    zoom: 0.997396;
    will-change: auto;
    width: 389px;
    height: 57px;
    transform: translate3d(518px, 77px, 0px);
    z-index: 1008;
    background-color: transparent;
  ">
          <div class="chart-box animate__animated background selfAnimate_none"
              style="background: url('') no-repeat; transform: rotate(0deg)">
              <div id="772e7d79-39ac-4f00-81c4-eeb728ff4178"
                  class="main animate__animated animate__slideInLeft">
                  <p class="baseCss" id="772e7d79-39ac-4f00-81c4-eeb728ff4178" style="
          justify-content: flex-start;
          font-size: 25px;
          color: rgb(255, 255, 255);
          font-family: FZHZGBJW;
          font-style: normal;
          font-weight: 500;
        ">
                      <span>| 进尺数据</span>
                  </p>
              </div>
          </div>
      </li> -->

        <!--      <li id="772e7d79-39ac-4f00-81c4-eeb728ff4178chart-item" class="chart-item" style="-->
        <!--              zoom: 0.997396;-->
        <!--              will-change: auto;-->
        <!--              width: 468px;-->
        <!--              transform: translate3d(475px, 81px, 0px);-->
        <!--              z-index: 1008;-->
        <!--              background-color: transparent;-->
        <!--            ">-->
        <!--        <div class="chart-box animate__animated background selfAnimate_none"-->
        <!--             style="background: url('') no-repeat; transform: rotate(0deg)">-->
        <!--          <div style="display: flex; gap: 20px; padding: 5px">-->
        <!--            &lt;!&ndash; 进尺数据面板 &ndash;&gt;-->
        <!--            <div style="width: 100%;">-->
        <!--              &lt;!&ndash; 三班进尺数据 &ndash;&gt;-->
        <!--              <div style="-->
        <!--              display: flex;-->
        <!--              gap: 15px;-->
        <!--              margin-bottom: 5px;-->
        <!--            ">-->
        <!--                <div style="-->
        <!--                flex: 1;-->
        <!--                background: #0C2A52;-->
        <!--                border-radius: 4px;-->
        <!--                padding: 9px;-->
        <!--              ">-->
        <!--                  <div style="-->
        <!--                  display: flex;-->
        <!--                  align-items: center;-->
        <!--                  gap: 5px;-->
        <!--                  margin-bottom: 2px;-->
        <!--                ">-->
        <!--                    <span style="color: #ffffff; font-size: 16px">夜班进尺</span>-->
        <!--                  </div>-->
        <!--                  <div style="-->
        <!--                  color: #ffffff;-->
        <!--                  font-size: 24px;-->

        <!--                  text-align: center;-->
        <!--                ">-->
        <!--                    {{nightProgress}}<span style="font-size: 14px; margin-left: 3px">m</span>-->
        <!--                  </div>-->
        <!--                </div>-->
        <!--                &lt;!&ndash; 早班进尺 &ndash;&gt;-->
        <!--                <div style="-->
        <!--                flex: 1;-->
        <!--               background: #0C2A52;-->
        <!--                border-radius: 4px;-->
        <!--                padding: 9px;-->
        <!--              ">-->

        <!--                  <div style="-->
        <!--                  display: flex;-->
        <!--                  align-items: center;-->
        <!--                  gap: 5px;-->
        <!--                  margin-bottom: 2px;-->
        <!--                ">-->
        <!--                    <span style="color: #ffffff; font-size: 16px">早班进尺</span>-->
        <!--                  </div>-->
        <!--                  <div style="-->
        <!--                  color: #ffffff;-->
        <!--                  font-size: 24px;-->

        <!--                  text-align: center;-->
        <!--                ">-->
        <!--                    {{morningProgress}}<span style="font-size: 14px; margin-left: 3px">m</span>-->
        <!--                  </div>-->
        <!--                </div>-->

        <!--                &lt;!&ndash; 中班进尺 &ndash;&gt;-->
        <!--                <div style="-->
        <!--                flex: 1;-->
        <!--                background: #0C2A52;-->
        <!--                border-radius: 4px;-->
        <!--                padding: 9px;-->
        <!--              ">-->
        <!--                  <div style="-->
        <!--                  display: flex;-->
        <!--                  align-items: center;-->
        <!--                  gap: 5px;-->
        <!--                  margin-bottom: 2px;-->
        <!--                ">-->
        <!--                    <span style="color: #ffffff; font-size: 16px">中班进尺</span>-->
        <!--                  </div>-->
        <!--                  <div style="-->
        <!--                  color: #ffffff;-->
        <!--                  font-size: 24px;-->

        <!--                  text-align: center;-->
        <!--                ">-->
        <!--                    {{dayProgress}}<span style="font-size: 14px; margin-left: 3px">m</span>-->
        <!--                  </div>-->
        <!--                </div>-->

        <!--                &lt;!&ndash; 晚班进尺 &ndash;&gt;-->

        <!--              </div>-->

        <!--              <div style="-->
        <!--              display: flex;-->
        <!--              gap: 15px;-->
        <!--              margin-bottom: 5px;-->
        <!--            ">-->
        <!--                &lt;!&ndash; 早班进尺 &ndash;&gt;-->
        <!--                <div style="-->
        <!--                flex: 1;-->
        <!--                background: #0C2A52;-->
        <!--                border-radius: 4px;-->
        <!--                padding: 9px;-->
        <!--              ">-->
        <!--                  <div style="-->
        <!--                  display: flex;-->
        <!--                  align-items: center;-->
        <!--                  gap: 5px;-->
        <!--                  margin-bottom: 2px;-->
        <!--                ">-->
        <!--                    <span style="color: #ffffff; font-size: 16px">昨日进尺</span>-->
        <!--                  </div>-->
        <!--                  <div style="-->
        <!--                  color: #ffffff;-->
        <!--                  font-size: 24px;-->
        <!--                  text-align: center;-->
        <!--                ">-->
        <!--                    {{dailyDis}}  <span style="font-size: 14px; margin-left: 3px">m</span>-->
        <!--                  </div>-->
        <!--                </div>-->

        <!--                &lt;!&ndash; 中班进尺 &ndash;&gt;-->
        <!--                <div style="-->
        <!--                flex: 1;-->
        <!--                background: #0C2A52;-->
        <!--                border-radius: 4px;-->
        <!--                padding: 9px;-->
        <!--              ">-->
        <!--                  <div style="-->
        <!--                  display: flex;-->
        <!--                  align-items: center;-->
        <!--                  gap: 5px;-->
        <!--                  margin-bottom: 2px;-->
        <!--                ">-->
        <!--                    <span style="color: #ffffff; font-size: 16px">当月进尺</span>-->
        <!--                  </div>-->
        <!--                  <div style="-->
        <!--                  color: #ffffff;-->
        <!--                  font-size: 24px;-->

        <!--                  text-align: center;-->
        <!--                ">-->
        <!--                   {{monthDis}} <span style="font-size: 14px; margin-left: 3px">m</span>-->
        <!--                  </div>-->
        <!--                </div>-->

        <!--                &lt;!&ndash; 晚班进尺 &ndash;&gt;-->
        <!--                <div style="-->
        <!--                flex: 1;-->
        <!--                background: #0C2A52;-->
        <!--                border-radius: 4px;-->
        <!--                padding: 9px;-->
        <!--              ">-->
        <!--                  <div style="-->
        <!--                  display: flex;-->
        <!--                  align-items: center;-->
        <!--                  gap: 5px;-->
        <!--                  margin-bottom: 2px;-->
        <!--                ">-->
        <!--                    <span style="color: #ffffff; font-size: 16px">当前总进尺</span>-->
        <!--                  </div>-->
        <!--                  <div style="-->
        <!--                  color: #ffffff;-->
        <!--                  font-size: 24px;-->

        <!--                  text-align: center;-->
        <!--                ">-->
        <!--                   {{totalDis}} <span style="font-size: 14px; margin-left: 3px">m</span>-->
        <!--                  </div>-->
        <!--                </div>-->
        <!--              </div>-->

        <!--              <div style="-->
        <!--              display: flex;-->
        <!--              gap: 15px;-->
        <!--              margin-bottom: 5px;-->
        <!--            ">-->
        <!--                &lt;!&ndash; 早班进尺 &ndash;&gt;-->
        <!--                <div style="-->
        <!--                flex: 1;-->
        <!--             background: #0C2A52;-->
        <!--                border-radius: 4px;-->
        <!--                padding: 9px;-->
        <!--              ">-->
        <!--                  <div style="-->
        <!--                  display: flex;-->
        <!--                  align-items: center;-->
        <!--                  gap: 5px;-->
        <!--                  margin-bottom: 2px;-->
        <!--                ">-->
        <!--                    <span style="color: #ffffff; font-size: 16px">当日计划进尺</span>-->
        <!--                  </div>-->
        <!--                  <div style="-->
        <!--                  color: #ffffff;-->
        <!--                  font-size: 24px;-->

        <!--                  text-align: center;-->
        <!--                ">-->
        <!--                   {{dailyPlanDis}} <span style="font-size: 14px; margin-left: 3px">m</span>-->
        <!--                  </div>-->
        <!--                </div>-->

        <!--                &lt;!&ndash; 中班进尺 &ndash;&gt;-->
        <!--                <div style="-->
        <!--                flex: 1;-->
        <!--              background: #0C2A52;-->
        <!--                border-radius: 4px;-->
        <!--                padding: 9px;-->
        <!--              ">-->
        <!--                  <div style="-->
        <!--                  display: flex;-->
        <!--                  align-items: center;-->
        <!--                  gap: 5px;-->
        <!--                  margin-bottom: 2px;-->
        <!--                ">-->
        <!--                    <span style="color: #ffffff; font-size: 16px">当月计划进尺</span>-->
        <!--                  </div>-->
        <!--                  <div style="-->
        <!--                  color: #ffffff;-->
        <!--                  font-size: 24px;-->

        <!--                  text-align: center;-->
        <!--                ">-->
        <!--                   {{monthPlanDis}} <span style="font-size: 14px; margin-left: 3px">m</span>-->
        <!--                  </div>-->
        <!--                </div>-->

        <!--                &lt;!&ndash; 晚班进尺 &ndash;&gt;-->
        <!--                <div style="-->
        <!--                flex: 1;-->
        <!--               background: #0C2A52;-->
        <!--                border-radius: 4px;-->
        <!--                padding: 9px;-->
        <!--              ">-->
        <!--                  <div style="-->
        <!--                  display: flex;-->
        <!--                  align-items: center;-->
        <!--                  gap: 5px;-->
        <!--                  margin-bottom: 2px;-->
        <!--                ">-->
        <!--                    <span style="color: #ffffff; font-size: 16px">计划总进尺</span>-->
        <!--                  </div>-->
        <!--                  <div style="-->
        <!--                  color: #ffffff;-->
        <!--                  font-size: 24px;-->

        <!--                  text-align: center;-->
        <!--                ">-->
        <!--                  {{totalPlanDis}}  <span style="font-size: 14px; margin-left: 3px">m</span>-->
        <!--                  </div>-->
        <!--                </div>-->
        <!--              </div>-->

        <!--              &lt;!&ndash; 当日和总进尺 &ndash;&gt;-->

        <!--            </div>-->
        <!--          </div>-->

        <!--        </div>-->
        <!--      </li>-->
        <li id="772e7d79-39ac-4f00-81c4-eeb728ff4178chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 468px;
              transform: translate3d(475px, 95px, 0px);
              z-index: 1008;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div style="display: flex; gap: 20px; padding: 5px">
              <!-- 进尺数据面板 -->
              <div style="width: 100%;">
                <!-- 三班进尺数据 -->
                <div style="
              display: flex;
              gap: 15px;
              margin-bottom: 27px;
            ">
                  <div style="
                flex: 1;
                background: #0C2A52;
                border-radius: 4px;
                padding: 19px;
              ">
                    <div style="
                  display: flex;
                  align-items: center;
                  gap: 5px;
                  margin-bottom: 2px;
                ">
                      <span style="color: #ffffff; font-size: 16px">夜班进尺</span>
                    </div>
                    <div style="
                  color: #ffffff;
                  font-size: 24px;
                  text-align: center;
                ">
                      {{nightProgress}}<span style="font-size: 14px; margin-left: 3px">m</span>
                    </div>
                  </div>
                  <!-- 早班进尺 -->
                  <div style="
                flex: 1;
               background: #0C2A52;
                border-radius: 4px;
                padding: 19px;
              ">
                    <div style="
                  display: flex;
                  align-items: center;
                  gap: 5px;
                  margin-bottom: 2px;
                ">
                      <span style="color: #ffffff; font-size: 16px">早班进尺</span>
                    </div>
                    <div style="
                  color: #ffffff;
                  font-size: 24px;
                  text-align: center;
                ">
                      {{morningProgress}}<span style="font-size: 14px; margin-left: 3px">m</span>
                    </div>
                  </div>
                  <!-- 中班进尺 -->
                  <div style="
                flex: 1;
                background: #0C2A52;
                border-radius: 4px;
                padding: 19px;
              ">
                    <div style="
                  display: flex;
                  align-items: center;
                  gap: 5px;
                  margin-bottom: 2px;
                ">
                      <span style="color: #ffffff; font-size: 16px">中班进尺</span>
                    </div>
                    <div style="
                  color: #ffffff;
                  font-size: 24px;
                  text-align: center;
                ">
                      {{dayProgress}}<span style="font-size: 14px; margin-left: 3px">m</span>
                    </div>
                  </div>
                </div>

                <div style="
              display: flex;
              gap: 15px;
              margin-bottom: 5px;
            ">
                  <!-- 昨日进尺 -->
                  <div style="
                flex: 1;
                background: #0C2A52;
                border-radius: 4px;
                padding: 19px;
              ">
                    <div style="
                  display: flex;
                  align-items: center;
                  gap: 5px;
                  margin-bottom: 2px;
                ">
                      <span style="color: #ffffff; font-size: 16px">日进尺</span>
                    </div>
                    <div style="
                  color: #ffffff;
                  font-size: 24px;
                  text-align: center;
                ">
                      {{dailyDis}} <span style="font-size: 14px; margin-left: 3px">m</span>
                    </div>
                  </div>

                  <!-- 当月进尺 -->
                  <div style="
                flex: 1;
                background: #0C2A52;
                border-radius: 4px;
                padding: 19px;
              ">
                    <div style="
                  display: flex;
                  align-items: center;
                  gap: 5px;
                  margin-bottom: 2px;
                ">
                      <span style="color: #ffffff; font-size: 16px">月度进尺</span>
                    </div>
                    <div style="
                  color: #ffffff;
                  font-size: 24px;
                  text-align: center;
                ">
                      {{monthDis}} <span style="font-size: 14px; margin-left: 3px">m</span>
                    </div>
                  </div>

                  <!-- 当前总进尺 -->
                  <div style="
                flex: 1;
                background: #0C2A52;
                border-radius: 4px;
                padding: 19px;
              ">
                    <div style="
                  display: flex;
                  align-items: center;
                  gap: 5px;
                  margin-bottom: 2px;
                ">
                      <span style="color: #ffffff; font-size: 16px">当前总进尺</span>
                    </div>
                    <div style="
                  color: #ffffff;
                  font-size: 24px;
                  text-align: center;
                ">
                      {{totalDis}} <span style="font-size: 14px; margin-left: 3px">m</span>
                    </div>
                  </div>
                </div>
                <!-- 当日和总进尺 -->
              </div>
            </div>
          </div>
        </li>
        <!-- <li id="6eb4e86e-c4bd-45a0-be4b-c2a92a02d828chart-item" class="chart-item" style="
    zoom: 0.997396;
    will-change: auto;
    width: 389px;
    height: 57px;
    transform: translate3d(34px, 77px, 0px);
    z-index: 1007;
    background-color: transparent;
  ">
          <div class="chart-box animate__animated background selfAnimate_none"
              style="background: url('') no-repeat; transform: rotate(0deg)">
              <div id="6eb4e86e-c4bd-45a0-be4b-c2a92a02d828"
                  class="main animate__animated animate__slideInLeft">
                  <p class="baseCss" id="6eb4e86e-c4bd-45a0-be4b-c2a92a02d828" style="
          justify-content: flex-start;
          font-size: 25px;
          color: rgb(255, 255, 255);
          font-family: FZHZGBJW;
          font-style: normal;
          font-weight: 500;
        ">
                      <span>| 人员定位数据</span>
                  </p>
              </div>
          </div>
      </li> -->



        <li id="6eb4e86e-c4bd-45a0-be4b-c2a92a02d828chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 427px;
              transform: translate3d(34px, 88px, 0px);
              z-index: 1007;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="6eb4e86e-c4bd-45a0-be4b-c2a92a02d828" class="main animate__animated animate__slideInLeft">
              <!-- 人员定位面板 -->
              <div style="
                    display: flex;
                    padding: 15px;
                    height: 100%;
                    color: #fff;
                  ">
                <!-- 左侧:人员图片和数量 -->
                <!-- 左侧:人员图片和数量 -->
                <div style="
                      flex: 1;
                      display: flex;
                      flex-direction: column;
                      align-items: center;
                    ">
                  <!-- 图片容器,使用relative定位以便放置当前人数 -->
                  <div style="position: relative; margin-bottom: 15px">
                    <img src="static/ren.png" style="width: 120px; height: 161px; margin-left: -22px" />

                    <!-- 当前人数显示,使用absolute定位到右上角 -->
                    <div style="
                          position: absolute;
                          top: -10px;
                          right: -20px;
                          text-align: center;
                        ">
                      <div style="
                            color: #7ce7fd;
                            font-size: 15px;
                            margin-bottom: 5px;
                          ">
                        当前人数
                      </div>
                      <div style="
                            background: rgba(0, 0, 0, 0.3);
                            border: 1px solid #7ce7fd;
                            padding: 2px 15px;
                            border-radius: 4px;
                            display: flex;
                            align-items: center;
                          ">
                        <span style="
                              color: #7ce7fd;
                              font-size: 17px;
                              font-family: DIGITALDREAMFAT;
                            ">{{currentPersonnel}}</span>
                      </div>
                    </div>
                  </div>

                  <!-- 定员人数显示,位于图片下方 -->
                  <div style="color: #7ce7fd; font-size: 16px">
                    定员人数:
                    <span style="color: #fff">{{totalPersonnel}}</span>
                  </div>
                </div>

                <!-- 分隔线 -->
                <div style="
                      width: 2px;
                      background: rgba(124, 231, 253, 0.3);
                      margin: 0 20px;
                      height: 200px;
                    "></div>
                <div style="flex: 3">
                  <!-- 表头 -->
                  <div style="
                        display: flex;
                        padding: 8px 0;
                        background: rgba(124, 231, 253, 0.1);
                        margin-bottom: 10px;
                      ">
                    <div style="
                          flex: 0.5;
                          text-align: center;
                          color: #7ce7fd;
                          font-weight: bold;
                        ">
                      人员
                    </div>
                    <div style="
                          width: 1px;
                          background: rgba(124, 231, 253, 0.3);
                          margin: 0 15px;
                        "></div>
                    <div style="
                          flex: 1;
                          text-align: center;
                          color: #7ce7fd;
                          font-weight: bold;
                        ">
                      位置
                    </div>
                  </div>

                  <!-- 滚动列表容器 -->
                  <div class="scroll-list1" style="height: 143px; overflow: auto">
                    <div class="scroll-content1" :style="{animationDuration:animationDuration +'s'}">
                      <!-- 列表项会被克隆并循环滚动 -->
                      <div v-for="item in personnelList" :key="item.id" style="margin-bottom: 2px">
                        <div style="display: flex; padding: 5px 0">
                          <div style="flex: 0.5; text-align: center">
                            {{item.name}}
                          </div>
                          <div style="
                                width: 1px;
                                background: rgba(124, 231, 253, 0.3);
                                margin: 0 15px;
                              "></div>
                          <div style="flex: 1; text-align: center">
                            {{item.location}}
                          </div>
                        </div>
                        <div style="
                              height: 1px;
                              background: rgba(124, 231, 253, 0.3);
                            "></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </li>




        <!--      <li id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74achart-item" class="chart-item" style="-->
        <!--              zoom: 0.997396;-->
        <!--              will-change: auto;-->
        <!--              width: 452px;-->
        <!--              height: 73px;-->
        <!--    transform: translate3d(1054px, 783px, 0px);-->
        <!--              z-index: 2000;-->
        <!--              background-color: transparent;-->
        <!--            " v-if="isShowLeft">-->
        <!--        <div class="chart-box animate__animated background selfAnimate_none"-->
        <!--             style="background: url('') no-repeat; transform: rotate(0deg)">-->
        <!--          <div id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74a"-->
        <!--               class="main animate__animated animate__slideInLeft">-->
        <!--            <img src="static/gif_02_left.gif" >-->
        <!--          </div>-->
        <!--        </div>-->
        <!--      </li>-->

        <!--      <li id="9d7d462f-bfcb-4b8c-9549-df7963bdc803chart-item"-->
        <!--          class="chart-item" style="zoom: 0.997396;-->
        <!--          will-change: auto; width: 143px;height: 89px;-->
        <!--    transform: translate3d(1281px, 975px, 0px); z-index: 2000; background-color: transparent;" v-if="isShowLeft">-->
        <!--        <div class="chart-box animate__animated background selfAnimate_none" style="background: url(&quot;&quot;) no-repeat; transform: rotate(0deg);">-->
        <!--          <div id="9d7d462f-bfcb-4b8c-9549-df7963bdc803" class="main animate__animated animate__slideInLeft">-->
        <!--            <div class="el-image full" id="9d7d462f-bfcb-4b8c-9549-df7963bdc803">-->
        <!--              <img src="static/gif_02_left.gif" alt="图片" class="el-image__inner" style="object-fit: fill;">-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->
        <!--      </li>-->

        <li id="9d7d462f-bfcb-4b8c-9549-df7963bdc803chart-item" class="chart-item"
          style="zoom: 1; will-change: auto; width: 121px; height: 91px; transform: translate3d(1303px, 970px, 0px); z-index: 2000; background-color: transparent;"
          v-show="isShowLeft">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url(&quot;&quot;) no-repeat; transform: rotate(0deg);">
            <div id="9d7d462f-bfcb-4b8c-9549-df7963bdc803" class="main animate__animated animate__slideInLeft">
              <div class="el-image full" id="9d7d462f-bfcb-4b8c-9549-df7963bdc803">
                <img src="static/gif_02_left.gif" alt="图片" class="el-image__inner" style="object-fit: fill;">
              </div>
            </div>
          </div>
        </li>

        <li id="84edface-ce8c-4895-98a8-76e6637fd052chart-item" class="chart-item" style="zoom: 0.997396; will-change: auto;    width: 1079px;
    height: 111px;
    transform: translate3d(27px, 878px, 0px); z-index: 2000; background-color: transparent;" v-show="isShowRight">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url(&quot;&quot;) no-repeat; transform: rotate(0deg);">
            <div id="84edface-ce8c-4895-98a8-76e6637fd052" class="main animate__animated animate__slideInLeft">
              <div class="el-image full" id="84edface-ce8c-4895-98a8-76e6637fd052">
                <img src="static/gif_02_right.gif" alt="图片" class="el-image__inner" style="object-fit: fill;">
              </div>
            </div>
          </div>
        </li>

        <!--      <li id="84edface-ce8c-4895-98a8-76e6637fd052chart-item" class="chart-item"-->
        <!--          style="zoom: 0.997396; will-change: auto; width: 1194px; height: 118px;-->

        <!--    transform: translate3d(24px, 884px, 0px); z-index: 2000; background-color: transparent;" v-if="isShowRight">-->
        <!--        <div class="chart-box animate__animated background selfAnimate_none" >-->
        <!--          <div id="84edface-ce8c-4895-98a8-76e6637fd052" class="main animate__animated animate__slideInLeft">-->
        <!--            <div class="el-image full" id="84edface-ce8c-4895-98a8-76e6637fd052">-->
        <!--              <img src="static/gif_02_right.gif" alt="图片" class="el-image__inner" style="object-fit: fill;">-->
        <!--              &lt;!&ndash;&ndash;&gt;</div>-->
        <!--          </div>-->
        <!--        </div>-->
        <!--      </li>-->


        <!--      <li id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74achart-item" class="chart-item" style="-->
        <!--              position: absolute; /* 使用绝对定位 */-->
        <!--              bottom: 50px; /* 垂直居中 */-->
        <!--              left: 50%; /* 水平居中 */-->
        <!--              transform: translate3d(1054px, 783px, 0px); /* 使元素中心对齐 */-->
        <!--              zoom: 0.997396;-->
        <!--              will-change: auto;-->
        <!--              width: 696px;-->
        <!--              height: 79px;-->
        <!--              z-index: 2000;-->
        <!--              background-color: transparent;-->
        <!--            " >-->
        <!--        <div class="chart-box animate__animated background selfAnimate_none"-->
        <!--             style="background: url('') no-repeat; transform: rotate(0deg)">-->
        <!--          <div id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74a"-->
        <!--               class="main animate__animated animate__slideInLeft">-->
        <!--            <img src="static/gif_02_left.gif" style="width: 12%; height: 73px;">-->
        <!--          </div>-->
        <!--        </div>-->
        <!--      </li>-->



        <!--      <li id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74achart-item" class="chart-item" style="-->
        <!--              zoom: 0.997396;-->
        <!--              will-change: auto;-->
        <!--              width: 50%;-->
        <!--    height: 118px;-->
        <!--    transform: translate3d(24px, 884px, 0px);-->
        <!--              z-index: 2000;-->
        <!--              background-color: transparent;-->
        <!--            "  >-->
        <!--        <div class="chart-box animate__animated background selfAnimate_none"-->
        <!--             style="background: url('') no-repeat; transform: rotate(0deg)"  >-->
        <!--          <div id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74a"-->
        <!--               class="main animate__animated animate__slideInLeft">-->
        <!--            <img src="static/gif_02_right.gif" class="el-image__inner" style="object-fit: fill;">-->
        <!--          </div>-->
        <!--        </div>-->
        <!--      </li>-->


        <li id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74achart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 1452px;
    height: 264px;
    transform: translate3d(9px, 815px, 0px);
              z-index: 2;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74a" class="main animate__animated animate__slideInLeft">
              <div full="" id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74a">
                <img src="static/biankuang.png" style="width: 100%;height: 265px;" />
              </div>
            </div>
        </li>







        <li id="671478d7-0843-4939-a8c0-934e01ef5f22chart-item" class="chart-item" style="zoom: 0.7375; will-change: auto;      width: 1318px;
                height: 77px;
                transform: translate3d(168px, 9px, 0px);
                z-index: 0; background-color: transparent;">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url(&quot;&quot;) no-repeat; transform: rotate(0deg);">
            <div id="671478d7-0843-4939-a8c0-934e01ef5f22" class="main animate__animated animate__slideInLeft">
              <p class="baseCss" id="671478d7-0843-4939-a8c0-934e01ef5f22"
                style="justify-content: center; font-size: 20px; color: rgb(255, 255, 255); font-family: &quot;Microsoft YaHei&quot;; font-style: normal; font-weight: 500;">
                <img src="./static/角标_02/back01.png" style="height: 58px; margin-left: 8px;" alt="icon" />

              </p>
            </div>
          </div>
        </li>
        <li id="671478d7-0843-4939-a8c0-934e01ef5f22chart-item" class="chart-item"
          style="zoom: 0.7375; will-change: auto; width: 161px; height: 67px; transform: translate3d(1290px, 14px, 0px); z-index: 2; background-color: transparent;">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url(&quot;&quot;) no-repeat; transform: rotate(0deg);">
            <div id="671478d7-0843-4939-a8c0-934e01ef5f22" class="main animate__animated animate__slideInLeft">
              <p class="baseCss" id="671478d7-0843-4939-a8c0-934e01ef5f22"
                style="justify-content: center; font-size: 20px; color: rgb(255, 255, 255); font-family: &quot;Microsoft YaHei&quot;; font-style: normal; font-weight: 500;">
                <img src="./static/角标_02/角标_07.png" style="height: 58px; margin-left: 8px;" alt="icon" />
                <span>用户名：</span>
              </p>
            </div>
          </div>
        </li>
        <li id="1bb35d14-a4f4-4709-9bb4-f55f7767898echart-item" class="chart-item"
          style="zoom: 0.7375; will-change: auto; width: 175px; height: 65px; transform: translate3d(1118px, 14px, 0px); z-index: 2; background-color: transparent;">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url(&quot;&quot;) no-repeat; transform: rotate(0deg);">
            <div id="1bb35d14-a4f4-4709-9bb4-f55f7767898e" class="main animate__animated animate__slideInLeft">
              <p class="baseCss" id="1bb35d14-a4f4-4709-9bb4-f55f7767898e"
                style="justify-content: center; font-size: 24px; color: rgb(255, 255, 255); font-family: &quot;Microsoft YaHei&quot;; font-style: normal; font-weight: 500; display: flex; align-items: center;">
                <img src="./static/角标_02/角标_07.png" style="height: 58px; margin-left: 8px;" alt="icon" />
                <span style="margin-left: 20px;">智能分析</span>

              </p>
            </div>
          </div>
        </li>
        <li id="c4e46a46-a438-4ea5-8dbc-1dea9624e49bchart-item" class="chart-item"
          style="zoom: 0.7375; will-change: auto; width: 175px; height: 68px; transform: translate3d(938px, 14px, 0px); z-index: 2; background-color: transparent;"
          @click="jinchi">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url(&quot;&quot;) no-repeat; transform: rotate(0deg);">
            <div id="c4e46a46-a438-4ea5-8dbc-1dea9624e49b" class="main animate__animated animate__slideInLeft">
              <p class="baseCss" id="c4e46a46-a438-4ea5-8dbc-1dea9624e49b"
                style="justify-content: center; font-size: 24px; color: rgb(255, 255, 255); font-family: &quot;Microsoft YaHei&quot;; font-style: normal; font-weight: 500; display: flex; align-items: center;">
                <img src="./static/角标_02/角标_07.png" style="height: 58px; margin-left: 8px;" alt="icon" />
                <span style="padding-left: 20px;">进尺退尺</span>

              </p>
            </div>
          </div>
        </li>
        <li id="73166eee-d4c2-475e-860c-ae942bdc7563chart-item" class="chart-item"
          style="zoom: 0.7375; will-change: auto; width: 534px; height: 63px; transform: translate3d(502px, 14px, 0px); z-index: 2; background-color: transparent;">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url(&quot;&quot;) no-repeat; transform: rotate(0deg);">
            <div id="73166eee-d4c2-475e-860c-ae942bdc7563" class="main animate__animated animate__slideInLeft">
              <p class="baseCss" id="73166eee-d4c2-475e-860c-ae942bdc7563"
                style="justify-content: center; font-size: 20px; color: rgb(255, 255, 255); font-family: &quot;Microsoft YaHei&quot;; font-style: normal; font-weight: 500; display: flex; align-items: center;">
                <!--              <span>潘二矿18425智能掘进面</span>-->
                <span>{{sceneName}}</span>
              </p>
            </div>
          </div>
        </li>
        <li id="81c9df05-84ee-4d42-ba75-0b8d7e6f0dbechart-item" class="chart-item"
          style="zoom: 0.7375; will-change: auto; width: 193px; height: 65px; transform: translate3d(365px, 11px, 0px); z-index: 2; background-color: transparent;">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url(&quot;&quot;) no-repeat; transform: rotate(0deg);">
            <div id="81c9df05-84ee-4d42-ba75-0b8d7e6f0dbe" class="main animate__animated animate__slideInLeft">
              <p class="baseCss" id="c4e46a46-a438-4ea5-8dbc-1dea9624e49b"
                style="justify-content: center; font-size: 24px; color: rgb(255, 255, 255); font-family: &quot;Microsoft YaHei&quot;; font-style: normal; font-weight: 500; display: flex; align-items: center;">
                <span style="padding-left: 20px;">历史查询</span>
                <img src="./static/角标_02/左侧_03.png" style="height: 58px; margin-left: 8px;" alt="icon" />


              </p>
            </div>
          </div>
        </li>

        <li id="81c9df05-84ee-4d42-ba75-0b8d7e6f0dbechart-item" class="chart-item"
          style="zoom: 0.7375; will-change: auto; width: 193px; height: 65px; transform: translate3d(500px, 11px, 0px); z-index: 2; background-color: transparent;">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url(&quot;&quot;) no-repeat; transform: rotate(0deg);">
            <div id="81c9df05-84ee-4d42-ba75-0b8d7e6f0dbe" class="main animate__animated animate__slideInLeft">
              <p class="baseCss" id="c4e46a46-a438-4ea5-8dbc-1dea9624e49b"
                style="justify-content: center; font-size: 24px; color: rgb(255, 255, 255); font-family: &quot;Microsoft YaHei&quot;; font-style: normal; font-weight: 500; display: flex; align-items: center;">
                <img src="./static/角标_02/logo_03.png" style="height: 40px; margin-left: 8px;" alt="icon" />


              </p>
            </div>
          </div>
        </li>
        <li id="a8b0c8fc-74fe-433d-b0b4-e79fb72f431bchart-item" class="chart-item"
          style="zoom: 0.7375; will-change: auto; width: 157px; height: 61px; transform: translate3d(233px, 14px, 0px); z-index: 2; background-color: transparent;">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url(&quot;&quot;) no-repeat; transform: rotate(0deg);">
            <div id="a8b0c8fc-74fe-433d-b0b4-e79fb72f431b" class="main animate__animated animate__slideInLeft">
              <p class="baseCss" id="c4e46a46-a438-4ea5-8dbc-1dea9624e49b"
                style="justify-content: center; font-size: 24px; color: rgb(255, 255, 255); font-family: &quot;Microsoft YaHei&quot;; font-style: normal; font-weight: 500; display: flex; align-items: center;">
                <span style="padding-left: 20px;">综合监测</span>
                <img src="./static/角标_02/左侧_03.png" style="height: 58px; margin-left: 8px;" alt="icon" />


              </p>
            </div>
          </div>
        </li>

        <!--      <li id="a8b0c8fc-74fe-433d-b0b4-e79fb72f431bchart-item" class="chart-item"-->
        <!--          style="zoom: 0.7375; will-change: auto; width: 127px; height: 61px; transform: translate3d(233px, 14px, 0px); z-index: 2; background-color: transparent;">-->
        <!--        <div class="chart-box animate__animated background selfAnimate_none"-->
        <!--             style="background: url(&quot;&quot;) no-repeat; transform: rotate(0deg);">-->
        <!--          <div id="a8b0c8fc-74fe-433d-b0b4-e79fb72f431b"-->
        <!--               class="main animate__animated animate__slideInLeft">-->
        <!--           -->
        <!--          </div>-->
        <!--        </div>-->
        <!--      </li>-->
        <li id="7a6e5dd8-393e-4166-9a96-f1a676072698chart-item" class="chart-item"
          style="zoom: 0.7375; will-change: auto; width: 210px; height: 61px; transform: translate3d(22px, 14px, 0px); z-index: 0; background-color: transparent;">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url(&quot;data:image/png;base64,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&quot;) no-repeat; transform: rotate(0deg);">
            <div id="7a6e5dd8-393e-4166-9a96-f1a676072698" class="main animate__animated animate__slideInLeft"><x-empty
                option="[object Object]" id="7a6e5dd8-393e-4166-9a96-f1a676072698"></x-empty></div>
          </div>
        </li>
        <li id="6f74bd98-c73d-495e-ac74-56be6e1e6af1chart-item" class="chart-item"
          style="zoom: 0.7375; will-change: auto; width: 183px; height: 58px; transform: translate3d(16px, 5px, 0px); z-index: 2; background-color: transparent;">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url(&quot;&quot;) no-repeat; transform: rotate(0deg);">
            <div id="6f74bd98-c73d-495e-ac74-56be6e1e6af1" class="main animate__animated animate__slideInLeft">
              <p class="baseCss" id="6f74bd98-c73d-495e-ac74-56be6e1e6af1"
                style="justify-content: center; font-size: 20px; color: rgb(255, 255, 255); font-family: &quot;Microsoft YaHei&quot;; font-style: normal; font-weight: 500;">
                <span>通讯状态：· 在线</span>
              </p>
            </div>
          </div>
        </li>
        <li id="cac3936f-f947-48b6-a1ff-47536d0a57d3chart-item" class="chart-item"
          style="zoom: 0.7375; will-change: auto; width: 159px; height: 54px; transform: translate3d(29px, 34px, 0px); z-index: 2; background-color: transparent;">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url(&quot;&quot;) no-repeat; transform: rotate(0deg);">
            <div id="cac3936f-f947-48b6-a1ff-47536d0a57d3" class="main animate__animated animate__slideInLeft">
              <p class="baseCss" id="cac3936f-f947-48b6-a1ff-47536d0a57d3"
                style="justify-content: center; font-size: 15px; color: rgb(255, 255, 255); font-family: &quot;Microsoft YaHei&quot;; font-style: normal; font-weight: 500;">
                <span>{{time}}</span>
              </p>
            </div>
          </div>
        </li>







        <!-- 底部 -->
        <li id="eee905c7-bdc3-4ec8-add2-74bc16fe1775chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 172px;
              height: 54px;
              transform: translate3d(569px, 577px, 0px);
              z-index: 1013;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="eee905c7-bdc3-4ec8-add2-74bc16fe1775" class="main animate__animated animate__slideInLeft">
              <p class="baseCss" id="eee905c7-bdc3-4ec8-add2-74bc16fe1775" style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  ">
                <span>| 掘锚一体机</span>
              </p>
            </div>
          </div>
        </li>


        <li id="eee905c7-bdc3-4ec8-add2-74bc16fe1775chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 328px;

              transform: translate3d(569px, 617px, 0px);
              z-index: 1013;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="eee905c7-bdc3-4ec8-add2-74bc16fe1775" class="main animate__animated animate__slideInLeft">
              <div class="data-panel" style="
                    padding: 15px 0px;
                    display: flex;
                    justify-content: space-between;
                  ">
                <!-- 左侧数据列 -->
                <div class="left-column" style="width: 53%">
                  <div v-for="item in leftColumnConfig" :key="item.code" class="data-row"
                    style="display: flex; justify-content: space-between; color: #ffffff; font-size: 17px;"
                    :style="{ marginBottom: item.isLast ? '0' : '10px' }">
                    <span>{{item.frontViewName}}</span>
                    <span>{{item.indexValue || 0}}{{item.unit}}</span>
                  </div>
                </div>

                <!-- 右侧数据列 -->
                <!--              <div class="right-column" style="width: 68%; margin-left: 9px">-->
                <!--                <div v-for="item in rightColumnConfig"-->
                <!--                     :key="item.code"-->
                <!--                     class="data-row"-->
                <!--                     style="display: flex; justify-content: space-around; color: #ffffff; font-size: 16px;"-->
                <!--                     :style="{ marginBottom: item.isLast ? '0' : '10px' }">-->
                <!--                  <span>{{item.name}}{{item.showColon ? ':' : ''}}</span>-->
                <!--                  <span :style="{ color: getStatusColorAll(item, monitorDataRight[item.key]) }">-->
                <!--                        {{getStatusTextAll(item, monitorDataRight[item.key])}}-->
                <!--                      </span>-->
                <!--                </div>-->
                <!--              </div>-->
                <div class="right-column" style="width: 60%;">
                  <div v-for="item in rightColumnConfig" :key="item.code" class="data-row"
                    style="display: flex; color: #ffffff; font-size: 17px;"
                    :style="{ marginBottom: item.isLast ? '0' : '10px' }">

                    <span style="width: 120px; text-align: right">{{item.frontViewName}}</span>

                    <span style="margin-left: 10px;width: 70px">
                      {{item.indexValue || 0}}{{item.unit}}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </li>


        <li id="d17cf223-06b8-4aef-b72a-7cd4757ea525chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 182px;
              height: 56px;
              transform: translate3d(1247px, 577px, 0px);
              z-index: 1015;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="d17cf223-06b8-4aef-b72a-7cd4757ea525" class="main animate__animated animate__slideInLeft">
              <p class="baseCss" id="d17cf223-06b8-4aef-b72a-7cd4757ea525" style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  ">
                <span>| 自移机尾</span>
              </p>
            </div>
          </div>
        </li>

        <li id="d17cf223-06b8-4aef-b72a-7cd4757ea525chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 182px;
              top: 7px;
              transform: translate3d(1247px, 597px, 0px);
              z-index: 1015;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="d17cf223-06b8-4aef-b72a-7cd4757ea525" class="main animate__animated animate__slideInLeft">
              <div class="data-panel" style="padding: 15px">
                <div v-for="item in actionConfig" :key="item.code" class="data-row"
                  style="display: flex; justify-content: space-between; color: #ffffff; font-size: 17px;"
                  :style="{ marginBottom: item.isLast ? '0' : '10px' }">
                  <span>{{item.frontViewName}}:</span>
                  <span>
                    {{item.indexValue}}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </li>

        <li id="df5709ed-ee81-42e7-88d3-6813731c30c8chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 144px;
              height: 56px;
              transform: translate3d(983px, 577px, 0px);
              z-index: 1018;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="df5709ed-ee81-42e7-88d3-6813731c30c8" class="main animate__animated animate__slideInLeft">
              <p class="baseCss" id="df5709ed-ee81-42e7-88d3-6813731c30c8" style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  ">
                <span>| 变频张紧</span>
              </p>
            </div>
          </div>
        </li>

        <li id="df5709ed-ee81-42e7-88d3-6813731c30c8chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 144px;
              top: 7px;
              transform: translate3d(983px, 617px, 0px);
              z-index: 1018;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="df5709ed-ee81-42e7-88d3-6813731c30c8" class="main animate__animated animate__slideInLeft">
              <div class="data-panel" style="border-radius: 4px">
                <div v-for="item in frequencyConfig" :key="item.code" class="data-row"
                  style="display: flex; justify-content: space-between; color: #ffffff; font-size: 17px;"
                  :style="{ marginBottom: item.isLast ? '0' : '8px' }">
                  <span>{{item.frontViewName}}:</span>
                  <!--                <span >{{monitorDataBianPin[item.key]}}{{item.unit}}</span>-->
                  <!--                :style="{ color: getStatusColorAll(item, monitorDataBianPin[item.key]) }"-->
                  <span>
                    {{item.indexValue|| 0}} {{item.unit}}
                  </span>
                </div>


              </div>
            </div>
          </div>
        </li>
        <li id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 139px;
              height: 56px;
              transform: translate3d(280px, 578px, 0px);
              z-index: 1013;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2" class="main animate__animated animate__slideInLeft">
              <p class="baseCss" id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2" style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  ">
                <span>| 皮带运输</span>
              </p>
            </div>
          </div>
        </li>


        <li id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
                  width: 52px;
    height: 231px;
    transform: translate3d(249px, 578px, 0px);
              z-index: 1013;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2" class="main animate__animated animate__slideInLeft">
              <img src="static/shu.png" width="2px">
            </div>
          </div>
        </li>


        <li id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
                  width: 52px;
    height: 231px;
    transform: translate3d(509px, 578px, 0px);
              z-index: 1013;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2" class="main animate__animated animate__slideInLeft">
              <img src="static/shu.png" width="2px">
            </div>
          </div>
        </li>


        <li id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
                  width: 52px;
    height: 231px;
    transform: translate3d(903px, 578px, 0px);
              z-index: 1013;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2" class="main animate__animated animate__slideInLeft">
              <img src="static/shu.png" width="2px">
            </div>
          </div>
        </li>

        <li id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
                  width: 52px;
    height: 231px;
    transform: translate3d(1200px, 578px, 0px);
              z-index: 1013;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2" class="main animate__animated animate__slideInLeft">
              <img src="static/shu.png" width="2px">
            </div>
          </div>
        </li>

        <li id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 175px;

              transform: translate3d(280px, 617px, 0px);
              z-index: 1013;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2" class="main animate__animated animate__slideInLeft">
              <div class="status-panel" style="padding: 15px">


                <div v-for="item in statusConfig" :key="item.code" class="status-item"
                  style="display: flex; justify-content: space-between; color: #ffffff; font-size: 17px;"
                  :style="{ marginBottom: item.isLast ? '0' : '10px' }">
                  <span>{{item.frontViewName}}:</span>

                  <span>
                    {{item.indexValue}} {{item.unit}}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </li>

        <li id="5715f732-652a-44a2-ba81-62b87c621894chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 128px;
              height: 56px;
              transform: translate3d(32px, 578px, 0px);
              z-index: 1012;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="5715f732-652a-44a2-ba81-62b87c621894" class="main animate__animated animate__slideInLeft">
              <p class="baseCss" id="5715f732-652a-44a2-ba81-62b87c621894" style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  ">
                <span>| 局扇 </span>
              </p>
            </div>
          </div>
        </li>

        <li id="5715f732-652a-44a2-ba81-62b87c621894chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 202px;
              margin-left: -23px;


              transform: translate3d(32px, 607px, 0px);
              z-index: 1012;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('') no-repeat; transform: rotate(0deg)">
            <div id="5715f732-652a-44a2-ba81-62b87c621894" class="main animate__animated animate__slideInLeft">
              <div class="data-panel" style="padding: 15px">
                <div v-for="item in fanConfig" :key="item.code" class="data-item" style="
                      display: flex;
                      justify-content: space-between;
                      margin-bottom: 10px;
                      color: #ffffff;
                      font-size: 17px;
                    " :style="{ marginBottom: item.isLast ? '0' : '10px' }">
                  <span>{{item.frontViewName}}</span>
                  <span>{{item.indexValue|| 0}}{{item.unit}}</span>
                </div>
              </div>
            </div>
          </div>
        </li>





        <li id="473caadc-a0d8-4f66-b858-761dd03afef3chart-item" class="chart-item" style="
              zoom: 0.997396;
              will-change: auto;
              width: 1429px;
 margin-left: -10px;
 margin-top: -20px;
 height: 79px;
              transform: translate3d(34px, 849px, 0px);
              z-index: 2;
              background-color: transparent;
            ">
          <div class="chart-box animate__animated background selfAnimate_none"
            style="background: url('static/gif_02.png') no-repeat;height: 240px;">
            <div id="473caadc-a0d8-4f66-b858-761dd03afef3" class="main animate__animated animate__slideInLeft">
              <div class="el-image full" id="473caadc-a0d8-4f66-b858-761dd03afef3">
                <div class="image-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
                <!---->
              </div>
            </div>
          </div>
        </li>
      </ul>
      <!-- <div class="background" style="background: url('static/back.jpg');"></div> -->
      <div class="background" style="background-color: #07192B;"></div>
    </div>
    <div class="el-dialog__wrapper resize" style="display: none">
      <div role="dialog" aria-modal="true" aria-label="dialog" class="el-dialog is-fullscreen">
        <div class="el-dialog__header">
          <span class="el-dialog__title"></span><button type="button" aria-label="Close" class="el-dialog__headerbtn">
            <i class="el-dialog__close el-icon el-icon-close"></i>
          </button>
        </div>
        <!----><!---->
      </div>
    </div>
    <div class="el-loading-mask" style="background-color: rgba(0, 0, 0, 0.8); display: none">
      <div class="el-loading-spinner">
        <svg viewBox="25 25 50 50" class="circular">
          <circle cx="50" cy="50" r="20" fill="none" class="path"></circle>
        </svg>
        <p class="el-loading-text">拼命加载中</p>
      </div>
    </div>


    <el-dialog title="" :visible.sync="progressDialogVisible" width="85%" custom-class="progress-dialog"
      :close-on-click-modal="false" :show-close="true" style="height: 1200px;">
      <div class="dialog-content" style="height: 70%">
        <!-- 掘进面进尺表格 -->
        <div class="table-section">
          <div class="section-title" style="display: flex;align-items: center;justify-content: space-between">
            <span style=" margin: 0 auto">
              掘进面进尺
            </span>
            <el-button @click="progressDialogVisible = false"
              style="background-color: #0B2B56; color: white;">关闭</el-button>
          </div>

          <div class="table-container">
            <el-table :data="progressData" style="width: 100%" border height="430" header-align="center" align="center"
              :header-cell-style="{background:'#0d2c5a',color:'#fff',padding:'12px 0'}" :cell-style="{padding:'12px 0'}"
              :row-style="{background:'#0d1d31',color:'#fff'}">
              <el-table-column label="序号" width="80">
                <template slot-scope="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>

              <el-table-column prop="typeName" label="类型" width="100"></el-table-column>

              <el-table-column prop="sceneName" label="名称" width="300"></el-table-column>

              

              <el-table-column label="中班进尺(米)">
                <template slot-scope="scope">

                  <span v-if="scope.row.afternoonShift">{{(scope.row.afternoonShift/100).toFixed(1)}}</span>
                  <span v-else>{{(scope.row.afternoonShift/100).toFixed(1)}}</span>
                </template>
              </el-table-column>

              <el-table-column label="夜班进尺(米)">
                <template slot-scope="scope">

                  <span v-if="scope.row.eveningShift">{{(scope.row.eveningShift/100).toFixed(1)}}</span>
                  <span v-else>{{(scope.row.eveningShift/100).toFixed(1)}}</span>
                </template>
              </el-table-column>

              <el-table-column label="早班进尺(米)">
                <template slot-scope="scope">
                  <span v-if="scope.row.morningShift">{{(scope.row.morningShift/100).toFixed(1)}}</span>
                  <span v-else>{{(scope.row.morningShift/100).toFixed(1)}}</span>
                </template>
              </el-table-column>

              <el-table-column label="本日进尺(米)">
                <el-table-column label="计划">
                  <template slot="header">
                    <span>计划</span>
                  </template>
                  <template slot-scope="scope">

                    <span v-if="scope.row.dailyPlanDis">{{(scope.row.dailyPlanDis/100).toFixed(1)}}</span>
                    <span v-else>{{(scope.row.dailyPlanDis/100).toFixed(1)}}</span>
                  </template>
                </el-table-column>
                <el-table-column label="实际">
                  <template slot="header">
                    <span>实际</span>
                  </template>
                  <template slot-scope="scope">

                    <span v-if="scope.row.dailyDis">{{(scope.row.dailyDis/100).toFixed(1)}}</span>
                    <span v-else>{{(scope.row.dailyDis/100).toFixed(1)}}</span>
                  </template>
                </el-table-column>
              </el-table-column>

              <el-table-column label="昨日进尺(米)">
                <template slot-scope="scope">
                  <span >{{(scope.row.yesTodayDailyDis/100).toFixed(1)}}</span>
                </template>
              </el-table-column>

              <el-table-column label="本月进尺(米)">
                <el-table-column label="计划">
                  <template slot="header">
                    <span>计划</span>
                  </template>
                  <template slot-scope="scope">

                    <span v-if="scope.row.monthPlanDis">{{(scope.row.monthPlanDis/100).toFixed(1)}}</span>
                    <span v-else>{{(scope.row.monthPlanDis/100).toFixed(1)}}</span>
                  </template>
                </el-table-column>
                <el-table-column label="实际">
                  <template slot="header">
                    <span>实际</span>
                  </template>
                  <template slot-scope="scope">

                    <span v-if="scope.row.monthDis">{{(scope.row.monthDis/100).toFixed(1)}}</span>
                    <span v-else>{{(scope.row.monthDis/100).toFixed(1)}}</span>
                  </template>
                </el-table-column>
              </el-table-column>

              <el-table-column label="总进尺(米)">
                <el-table-column label="计划" width="100">
                  <template slot="header">
                    <span>计划</span>
                  </template>
                  <template slot-scope="scope">

                    <span v-if="scope.row.totalPlanDis">{{(scope.row.totalPlanDis/100).toFixed(1)}}</span>
                    <span v-else>{{(scope.row.totalPlanDis/100).toFixed(1)}}</span>
                  </template>
                </el-table-column>
                <el-table-column label="实际" width="100">
                  <template slot="header">
                    <span>实际</span>
                  </template>
                  <template slot-scope="scope">

                    <span v-if="scope.row.totalDis">{{(scope.row.yesTodayTotalDis/100).toFixed(1)}}</span>
                    <span v-else>{{(scope.row.yesTodayTotalDis/100).toFixed(1)}}</span>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="备注" width="120">

              </el-table-column>
              <!--            <el-table-column label="数据调校" width="220">-->
              <!--              <template slot-scope="scope">-->
              <!--                <el-input v-model="scope.row.adjustValue" style="width: 60px" size="mini"></el-input>-->
              <!--                <el-button size="mini" type="primary" @click="adjustData(scope.row)" style="margin-left: 5px">调校</el-button>-->
              <!--              </template>-->
              <!--            </el-table-column>-->
            </el-table>
          </div>
        </div>

        <!-- 采煤面退尺表格 -->
        <div class="table-section" style="margin-top: 20px;">
          <div class="section-title" style="margin-left: -4%">采煤面退尺</div>
          <div class="table-container">
            <el-table :data="retreatData" style="width: 100%" border height="229" align="center" header-align="center"
              :header-cell-style="{background:'#0d2c5a',color:'#fff',padding:'12px 0'}" :cell-style="{padding:'12px 0'}"
              :row-style="{background:'#0d1d31',color:'#fff'}">
              <el-table-column label="序号" width="80">
                <template slot-scope="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column label="类型" width="100">
                <template slot-scope="scope">
                  <span >采煤</span>
                </template>
              </el-table-column>

              <el-table-column prop="sceneName" label="名称" width="320"></el-table-column>


              <el-table-column label="本日退尺(米)">
            
                <template slot-scope="scope">

                  <span v-if="scope.row.dailyDis">{{(scope.row.dailyDis/100).toFixed(1)}}</span>
                  <span v-else>{{(scope.row.dailyDis/100).toFixed(1)}}</span>
                </template>
              </el-table-column>

              <el-table-column label="昨日退尺(米)">
              
                <template slot-scope="scope">

                  <span>{{(scope.row.yesTodayDailyDis/100).toFixed(1)}}</span>
             
                </template>
              </el-table-column>

              <el-table-column label="月退尺(米)">
                <el-table-column label="计划">
                  <template slot="header">
                    <span>计划</span>
                  </template>
                  <template slot-scope="scope">

                    <span v-if="scope.row.monthPlanDis">{{(scope.row.monthPlanDis/100).toFixed(1)}}</span>
                    <span v-else>{{(scope.row.monthPlanDis/100).toFixed(1)}}</span>
                  </template>
                </el-table-column>
                <el-table-column label="实际">
                  <template slot="header">
                    <span>实际</span>
                  </template>
                  <template slot-scope="scope">

                    <span v-if="scope.row.monthActual">{{(scope.row.monthDis/100).toFixed(1)}}</span>
                    <span v-else>{{(scope.row.monthDis/100).toFixed(1)}}</span>
                  </template>
                </el-table-column>
              </el-table-column>

              <el-table-column label="总退尺(米)">
                <!-- <el-table-column label="计划">
                  <template slot="header">
                    <span>计划</span>
                  </template>
                  <template slot-scope="scope">
                    <span>{{(scope.row.totalPlanDis/100).toFixed(1)}}</span>
                  </template>
                </el-table-column> -->
                <!-- <el-table-column label="实际"> -->
                  <!-- <template slot="header">
                    <span>实际</span>
                  </template> -->
                  <template slot-scope="scope">
                    <span>{{(scope.row.yesTodayTotalDis/100).toFixed(1)}}</span>
                  </template>
                <!-- </el-table-column> -->
              </el-table-column>

              <!--            <el-table-column label="数据调校" width="220">-->
              <!--              <template slot-scope="scope">-->
              <!--                <el-input v-model="scope.row.adjustValue" style="width: 60px" size="mini"></el-input>-->
              <!--                <el-button size="mini" type="primary" @click="adjustData(scope.row)" style="margin-left: 5px">调校</el-button>-->
              <!--              </template>-->
              <!--            </el-table-column>-->
            </el-table>
          </div>
        </div>
      </div>


    </el-dialog>

    <el-dialog title="历史查询" :visible.sync="historyDialogVisible" v-if="historyDialogVisible" width="75%"
      style="margin-top: -6vh; height: 900px;" :before-close="handleClose" custom-class="history-dialog">
      <!-- 查询条件 -->
      <div class="query-form">
        <el-form :inline="true" :model="queryForm">
          <el-form-item label="查询类型">
            <el-select v-model="alarmType">
              <el-option label="报警记录" value="0"></el-option>
              <el-option label="运行记录" value="1"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="时间范围">
            <el-date-picker v-model="queryTime" type="datetimerange" format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss" range-separator="至" @change="timeChange" start-placeholder="开始时间"
              end-placeholder="结束时间">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getData">查询</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="alarmTime" label="时间" width="180"></el-table-column>
        <el-table-column prop="alarmInfo" label="告警内容" align="center"></el-table-column>

        <el-table-column prop="alarmType" label="状态" width="100">
          <template v-slot="scope">
            <span v-if="scope.row.alarmType === 0">报警记录</span>
            <span v-if="scope.row.alarmType === 1">运行记录</span>

          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container" style="margin-top: 20px; text-align: right">
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="page"
          layout="total, sizes, prev, pager, next, jumper" :total="total">
        </el-pagination>
      </div>
    </el-dialog>

    <el-dialog title="智能分析" :visible.sync="analysisDialogVisible" width="80%" :close-on-click-modal="false"
      custom-class="analysis-dialog">
      <div style="
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  ">
        <span style="color: #fff; font-size: 14px;">统计时间：</span>
        <el-date-picker v-model="analysisTimeRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" :picker-options="pickerOptions" value-format="yyyy-MM-dd" style="width: 380px;">
        </el-date-picker>
        <el-button type="primary" @click="initAnalysisChart" :loading="loading" style="
        margin-left: 10px;
        background-color: #188df0;
        border-color: #188df0;
      ">
          查询
        </el-button>
      </div>
      <div style="height: 500px">
        <div ref="analysisChart" style="width: 100%; height: 100%"></div>
      </div>
    </el-dialog>
  </div>
  <!-- Code injected by live-server -->
  <script type="text/javascript">
    // <![CDATA[  <-- For SVG support
    if ("WebSocket" in window) {
      (function () {
        function refreshCSS() {
          var sheets = [].slice.call(document.getElementsByTagName("link"));
          var head = document.getElementsByTagName("head")[0];
          for (var i = 0; i < sheets.length; ++i) {
            var elem = sheets[i];
            head.removeChild(elem);
            var rel = elem.rel;
            if (
              (elem.href && typeof rel != "string") ||
              rel.length == 0 ||
              rel.toLowerCase() == "stylesheet"
            ) {
              var url = elem.href.replace(/(&|\?)_cacheOverride=\d+/, "");
              elem.href =
                url +
                (url.indexOf("?") >= 0 ? "&" : "?") +
                "_cacheOverride=" +
                new Date().valueOf();
            }
            head.appendChild(elem);
          }
        }
        var protocol =
          window.location.protocol === "http:" ? "ws://" : "wss://";
        var address =
          protocol + window.location.host + window.location.pathname + "/ws";
        var socket = new WebSocket(address);
        socket.onmessage = function (msg) {
          if (msg.data == "reload") window.location.reload();
          else if (msg.data == "refreshcss") refreshCSS();
        };
        console.log("Live reload enabled.");
      })();
    }
    // ]]>
  </script>

  <script src="./static/js/vue.min.js"></script>
  <script src="./static/js/jquery.min.js"></script>
  <script src="./static/js/element-ui.js"></script>
  <script src="./static/js/axios.min.js"></script>
  <script src="./static/js/js/commonFun.js"></script>
  <script src="./static/js/js/mixinsComponents.js"></script>
  <script src="./static/js/js/mixinsMap.js"></script>
  <script src="./static/js/js/mixinsThree.js"></script>
  <script src="./static/js/js/mixinsDecoration.js"></script>
  <script src="./static/js/js/mixinsEarth.js"></script>
  <script src="./static/js/js/mixinsCesiumEarth.js"></script>
  <script src="./static/js/js/video.min.js" async=""></script>
  <script src="./static/js/js/vue-count-to.min.js" async=""></script>

  <script>
    // var baseurl1 = "http://**************:18001";
    // var baseurl2 = "http://**************:18002";
    // var baseurl3 = "http://**************:18003";
    // var baseurl4 = "http://**************:18004";
    // var baseurl5 = "http://**************:18005";


    var vm = new Vue({
      el: "#body",
      mixins: [mixinsComponents],
      data() {
        const end = new Date();
        const start = new Date();
        start.setDate(end.getDate() - 6);
        return {
          flvPlayer: null,
          videoSrc: 'http://example.com/live.flv', // 替换为你的FLV流地址
          showVideo: true,
          // 对话框显示控制
          progressDialogVisible: false,

          // 进尺数据
          progressData: [],

          // 退尺数据
          retreatData: [],
          video1: "",
          video1url: "",
          video2: "",
          video2url: "",
          video3: "",
          video3url: "",
          video4: "",
          video4url: "",
          video5: "",
          video5url: "",
          sceneId: null,
          sceneConfig: null,
          sceneName: "",
          bussName:"",
          baseurl1: "",
          baseurl2: "",
          baseurl3: "",
          baseurl4: "",
          baseurl5: "",
          baseurl6: "",
          baseurl7: "",
          baseurl8: "",
          baseurl9: "",
          baseurl10: [],
          baseurl11: "",
          baseurl12: "",
          baseurl13: "",
          baseurl14: "",
          baseurl15: "",
          baseurl16: "",
          baseurl17: "",
          baseurl18: "",
          baseurl19: "",
          baseurl20: "",
          baseurl21: "",
          baseurl22: "",
          apiBaseUrl: 'http://************:18002',
          leftChart: null,
          rightChart: null,
          // 模拟数据,实际使用时替换为真实数据
          leftData: [],
          rightData: [],
          leftXAxisData: [],
          rightXAxisData: [],
          background: null,
          zoomWidth: 1,
          zoomHeight: 1,
          basicConfig: {
            description: "",
            width: 1920,
            height: 1080,
            window: [0],
            background: "#0D1D31",
            bluePrintConfig: {
              exportIdList: [],
              nodeData: { nodes: [], edges: [] },
              allNodeConfig: {
                judge: [],
                interActive: [],
                component: [],
                dataHandler: [],
              },
            },
            zoomMode: {
              default: "YOY",
              option: [
                { label: "同比缩放", value: "YOY" },
                { label: "自适应缩放", value: "adaptive" },
              ],
            },
            data: [],
          },
          loading: true,
          debounceTimer: null, // 防抖定时器
          isShowResize: false, // 放大图表的modal
          willResizeTemplate: [], // 将要放大的组件
          url: "static/js", // 加载js的地址
          EventSource: null, // 全局SSE source时间
          nowWindowIndex: 0, // 多桌面模式, 现在处于的桌面序号
          timer: null, // 定时器
          // bakAllTemplateList: null, // 备份组件 防止改变了大小改变了原始数据
          innerZoom: null, // 子元素缩放的倍数
          isLoadJsList: [], // 已经加载了的js列表
          cssMerge: "", // 通过id访问的css
          staticUrl: "./", // 静态资源地址
          zoom: 0.853,
          chart: null,
          chartData: {
            xData: [

            ],
            values: [

            ],
          },
          currentPersonnel: 0,
          totalPersonnel: 0,
          personnelList: [

            // 添加更多人员数据...
          ],
          webRtcServer: null,
          webRtcServer1: null,
          historyDialogVisible: false,
          queryForm: {
            type: "alarm",
            timeRange: [],
          },
          tableData: [],
          currentPage: 1,
          pageSize: 10,
          total: 0,
          temperature: 0.0,
          carbon: 0.0,
          smoke: 0.0,
          dust: 0.0,
          windSpeed: 0.00,
          t1Amount: 0.00,
          t2Amount: 0.00,
          t3Amount: 0.00,
          time: this.formatDate(new Date()),
          timer: null,
          isStatus: true,
          monitorData: [],
          monitorDataJuShan: [],
          monitorDataPiDai: [],
          monitorDataLeft: [],
          monitorDataRight: [],
          monitorDataBianPin: [],
          monitorDataZiYi: [],
          fanConfig: [

          ],
          shebeiStatus: 0,
          yunxingStatus: 0,
          statusConfig: [

          ],
          leftColumnConfig: [

          ],
          rightColumnConfig: [

          ],
          frequencyConfig: [


          ],
          actionConfig: [

          ],
          tiaojieStatus: "手动",
          isShowLeft: false,
          isShowRight: false,
          page: 1,
          alarmType: "0",
          queryTime: "",
          total: 0,
          startTime: "",
          endTime: "",
          ulStyle: {
            marginLeft: '8%'
          },
          dayProgress: "",
          morningProgress: "",
          nightProgress: "",

          todayTotalProgress: "",
          totalProgress: "",
          totalPlanDis: "",
          monthPlanDis: "",
          dailyPlanDis: "",
          totalDis: "",
          monthDis: "",
          dailyDis: "",
          analysisDialogVisible: false,
          analysisChart: null,
          chartData: {
            dates: [], // 日期数组
            progress: [], // 进尺数据数组
            rate: [] // 进尺率数组
          },
          analysisTimeRange: [
            start.toISOString().split('T')[0],
            end.toISOString().split('T')[0]
          ],
          loading: false, // 添加loading状态
          pickerOptions: {
            disabledDate(time) {
              return time.getTime() > Date.now();
            },
            onPick: ({ maxDate, minDate }) => {
              if (minDate && !maxDate) {
                const thirtyDays = 30 * 24 * 60 * 60 * 1000;
                const maxLimit = new Date(minDate.getTime() + thirtyDays);
                this.pickerOptions.selectableRange = [
                  minDate,
                  maxLimit
                ];
              }
            }
          }
        };
      },
      watch: {
        alarmType(newVal, oldVal) {
          if (newVal !== oldVal) {
            this.page = 1;
            this.total = 0
            this.getData()
          }
        }
      },


      mounted() {
        //这个time可以实时变动
        this.timer = setInterval(() => {
          this.time = this.formatDate(new Date());
        }, 1000);
        this.loadECharts();
        this.setZoom();
        window.addEventListener("resize", this.setZoom);

        // 模拟实时数据更新
        setInterval(this.updateData, 5000);
        const container = this.$el.querySelector(".scroll-content");
        container.innerHTML += container.innerHTML;
      },

      beforeDestroy() {
        if (this.timer) {
          clearInterval(this.timer);
        }
        if (this.timer22) {
          clearInterval(this.timer22);
        }
        window.removeEventListener("resize", this.handleResize);
        if (this.chart) {
          this.chart.dispose();
          this.chart = null;
        }
        // 移除监听
        clearTimeout(this.debounceTimer);
        clearInterval(this.timer);
        this.webRtcServer.disconnect();
        this.webRtcServer = null;
      },
      computed: {
        templateList() {
          //return this.allTemplateList[this.nowWindowIndex];
        },
        animationDuration() {
          return this.personnelList.length * 1
        }
      },
      async created() {
        this.loading = true;
        let searchKey = this.urlArgs();
        searchKey.id && (await this.initId(searchKey.id));
        if (!this.basicConfig.zoomMode) {
          this.basicConfig.zoomMode = {
            default: "YOY",
          };
        }
        await this.initJs(this.templateList);
        this.basicConfig.window &&
          this.basicConfig.window.length > 1 &&
          window.addEventListener("mousewheel", this.scrollFunc, {
            passive: false,
          });
        window.onresize = () => {
          this.onResize();
        };
        // 从URL获取sceneId
        const urlParams = new URLSearchParams(window.location.search);
        this.sceneId = urlParams.get('sceneId');
        if (!this.sceneId) {
          this.$message.error('场景ID不能为空');
          return;
        }
        // this.sceneName = urlParams.get('sceneName');
        // const currentUrl = window.location.href;
        // const cleanUrl = currentUrl.split("?")[0];
        // history.replaceState({},"",cleanUrl)
        await this.getSceneConfig();
        await this.getSceneName();




        this.timer22 = setInterval(() => {
          this.updatePersonnel();
          this.updateTemperature();
          this.updateDust();
          this.updateWindSpeed();
          this.updateT1Amount();
          this.updateT2Amount();
          this.updateT3Amount();
          this.getkuangya()
          this.getkuangya2()
          this.piDaiApiResponse();
          this.juShanApiResponse();
          this.getJueMao();
          this.getJueMaoYou();
          this.getStatus()
          this.getBianPing();
          this.getziyi();
          this.isShowLeftStatus();
          this.isShowRightStatus();

          this.getJinChi();
        }, 60000)
      },
      methods: {
        getSceneName() {
          axios.get(`${this.apiBaseUrl}/interface-server-automated/adminConfig/getSceneById?id=${this.sceneId}`)
            .then(response => {
              if (response.data.code === 200) {
                this.sceneName = response.data.data[0].bussName;
                document.title = this.sceneName.split("矿")[0]+"矿智能掘进工作面WEB";
              }
            })
            .catch(error => {
              this.$message.error('获取场景配置失败');
            });
        },
        jinchi() {
          //请求
          axios.get(this.baseurl22)
            .then((res) => {
              // 初始化数组
              this.progressDialogVisible = true;
              this.retreatData = [];
              this.progressData = [];
				
              // 遍历返回数据进行分类
              res.data.data.forEach(item => {
                console.log(item)
                if (item.sceneType === 1) {
                  // 采煤工作面数据
                  this.retreatData.push(item);
                } else if (item.sceneType === 2) {
                  // 掘进工作面数据
                  const progressItem = { ...item };

                  // 根据sceneSubType添加typeName
                  if (item.sceneSubType === 1) {
                    progressItem.typeName = '煤巷';
                  } else if (item.sceneSubType === 2) {
                    progressItem.typeName = '岩巷';
                  }

                  this.progressData.push(progressItem);
                }
              });


            })
				.catch((error) => {
				   // 处理 AxiosError
				   if (error.response) {
				     // 请求成功但状态码不在 2xx 范围
				     alert(error.response.data.message);
				     console.log(error.response.status);
				     console.log(error.response.headers);
				   } else if (error.request) {
				     // 请求发出但没有收到响应
				     console.log(error.request);
				   } else {
				     // 在设置请求时发生了错误
				     console.log('Error', error.message);
				   }
				  });


        },
        adjustData(row) {
          // 这里添加调校数据的逻辑
          this.$message({
            message: '数据调校成功',
            type: 'success',
            customClass: 'custom-message'
          });
        },
        getSceneConfig() {
          axios.get(`${this.apiBaseUrl}/interface-server-automated/adminConfig/getSceneConfigBySceneId?sceneId=${this.sceneId}`)
            .then(response => {
              if (response.data.code === 200) {
                this.sceneConfig = response.data.data;
                // 根据configType配置接口

                this.configureApis();
              }
            })
            .catch(error => {
              this.$message.error('获取场景配置失败');
            });
        },

        async configureApis() {
          if (!this.sceneConfig || this.sceneConfig.length == 0) {
            window.open("./404.html", '_self')
          }
          if (!this.sceneConfig) return;

          // 根据configType配置不同的接口地址
          for (let i = 0; i < this.sceneConfig.length; i++) {
            if (this.sceneConfig[i].configType == 1) {
              this.baseurl1 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 2) {
              this.baseurl2 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 3) {
              this.baseurl3 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 4) {
              this.baseurl4 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 5) {
              this.baseurl5 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 6) {
              this.baseurl6 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 7) {
              this.baseurl7 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 8) {
              this.baseurl8 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 9) {
              this.baseurl9 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 10) {
              this.baseurl10.push(this.sceneConfig[i]);

            } else if (this.sceneConfig[i].configType == 11) {
              this.baseurl11 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 12) {
              this.baseurl12 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 13) {
              this.baseurl13 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 14) {
              this.baseurl14 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 15) {
              this.baseurl15 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 16) {
              this.baseurl16 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 17) {
              this.baseurl17 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 18) {
              this.baseurl18 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 19) {
              this.baseurl19 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 20) {
              this.baseurl20 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 21) {
              //烟雾
              this.baseurl21 = this.sceneConfig[i].configValue;
            } else if (this.sceneConfig[i].configType == 22) {
              //进退尺
              this.baseurl22 = this.sceneConfig[i].configValue;
            }
          }

          if (this.baseurl10 && this.baseurl10.length > 0) {
            if (this.baseurl10[0].configResType * 1 == 1) {
              this.video1 = this.baseurl10[0].info;
              this.video1url = this.baseurl10[0].configValue
            } else if (this.baseurl10[0].configResType * 1 == 2) {
              this.video1 = this.baseurl10[0].info;
              const response = await axios.get(this.baseurl10[0].configValue)
              this.video1url = response.data.data[0].rtspURL
            }

            if (this.baseurl10[1].configResType * 1 == 1) {
              this.video2 = this.baseurl10[1].info;
              this.video2url = this.baseurl10[1].configValue
            } else if (this.baseurl10[1].configResType * 1 == 2) {
              this.video2 = this.baseurl10[1].info;
              const response = await axios.get(this.baseurl10[1].configValue)
              this.video2url = response.data.data[0].rtspURL
            }

            if (this.baseurl10[2].configResType * 1 == 1) {
              this.video3 = this.baseurl10[2].info;
              this.video3url = this.baseurl10[2].configValue
            } else if (this.baseurl10[2].configResType * 1 == 2) {
              this.video3 = this.baseurl10[2].info;
              const response = await axios.get(this.baseurl10[2].configValue)
              this.video3url = response.data.data[0].rtspURL
            }

            if (this.baseurl10[3].configResType * 1 == 1) {
              this.video4 = this.baseurl10[3].info;
              this.video4url = this.baseurl10[3].configValue
            } else if (this.baseurl10[3].configResType * 1 == 2) {
              this.video4 = this.baseurl10[3].info;
              const response = await axios.get(this.baseurl10[3].configValue)
              this.video4url = response.data.data[0].rtspURL
            }
            if (this.baseurl10[4].configResType * 1 == 1) {
              this.video5 = this.baseurl10[4].info;
              this.video5url = this.baseurl10[4].configValue
            } else if (this.baseurl10[4].configResType * 1 == 2) {
              this.video5 = this.baseurl10[4].info;
              const response = await axios.get(this.baseurl10[4].configValue)
              this.video5url = response.data.data[0].rtspURL
            }
            // this.video2=this.baseurl10[1].split(",")[0]
            // this.video2url = this.baseurl10[1].split(",")[1]
            // this.video3=this.baseurl10[2].split(",")[0]
            // this.video3url = this.baseurl10[2].split(",")[1]
            // this.video4=this.baseurl10[3].split(",")[0]
            // this.video4url = this.baseurl10[3].split(",")[1]
          }

          this.playVideo();
          //业务系统的数据请求
          this.updatePersonnel();
          this.updateTemperature();
          this.updateDust();
          this.updateWindSpeed();
          this.updateT1Amount();
          this.updateT2Amount();
          this.updateT3Amount();
          this.getkuangya()
          this.getkuangya2()
          this.getJinChi();
          this.piDaiApiResponse();
          this.juShanApiResponse();
          this.getJueMao();
          this.getJueMaoYou();
          this.getStatus();
          this.getBianPing();
          this.getziyi();
          this.isShowLeftStatus();
          this.isShowRightStatus();


        },
        getUrl(url) {

          axios.get(
            url
          )
            .then((res) => {
              console.log(444444, res)
            })
        },
        getJinChi() {

          axios.get(
            this.baseurl11
          )
            .then((response) => {
              this.morningProgress = (response.data.data.morningShift / 100).toFixed(1);

              // this.progressData[0].morningProgress = (response.data.data.morningShift/100).toFixed(1);
              // this.progressData[0].dayProgress = (response.data.data.afternoonShift/100).toFixed(1);
              // this.progressData[0].nightProgress = (response.data.data.eveningShift/100).toFixed(1);
              // this.progressData[0].dayPlan = (response.data.data.dailyPlanDis/100).toFixed(1);
              // this.progressData[0].dayActual = (response.data.data.yesTodayDailyDis/100).toFixed(1);
              // this.progressData[0].monthPlan = (response.data.data.monthPlanDis/100).toFixed(1);
              // this.progressData[0].monthActual = (response.data.data.monthDis/100).toFixed(1);
              // this.progressData[0].totalPlan = (response.data.data.totalPlanDis/100).toFixed(1);
              // this.progressData[0].totalActual = (response.data.data.yesTodayTotalDis/100).toFixed(1);
              this.dayProgress = (response.data.data.afternoonShift / 100).toFixed(1);
              this.nightProgress = (response.data.data.eveningShift / 100).toFixed(1);
              // this.dailyDis = (response.data.data.yesTodayDailyDis/100).toFixed(1);
              // this.totalDis = (response.data.data.yesTodayTotalDis/100).toFixed(1)
              this.dailyDis = (response.data.data.dailyDis / 100).toFixed(1);
              this.totalDis = (response.data.data.yesTodayTotalDis / 100).toFixed(1)
              this.monthDis = (response.data.data.monthDis / 100).toFixed(1);
              this.dailyPlanDis = (response.data.data.dailyPlanDis / 100).toFixed(1);
              this.monthPlanDis = (response.data.data.monthPlanDis / 100).toFixed(1)
              this.totalPlanDis = (response.data.data.totalPlanDis / 100).toFixed(1);

            })
        },
        timeChange(value) {

          this.startTime = value[0]
          this.endTime = value[1]
          this.getData()
        },
        getStatus() {

          axios.get(
            this.baseurl17

          )
            .then((response) => {

              if (response.data.data.value && response.data.data.value * 1 == 0) {
                this.isStatus = true
              } else {
                this.isStatus = false
              }

            })
        },
        isShowRightStatus() {
          // 模拟请求
          axios.get(
            this.baseurl19
          )
            .then((response) => {
              if (response.data.data.indexValue * 1 == 4) {
                this.isShowRight = true
              }
            })
        },
        isShowLeftStatus() {
          // 模拟请求
          axios.get(
            this.baseurl18
          )
            .then((response) => {
              console.log(response)
              if (response.data.data.indexValue * 1 == 1) {
                this.isShowLeft = true
              }
            })
        },
        getziyi() {

          // 模拟请求
          axios.get(
            this.baseurl16
          )
            .then((response) => {
              this.actionConfig = this.handleData(response.data.data)
            })
        },
        getActionStatus(pe001, pe002) {
          if (!pe001 && !pe002) return '停止';  // 修改为停止
          if (pe001 && !pe002) return '伸';
          if (!pe001 && pe002) return '收';
          return '收';
        },
        getBianPing() {
          // 模拟请求
          axios.get(
            this.baseurl15
          )
            .then((response) => {

              this.frequencyConfig = this.handleData(response.data.data);

            })

        },
        getJueMao() {
          // 模拟请求
          axios.get(
            this.baseurl14
          )
            .then((response) => {
              this.leftColumnConfig = this.handleData(response.data.data)
            })
            .catch((error) => {
              console.error("Error fetching personnel data:", error);
            });
        },

        getJueMaoYou() {
          // 模拟请求
          axios.get(
            this.baseurl20
          )
            .then((response) => {
              this.rightColumnConfig = this.handleData(response.data.data)


            })
            .catch((error) => {
              console.error("Error fetching personnel data:", error);
            });
        },
        //处理整个返回数据
        handleData(originalList) {
          // 检查输入是否有效
          if (!originalList || !Array.isArray(originalList) || originalList.length === 0) {
            return [];
          }

          // 使用map处理每个数据项
          return originalList.map(data => {
            // 创建新对象，解构复制基本属性
            const processedData = {
              ...data,
              indexValue: data.indexValue // 默认保持原值
            };

            // 检查状态配置列表是否存在且有值
            if (data.indexStatusConfigList.length > 0) {
              // 查找匹配的状态配置
              const matchingConfig = data.indexStatusConfigList.find(config =>
                Number(config.original) === Number(data.indexValue)
              );

              // 如果找到匹配的配置，使用escape值
              if (matchingConfig) {
                processedData.indexValue = matchingConfig.escape;
              }
            }

            return processedData;
          });
        },
        // 处理接口返回数据
        piDaiApiResponse() {
          // 模拟请求
          axios
            .get(
              this.baseurl13
            )
            .then((response) => {
              this.statusConfig = this.handleData(response.data.data);


            })
            .catch((error) => {
              console.error("Error fetching personnel data:", error);
            });
        },
        formatValue(indexValue, config) {
          // 设备状态判断

          if (config.code === 'PE_CODE_0039' && indexValue === 1) {
            return '离线';
          } else if (config.code === 'PE_CODE_0040' && indexValue === 1) {
            return '近控';
          } else if (config.code === 'PE_CODE_0041' && indexValue === 1) {
            return '远控';
          }

          // 运行状态判断
          if (config.code === 'PE_CODE_0035' && indexValue === 1) {
            return '设备停止';
          } else if (config.code === 'PE_CODE_0036' && indexValue === 1) {
            return '正在运行';
          } else if (config.code === 'PE_CODE_0037' && indexValue === 1) {
            return '将要启车';
          } else if (config.code === 'PE_CODE_0038' && indexValue === 1) {
            return '将要停车';
          }

          // 如果不是特殊状态码，则返回原值
          return indexValue;
        },

        // 获取状态文本
        getStatusText(code, type) {
          if (code === 'shebei') {
            if (type == 0) {
              return '离线'
            } else if (type == 1) {
              return '远控'
            } else if (type == 2) {
              return '近控'
            }
          } else if (code === 'yunxing') {
            if (type == 0) {
              return '设备停止'
            } else if (type == 1) {
              return '正在运行'
            } else if (type == 2) {
              return '将要启车'
            } else if (type === 3) {
              return '将要停车'
            }
          }

        },

        // 获取状态颜色
        getStatusColor(code, type) {
          if (code === 'shebei') {
            if (type == 0) {
              return '#f60606'
            } else if (type == 1) {
              return '#f6ea0e'
            } else if (type == 2) {
              return '#15f54a'
            }
          } else if (code === 'yunxing') {
            if (type == 0) {
              return '#f60606'
            } else if (type == 1) {
              return '#15f54a'
            } else if (type == 2) {
              return '#f6ea0e'
            } else if (type === 3) {
              return '#f6ea0e'
            }
          }


        },

        // 获取状态文本
        getStatusTextAll(config, value) {
          if (config.statusMap) {
            return config.statusMap[value].text || '--'
          }
          if (config.format) {
            return `${config.format(value || 0)}${config.unit ? ' ' + config.unit : ''}`
          }

          return value || 0
        },

        // 获取状态颜色
        getStatusColorAll(config, value) {
          if (config.statusMap) {
            return config.statusMap[value].color || '#00ffff'
          }
          return config.color || '#00ffff'
        },
        // 处理接口返回数据
        juShanApiResponse() {

          // 模拟请求
          axios.get(
            this.baseurl12
          )
            .then((response) => {

              this.fanConfig = this.handleData(response.data.data)

            })
            .catch((error) => {
              console.error("Error fetching personnel data:", error);
            });
        },
        formatDate(date) {
          const year = date.getFullYear();
          const month = date.getMonth() + 1;
          const day = date.getDate();
          const hours = String(date.getHours()).padStart(2, "0");
          const minutes = String(date.getMinutes()).padStart(2, "0");
          const seconds = String(date.getSeconds()).padStart(2, "0");

          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        //业务系统的数据请求
        updatePersonnel() {
          // 模拟请求

          axios
            .get(
              this.baseurl1
            )
            .then((response) => {

              this.personnelList = response.data.data;
              this.currentPersonnel = this.personnelList.length;
              this.totalPersonnel = response.data.totalPersonnel;
            })
            .catch((error) => {
              console.error("Error fetching personnel data:", error);
            });
        },
        getkuangya() {
          // 模拟请求
          this.leftData = [];
          this.leftXAxisData = [];
          axios
            .get(
              this.baseurl8
            )
            .then((response) => {

              if (response.data.data) {
                for (let i = 0; i < response.data.data.length; i++) {
                  this.leftXAxisData.push(response.data.data[i].monitorArea)
                  this.leftData.push(response.data.data[i].monitorValue)
                }
                this.setChartOption();

              }

            })
            .catch((error) => {
              console.error("Error fetching temperature data:", error);
            });
        },
        getkuangya2() {
          // 模拟请求
          this.rightData = [];
          this.rightXAxisData = [];
          axios
            .get(
              this.baseurl9
            )
            .then((response) => {

              if (response.data.data) {
                for (let i = 0; i < response.data.data.length; i++) {
                  this.rightXAxisData.push(response.data.data[i].monitorArea)
                  this.rightData.push(response.data.data[i].monitorValue)
                }
                this.setChartOption();

              }
            })
            .catch((error) => {
              console.error("Error fetching temperature data:", error);
            });
        },
        updateTemperature() {
          // 模拟请求
          axios
            .get(
              this.baseurl2
            )
            .then((response) => {
              this.temperature = response.data.data[0].ssAnalogValue;
            })
            .catch((error) => {
              console.error("Error fetching temperature data:", error);
            });
        },
        updateDust() {
          // 模拟请求
          axios
            .get(
              this.baseurl3
            )
            .then((response) => {
              this.dust = response.data.data[0].ssAnalogValue;
            })
            .catch((error) => {
              console.error("Error fetching dust data:", error);
            });
        },
        updateWindSpeed() {
          // 模拟请求
          axios
            .get(
              this.baseurl4
            )
            .then((response) => {
              this.windSpeed = response.data.data[0].ssAnalogValue;
            })
            .catch((error) => {
              console.error("Error fetching wind speed data:", error);
            });
        },
        updateT1Amount() {
          // 模拟请求
          axios
            .get(
              this.baseurl5
            )
            .then((response) => {
              this.t1Amount = response.data.data[0].ssAnalogValue;
            })
            .catch((error) => {
              console.error("Error fetching t1 amount data:", error);
            });
        },
        updateT2Amount() {
          // 模拟请求
          axios
            .get(
              this.baseurl6
            )
            .then((response) => {
              this.t2Amount = response.data.data[0].ssAnalogValue;
            })
            .catch((error) => {
              console.error("Error fetching t2 amount data:", error);
            });
        },
        updateT3Amount() {
          // 模拟请求
          axios
            .get(
              this.baseurl7
            )
            .then((response) => {
              this.t3Amount = response.data.data[0].ssAnalogValue;
            })
            .catch((error) => {
              console.error("Error fetching t3 amount data:", error);
            });
        },
        getData() {
          axios
            .get(
              baseurl2 +
              "/interface-server-pe/alarmIndexResult/page?alarmCycleType=0&alarmType=" + this.alarmType + "&page=" + this.page + "&size=10" + "&startTime=" + this.startTime + "&endTime=" + this.endTime
            )
            .then((response) => {
              this.tableData = response.data.data.content;
              this.total = response.data.data.totalPages
            })
            .catch((error) => {
              console.error("Error fetching t3 amount data:", error);
            });
        },
        setZoom() {
          const designHeight = 1080;
          const clientHeight = document.documentElement.clientHeight;
          console.log(5555, document.documentElement.clientHeight)
          const zoomScale = clientHeight / designHeight;
          if (document.documentElement.clientHeight > 980) {
            this.ulStyle = {
              marginLeft: '0'
            }
          } else {
            this.ulStyle = {
              marginLeft: '7%'
            }
          }
          // 使用Vue的方式获取DOM元素
          const liElements = document.querySelectorAll("li");
          liElements.forEach((li) => {
            li.style.zoom = zoomScale;
          });
          this.zoom = zoomScale;
          // 如果需要也可以设置图表容器的zoom
          if (this.$refs.chartBox) {
            this.$refs.chartBox.style.zoom = zoomScale;
          }

          // 重要：更新图表大小和缩放
          this.updateChartSize();
        },
        updateChartSize() {
          if (this.chart) {
            // 获取容器当前大小
            // const container = this.$refs.barChart;
            // const width = container.clientWidth;
            // const height = container.clientHeight;

            // // 应用缩放后的尺寸
            // this.chart.resize({
            //   width: width * this.zoomScale,
            //   height: height * this.zoomScale,
            // });

            // // 更新图表选项以适应新的尺寸
            // this.setChartOption();
            this.chart.resize();
          }
          this.leftChart && this.leftChart.resize()
          this.rightChart && this.rightChart.resize()
        },
        showHistoryDialog() {
          this.getData();
          this.historyDialogVisible = true;
        },
        handleClose() {
          this.page = 1,
            this.alarmType = "0",
            this.queryTime = "",
            this.total = 0,
            this.startTime = "",
            this.endTime = ""
          this.historyDialogVisible = false;
        },
        handleQuery() {
          // 实现查询逻辑
          // 根据 queryForm 中的条件调用后端接口获取数据
        },
        handleSizeChange(val) {
          this.pageSize = val;
          this.getData();
        },
        handleCurrentChange(val) {
          this.page = val;
          this.getData();
        },
        playVideo() {
          if (mpegts.isSupported()) {
            if (mpegts.getFeatureList().mseLivePlayback) {
              var videoElement1 = document.getElementById('videoElement1');
              var player1 = mpegts.createPlayer({
                type: 'mse',  // could also be mpegts, m2ts, flv
                isLive: true,
                url: this.video1url
              });
              player1.attachMediaElement(videoElement1);
              player1.load();
              player1.play();


              var videoElement3 = document.getElementById('videoElement3');
              var player3 = mpegts.createPlayer({
                type: 'mse',  // could also be mpegts, m2ts, flv
                isLive: true,
                url: this.video2url
              });
              player3.attachMediaElement(videoElement3);
              player3.load();
              player3.play();

              var videoElement4 = document.getElementById('videoElement4');
              var player4 = mpegts.createPlayer({
                type: 'mse',  // could also be mpegts, m2ts, flv
                isLive: true,
                url: this.video3url
              });
              player4.attachMediaElement(videoElement4);
              player4.load();
              player4.play();


              var videoElement2 = document.getElementById('videoElement2');
              var player2 = mpegts.createPlayer({
                type: 'mse',  // could also be mpegts, m2ts, flv
                isLive: true,
                url: this.video4url
              });
              player2.attachMediaElement(videoElement2);
              player2.load();
              player2.play();

              var videoElement5 = document.getElementById('videoElement5');
              var player5 = mpegts.createPlayer({
                type: 'mse',  // could also be mpegts, m2ts, flv
                isLive: true,
                url: this.video5url
              });
              player5.attachMediaElement(videoElement5);
              player5.load();
              player5.play();
            }

          }

        },
        initChart() {
          // this.chart = echarts.init(this.$refs.barChart);
          // this.setChartOption();

          this.leftChart = echarts.init(this.$refs.leftBarChart)


          // 初始化右侧图表
          this.rightChart = echarts.init(this.$refs.rightBarChart)
          this.setChartOption();
        },
        setChartOption() {
          this.leftChart.setOption({
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              }
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: this.leftXAxisData,
              barCategoryGap: '0%',
              boundaryGap: ['0%', '100%'],
              // 调整刻度标签位置
              axisLabel: {
                interval: 0,
                align: 'left',
                color: '#fff',

                fontSize: 14
              },

              splitLine: {
                show: false
              },


            },
            yAxis: {
              type: 'value',
              name: 'KN',
              show: 'false',
              nameTextStyle: {
                color: '#fff'
              },
              axisLabel: {
                color: '#fff',
                show: false,
              },
              splitLine: {
                show: false
              },
            },
            series: [{
              data: this.leftData,
              type: 'bar',
              barMaxWidth: 50,
              barGap: '10px', // 设置柱状图之间的间隔为10px
              barCategoryGap: '0%', // 设置类别之间的间隔为0，确保靠左
              itemStyle: {
                color: '#7ce7fd'
              },
              label: {
                show: true,
                position: 'top',
                formatter: '{c} kN',
                fontSize: 14,
                textStyle: {
                  color: '#fff', // 标签字体颜色
                  fontSize: 14, // 标签字体大小
                  fontWeight: 'bold', // 标签字体加粗

                  fontFamily: 'Arial' // 标签字体
                }
              }
            }]
          })
          this.rightChart.setOption({
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              },
              formatter: function (params) {
                // params 是一个数组，包含当前坐标轴下所有系列的数据
                let result = ''; // 初始化提示框内容
                params.forEach(function (item) {
                  // item 是当前系列的数据
                  result += `${item.name} --${item.value} mm\n`; // 添加系列名称、数值和单位
                });
                return result;
              }
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: this.rightXAxisData,

              axisLabel: {
                interval: 0,
                align: 'left',
                color: '#fff',

                fontSize: 14
              },
              splitLine: {
                show: false
              },
            },
            yAxis: {
              type: 'value',
              name: 'mm',
              show: 'false',
              nameTextStyle: {
                color: '#fff'
              },
              axisLabel: {
                color: '#fff',
                show: false,
              },
              splitLine: {
                show: false
              },
            },
            series: [{
              data: this.rightData,
              type: 'bar',
              barMaxWidth: 50,
              itemStyle: {
                color: '#7ce7fd'
              },
              label: {
                show: true,
                position: 'top',
                formatter: '{c} mm',
                fontSize: 14,
                textStyle: {
                  color: '#fff', // 标签字体颜色
                  fontSize: 14, // 标签字体大小
                  fontWeight: 'bold', // 标签字体加粗

                  fontFamily: 'Arial' // 标签字体
                }
              }
            }]
          })
          const option = {
            grid: {
              left: "3%",
              right: "4%",
              bottom: "8%",
              top: "8%",
              containLabel: true,
            },
            xAxis: {
              type: "category",
              data: this.chartData.xData,
              barCategoryGap: '0%',
              axisLabel: {
                color: "#fff",
                interval: 0,
                rotate: 0,
                fontSize: 10 * this.zoomScale, // 根据缩放调整字体大小
              },
              axisLine: {
                lineStyle: {
                  color: "#0B3E7F",
                },
              },
            },
            yAxis: {
              type: "value",

              axisLabel: {
                color: "#fff",
                show: false,
                fontSize: 10 * this.zoomScale, // 根据缩放调整字体大小
              },
              splitLine: {
                lineStyle: {
                  color: "rgba(11,62,127,0.3)",
                },
              },
              min: 0,
              max: 100,
              interval: 50, // 设置刻度间隔为500
              splitNumber: 3, // 将y轴分成3段
              splitLine: {
                lineStyle: {
                  color: "rgba(11,62,127,0.3)",
                },
                // 只显示主刻度的分隔线
                show: false,
              },
              axisLine: {
                lineStyle: {
                  color: "#0B3E7F",
                },
              },
            },
            series: [
              {
                data: this.chartData.values,
                type: "bar",
                barWidth: "50%",
                // itemStyle: {
                //   normal: {
                //     color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                //       {
                //         offset: 0,
                //         color: "#00FFFF",
                //       },
                //       {
                //         offset: 1,
                //         color: "#0066FF",
                //       },
                //     ]),
                //   },
                // },
                animation: true,
                animationDuration: 2000,
                animationEasing: "cubicInOut",
              },
            ],
            tooltip: {
              trigger: "axis",
              axisPointer: {
                type: "shadow",
              },
              formatter: function (params) {
                return (
                  params[0].name + "<br/>距离: " + params[0].value + "mm"
                );
              },
            },
          };
          // this.chart.setOption(option);
        },
        handleResize() {
          this.leftChart && this.leftChart.resize()
          this.rightChart && this.rightChart.resize()
        },
        // 更新数据的方法
        updateData() {
          // 模拟新数据
          // this.chartData.values = this.chartData.values.map((value) => {
          //   return value + Math.random() * 100 - 50;
          // });
          // 更新图表
          this.setChartOption();
        },
        // 外部调用的方法,用于更新实际数据
        updateChartData(newData) {
          this.chartData.values = newData;
          this.setChartOption();
        },
        urlArgs() {
          var args = {};
          var query = location.search.substring(1);
          var pairs = query.split("&");
          for (var i = 0; i < pairs.length; i++) {
            var pos = pairs[i].indexOf("=");
            if (pos == -1) {
              continue;
            }
            var name = pairs[i].substring(0, pos);
            var value = pairs[i].substring(pos + 1);
            args[name] = value;
          }
          return args;
        },
        async onResize() {
          // 根据当前屏幕尺寸进行缩放达到和编辑器显示的效果一致
          // for (let item of this.templateList) {
          //   if (this.basicConfig.zoomMode.default === "YOY") {
          //     this.initElementZoom(item.property);
          //   }
          //   this.initSize(item.property);
          //   item.isLoading = this.guid();
          //   this.$forceUpdate();
          //   await this.initData(item);
          // }
          this.loading = false;
          let zoomHeight = 1;
          if (this.basicConfig.zoomMode.default === "YOY") {
            // if (document.documentElement.clientWidth > this.basicConfig.width) {
            // 实际宽比预设宽大 则按照页面实际高除去 预设放大的高 进行缩放
            zoomHeight =
              document.documentElement.clientHeight /
              (this.innerZoom * this.basicConfig.height);
            if (zoomHeight > 1) {
              zoomHeight = 1;
            }
            // }
            this.$refs.body.style.zoom = zoomHeight;
          }
        },
        initElementZoom(item) {
          // 刷新组件的缩放
          let elementWidth = this.basicConfig.width / item.width;
          let element = this.$refs[item.elementId + "Template"];
          this.innerZoom =
            ((elementWidth / this.basicConfig.width) *
              document.documentElement.clientWidth) /
            elementWidth;
          element && (element[0].style.zoom = this.innerZoom);
        },
        scrollFunc(e) {
          if (e.wheelDelta > 0) {
            this.$refs.carousel.setActiveItem(this.nowWindowIndex + 1);
          } else {
            this.$refs.carousel.setActiveItem(this.nowWindowIndex - 1);
          }
        },
        windowChange(e) {
          // 改变窗口
          this.nowWindowIndex = e;
          this.$nextTick(() => {
            this.onResize();
          });
        },
        async initId(id) {
          this.loading = true;
          this.templateList = [];
          let apiUrlOrigin = this.url.substr(0, this.url.length - 12);
          if (apiUrlOrigin === "..") {
            apiUrlOrigin = "https://moctest.sobeylingyun.com";
          }
          const apiUrl = `${apiUrlOrigin}/screen/api/v1/screen/${id}`;
          const res = await axios(apiUrl, {
            withCredentials: true, // 带上cookie
          });

          const { name, background, componentList, extend } = res.data.data;
          const templateList = {
            templateList: componentList,
            ...extend,
          };
          const tempData = [];
          this.allTemplateList = [];
          templateList.templateList.map((item) => {
            const upIndex = item.extend.upIndex || 0; // 获取保存时的upIndex 即所处的桌面序号
            if (!this.allTemplateList[upIndex]) {
              this.allTemplateList[upIndex] = [];
            }
            this.allTemplateList[upIndex].push(item.extend);
          });
          this.nowWindowIndex = 0;
          delete templateList.templateList;
          this.basicConfig = {
            ...templateList,
          };
          if (!this.basicConfig.window) {
            this.basicConfig.window = [0];
          }
          const cssList = [];
          this.cssMerge = ""; // 每次都清空
          this.allTemplateList = this.allTemplate2NotGroupAllTemplate(
            this.allTemplateList
          );
          this.allTemplateList.map((item) => {
            this.loadCss(item, cssList);
          });
          var style = document.createElement("style");
          style.type = "text/css";
          style.innerHTML = this.cssMerge.replaceAll("../../../", "../");
          document.getElementsByTagName("head")[0].appendChild(style);
        },
        loadCss(templateList, cssList) {
          //动态加载css使用到的css文件 并合并
          const find = (node) => {
            // 匿名函数防止this指向错误
            node.map((item) => {
              let { type, componentsType } = item.property;
              if (type == "group") {
                find(item.property.children);
              } else {
                if (
                  !cssList.includes(item.type) &&
                  componentsType !== "charts"
                ) {
                  if (componentsType === "private") {
                    // 页面组件 在进行细分
                    this.loadCss(item.property.option.templateList, cssList);
                  } else {
                    // 防止重复加载
                    cssList.push(type);
                    if (type.slice(0, 3) === "tab") {
                      type = "tab"; // tab1 tab2 用的都是tab.css文件
                    }
                    $.ajax({
                      async: false,
                      url: `./lib/css/${type}.css`,
                      success: (data) => {
                        this.cssMerge += data;
                      },
                    });
                  }
                }
              }
            });
          };
          find(templateList);
        },
        async initJs(templateList) {
          // 动态加载js文件
          if (!templateList) {
            this.loading = false;
            return;
          }
          for (let item of templateList) {
            const { type, componentsType, option, secondType } =
              item.property;
            if (secondType === "city" && !this.isLoadJsList.includes("L7")) {
              await this.loadJS(`${this.url}/js/L7-min.js`);
              await this.initMap();
              this.isLoadJsList.push("L7");
            } else if (
              ["charts", "pyramid", "earth", "cesiumEarth", "L7"].includes(
                componentsType
              )
            ) {
              // pyramid 用到了 echarts
              if (!this.isLoadJsList.includes("echarts")) {
                await this.loadJS("static/js/echarts.min.js");
                this.isLoadJsList.push("echarts");
              }
              if (
                ["area", "areaPoint"].includes(componentsType) &&
                !this.isLoadJsList.includes("china")
              ) {
                await this.loadJS(`${this.url}/js/china.js`);
                this.isLoadJsList.push("china");
              } else if (
                secondType === "earth" &&
                !this.isLoadJsList.includes("echartsGl")
              ) {
                await this.loadJS(`${this.url}/js/echarts-gl.min.js`);
                this.isLoadJsList.push("echartsGl");
                await this.initEarth();
              } else if (
                secondType === "cesiumEarth" &&
                !this.isLoadJsList.includes("cesiumEarth")
              ) {
                await this.loadJS(`${this.url}/Cesium/Cesium.js`);
                this.isLoadJsList.push("cesiumEarth");
                await this.initCesiumEarth();
              } else if (
                ["textCloud1", "textCloud"].includes(secondType) &&
                !this.isLoadJsList.includes("echarts-wordcloud")
              ) {
                await this.loadJS(`${this.url}/js/echarts-wordcloud.min.js`);
                this.isLoadJsList.push("echarts-wordcloud");
              }
            } else if (
              ["border", "decoration"].includes(componentsType) &&
              !this.isLoadJsList.includes("dataV")
            ) {
              await this.loadJS(`${this.url}/js/dataV.js`);
              await this.initDecoration();
              this.isLoadJsList.push("dataV");
            } else if (
              ((componentsType === "L7" && type === "queue") ||
                type === "spaceTime") &&
              !this.isLoadJsList.includes("three")
            ) {
              await this.loadJS(`${this.url}/js/three.min.js`);
              await this.loadJS(
                "https://cdn.bootcdn.net/ajax/libs/tween.js/0.11.0/Tween.min.js"
              );
              await this.loadJS(`${this.url}/js/TrackballControls.js`);
              await this.loadJS(`${this.url}/js/CSS3DRenderer.js`);
              await this.initThree(); // 初始化three 组件
              this.isLoadJsList.push("three");
            } else if (
              type === "smartCity" &&
              !this.isLoadJsList.includes("smartCity")
            ) {
              await this.loadJS(
                "http://earthsdk.com/v/last/XbsjEarth/XbsjEarth.js"
              );
            } else if (componentsType === "private") {
              // 存在页面组件 则加载重新遍历
              await this.initJs(option.templateList);
              return;
            }
          }

          this.basicConfig.waterMask &&
            this.addWaterMask(
              "productPreview",
              this.basicConfig.waterMask,
              document.body
            );
          await this.initTemplateList();
        },
        async loadJS(url) {
          return new Promise(function (resolve, reject) {
            var script = document.createElement("script");
            script.type = "text/javascript";
            script.async = false;
            if (script.readyState) {
              //IE
              script.onreadystatechange = function () {
                if (
                  script.readyState == "loaded" ||
                  script.readyState == "complete"
                ) {
                  script.onreadystatechange = null;
                  resolve("success: " + url);
                }
              };
            } else {
              //Others
              script.onload = function () {
                resolve("success: " + url);
              };
            }
            script.onerror = function () {
              reject(Error(url + "load error!"));
            };
            script.src = url;
            document.body.appendChild(script);
          });
        },
        closeModal() {
          this.isShowResize = false;
        },
        resizeTemplate(item) {
          if (item.property.componentsType !== "L7") {
            this.isShowResize = true;
            this.willResizeTemplate = item;
          }
        },
        async initTemplateList() {
          // 初始化获取templateList
          await this.initComponent();
          this.initPage();
        },
        async initData(template) {
          const item = template.property;
          switch (item.dataBindType.default) {
            case "api":
              {
                if (this.timer) {
                  clearInterval(this.timer);
                }
                this.timer = setInterval(async () => {
                  // 自动更新数据
                  await this.initApi(template);
                }, (item.refresh || 10) * 1000 * 60);
                await this.initApi(template);
              }
              break;

            case "SSE":
              {
                await this.SSEBind();
              }
              break;
          }
        },
        initSize(item) {

          Object.keys(item).map((key) => {
            switch (key) {
              case "rotate":
                {
                  let elementRef = this.$refs["chartBox" + item.elementId]; // 用ref代替
                  elementRef[0].style.transform = `rotate(${item[key]}deg)`;
                }
                break;
              case "width":
              case "height":
              case "left":
              case "top":
                {
                  if (this.basicConfig.zoomMode.default === "YOY") {
                    this.setValue(item, key, item[key]);
                  } else {
                    let basicConfigKey = key;
                    if (key === "left") basicConfigKey = "width";
                    if (key === "top") basicConfigKey = "height";
                    this.setValue(
                      item,
                      key,
                      `${(item[key] / this.basicConfig[basicConfigKey]) * 100
                      }%`
                    );
                  }
                }
                break;
              case "zIndex":
              case "backgroundColor":
                {
                  this.setValue(item, key, item[key]);
                }
                break;

                break;
            }
          });
        },
        async initPage() {
          // 渲染数据
          this.templateList.sort((a, b) => {
            return a.property.shareApiId ? 1 : -1; // 排序 shareApiId 往后排
          });
          this.onResize();
          // 初始化背景图片的属性
          if (this.basicConfig.blur) {
            this.$refs.background.style.filter = `blur(${this.basicConfig.blur}px)`;
          }
        },
        async loadECharts() {
          if (typeof echarts === "undefined") {
            await this.loadJS("static/js/echarts.min.js");
          }
          this.initChart();
        },
        showAnalysisDialog() {
          this.analysisDialogVisible = true;
          this.$nextTick(() => {
            this.initAnalysisChart();
          });
        },

        handleAnalysisTimeChange(val) {
          if (val) {
            const [startDate, endDate] = val;
            const start = new Date(startDate);
            const end = new Date(endDate);
            const diffDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));

            if (diffDays > 30) {
              this.$message.warning('时间区间不能超过30天');
              this.analysisTimeRange = [];
              return;
            }

            // 获取选定时间范围的数据
            this.getAnalysisData(startDate, endDate);
          }
        },

        async initAnalysisChart() {
          if (!this.analysisChart) {
            this.analysisChart = echarts.init(this.$refs.analysisChart);
          }
          if (!this.analysisTimeRange || this.analysisTimeRange.length !== 2) {
            this.$message.warning('请选择时间范围');
            return;
          }

          const [startDate, endDate] = this.analysisTimeRange;
          const start = new Date(startDate);
          const end = new Date(endDate);
          const diffDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));

          if (diffDays > 30) {
            this.$message.warning('时间区间不能超过30天');
            return;
          }

          // 设置loading状态
          this.loading = true;
          try {
            await this.getAnalysisData(startDate, endDate);
          } finally {
            this.loading = false;
          }
          // await this.getAnalysisData(startDate, endDate);

          const option = {
            backgroundColor: 'transparent',
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'cross'
              },
              formatter: function (params) {
                let html = `${params[0].axisValue}<br/>`;
                params.forEach(item => {
                  let value = item.value;
                  let unit = item.seriesName === '进尺率' ? ' %' : ' cm';
                  let marker = item.marker;
                  html += `${marker}${item.seriesName}: ${value}${unit}<br/>`;
                });
                return html;
              }
            },
            legend: {
              data: ['计划进度', '实际进度', '进尺率'],
              textStyle: {
                color: '#fff'
              }
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: [{
              type: 'category',
              data: this.chartData.dates,
              axisLabel: {
                color: '#fff'
              },
              axisLine: {
                lineStyle: {
                  color: '#7ce7fd'
                }
              }
            }],
            yAxis: [
              {
                type: 'value',
                name: '进度(米)',
                show: 'false',
                nameTextStyle: {
                  color: '#fff'
                },
                axisLabel: {
                  color: '#fff',
                  show: false,
                },

                axisTick: {
                  show: false // 显示刻度线
                },
                axisLine: {
                  lineStyle: {
                    color: '#7ce7fd'
                  }
                },
                splitLine: {
                  lineStyle: {
                    color: 'rgba(124,231,253,0.1)'
                  }
                }
              },
              {
                type: 'value',
                name: '进尺率(%)',
                nameTextStyle: {
                  color: '#fff'
                },
                axisLabel: {
                  color: '#fff'
                },
                axisLine: {
                  lineStyle: {
                    color: '#7ce7fd'
                  }
                },
                splitLine: {
                  show: false
                }
              }
            ],
            series: [
              {
                name: '计划进度',
                type: 'bar',
                data: this.chartData.plannedProgress,
                // itemStyle: {
                //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                //     { offset: 0, color: '#91cc75' },
                //     { offset: 1, color: '#5eb95e' }
                //   ])
                // },
                barGap: '0%' // 调整柱间距
              },
              {
                name: '实际进度',
                type: 'bar',
                data: this.chartData.actualProgress,
                itemStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#83bff6' },
                    { offset: 1, color: '#188df0' }
                  ])
                }
              },
              {
                name: '进尺率',
                type: 'line',
                yAxisIndex: 1,
                data: this.chartData.rate,
                symbol: 'circle',
                symbolSize: 8,
                lineStyle: {
                  color: '#ffeb3b',
                  width: 2
                },
                itemStyle: {
                  color: '#ffeb3b'
                }
              }
            ]
          };

          this.analysisChart.setOption(option);
        },



        async getAnalysisData(startDate, endDate) {
          try {
            if (this.analysisChart) {
              this.analysisChart.showLoading({
                text: '加载中...',
                color: '#7ce7fd',
                textColor: '#fff',
                maskColor: 'rgba(0, 19, 52, 0.8)'
              });
            }

            // 这里替换为实际的API调用
            const response = await axios.get(baseurl2 + "/interface-server-pe/footage/result/getFootageResult?page=0&size=100000&cycle=0&startTime=" + startDate + "&endTime=" + endDate);




            // 模拟数据
            const days = [];
            const plannedProgress = [];
            const actualProgress = [];
            const rate = [];

            if (response.data.data.content && response.data.data.content.length > 0) {
              for (let i = 0; i < response.data.data.content.length; i++) {
                days.push(response.data.data.content[i].dateTime);
                plannedProgress.push(response.data.data.content[i].dailyPlan);
                actualProgress.push(response.data.data.content[i].dailyDis);
                rate.push(response.data.data.content[i].dailyDisRatio);
              }
            }

            this.chartData = {
              dates: days,
              plannedProgress,
              actualProgress,
              rate
            };


          } catch (error) {
            console.error('获取分析数据失败:', error);
            this.$message.error('获取数据失败');
          } finally {
            if (this.analysisChart) {
              this.analysisChart.hideLoading();
            }
          }
        }
      },
    });
  </script>
  <script type="text/javascript" src="static/js/js/dataV.js"></script>
</body>

</html>