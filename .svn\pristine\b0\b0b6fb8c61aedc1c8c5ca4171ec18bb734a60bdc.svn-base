.staffMix-container {
  width: 100%;
  height: 100%;
}
.staffMix-container .person {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
}
.staffMix-container .person .pers {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center center;
}
.staffMix-container .person .pers.person1 {
  top: 16%;
  background-image: url(../../../iframe/image/staffMix/online.png);
  /* background-size: 100% 100%; */
  transform: rotateX(64deg) rotateZ(0deg);
}
.staffMix-container .person .pers.person2 {
  top: 16%;
  background-image: url(../../../iframe/image/staffMix/cir-2.png);
  transform: rotateX(64deg);
  /* background-size: 100% 100%; */
  -webkit-animation: move 10s linear infinite;
  animation: move 10s linear infinite;
}
.staffMix-container .person .pers.person3 {
  /* background-size: 100% 100%; */
  top: 5%;
  background-image: url(../../../iframe/image/staffMix/person-1.png);
}
.staffMix-container .svgs {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 270px;
  height: 260px;
  margin: auto;
}
.staffMix-container .text {
  position: absolute;
  width: 50px;
  height: 50px;
  text-align: center;
  background-image: url(../../../../../iframe/image/staffMix//cir-1.png);
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
}
.staffMix-container .text span:nth-child(1) {
  display: block;
  width: 52px;
  height: 22px;
  margin-top: 10px;
  line-height: 12px;
  transform: scale(0.85);
}
.staffMix-container .text span:nth-child(2) {
  font-weight: bold;
  font-size: 14px;
}
.staffMix-container .text.per0 {
  top: 215px;
  left: 50px;
}
.staffMix-container .text.per1 {
  top: 113px;
  left: 3px;
}
.staffMix-container .text.per2 {
  top: 45px;
  left: 35px;
}
.staffMix-container .text.per3 {
  left: 120px;
}
.staffMix-container .text.per4 {
  top: 45px;
  left: 190px;
}
.staffMix-container .text.per5 {
  top: 105px;
  left: 210px;
}
.staffMix-container .text.per6 {
  top: 215px;
  left: 180px;
}

@-webkit-keyframes move {
  0% {
    transform: rotateX(-64deg) rotateZ(360deg);
  }
  100% {
    transform: rotateX(-64deg) rotateZ(0deg);
  }
}
@keyframes move {
  0% {
    transform: rotateX(-64deg) rotateZ(360deg);
  }
  100% {
    transform: rotateX(-64deg) rotateZ(0deg);
  }
}
