.bubble {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  animation: star 15s ease infinite;
  left: 70px;
  top: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

@keyframes star {
  0% {
    transform: translate(-10px, -10px);
  }

  50% {
    transform: translate(10px, 10px);
  }

  100% {
    transform: translate(-10px, -10px);
  }
}
