!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(require("vue")):"function"==typeof define&&define.amd?define(["vue"],t):t((e=e||self).Vue)}(this,(function(e){"use strict";function t(e,t){return 1===arguments.length?parseInt(Math.random()*e+1,10):parseInt(Math.random()*(t-e+1)+e,10)}function n(e,t){const n=Math.abs(e[0]-t[0]),r=Math.abs(e[1]-t[1]);return Math.sqrt(n*n+r*r)}function r(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}))}e=e&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e;var i={data:()=>({dom:"",width:0,height:0,debounceInitWHFun:"",domObserver:""}),methods:{async autoResizeMixinInit(){const{initWH:e,getDebounceInitWHFun:t,bindDomResizeCallback:n,afterAutoResizeMixinInit:r}=this;await e(!1),t(),n(),"function"==typeof r&&r()},initWH(e=!0){const{$nextTick:t,$refs:n,ref:r,onResize:i}=this;return new Promise(o=>{t(t=>{const a=this.dom=n[r];this.width=a.clientWidth,this.height=a.clientHeight,"function"==typeof i&&e&&i(),o()})})},getDebounceInitWHFun(){const{initWH:e}=this;this.debounceInitWHFun=function(e,t){let n;return function(){clearTimeout(n);const[r,i]=[this,arguments];n=setTimeout(()=>{t.apply(r,i)},e)}}(100,e)},bindDomResizeCallback(){const{dom:e,debounceInitWHFun:t}=this;this.domObserver=function(e,t){const n=new(window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver)(t);return n.observe(e,{attributes:!0,attributeFilter:["style"],attributeOldValue:!0}),n}(e,t),window.addEventListener("resize",t)},unbindDomResizeCallback(){let{domObserver:e,debounceInitWHFun:t}=this;e.disconnect(),e.takeRecords(),e=null,window.removeEventListener("resize",t)}},mounted(){const{autoResizeMixinInit:e}=this;e()},beforeDestroy(){const{unbindDomResizeCallback:e}=this;e()}},o={name:"DvFullScreenContainer",mixins:[i],data:()=>({ref:"full-screen-container",allWidth:0,scale:0,datavRoot:"",ready:!1}),methods:{afterAutoResizeMixinInit(){const{initConfig:e,setAppScale:t}=this;e(),t(),this.ready=!0},initConfig(){const{dom:e}=this,{width:t,height:n}=screen;this.allWidth=t,e.style.width=t+"px",e.style.height=n+"px"},setAppScale(){const{allWidth:e,dom:t}=this,n=document.body.clientWidth;t.style.transform=`scale(${n/e})`},onResize(){const{setAppScale:e}=this;e()}}};function a(e,t,n,r,i,o,a,s,l,d){"boolean"!=typeof a&&(l=s,s=a,a=!1);const c="function"==typeof n?n.options:n;let u;if(e&&e.render&&(c.render=e.render,c.staticRenderFns=e.staticRenderFns,c._compiled=!0,i&&(c.functional=!0)),r&&(c._scopeId=r),o?(u=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,l(e)),e&&e._registeredComponents&&e._registeredComponents.add(o)},c._ssrRegister=u):t&&(u=a?function(e){t.call(this,d(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,s(e))}),u)if(c.functional){const e=c.render;c.render=function(t,n){return u.call(n),e(t,n)}}else{const e=c.beforeCreate;c.beforeCreate=e?[].concat(e,u):[u]}return n}const s="undefined"!=typeof navigator&&/msie [6-9]\\b/.test(navigator.userAgent.toLowerCase());function l(e){return(e,t)=>function(e,t){const n=s?t.media||"default":e,r=c[n]||(c[n]={ids:new Set,styles:[]});if(!r.ids.has(e)){r.ids.add(e);let n=t.source;if(t.map&&(n+="\n/*# sourceURL="+t.map.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(t.map))))+" */"),r.element||(r.element=document.createElement("style"),r.element.type="text/css",t.media&&r.element.setAttribute("media",t.media),void 0===d&&(d=document.head||document.getElementsByTagName("head")[0]),d.appendChild(r.element)),"styleSheet"in r.element)r.styles.push(n),r.element.styleSheet.cssText=r.styles.filter(Boolean).join("\n");else{const e=r.ids.size-1,t=document.createTextNode(n),i=r.element.childNodes;i[e]&&r.element.removeChild(i[e]),i.length?r.element.insertBefore(t,i[e]):r.element.appendChild(t)}}}(e,t)}let d;const c={};const u=o;var f=function(){var e=this.$createElement;return(this._self._c||e)("div",{ref:this.ref,attrs:{id:"dv-full-screen-container"}},[this.ready?[this._t("default")]:this._e()],2)};f._withStripped=!0;const h=a({render:f,staticRenderFns:[]},(function(e){e&&e("data-v-2da16e2c_0",{source:"#dv-full-screen-container {\n  position: fixed;\n  top: 0px;\n  left: 0px;\n  overflow: hidden;\n  transform-origin: left top;\n  z-index: 999;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,eAAe;EACf,QAAQ;EACR,SAAS;EACT,gBAAgB;EAChB,0BAA0B;EAC1B,YAAY;AACd",file:"main.vue",sourcesContent:["#dv-full-screen-container {\n  position: fixed;\n  top: 0px;\n  left: 0px;\n  overflow: hidden;\n  transform-origin: left top;\n  z-index: 999;\n}\n"]},media:void 0})}),u,void 0,!1,void 0,!1,l,void 0,void 0);function p(e){e.component(h.name,h)}const g={name:"DvLoading"};var v=function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"dv-loading"},[t("svg",{attrs:{width:"50px",height:"50px"}},[t("circle",{attrs:{cx:"25",cy:"25",r:"20",fill:"transparent","stroke-width":"3","stroke-dasharray":"31.415, 31.415",stroke:"#02bcfe","stroke-linecap":"round"}},[t("animateTransform",{attrs:{attributeName:"transform",type:"rotate",values:"0, 25 25;360, 25 25",dur:"1.5s",repeatCount:"indefinite"}}),this._v(" "),t("animate",{attrs:{attributeName:"stroke",values:"#02bcfe;#3be6cb;#02bcfe",dur:"3s",repeatCount:"indefinite"}})],1),this._v(" "),t("circle",{attrs:{cx:"25",cy:"25",r:"10",fill:"transparent","stroke-width":"3","stroke-dasharray":"15.7, 15.7",stroke:"#3be6cb","stroke-linecap":"round"}},[t("animateTransform",{attrs:{attributeName:"transform",type:"rotate",values:"360, 25 25;0, 25 25",dur:"1.5s",repeatCount:"indefinite"}}),this._v(" "),t("animate",{attrs:{attributeName:"stroke",values:"#3be6cb;#02bcfe;#3be6cb",dur:"3s",repeatCount:"indefinite"}})],1)]),this._v(" "),t("div",{staticClass:"loading-tip"},[this._t("default")],2)])};v._withStripped=!0;const m=a({render:v,staticRenderFns:[]},(function(e){e&&e("data-v-c8b3d976_0",{source:".dv-loading {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n.dv-loading .loading-tip {\n  font-size: 15px;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,WAAW;EACX,YAAY;EACZ,aAAa;EACb,sBAAsB;EACtB,uBAAuB;EACvB,mBAAmB;AACrB;AACA;EACE,eAAe;AACjB",file:"main.vue",sourcesContent:[".dv-loading {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n.dv-loading .loading-tip {\n  font-size: 15px;\n}\n"]},media:void 0})}),g,void 0,!1,void 0,!1,l,void 0,void 0);function A(e){e.component(m.name,m)}function C(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function b(e,t){return e(t={exports:{}},t.exports),t.exports}var y=b((function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}}}));C(y);var x=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r};var w=function(e){if(Array.isArray(e))return x(e)};var k=function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)};var E=function(e,t){if(e){if("string"==typeof e)return x(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?x(e,t):void 0}};var B=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")};var P=function(e){return w(e)||k(e)||E(e)||B()},_=b((function(e){function t(n){return"function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?e.exports=t=function(e){return typeof e}:e.exports=t=function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(n)}e.exports=t}));var S=function(e){if(Array.isArray(e))return e};var O=function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var n=[],r=!0,i=!1,o=void 0;try{for(var a,s=e[Symbol.iterator]();!(r=(a=s.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){i=!0,o=e}finally{try{r||null==s.return||s.return()}finally{if(i)throw o}}return n}};var I=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")};var W=function(e,t){return S(e)||O(e,t)||E(e,t)||I()},L=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.deepClone=f,t.eliminateBlur=h,t.checkPointIsInCircle=p,t.getTwoPointDistance=g,t.checkPointIsInPolygon=v,t.checkPointIsInSector=m,t.checkPointIsNearPolyline=C,t.checkPointIsInRect=function(e,t,n,i,o){var a=(0,r.default)(e,2),s=a[0],l=a[1];return!(s<t)&&(!(l<n)&&(!(s>t+i)&&!(l>n+o)))},t.getRotatePointPos=b,t.getScalePointPos=x,t.getTranslatePointPos=w,t.getDistanceBetweenPointAndLine=k,t.getCircleRadianPoint=E,t.getRegularPolygonPoints=B,t.default=void 0;var n=y(P),r=y(W),i=y(_),o=Math.abs,a=Math.sqrt,s=Math.sin,l=Math.cos,d=Math.max,c=Math.min,u=Math.PI;function f(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return e;var n=JSON.parse,r=JSON.stringify;if(!t)return n(r(e));var o=e instanceof Array?[]:{};if(e&&"object"===(0,i.default)(e))for(var a in e)e.hasOwnProperty(a)&&(e[a]&&"object"===(0,i.default)(e[a])?o[a]=f(e[a],!0):o[a]=e[a]);return o}function h(e){return e.map((function(e){var t=(0,r.default)(e,2),n=t[0],i=t[1];return[parseInt(n)+.5,parseInt(i)+.5]}))}function p(e,t,n,r){return g(e,[t,n])<=r}function g(e,t){var n=(0,r.default)(e,2),i=n[0],s=n[1],l=(0,r.default)(t,2),d=l[0],c=l[1],u=o(i-d),f=o(s-c);return a(u*u+f*f)}function v(e,t){for(var n=0,i=(0,r.default)(e,2),o=i[0],a=i[1],s=t.length,l=1,u=t[0];l<=s;l++){var f=t[l%s];if(o>c(u[0],f[0])&&o<=d(u[0],f[0])&&a<=d(u[1],f[1])&&u[0]!==f[0]){var h=(o-u[0])*(f[1]-u[1])/(f[0]-u[0])+u[1];(u[1]===f[1]||a<=h)&&n++}u=f}return n%2==1}function m(e,t,n,i,o,a,s){if(!e)return!1;if(g(e,[t,n])>i)return!1;if(!s){var l=f([a,o]),d=(0,r.default)(l,2);o=d[0],a=d[1]}var c=o>a;if(c){var h=[a,o];o=h[0],a=h[1]}var p=a-o;if(p>=2*u)return!0;var v=(0,r.default)(e,2),m=v[0],C=v[1],b=E(t,n,i,o),y=(0,r.default)(b,2),x=y[0],w=y[1],k=E(t,n,i,a),B=(0,r.default)(k,2),P=[m-t,C-n],_=[x-t,w-n],S=[B[0]-t,B[1]-n],O=p>u;if(O){var I=f([S,_]),W=(0,r.default)(I,2);_=W[0],S=W[1]}var L=A(_,P)&&!A(S,P);return O&&(L=!L),c&&(L=!L),L}function A(e,t){var n=(0,r.default)(e,2),i=n[0],o=n[1],a=(0,r.default)(t,2);return-o*a[0]+i*a[1]>0}function C(e,t,i){var o=i/2,a=t.map((function(e){var t=(0,r.default)(e,2);return[t[0],t[1]-o]})),s=t.map((function(e){var t=(0,r.default)(e,2);return[t[0],t[1]+o]}));return v(e,[].concat((0,n.default)(a),(0,n.default)(s.reverse())))}function b(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[0,0];if(!t)return!1;if(e%360==0)return t;var i=(0,r.default)(t,2),o=i[0],a=i[1],d=(0,r.default)(n,2),c=d[0],f=d[1];return[(o-c)*l(e*=u/180)-(a-f)*s(e)+c,(o-c)*s(e)+(a-f)*l(e)+f]}function x(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[1,1],t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[0,0];if(!t)return!1;if(1===e)return t;var i=(0,r.default)(t,2),o=i[0],a=i[1],s=(0,r.default)(n,2),l=s[0],d=s[1],c=(0,r.default)(e,2),u=c[0],f=c[1],h=o-l,p=a-d;return[h*u+l,p*f+d]}function w(e,t){if(!e||!t)return!1;var n=(0,r.default)(t,2),i=n[0],o=n[1],a=(0,r.default)(e,2);return[i+a[0],o+a[1]]}function k(e,t,n){if(!e||!t||!n)return!1;var i=(0,r.default)(e,2),s=i[0],l=i[1],d=(0,r.default)(t,2),c=d[0],u=d[1],f=(0,r.default)(n,2),h=f[0],p=f[1],g=p-u,v=c-h;return o(g*s+v*l+(u*(h-c)-c*(p-u)))/a(g*g+v*v)}function E(e,t,n,r){return[e+l(r)*n,t+s(r)*n]}function B(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:-.5*u,o=2*u/r,a=new Array(r).fill("").map((function(e,t){return t*o+i}));return a.map((function(r){return E(e,t,n,r)}))}var S={deepClone:f,eliminateBlur:h,checkPointIsInCircle:p,checkPointIsInPolygon:v,checkPointIsInSector:m,checkPointIsNearPolyline:C,getTwoPointDistance:g,getRotatePointPos:b,getScalePointPos:x,getTranslatePointPos:w,getCircleRadianPoint:E,getRegularPolygonPoints:B,getDistanceBetweenPointAndLine:k};t.default=S}));C(L);var j=L.deepClone,M=(L.eliminateBlur,L.checkPointIsInCircle,L.getTwoPointDistance,L.checkPointIsInPolygon,L.checkPointIsInSector,L.checkPointIsNearPolyline,L.checkPointIsInRect,L.getRotatePointPos,L.getScalePointPos,L.getTranslatePointPos,L.getDistanceBetweenPointAndLine,L.getCircleRadianPoint,L.getRegularPolygonPoints,b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.filterNonNumber=i,t.deepMerge=o,t.mulAdd=a,t.mergeSameStackData=function(e,t){var r=e.stack;if(!r)return(0,n.default)(e.data);var i=t.filter((function(e){return e.stack===r})),o=i.findIndex((function(t){return t.data===e.data})),s=i.splice(0,o+1).map((function(e){return e.data})),l=s[0].length;return new Array(l).fill(0).map((function(e,t){return a(s.map((function(e){return e[t]})))}))},t.getTwoPointDistance=s,t.getLinearGradientColor=function(e,t,r,i){if(!(e&&t&&r&&i.length))return;var o=i;"string"==typeof o&&(o=[i,i]);var a=e.createLinearGradient.apply(e,(0,n.default)(t).concat((0,n.default)(r))),s=1/(o.length-1);return o.forEach((function(e,t){return a.addColorStop(s*t,e)})),a},t.getPolylineLength=function(e){return a(new Array(e.length-1).fill(0).map((function(t,n){return[e[n],e[n+1]]})).map((function(e){return s.apply(void 0,(0,n.default)(e))})))},t.getPointToLineDistance=function(e,t,n){var r=s(e,t),i=s(e,n),o=s(t,n);return.5*Math.sqrt((r+i+o)*(r+i-o)*(r+o-i)*(i+o-r))/o},t.initNeedSeries=function(e,t,n){return(e=(e=e.filter((function(e){return e.type===n}))).map((function(e){return o((0,L.deepClone)(t,!0),e)}))).filter((function(e){return e.show}))},t.radianToAngle=function(e){return e/Math.PI*180};var n=y(P),r=y(_);function i(e){return e.filter((function(e){return"number"==typeof e}))}function o(e,t){for(var n in t)e[n]&&"object"===(0,r.default)(e[n])?o(e[n],t[n]):"object"!==(0,r.default)(t[n])?e[n]=t[n]:e[n]=(0,L.deepClone)(t[n],!0);return e}function a(e){return(e=i(e)).reduce((function(e,t){return e+t}),0)}function s(e,t){var n=Math.abs(e[0]-t[0]),r=Math.abs(e[1]-t[1]);return Math.sqrt(n*n+r*r)}})));C(M);M.filterNonNumber;var F=M.deepMerge,R=(M.mulAdd,M.mergeSameStackData,M.getTwoPointDistance,M.getLinearGradientColor,M.getPolylineLength);M.getPointToLineDistance,M.initNeedSeries,M.radianToAngle;const G={name:"DvBorderBox1",mixins:[i],props:{color:{type:Array,default:()=>[]},backgroundColor:{type:String,default:"transparent"}},data:()=>({ref:"border-box-1",border:["left-top","right-top","left-bottom","right-bottom"],defaultColor:["#4fd2dd","#235fa7"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var D=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-border-box-1"},[n("svg",{staticClass:"border",attrs:{width:e.width,height:e.height}},[n("polygon",{attrs:{fill:e.backgroundColor,points:"10, 27 10, "+(e.height-27)+" 13, "+(e.height-24)+" 13, "+(e.height-21)+" 24, "+(e.height-11)+"\n    38, "+(e.height-11)+" 41, "+(e.height-8)+" 73, "+(e.height-8)+" 75, "+(e.height-10)+" 81, "+(e.height-10)+"\n    85, "+(e.height-6)+" "+(e.width-85)+", "+(e.height-6)+" "+(e.width-81)+", "+(e.height-10)+" "+(e.width-75)+", "+(e.height-10)+"\n    "+(e.width-73)+", "+(e.height-8)+" "+(e.width-41)+", "+(e.height-8)+" "+(e.width-38)+", "+(e.height-11)+"\n    "+(e.width-24)+", "+(e.height-11)+" "+(e.width-13)+", "+(e.height-21)+" "+(e.width-13)+", "+(e.height-24)+"\n    "+(e.width-10)+", "+(e.height-27)+" "+(e.width-10)+", 27 "+(e.width-13)+", 25 "+(e.width-13)+", 21\n    "+(e.width-24)+", 11 "+(e.width-38)+", 11 "+(e.width-41)+", 8 "+(e.width-73)+", 8 "+(e.width-75)+", 10\n    "+(e.width-81)+", 10 "+(e.width-85)+", 6 85, 6 81, 10 75, 10 73, 8 41, 8 38, 11 24, 11 13, 21 13, 24"}})]),e._v(" "),e._l(e.border,(function(t){return n("svg",{key:t,class:t+" border",attrs:{width:"150px",height:"150px"}},[n("polygon",{attrs:{fill:e.mergedColor[0],points:"6,66 6,18 12,12 18,12 24,6 27,6 30,9 36,9 39,6 84,6 81,9 75,9 73.2,7 40.8,7 37.8,10.2 24,10.2 12,21 12,24 9,27 9,51 7.8,54 7.8,63"}},[n("animate",{attrs:{attributeName:"fill",values:e.mergedColor[0]+";"+e.mergedColor[1]+";"+e.mergedColor[0],dur:"0.5s",begin:"0s",repeatCount:"indefinite"}})]),e._v(" "),n("polygon",{attrs:{fill:e.mergedColor[1],points:"27.599999999999998,4.8 38.4,4.8 35.4,7.8 30.599999999999998,7.8"}},[n("animate",{attrs:{attributeName:"fill",values:e.mergedColor[1]+";"+e.mergedColor[0]+";"+e.mergedColor[1],dur:"0.5s",begin:"0s",repeatCount:"indefinite"}})]),e._v(" "),n("polygon",{attrs:{fill:e.mergedColor[0],points:"9,54 9,63 7.199999999999999,66 7.199999999999999,75 7.8,78 7.8,110 8.4,110 8.4,66 9.6,66 9.6,54"}},[n("animate",{attrs:{attributeName:"fill",values:e.mergedColor[0]+";"+e.mergedColor[1]+";transparent",dur:"1s",begin:"0s",repeatCount:"indefinite"}})])])})),e._v(" "),n("div",{staticClass:"border-box-content"},[e._t("default")],2)],2)};D._withStripped=!0;const z=a({render:D,staticRenderFns:[]},(function(e){e&&e("data-v-5d85361d_0",{source:".dv-border-box-1 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-1 .border {\n  position: absolute;\n  display: block;\n}\n.dv-border-box-1 .right-top {\n  right: 0px;\n  transform: rotateY(180deg);\n}\n.dv-border-box-1 .left-bottom {\n  bottom: 0px;\n  transform: rotateX(180deg);\n}\n.dv-border-box-1 .right-bottom {\n  right: 0px;\n  bottom: 0px;\n  transform: rotateX(180deg) rotateY(180deg);\n}\n.dv-border-box-1 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd;AACA;EACE,kBAAkB;EAClB,cAAc;AAChB;AACA;EACE,UAAU;EACV,0BAA0B;AAC5B;AACA;EACE,WAAW;EACX,0BAA0B;AAC5B;AACA;EACE,UAAU;EACV,WAAW;EACX,0CAA0C;AAC5C;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-border-box-1 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-1 .border {\n  position: absolute;\n  display: block;\n}\n.dv-border-box-1 .right-top {\n  right: 0px;\n  transform: rotateY(180deg);\n}\n.dv-border-box-1 .left-bottom {\n  bottom: 0px;\n  transform: rotateX(180deg);\n}\n.dv-border-box-1 .right-bottom {\n  right: 0px;\n  bottom: 0px;\n  transform: rotateX(180deg) rotateY(180deg);\n}\n.dv-border-box-1 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),G,void 0,!1,void 0,!1,l,void 0,void 0);function T(e){e.component(z.name,z)}const Y={name:"DvBorderBox2",mixins:[i],props:{color:{type:Array,default:()=>[]},backgroundColor:{type:String,default:"transparent"}},data:()=>({ref:"border-box-2",defaultColor:["#fff","rgba(255, 255, 255, 0.6)"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var N=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-border-box-2"},[n("svg",{staticClass:"dv-border-svg-container",attrs:{width:e.width,height:e.height}},[n("polygon",{attrs:{fill:e.backgroundColor,points:"\n      7, 7 "+(e.width-7)+", 7 "+(e.width-7)+", "+(e.height-7)+" 7, "+(e.height-7)+"\n    "}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],points:"2, 2 "+(e.width-2)+" ,2 "+(e.width-2)+", "+(e.height-2)+" 2, "+(e.height-2)+" 2, 2"}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[1],points:"6, 6 "+(e.width-6)+", 6 "+(e.width-6)+", "+(e.height-6)+" 6, "+(e.height-6)+" 6, 6"}}),e._v(" "),n("circle",{attrs:{fill:e.mergedColor[0],cx:"11",cy:"11",r:"1"}}),e._v(" "),n("circle",{attrs:{fill:e.mergedColor[0],cx:e.width-11,cy:"11",r:"1"}}),e._v(" "),n("circle",{attrs:{fill:e.mergedColor[0],cx:e.width-11,cy:e.height-11,r:"1"}}),e._v(" "),n("circle",{attrs:{fill:e.mergedColor[0],cx:"11",cy:e.height-11,r:"1"}})]),e._v(" "),n("div",{staticClass:"border-box-content"},[e._t("default")],2)])};N._withStripped=!0;const X=a({render:N,staticRenderFns:[]},(function(e){e&&e("data-v-1e24c1c8_0",{source:".dv-border-box-2 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-2 .dv-border-svg-container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0px;\n  left: 0px;\n}\n.dv-border-box-2 .dv-border-svg-container polyline {\n  fill: none;\n  stroke-width: 1;\n}\n.dv-border-box-2 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,QAAQ;EACR,SAAS;AACX;AACA;EACE,UAAU;EACV,eAAe;AACjB;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-border-box-2 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-2 .dv-border-svg-container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0px;\n  left: 0px;\n}\n.dv-border-box-2 .dv-border-svg-container polyline {\n  fill: none;\n  stroke-width: 1;\n}\n.dv-border-box-2 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),Y,void 0,!1,void 0,!1,l,void 0,void 0);function $(e){e.component(X.name,X)}const Q={name:"DvBorderBox3",mixins:[i],props:{color:{type:Array,default:()=>[]},backgroundColor:{type:String,default:"transparent"}},data:()=>({ref:"border-box-3",defaultColor:["#2862b7","#2862b7"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var U=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-border-box-3"},[n("svg",{staticClass:"dv-border-svg-container",attrs:{width:e.width,height:e.height}},[n("polygon",{attrs:{fill:e.backgroundColor,points:"\n      23, 23 "+(e.width-24)+", 23 "+(e.width-24)+", "+(e.height-24)+" 23, "+(e.height-24)+"\n    "}}),e._v(" "),n("polyline",{staticClass:"dv-bb3-line1",attrs:{stroke:e.mergedColor[0],points:"4, 4 "+(e.width-22)+" ,4 "+(e.width-22)+", "+(e.height-22)+" 4, "+(e.height-22)+" 4, 4"}}),e._v(" "),n("polyline",{staticClass:"dv-bb3-line2",attrs:{stroke:e.mergedColor[1],points:"10, 10 "+(e.width-16)+", 10 "+(e.width-16)+", "+(e.height-16)+" 10, "+(e.height-16)+" 10, 10"}}),e._v(" "),n("polyline",{staticClass:"dv-bb3-line2",attrs:{stroke:e.mergedColor[1],points:"16, 16 "+(e.width-10)+", 16 "+(e.width-10)+", "+(e.height-10)+" 16, "+(e.height-10)+" 16, 16"}}),e._v(" "),n("polyline",{staticClass:"dv-bb3-line2",attrs:{stroke:e.mergedColor[1],points:"22, 22 "+(e.width-4)+", 22 "+(e.width-4)+", "+(e.height-4)+" 22, "+(e.height-4)+" 22, 22"}})]),e._v(" "),n("div",{staticClass:"border-box-content"},[e._t("default")],2)])};U._withStripped=!0;const V=a({render:U,staticRenderFns:[]},(function(e){e&&e("data-v-82d94504_0",{source:".dv-border-box-3 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-3 .dv-border-svg-container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0px;\n  left: 0px;\n}\n.dv-border-box-3 .dv-border-svg-container polyline {\n  fill: none;\n}\n.dv-border-box-3 .dv-bb3-line1 {\n  stroke-width: 3;\n}\n.dv-border-box-3 .dv-bb3-line2 {\n  stroke-width: 1;\n}\n.dv-border-box-3 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,QAAQ;EACR,SAAS;AACX;AACA;EACE,UAAU;AACZ;AACA;EACE,eAAe;AACjB;AACA;EACE,eAAe;AACjB;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-border-box-3 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-3 .dv-border-svg-container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0px;\n  left: 0px;\n}\n.dv-border-box-3 .dv-border-svg-container polyline {\n  fill: none;\n}\n.dv-border-box-3 .dv-bb3-line1 {\n  stroke-width: 3;\n}\n.dv-border-box-3 .dv-bb3-line2 {\n  stroke-width: 1;\n}\n.dv-border-box-3 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),Q,void 0,!1,void 0,!1,l,void 0,void 0);function H(e){e.component(V.name,V)}const q={name:"DvBorderBox4",mixins:[i],props:{color:{type:Array,default:()=>[]},reverse:{type:Boolean,default:!1},backgroundColor:{type:String,default:"transparent"}},data:()=>({ref:"border-box-4",defaultColor:["red","rgba(0,0,255,0.8)"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var Z=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-border-box-4"},[n("svg",{class:"dv-border-svg-container "+(e.reverse&&"dv-reverse"),attrs:{width:e.width,height:e.height}},[n("polygon",{attrs:{fill:e.backgroundColor,points:"\n      "+(e.width-15)+", 22 170, 22 150, 7 40, 7 28, 21 32, 24\n      16, 42 16, "+(e.height-32)+" 41, "+(e.height-7)+" "+(e.width-15)+", "+(e.height-7)+"\n    "}}),e._v(" "),n("polyline",{staticClass:"dv-bb4-line-1",attrs:{stroke:e.mergedColor[0],points:"145, "+(e.height-5)+" 40, "+(e.height-5)+" 10, "+(e.height-35)+"\n        10, 40 40, 5 150, 5 170, 20 "+(e.width-15)+", 20"}}),e._v(" "),n("polyline",{staticClass:"dv-bb4-line-2",attrs:{stroke:e.mergedColor[1],points:"245, "+(e.height-1)+" 36, "+(e.height-1)+" 14, "+(e.height-23)+"\n        14, "+(e.height-100)}}),e._v(" "),n("polyline",{staticClass:"dv-bb4-line-3",attrs:{stroke:e.mergedColor[0],points:"7, "+(e.height-40)+" 7, "+(e.height-75)}}),e._v(" "),n("polyline",{staticClass:"dv-bb4-line-4",attrs:{stroke:e.mergedColor[0],points:"28, 24 13, 41 13, 64"}}),e._v(" "),n("polyline",{staticClass:"dv-bb4-line-5",attrs:{stroke:e.mergedColor[0],points:"5, 45 5, 140"}}),e._v(" "),n("polyline",{staticClass:"dv-bb4-line-6",attrs:{stroke:e.mergedColor[1],points:"14, 75 14, 180"}}),e._v(" "),n("polyline",{staticClass:"dv-bb4-line-7",attrs:{stroke:e.mergedColor[1],points:"55, 11 147, 11 167, 26 250, 26"}}),e._v(" "),n("polyline",{staticClass:"dv-bb4-line-8",attrs:{stroke:e.mergedColor[1],points:"158, 5 173, 16"}}),e._v(" "),n("polyline",{staticClass:"dv-bb4-line-9",attrs:{stroke:e.mergedColor[0],points:"200, 17 "+(e.width-10)+", 17"}}),e._v(" "),n("polyline",{staticClass:"dv-bb4-line-10",attrs:{stroke:e.mergedColor[1],points:"385, 17 "+(e.width-10)+", 17"}})]),e._v(" "),n("div",{staticClass:"border-box-content"},[e._t("default")],2)])};Z._withStripped=!0;const K=a({render:Z,staticRenderFns:[]},(function(e){e&&e("data-v-10a833ad_0",{source:".dv-border-box-4 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-4 .dv-reverse {\n  transform: rotate(180deg);\n}\n.dv-border-box-4 .dv-border-svg-container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0px;\n  left: 0px;\n}\n.dv-border-box-4 .dv-border-svg-container polyline {\n  fill: none;\n}\n.dv-border-box-4 .sw1 {\n  stroke-width: 1;\n}\n.dv-border-box-4 .sw3 {\n  stroke-width: 3px;\n  stroke-linecap: round;\n}\n.dv-border-box-4 .dv-bb4-line-1 {\n  stroke-width: 1;\n}\n.dv-border-box-4 .dv-bb4-line-2 {\n  stroke-width: 1;\n}\n.dv-border-box-4 .dv-bb4-line-3 {\n  stroke-width: 3px;\n  stroke-linecap: round;\n}\n.dv-border-box-4 .dv-bb4-line-4 {\n  stroke-width: 3px;\n  stroke-linecap: round;\n}\n.dv-border-box-4 .dv-bb4-line-5 {\n  stroke-width: 1;\n}\n.dv-border-box-4 .dv-bb4-line-6 {\n  stroke-width: 1;\n}\n.dv-border-box-4 .dv-bb4-line-7 {\n  stroke-width: 1;\n}\n.dv-border-box-4 .dv-bb4-line-8 {\n  stroke-width: 3px;\n  stroke-linecap: round;\n}\n.dv-border-box-4 .dv-bb4-line-9 {\n  stroke-width: 3px;\n  stroke-linecap: round;\n  stroke-dasharray: 100 250;\n}\n.dv-border-box-4 .dv-bb4-line-10 {\n  stroke-width: 1;\n  stroke-dasharray: 80 270;\n}\n.dv-border-box-4 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd;AACA;EACE,yBAAyB;AAC3B;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,QAAQ;EACR,SAAS;AACX;AACA;EACE,UAAU;AACZ;AACA;EACE,eAAe;AACjB;AACA;EACE,iBAAiB;EACjB,qBAAqB;AACvB;AACA;EACE,eAAe;AACjB;AACA;EACE,eAAe;AACjB;AACA;EACE,iBAAiB;EACjB,qBAAqB;AACvB;AACA;EACE,iBAAiB;EACjB,qBAAqB;AACvB;AACA;EACE,eAAe;AACjB;AACA;EACE,eAAe;AACjB;AACA;EACE,eAAe;AACjB;AACA;EACE,iBAAiB;EACjB,qBAAqB;AACvB;AACA;EACE,iBAAiB;EACjB,qBAAqB;EACrB,yBAAyB;AAC3B;AACA;EACE,eAAe;EACf,wBAAwB;AAC1B;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-border-box-4 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-4 .dv-reverse {\n  transform: rotate(180deg);\n}\n.dv-border-box-4 .dv-border-svg-container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0px;\n  left: 0px;\n}\n.dv-border-box-4 .dv-border-svg-container polyline {\n  fill: none;\n}\n.dv-border-box-4 .sw1 {\n  stroke-width: 1;\n}\n.dv-border-box-4 .sw3 {\n  stroke-width: 3px;\n  stroke-linecap: round;\n}\n.dv-border-box-4 .dv-bb4-line-1 {\n  stroke-width: 1;\n}\n.dv-border-box-4 .dv-bb4-line-2 {\n  stroke-width: 1;\n}\n.dv-border-box-4 .dv-bb4-line-3 {\n  stroke-width: 3px;\n  stroke-linecap: round;\n}\n.dv-border-box-4 .dv-bb4-line-4 {\n  stroke-width: 3px;\n  stroke-linecap: round;\n}\n.dv-border-box-4 .dv-bb4-line-5 {\n  stroke-width: 1;\n}\n.dv-border-box-4 .dv-bb4-line-6 {\n  stroke-width: 1;\n}\n.dv-border-box-4 .dv-bb4-line-7 {\n  stroke-width: 1;\n}\n.dv-border-box-4 .dv-bb4-line-8 {\n  stroke-width: 3px;\n  stroke-linecap: round;\n}\n.dv-border-box-4 .dv-bb4-line-9 {\n  stroke-width: 3px;\n  stroke-linecap: round;\n  stroke-dasharray: 100 250;\n}\n.dv-border-box-4 .dv-bb4-line-10 {\n  stroke-width: 1;\n  stroke-dasharray: 80 270;\n}\n.dv-border-box-4 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),q,void 0,!1,void 0,!1,l,void 0,void 0);function J(e){e.component(K.name,K)}const ee={name:"DvBorderBox5",mixins:[i],props:{color:{type:Array,default:()=>[]},reverse:{type:Boolean,default:!1},backgroundColor:{type:String,default:"transparent"}},data:()=>({ref:"border-box-5",defaultColor:["rgba(255, 255, 255, 0.35)","rgba(255, 255, 255, 0.20)"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var te=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-border-box-5"},[n("svg",{class:"dv-svg-container  "+(e.reverse&&"dv-reverse"),attrs:{width:e.width,height:e.height}},[n("polygon",{attrs:{fill:e.backgroundColor,points:"\n      10, 22 "+(e.width-22)+", 22 "+(e.width-22)+", "+(e.height-86)+" "+(e.width-84)+", "+(e.height-24)+" 10, "+(e.height-24)+"\n    "}}),e._v(" "),n("polyline",{staticClass:"dv-bb5-line-1",attrs:{stroke:e.mergedColor[0],points:"8, 5 "+(e.width-5)+", 5 "+(e.width-5)+", "+(e.height-100)+"\n        "+(e.width-100)+", "+(e.height-5)+" 8, "+(e.height-5)+" 8, 5"}}),e._v(" "),n("polyline",{staticClass:"dv-bb5-line-2",attrs:{stroke:e.mergedColor[1],points:"3, 5 "+(e.width-20)+", 5 "+(e.width-20)+", "+(e.height-60)+"\n        "+(e.width-74)+", "+(e.height-5)+" 3, "+(e.height-5)+" 3, 5"}}),e._v(" "),n("polyline",{staticClass:"dv-bb5-line-3",attrs:{stroke:e.mergedColor[1],points:"50, 13 "+(e.width-35)+", 13"}}),e._v(" "),n("polyline",{staticClass:"dv-bb5-line-4",attrs:{stroke:e.mergedColor[1],points:"15, 20 "+(e.width-35)+", 20"}}),e._v(" "),n("polyline",{staticClass:"dv-bb5-line-5",attrs:{stroke:e.mergedColor[1],points:"15, "+(e.height-20)+" "+(e.width-110)+", "+(e.height-20)}}),e._v(" "),n("polyline",{staticClass:"dv-bb5-line-6",attrs:{stroke:e.mergedColor[1],points:"15, "+(e.height-13)+" "+(e.width-110)+", "+(e.height-13)}})]),e._v(" "),n("div",{staticClass:"border-box-content"},[e._t("default")],2)])};te._withStripped=!0;const ne=a({render:te,staticRenderFns:[]},(function(e){e&&e("data-v-289cabb8_0",{source:".dv-border-box-5 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-5 .dv-reverse {\n  transform: rotate(180deg);\n}\n.dv-border-box-5 .dv-svg-container {\n  position: absolute;\n  top: 0px;\n  left: 0px;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-5 .dv-svg-container polyline {\n  fill: none;\n}\n.dv-border-box-5 .dv-bb5-line-1,\n.dv-border-box-5 .dv-bb5-line-2 {\n  stroke-width: 1;\n}\n.dv-border-box-5 .dv-bb5-line-3,\n.dv-border-box-5 .dv-bb5-line-6 {\n  stroke-width: 5;\n}\n.dv-border-box-5 .dv-bb5-line-4,\n.dv-border-box-5 .dv-bb5-line-5 {\n  stroke-width: 2;\n}\n.dv-border-box-5 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd;AACA;EACE,yBAAyB;AAC3B;AACA;EACE,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,WAAW;EACX,YAAY;AACd;AACA;EACE,UAAU;AACZ;AACA;;EAEE,eAAe;AACjB;AACA;;EAEE,eAAe;AACjB;AACA;;EAEE,eAAe;AACjB;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-border-box-5 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-5 .dv-reverse {\n  transform: rotate(180deg);\n}\n.dv-border-box-5 .dv-svg-container {\n  position: absolute;\n  top: 0px;\n  left: 0px;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-5 .dv-svg-container polyline {\n  fill: none;\n}\n.dv-border-box-5 .dv-bb5-line-1,\n.dv-border-box-5 .dv-bb5-line-2 {\n  stroke-width: 1;\n}\n.dv-border-box-5 .dv-bb5-line-3,\n.dv-border-box-5 .dv-bb5-line-6 {\n  stroke-width: 5;\n}\n.dv-border-box-5 .dv-bb5-line-4,\n.dv-border-box-5 .dv-bb5-line-5 {\n  stroke-width: 2;\n}\n.dv-border-box-5 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),ee,void 0,!1,void 0,!1,l,void 0,void 0);function re(e){e.component(ne.name,ne)}const ie={name:"DvBorderBox6",mixins:[i],props:{color:{type:Array,default:()=>[]},backgroundColor:{type:String,default:"transparent"}},data:()=>({ref:"border-box-6",defaultColor:["rgba(255, 255, 255, 0.35)","gray"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var oe=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-border-box-6"},[n("svg",{staticClass:"dv-svg-container",attrs:{width:e.width,height:e.height}},[n("polygon",{attrs:{fill:e.backgroundColor,points:"\n      9, 7 "+(e.width-9)+", 7 "+(e.width-9)+", "+(e.height-7)+" 9, "+(e.height-7)+"\n    "}}),e._v(" "),n("circle",{attrs:{fill:e.mergedColor[1],cx:"5",cy:"5",r:"2"}}),e._v(" "),n("circle",{attrs:{fill:e.mergedColor[1],cx:e.width-5,cy:"5",r:"2"}}),e._v(" "),n("circle",{attrs:{fill:e.mergedColor[1],cx:e.width-5,cy:e.height-5,r:"2"}}),e._v(" "),n("circle",{attrs:{fill:e.mergedColor[1],cx:"5",cy:e.height-5,r:"2"}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],points:"10, 4 "+(e.width-10)+", 4"}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],points:"10, "+(e.height-4)+" "+(e.width-10)+", "+(e.height-4)}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],points:"5, 70 5, "+(e.height-70)}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],points:e.width-5+", 70 "+(e.width-5)+", "+(e.height-70)}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],points:"3, 10, 3, 50"}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],points:"7, 30 7, 80"}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],points:e.width-3+", 10 "+(e.width-3)+", 50"}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],points:e.width-7+", 30 "+(e.width-7)+", 80"}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],points:"3, "+(e.height-10)+" 3, "+(e.height-50)}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],points:"7, "+(e.height-30)+" 7, "+(e.height-80)}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],points:e.width-3+", "+(e.height-10)+" "+(e.width-3)+", "+(e.height-50)}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],points:e.width-7+", "+(e.height-30)+" "+(e.width-7)+", "+(e.height-80)}})]),e._v(" "),n("div",{staticClass:"border-box-content"},[e._t("default")],2)])};oe._withStripped=!0;const ae=a({render:oe,staticRenderFns:[]},(function(e){e&&e("data-v-03ae851c_0",{source:".dv-border-box-6 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-6 .dv-svg-container {\n  position: absolute;\n  top: 0px;\n  left: 0px;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-6 .dv-svg-container polyline {\n  fill: none;\n  stroke-width: 1;\n}\n.dv-border-box-6 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd;AACA;EACE,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,WAAW;EACX,YAAY;AACd;AACA;EACE,UAAU;EACV,eAAe;AACjB;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-border-box-6 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-6 .dv-svg-container {\n  position: absolute;\n  top: 0px;\n  left: 0px;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-6 .dv-svg-container polyline {\n  fill: none;\n  stroke-width: 1;\n}\n.dv-border-box-6 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),ie,void 0,!1,void 0,!1,l,void 0,void 0);function se(e){e.component(ae.name,ae)}const le={name:"DvBorderBox7",mixins:[i],props:{color:{type:Array,default:()=>[]},backgroundColor:{type:String,default:"transparent"}},data:()=>({ref:"border-box-7",defaultColor:["rgba(128,128,128,0.3)","rgba(128,128,128,0.5)"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var de=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-border-box-7",style:"box-shadow: inset 0 0 40px "+e.mergedColor[0]+"; border: 1px solid "+e.mergedColor[0]+"; background-color: "+e.backgroundColor},[n("svg",{staticClass:"dv-svg-container",attrs:{width:e.width,height:e.height}},[n("polyline",{staticClass:"dv-bb7-line-width-2",attrs:{stroke:e.mergedColor[0],points:"0, 25 0, 0 25, 0"}}),e._v(" "),n("polyline",{staticClass:"dv-bb7-line-width-2",attrs:{stroke:e.mergedColor[0],points:e.width-25+", 0 "+e.width+", 0 "+e.width+", 25"}}),e._v(" "),n("polyline",{staticClass:"dv-bb7-line-width-2",attrs:{stroke:e.mergedColor[0],points:e.width-25+", "+e.height+" "+e.width+", "+e.height+" "+e.width+", "+(e.height-25)}}),e._v(" "),n("polyline",{staticClass:"dv-bb7-line-width-2",attrs:{stroke:e.mergedColor[0],points:"0, "+(e.height-25)+" 0, "+e.height+" 25, "+e.height}}),e._v(" "),n("polyline",{staticClass:"dv-bb7-line-width-5",attrs:{stroke:e.mergedColor[1],points:"0, 10 0, 0 10, 0"}}),e._v(" "),n("polyline",{staticClass:"dv-bb7-line-width-5",attrs:{stroke:e.mergedColor[1],points:e.width-10+", 0 "+e.width+", 0 "+e.width+", 10"}}),e._v(" "),n("polyline",{staticClass:"dv-bb7-line-width-5",attrs:{stroke:e.mergedColor[1],points:e.width-10+", "+e.height+" "+e.width+", "+e.height+" "+e.width+", "+(e.height-10)}}),e._v(" "),n("polyline",{staticClass:"dv-bb7-line-width-5",attrs:{stroke:e.mergedColor[1],points:"0, "+(e.height-10)+" 0, "+e.height+" 10, "+e.height}})]),e._v(" "),n("div",{staticClass:"border-box-content"},[e._t("default")],2)])};de._withStripped=!0;const ce=a({render:de,staticRenderFns:[]},(function(e){e&&e("data-v-4b8597f9_0",{source:".dv-border-box-7 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-7 .dv-svg-container {\n  position: absolute;\n  top: 0px;\n  left: 0px;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-7 .dv-svg-container polyline {\n  fill: none;\n  stroke-linecap: round;\n}\n.dv-border-box-7 .dv-bb7-line-width-2 {\n  stroke-width: 2;\n}\n.dv-border-box-7 .dv-bb7-line-width-5 {\n  stroke-width: 5;\n}\n.dv-border-box-7 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd;AACA;EACE,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,WAAW;EACX,YAAY;AACd;AACA;EACE,UAAU;EACV,qBAAqB;AACvB;AACA;EACE,eAAe;AACjB;AACA;EACE,eAAe;AACjB;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-border-box-7 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-7 .dv-svg-container {\n  position: absolute;\n  top: 0px;\n  left: 0px;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-7 .dv-svg-container polyline {\n  fill: none;\n  stroke-linecap: round;\n}\n.dv-border-box-7 .dv-bb7-line-width-2 {\n  stroke-width: 2;\n}\n.dv-border-box-7 .dv-bb7-line-width-5 {\n  stroke-width: 5;\n}\n.dv-border-box-7 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),le,void 0,!1,void 0,!1,l,void 0,void 0);function ue(e){e.component(ce.name,ce)}const fe={name:"DvBorderBox8",mixins:[i],props:{color:{type:Array,default:()=>[]},dur:{type:Number,default:3},backgroundColor:{type:String,default:"transparent"},reverse:{type:Boolean,default:!1}},data(){const e=r();return{ref:"border-box-8",path:"border-box-8-path-"+e,gradient:"border-box-8-gradient-"+e,mask:"border-box-8-mask-"+e,defaultColor:["#235fa7","#4fd2dd"],mergedColor:[]}},computed:{length(){const{width:e,height:t}=this;return 2*(e+t-5)},pathD(){const{reverse:e,width:t,height:n}=this;return e?`M 2.5, 2.5 L 2.5, ${n-2.5} L ${t-2.5}, ${n-2.5} L ${t-2.5}, 2.5 L 2.5, 2.5`:`M2.5, 2.5 L${t-2.5}, 2.5 L${t-2.5}, ${n-2.5} L2.5, ${n-2.5} L2.5, 2.5`}},watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var he=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-border-box-8"},[n("svg",{staticClass:"dv-svg-container",attrs:{width:e.width,height:e.height}},[n("defs",[n("path",{attrs:{id:e.path,d:e.pathD,fill:"transparent"}}),e._v(" "),n("radialGradient",{attrs:{id:e.gradient,cx:"50%",cy:"50%",r:"50%"}},[n("stop",{attrs:{offset:"0%","stop-color":"#fff","stop-opacity":"1"}}),e._v(" "),n("stop",{attrs:{offset:"100%","stop-color":"#fff","stop-opacity":"0"}})],1),e._v(" "),n("mask",{attrs:{id:e.mask}},[n("circle",{attrs:{cx:"0",cy:"0",r:"150",fill:"url(#"+e.gradient+")"}},[n("animateMotion",{attrs:{dur:e.dur+"s",path:e.pathD,rotate:"auto",repeatCount:"indefinite"}})],1)])],1),e._v(" "),n("polygon",{attrs:{fill:e.backgroundColor,points:"5, 5 "+(e.width-5)+", 5 "+(e.width-5)+" "+(e.height-5)+" 5, "+(e.height-5)}}),e._v(" "),n("use",{attrs:{stroke:e.mergedColor[0],"stroke-width":"1","xlink:href":"#"+e.path}}),e._v(" "),n("use",{attrs:{stroke:e.mergedColor[1],"stroke-width":"3","xlink:href":"#"+e.path,mask:"url(#"+e.mask+")"}},[n("animate",{attrs:{attributeName:"stroke-dasharray",from:"0, "+e.length,to:e.length+", 0",dur:e.dur+"s",repeatCount:"indefinite"}})])]),e._v(" "),n("div",{staticClass:"border-box-content"},[e._t("default")],2)])};he._withStripped=!0;const pe=a({render:he,staticRenderFns:[]},(function(e){e&&e("data-v-5e705490_0",{source:".dv-border-box-8 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-8 svg {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  left: 0px;\n  top: 0px;\n}\n.dv-border-box-8 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,SAAS;EACT,QAAQ;AACV;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-border-box-8 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-8 svg {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  left: 0px;\n  top: 0px;\n}\n.dv-border-box-8 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),fe,void 0,!1,void 0,!1,l,void 0,void 0);function ge(e){e.component(pe.name,pe)}const ve={name:"DvBorderBox9",mixins:[i],props:{color:{type:Array,default:()=>[]},backgroundColor:{type:String,default:"transparent"}},data(){const e=r();return{ref:"border-box-9",gradientId:"border-box-9-gradient-"+e,maskId:"border-box-9-mask-"+e,defaultColor:["#11eefd","#0078d2"],mergedColor:[]}},watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var me=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-border-box-9"},[n("svg",{staticClass:"dv-svg-container",attrs:{width:e.width,height:e.height}},[n("defs",[n("linearGradient",{attrs:{id:e.gradientId,x1:"0%",y1:"0%",x2:"100%",y2:"100%"}},[n("animate",{attrs:{attributeName:"x1",values:"0%;100%;0%",dur:"10s",begin:"0s",repeatCount:"indefinite"}}),e._v(" "),n("animate",{attrs:{attributeName:"x2",values:"100%;0%;100%",dur:"10s",begin:"0s",repeatCount:"indefinite"}}),e._v(" "),n("stop",{attrs:{offset:"0%","stop-color":e.mergedColor[0]}},[n("animate",{attrs:{attributeName:"stop-color",values:e.mergedColor[0]+";"+e.mergedColor[1]+";"+e.mergedColor[0],dur:"10s",begin:"0s",repeatCount:"indefinite"}})]),e._v(" "),n("stop",{attrs:{offset:"100%","stop-color":e.mergedColor[1]}},[n("animate",{attrs:{attributeName:"stop-color",values:e.mergedColor[1]+";"+e.mergedColor[0]+";"+e.mergedColor[1],dur:"10s",begin:"0s",repeatCount:"indefinite"}})])],1),e._v(" "),n("mask",{attrs:{id:e.maskId}},[n("polyline",{attrs:{stroke:"#fff","stroke-width":"3",fill:"transparent",points:"8, "+.4*e.height+" 8, 3, "+(.4*e.width+7)+", 3"}}),e._v(" "),n("polyline",{attrs:{fill:"#fff",points:"8, "+.15*e.height+" 8, 3, "+(.1*e.width+7)+", 3\n            "+.1*e.width+", 8 14, 8 14, "+(.15*e.height-7)+"\n          "}}),e._v(" "),n("polyline",{attrs:{stroke:"#fff","stroke-width":"3",fill:"transparent",points:.5*e.width+", 3 "+(e.width-3)+", 3, "+(e.width-3)+", "+.25*e.height}}),e._v(" "),n("polyline",{attrs:{fill:"#fff",points:"\n            "+.52*e.width+", 3 "+.58*e.width+", 3\n            "+(.58*e.width-7)+", 9 "+(.52*e.width+7)+", 9\n          "}}),e._v(" "),n("polyline",{attrs:{fill:"#fff",points:"\n            "+.9*e.width+", 3 "+(e.width-3)+", 3 "+(e.width-3)+", "+.1*e.height+"\n            "+(e.width-9)+", "+(.1*e.height-7)+" "+(e.width-9)+", 9 "+(.9*e.width+7)+", 9\n          "}}),e._v(" "),n("polyline",{attrs:{stroke:"#fff","stroke-width":"3",fill:"transparent",points:"8, "+.5*e.height+" 8, "+(e.height-3)+" "+(.3*e.width+7)+", "+(e.height-3)}}),e._v(" "),n("polyline",{attrs:{fill:"#fff",points:"\n            8, "+.55*e.height+" 8, "+.7*e.height+"\n            2, "+(.7*e.height-7)+" 2, "+(.55*e.height+7)+"\n          "}}),e._v(" "),n("polyline",{attrs:{stroke:"#fff","stroke-width":"3",fill:"transparent",points:.35*e.width+", "+(e.height-3)+" "+(e.width-3)+", "+(e.height-3)+" "+(e.width-3)+", "+.35*e.height}}),e._v(" "),n("polyline",{attrs:{fill:"#fff",points:"\n            "+.92*e.width+", "+(e.height-3)+" "+(e.width-3)+", "+(e.height-3)+" "+(e.width-3)+", "+.8*e.height+"\n            "+(e.width-9)+", "+(.8*e.height+7)+" "+(e.width-9)+", "+(e.height-9)+" "+(.92*e.width+7)+", "+(e.height-9)+"\n          "}})])],1),e._v(" "),n("polygon",{attrs:{fill:e.backgroundColor,points:"\n      15, 9 "+(.1*e.width+1)+", 9 "+(.1*e.width+4)+", 6 "+(.52*e.width+2)+", 6\n      "+(.52*e.width+6)+", 10 "+(.58*e.width-7)+", 10 "+(.58*e.width-2)+", 6\n      "+(.9*e.width+2)+", 6 "+(.9*e.width+6)+", 10 "+(e.width-10)+", 10 "+(e.width-10)+", "+(.1*e.height-6)+"\n      "+(e.width-6)+", "+(.1*e.height-1)+" "+(e.width-6)+", "+(.8*e.height+1)+" "+(e.width-10)+", "+(.8*e.height+6)+"\n      "+(e.width-10)+", "+(e.height-10)+" "+(.92*e.width+7)+", "+(e.height-10)+"  "+(.92*e.width+2)+", "+(e.height-6)+"\n      11, "+(e.height-6)+" 11, "+(.15*e.height-2)+" 15, "+(.15*e.height-7)+"\n    "}}),e._v(" "),n("rect",{attrs:{x:"0",y:"0",width:e.width,height:e.height,fill:"url(#"+e.gradientId+")",mask:"url(#"+e.maskId+")"}})]),e._v(" "),n("div",{staticClass:"border-box-content"},[e._t("default")],2)])};me._withStripped=!0;const Ae=a({render:me,staticRenderFns:[]},(function(e){e&&e("data-v-50673f47_0",{source:".dv-border-box-9 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-9 svg {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  left: 0px;\n  top: 0px;\n}\n.dv-border-box-9 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,SAAS;EACT,QAAQ;AACV;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-border-box-9 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-9 svg {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  left: 0px;\n  top: 0px;\n}\n.dv-border-box-9 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),ve,void 0,!1,void 0,!1,l,void 0,void 0);function Ce(e){e.component(Ae.name,Ae)}const be={name:"DvBorderBox10",mixins:[i],props:{color:{type:Array,default:()=>[]},backgroundColor:{type:String,default:"transparent"}},data:()=>({ref:"border-box-10",border:["left-top","right-top","left-bottom","right-bottom"],defaultColor:["#1d48c4","#d3e1f8"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var ye=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-border-box-10",style:"box-shadow: inset 0 0 25px 3px "+e.mergedColor[0]},[n("svg",{staticClass:"border",attrs:{width:e.width,height:e.height}},[n("polygon",{attrs:{fill:e.backgroundColor,points:"\n      4, 0 "+(e.width-4)+", 0 "+e.width+", 4 "+e.width+", "+(e.height-4)+" "+(e.width-4)+", "+e.height+"\n      4, "+e.height+" 0, "+(e.height-4)+" 0, 4\n    "}})]),e._v(" "),e._l(e.border,(function(t){return n("svg",{key:t,class:t+" border",attrs:{width:"150px",height:"150px"}},[n("polygon",{attrs:{fill:e.mergedColor[1],points:"40, 0 5, 0 0, 5 0, 16 3, 19 3, 7 7, 3 35, 3"}})])})),e._v(" "),n("div",{staticClass:"border-box-content"},[e._t("default")],2)],2)};ye._withStripped=!0;const xe=a({render:ye,staticRenderFns:[]},(function(e){e&&e("data-v-3b7b23ff_0",{source:".dv-border-box-10 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  border-radius: 6px;\n}\n.dv-border-box-10 .border {\n  position: absolute;\n  display: block;\n}\n.dv-border-box-10 .right-top {\n  right: 0px;\n  transform: rotateY(180deg);\n}\n.dv-border-box-10 .left-bottom {\n  bottom: 0px;\n  transform: rotateX(180deg);\n}\n.dv-border-box-10 .right-bottom {\n  right: 0px;\n  bottom: 0px;\n  transform: rotateX(180deg) rotateY(180deg);\n}\n.dv-border-box-10 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,kBAAkB;AACpB;AACA;EACE,kBAAkB;EAClB,cAAc;AAChB;AACA;EACE,UAAU;EACV,0BAA0B;AAC5B;AACA;EACE,WAAW;EACX,0BAA0B;AAC5B;AACA;EACE,UAAU;EACV,WAAW;EACX,0CAA0C;AAC5C;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-border-box-10 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  border-radius: 6px;\n}\n.dv-border-box-10 .border {\n  position: absolute;\n  display: block;\n}\n.dv-border-box-10 .right-top {\n  right: 0px;\n  transform: rotateY(180deg);\n}\n.dv-border-box-10 .left-bottom {\n  bottom: 0px;\n  transform: rotateX(180deg);\n}\n.dv-border-box-10 .right-bottom {\n  right: 0px;\n  bottom: 0px;\n  transform: rotateX(180deg) rotateY(180deg);\n}\n.dv-border-box-10 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),be,void 0,!1,void 0,!1,l,void 0,void 0);function we(e){e.component(xe.name,xe)}var ke=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=new Map([["transparent","rgba(0,0,0,0)"],["black","#000000"],["silver","#C0C0C0"],["gray","#808080"],["white","#FFFFFF"],["maroon","#800000"],["red","#FF0000"],["purple","#800080"],["fuchsia","#FF00FF"],["green","#008000"],["lime","#00FF00"],["olive","#808000"],["yellow","#FFFF00"],["navy","#000080"],["blue","#0000FF"],["teal","#008080"],["aqua","#00FFFF"],["aliceblue","#f0f8ff"],["antiquewhite","#faebd7"],["aquamarine","#7fffd4"],["azure","#f0ffff"],["beige","#f5f5dc"],["bisque","#ffe4c4"],["blanchedalmond","#ffebcd"],["blueviolet","#8a2be2"],["brown","#a52a2a"],["burlywood","#deb887"],["cadetblue","#5f9ea0"],["chartreuse","#7fff00"],["chocolate","#d2691e"],["coral","#ff7f50"],["cornflowerblue","#6495ed"],["cornsilk","#fff8dc"],["crimson","#dc143c"],["cyan","#00ffff"],["darkblue","#00008b"],["darkcyan","#008b8b"],["darkgoldenrod","#b8860b"],["darkgray","#a9a9a9"],["darkgreen","#006400"],["darkgrey","#a9a9a9"],["darkkhaki","#bdb76b"],["darkmagenta","#8b008b"],["darkolivegreen","#556b2f"],["darkorange","#ff8c00"],["darkorchid","#9932cc"],["darkred","#8b0000"],["darksalmon","#e9967a"],["darkseagreen","#8fbc8f"],["darkslateblue","#483d8b"],["darkslategray","#2f4f4f"],["darkslategrey","#2f4f4f"],["darkturquoise","#00ced1"],["darkviolet","#9400d3"],["deeppink","#ff1493"],["deepskyblue","#00bfff"],["dimgray","#696969"],["dimgrey","#696969"],["dodgerblue","#1e90ff"],["firebrick","#b22222"],["floralwhite","#fffaf0"],["forestgreen","#228b22"],["gainsboro","#dcdcdc"],["ghostwhite","#f8f8ff"],["gold","#ffd700"],["goldenrod","#daa520"],["greenyellow","#adff2f"],["grey","#808080"],["honeydew","#f0fff0"],["hotpink","#ff69b4"],["indianred","#cd5c5c"],["indigo","#4b0082"],["ivory","#fffff0"],["khaki","#f0e68c"],["lavender","#e6e6fa"],["lavenderblush","#fff0f5"],["lawngreen","#7cfc00"],["lemonchiffon","#fffacd"],["lightblue","#add8e6"],["lightcoral","#f08080"],["lightcyan","#e0ffff"],["lightgoldenrodyellow","#fafad2"],["lightgray","#d3d3d3"],["lightgreen","#90ee90"],["lightgrey","#d3d3d3"],["lightpink","#ffb6c1"],["lightsalmon","#ffa07a"],["lightseagreen","#20b2aa"],["lightskyblue","#87cefa"],["lightslategray","#778899"],["lightslategrey","#778899"],["lightsteelblue","#b0c4de"],["lightyellow","#ffffe0"],["limegreen","#32cd32"],["linen","#faf0e6"],["magenta","#ff00ff"],["mediumaquamarine","#66cdaa"],["mediumblue","#0000cd"],["mediumorchid","#ba55d3"],["mediumpurple","#9370db"],["mediumseagreen","#3cb371"],["mediumslateblue","#7b68ee"],["mediumspringgreen","#00fa9a"],["mediumturquoise","#48d1cc"],["mediumvioletred","#c71585"],["midnightblue","#191970"],["mintcream","#f5fffa"],["mistyrose","#ffe4e1"],["moccasin","#ffe4b5"],["navajowhite","#ffdead"],["oldlace","#fdf5e6"],["olivedrab","#6b8e23"],["orange","#ffa500"],["orangered","#ff4500"],["orchid","#da70d6"],["palegoldenrod","#eee8aa"],["palegreen","#98fb98"],["paleturquoise","#afeeee"],["palevioletred","#db7093"],["papayawhip","#ffefd5"],["peachpuff","#ffdab9"],["peru","#cd853f"],["pink","#ffc0cb"],["plum","#dda0dd"],["powderblue","#b0e0e6"],["rosybrown","#bc8f8f"],["royalblue","#4169e1"],["saddlebrown","#8b4513"],["salmon","#fa8072"],["sandybrown","#f4a460"],["seagreen","#2e8b57"],["seashell","#fff5ee"],["sienna","#a0522d"],["skyblue","#87ceeb"],["slateblue","#6a5acd"],["slategray","#708090"],["slategrey","#708090"],["snow","#fffafa"],["springgreen","#00ff7f"],["steelblue","#4682b4"],["tan","#d2b48c"],["thistle","#d8bfd8"],["tomato","#ff6347"],["turquoise","#40e0d0"],["violet","#ee82ee"],["wheat","#f5deb3"],["whitesmoke","#f5f5f5"],["yellowgreen","#9acd32"]]);t.default=n}));C(ke);var Ee=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.getRgbValue=l,t.getRgbaValue=d,t.getOpacity=c,t.toRgb=u,t.toHex=f,t.getColorFromRgbValue=h,t.darken=p,t.lighten=g,t.fade=v,t.default=void 0;var n=y(P),r=y(ke),i=/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/,o=/^(rgb|rgba|RGB|RGBA)/,a=/^(rgba|RGBA)/;function s(e){var t=i.test(e),n=o.test(e);return t||n?e:(e=function(e){if(!e)return console.error("getColorByKeywords: Missing parameters!"),!1;return!!r.default.has(e)&&r.default.get(e)}(e))||(console.error("Color: Invalid color!"),!1)}function l(e){if(!e)return console.error("getRgbValue: Missing parameters!"),!1;if(!(e=s(e)))return!1;var t=i.test(e),n=o.test(e),r=e.toLowerCase();return t?function(e){3===(e=e.replace("#","")).length&&(e=Array.from(e).map((function(e){return e+e})).join(""));return e=e.split(""),new Array(3).fill(0).map((function(t,n){return parseInt("0x".concat(e[2*n]).concat(e[2*n+1]))}))}(r):n?function(e){return e.replace(/rgb\(|rgba\(|\)/g,"").split(",").slice(0,3).map((function(e){return parseInt(e)}))}(r):void 0}function d(e){if(!e)return console.error("getRgbaValue: Missing parameters!"),!1;var t=l(e);return!!t&&(t.push(c(e)),t)}function c(e){return e?!!(e=s(e))&&(a.test(e)?(e=e.toLowerCase(),Number(e.split(",").slice(-1)[0].replace(/[)|\s]/g,""))):1):(console.error("getOpacity: Missing parameters!"),!1)}function u(e,t){if(!e)return console.error("toRgb: Missing parameters!"),!1;var n=l(e);return!!n&&("number"==typeof t?"rgba("+n.join(",")+",".concat(t,")"):"rgb("+n.join(",")+")")}function f(e){return e?i.test(e)?e:!!(e=l(e))&&"#"+e.map((function(e){return Number(e).toString(16)})).map((function(e){return"0"===e?"00":e})).join(""):(console.error("toHex: Missing parameters!"),!1)}function h(e){if(!e)return console.error("getColorFromRgbValue: Missing parameters!"),!1;var t=e.length;if(3!==t&&4!==t)return console.error("getColorFromRgbValue: Value is illegal!"),!1;var n=3===t?"rgb(":"rgba(";return n+=e.join(",")+")"}function p(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(!e)return console.error("darken: Missing parameters!"),!1;var n=d(e);return!!n&&h(n=n.map((function(e,n){return 3===n?e:e-Math.ceil(2.55*t)})).map((function(e){return e<0?0:e})))}function g(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(!e)return console.error("lighten: Missing parameters!"),!1;var n=d(e);return!!n&&h(n=n.map((function(e,n){return 3===n?e:e+Math.ceil(2.55*t)})).map((function(e){return e>255?255:e})))}function v(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100;if(!e)return console.error("fade: Missing parameters!"),!1;var r=l(e);if(!r)return!1;var i=[].concat((0,n.default)(r),[t/100]);return h(i)}var m={fade:v,toHex:f,toRgb:u,darken:p,lighten:g,getOpacity:c,getRgbValue:l,getRgbaValue:d,getColorFromRgbValue:h};t.default=m}));C(Ee);Ee.getRgbValue,Ee.getRgbaValue,Ee.getOpacity,Ee.toRgb,Ee.toHex,Ee.getColorFromRgbValue,Ee.darken,Ee.lighten;var Be=Ee.fade;const Pe={name:"DvBorderBox11",mixins:[i],props:{color:{type:Array,default:()=>[]},titleWidth:{type:Number,default:250},title:{type:String,default:""},backgroundColor:{type:String,default:"transparent"}},data:()=>({ref:"border-box-11",filterId:"border-box-11-filterId-"+r(),defaultColor:["#8aaafb","#1f33a2"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])},fade:Be},mounted(){const{mergeColor:e}=this;e()}};var _e=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-border-box-11"},[n("svg",{staticClass:"dv-border-svg-container",attrs:{width:e.width,height:e.height}},[n("defs",[n("filter",{attrs:{id:e.filterId,height:"150%",width:"150%",x:"-25%",y:"-25%"}},[n("feMorphology",{attrs:{operator:"dilate",radius:"2",in:"SourceAlpha",result:"thicken"}}),e._v(" "),n("feGaussianBlur",{attrs:{in:"thicken",stdDeviation:"3",result:"blurred"}}),e._v(" "),n("feFlood",{attrs:{"flood-color":e.mergedColor[1],result:"glowColor"}}),e._v(" "),n("feComposite",{attrs:{in:"glowColor",in2:"blurred",operator:"in",result:"softGlowColored"}}),e._v(" "),n("feMerge",[n("feMergeNode",{attrs:{in:"softGlowColored"}}),e._v(" "),n("feMergeNode",{attrs:{in:"SourceGraphic"}})],1)],1)]),e._v(" "),n("polygon",{attrs:{fill:e.backgroundColor,points:"\n      20, 32 "+(.5*e.width-e.titleWidth/2)+", 32 "+(.5*e.width-e.titleWidth/2+20)+", 53\n      "+(.5*e.width+e.titleWidth/2-20)+", 53 "+(.5*e.width+e.titleWidth/2)+", 32\n      "+(e.width-20)+", 32 "+(e.width-8)+", 48 "+(e.width-8)+", "+(e.height-25)+" "+(e.width-20)+", "+(e.height-8)+"\n      20, "+(e.height-8)+" 8, "+(e.height-25)+" 8, 50\n    "}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],filter:"url(#"+e.filterId+")",points:"\n        "+(e.width-e.titleWidth)/2+", 30\n        20, 30 7, 50 7, "+(50+(e.height-167)/2)+"\n        13, "+(55+(e.height-167)/2)+" 13, "+(135+(e.height-167)/2)+"\n        7, "+(140+(e.height-167)/2)+" 7, "+(e.height-27)+"\n        20, "+(e.height-7)+" "+(e.width-20)+", "+(e.height-7)+" "+(e.width-7)+", "+(e.height-27)+"\n        "+(e.width-7)+", "+(140+(e.height-167)/2)+" "+(e.width-13)+", "+(135+(e.height-167)/2)+"\n        "+(e.width-13)+", "+(55+(e.height-167)/2)+" "+(e.width-7)+", "+(50+(e.height-167)/2)+"\n        "+(e.width-7)+", 50 "+(e.width-20)+", 30 "+(e.width+e.titleWidth)/2+", 30\n        "+((e.width+e.titleWidth)/2-20)+", 7 "+((e.width-e.titleWidth)/2+20)+", 7\n        "+(e.width-e.titleWidth)/2+", 30 "+((e.width-e.titleWidth)/2+20)+", 52\n        "+((e.width+e.titleWidth)/2-20)+", 52 "+(e.width+e.titleWidth)/2+", 30\n      "}}),e._v(" "),n("polygon",{attrs:{stroke:e.mergedColor[0],fill:"transparent",points:"\n        "+((e.width+e.titleWidth)/2-5)+", 30 "+((e.width+e.titleWidth)/2-21)+", 11\n        "+((e.width+e.titleWidth)/2-27)+", 11 "+((e.width+e.titleWidth)/2-8)+", 34\n      "}}),e._v(" "),n("polygon",{attrs:{stroke:e.mergedColor[0],fill:"transparent",points:"\n        "+((e.width-e.titleWidth)/2+5)+", 30 "+((e.width-e.titleWidth)/2+22)+", 49\n        "+((e.width-e.titleWidth)/2+28)+", 49 "+((e.width-e.titleWidth)/2+8)+", 26\n      "}}),e._v(" "),n("polygon",{attrs:{stroke:e.mergedColor[0],fill:e.fade(e.mergedColor[1]||e.defaultColor[1],30),filter:"url(#"+e.filterId+")",points:"\n        "+((e.width+e.titleWidth)/2-11)+", 37 "+((e.width+e.titleWidth)/2-32)+", 11\n        "+((e.width-e.titleWidth)/2+23)+", 11 "+((e.width-e.titleWidth)/2+11)+", 23\n        "+((e.width-e.titleWidth)/2+33)+", 49 "+((e.width+e.titleWidth)/2-22)+", 49\n      "}}),e._v(" "),n("polygon",{attrs:{filter:"url(#"+e.filterId+")",fill:e.mergedColor[0],opacity:"1",points:"\n        "+((e.width-e.titleWidth)/2-10)+", 37 "+((e.width-e.titleWidth)/2-31)+", 37\n        "+((e.width-e.titleWidth)/2-25)+", 46 "+((e.width-e.titleWidth)/2-4)+", 46\n      "}},[n("animate",{attrs:{attributeName:"opacity",values:"1;0.7;1",dur:"2s",begin:"0s",repeatCount:"indefinite"}})]),e._v(" "),n("polygon",{attrs:{filter:"url(#"+e.filterId+")",fill:e.mergedColor[0],opacity:"0.7",points:"\n        "+((e.width-e.titleWidth)/2-40)+", 37 "+((e.width-e.titleWidth)/2-61)+", 37\n        "+((e.width-e.titleWidth)/2-55)+", 46 "+((e.width-e.titleWidth)/2-34)+", 46\n      "}},[n("animate",{attrs:{attributeName:"opacity",values:"0.7;0.4;0.7",dur:"2s",begin:"0s",repeatCount:"indefinite"}})]),e._v(" "),n("polygon",{attrs:{filter:"url(#"+e.filterId+")",fill:e.mergedColor[0],opacity:"0.5",points:"\n        "+((e.width-e.titleWidth)/2-70)+", 37 "+((e.width-e.titleWidth)/2-91)+", 37\n        "+((e.width-e.titleWidth)/2-85)+", 46 "+((e.width-e.titleWidth)/2-64)+", 46\n      "}},[n("animate",{attrs:{attributeName:"opacity",values:"0.5;0.2;0.5",dur:"2s",begin:"0s",repeatCount:"indefinite"}})]),e._v(" "),n("polygon",{attrs:{filter:"url(#"+e.filterId+")",fill:e.mergedColor[0],opacity:"1",points:"\n        "+((e.width+e.titleWidth)/2+30)+", 37 "+((e.width+e.titleWidth)/2+9)+", 37\n        "+((e.width+e.titleWidth)/2+3)+", 46 "+((e.width+e.titleWidth)/2+24)+", 46\n      "}},[n("animate",{attrs:{attributeName:"opacity",values:"1;0.7;1",dur:"2s",begin:"0s",repeatCount:"indefinite"}})]),e._v(" "),n("polygon",{attrs:{filter:"url(#"+e.filterId+")",fill:e.mergedColor[0],opacity:"0.7",points:"\n        "+((e.width+e.titleWidth)/2+60)+", 37 "+((e.width+e.titleWidth)/2+39)+", 37\n        "+((e.width+e.titleWidth)/2+33)+", 46 "+((e.width+e.titleWidth)/2+54)+", 46\n      "}},[n("animate",{attrs:{attributeName:"opacity",values:"0.7;0.4;0.7",dur:"2s",begin:"0s",repeatCount:"indefinite"}})]),e._v(" "),n("polygon",{attrs:{filter:"url(#"+e.filterId+")",fill:e.mergedColor[0],opacity:"0.5",points:"\n        "+((e.width+e.titleWidth)/2+90)+", 37 "+((e.width+e.titleWidth)/2+69)+", 37\n        "+((e.width+e.titleWidth)/2+63)+", 46 "+((e.width+e.titleWidth)/2+84)+", 46\n      "}},[n("animate",{attrs:{attributeName:"opacity",values:"0.5;0.2;0.5",dur:"2s",begin:"0s",repeatCount:"indefinite"}})]),e._v(" "),n("text",{staticClass:"dv-border-box-11-title",attrs:{x:""+e.width/2,y:"32",fill:"#fff","font-size":"18","text-anchor":"middle","dominant-baseline":"middle"}},[e._v("\n      "+e._s(e.title)+"\n    ")]),e._v(" "),n("polygon",{attrs:{fill:e.mergedColor[0],filter:"url(#"+e.filterId+")",points:"\n        7, "+(53+(e.height-167)/2)+" 11, "+(57+(e.height-167)/2)+"\n        11, "+(133+(e.height-167)/2)+" 7, "+(137+(e.height-167)/2)+"\n      "}}),e._v(" "),n("polygon",{attrs:{fill:e.mergedColor[0],filter:"url(#"+e.filterId+")",points:"\n        "+(e.width-7)+", "+(53+(e.height-167)/2)+" "+(e.width-11)+", "+(57+(e.height-167)/2)+"\n        "+(e.width-11)+", "+(133+(e.height-167)/2)+" "+(e.width-7)+", "+(137+(e.height-167)/2)+"\n      "}})]),e._v(" "),n("div",{staticClass:"border-box-content"},[e._t("default")],2)])};_e._withStripped=!0;const Se=a({render:_e,staticRenderFns:[]},(function(e){e&&e("data-v-640dce48_0",{source:".dv-border-box-11 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-11 .dv-border-svg-container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0px;\n  left: 0px;\n}\n.dv-border-box-11 .dv-border-svg-container polyline {\n  fill: none;\n  stroke-width: 1;\n}\n.dv-border-box-11 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,QAAQ;EACR,SAAS;AACX;AACA;EACE,UAAU;EACV,eAAe;AACjB;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-border-box-11 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-11 .dv-border-svg-container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0px;\n  left: 0px;\n}\n.dv-border-box-11 .dv-border-svg-container polyline {\n  fill: none;\n  stroke-width: 1;\n}\n.dv-border-box-11 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),Pe,void 0,!1,void 0,!1,l,void 0,void 0);function Oe(e){e.component(Se.name,Se)}const Ie={name:"DvBorderBox12",mixins:[i],props:{color:{type:Array,default:()=>[]},backgroundColor:{type:String,default:"transparent"}},data:()=>({ref:"border-box-12",filterId:"borderr-box-12-filterId-"+r(),defaultColor:["#2e6099","#7ce7fd"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])},fade:Be},mounted(){const{mergeColor:e}=this;e()}};var We=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-border-box-12"},[n("svg",{staticClass:"dv-border-svg-container",attrs:{width:e.width,height:e.height}},[n("defs",[n("filter",{attrs:{id:e.filterId,height:"150%",width:"150%",x:"-25%",y:"-25%"}},[n("feMorphology",{attrs:{operator:"dilate",radius:"1",in:"SourceAlpha",result:"thicken"}}),e._v(" "),n("feGaussianBlur",{attrs:{in:"thicken",stdDeviation:"2",result:"blurred"}}),e._v(" "),n("feFlood",{attrs:{"flood-color":e.fade(e.mergedColor[1]||e.defaultColor[1],70),result:"glowColor"}},[n("animate",{attrs:{attributeName:"flood-color",values:"\n              "+e.fade(e.mergedColor[1]||e.defaultColor[1],70)+";\n              "+e.fade(e.mergedColor[1]||e.defaultColor[1],30)+";\n              "+e.fade(e.mergedColor[1]||e.defaultColor[1],70)+";\n            ",dur:"3s",begin:"0s",repeatCount:"indefinite"}})]),e._v(" "),n("feComposite",{attrs:{in:"glowColor",in2:"blurred",operator:"in",result:"softGlowColored"}}),e._v(" "),n("feMerge",[n("feMergeNode",{attrs:{in:"softGlowColored"}}),e._v(" "),n("feMergeNode",{attrs:{in:"SourceGraphic"}})],1)],1)]),e._v(" "),e.width&&e.height?n("path",{attrs:{fill:e.backgroundColor,"stroke-width":"2",stroke:e.mergedColor[0],d:"\n        M15 5 L "+(e.width-15)+" 5 Q "+(e.width-5)+" 5, "+(e.width-5)+" 15\n        L "+(e.width-5)+" "+(e.height-15)+" Q "+(e.width-5)+" "+(e.height-5)+", "+(e.width-15)+" "+(e.height-5)+"\n        L 15, "+(e.height-5)+" Q 5 "+(e.height-5)+" 5 "+(e.height-15)+" L 5 15\n        Q 5 5 15 5\n      "}}):e._e(),e._v(" "),n("path",{attrs:{"stroke-width":"2",fill:"transparent","stroke-linecap":"round",filter:"url(#"+e.filterId+")",stroke:e.mergedColor[1],d:"M 20 5 L 15 5 Q 5 5 5 15 L 5 20"}}),e._v(" "),n("path",{attrs:{"stroke-width":"2",fill:"transparent","stroke-linecap":"round",filter:"url(#"+e.filterId+")",stroke:e.mergedColor[1],d:"M "+(e.width-20)+" 5 L "+(e.width-15)+" 5 Q "+(e.width-5)+" 5 "+(e.width-5)+" 15 L "+(e.width-5)+" 20"}}),e._v(" "),n("path",{attrs:{"stroke-width":"2",fill:"transparent","stroke-linecap":"round",filter:"url(#"+e.filterId+")",stroke:e.mergedColor[1],d:"\n        M "+(e.width-20)+" "+(e.height-5)+" L "+(e.width-15)+" "+(e.height-5)+"\n        Q "+(e.width-5)+" "+(e.height-5)+" "+(e.width-5)+" "+(e.height-15)+"\n        L "+(e.width-5)+" "+(e.height-20)+"\n      "}}),e._v(" "),n("path",{attrs:{"stroke-width":"2",fill:"transparent","stroke-linecap":"round",filter:"url(#"+e.filterId+")",stroke:e.mergedColor[1],d:"\n        M 20 "+(e.height-5)+" L 15 "+(e.height-5)+"\n        Q 5 "+(e.height-5)+" 5 "+(e.height-15)+"\n        L 5 "+(e.height-20)+"\n      "}})]),e._v(" "),n("div",{staticClass:"border-box-content"},[e._t("default")],2)])};We._withStripped=!0;const Le=a({render:We,staticRenderFns:[]},(function(e){e&&e("data-v-40c58fd4_0",{source:".dv-border-box-12 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-12 .dv-border-svg-container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0px;\n  left: 0px;\n}\n.dv-border-box-12 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,QAAQ;EACR,SAAS;AACX;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-border-box-12 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-12 .dv-border-svg-container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0px;\n  left: 0px;\n}\n.dv-border-box-12 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),Ie,void 0,!1,void 0,!1,l,void 0,void 0);function je(e){e.component(Le.name,Le)}const Me={name:"DvBorderBox13",mixins:[i],props:{color:{type:Array,default:()=>[]},backgroundColor:{type:String,default:"transparent"}},data:()=>({ref:"border-box-13",defaultColor:["#6586ec","#2cf7fe"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var Fe=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-border-box-13"},[n("svg",{staticClass:"dv-border-svg-container",attrs:{width:e.width,height:e.height}},[n("path",{attrs:{fill:e.backgroundColor,stroke:e.mergedColor[0],d:"\n        M 5 20 L 5 10 L 12 3  L 60 3 L 68 10\n        L "+(e.width-20)+" 10 L "+(e.width-5)+" 25\n        L "+(e.width-5)+" "+(e.height-5)+" L 20 "+(e.height-5)+"\n        L 5 "+(e.height-20)+" L 5 20\n      "}}),e._v(" "),n("path",{attrs:{fill:"transparent","stroke-width":"3","stroke-linecap":"round","stroke-dasharray":"10, 5",stroke:e.mergedColor[0],d:"M 16 9 L 61 9"}}),e._v(" "),n("path",{attrs:{fill:"transparent",stroke:e.mergedColor[1],d:"M 5 20 L 5 10 L 12 3  L 60 3 L 68 10"}}),e._v(" "),n("path",{attrs:{fill:"transparent",stroke:e.mergedColor[1],d:"M "+(e.width-5)+" "+(e.height-30)+" L "+(e.width-5)+" "+(e.height-5)+" L "+(e.width-30)+" "+(e.height-5)}})]),e._v(" "),n("div",{staticClass:"border-box-content"},[e._t("default")],2)])};Fe._withStripped=!0;const Re=a({render:Fe,staticRenderFns:[]},(function(e){e&&e("data-v-6c30a53b_0",{source:".dv-border-box-13 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-13 .dv-border-svg-container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0px;\n  left: 0px;\n}\n.dv-border-box-13 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,QAAQ;EACR,SAAS;AACX;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-border-box-13 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-border-box-13 .dv-border-svg-container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0px;\n  left: 0px;\n}\n.dv-border-box-13 .border-box-content {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),Me,void 0,!1,void 0,!1,l,void 0,void 0);function Ge(e){e.component(Re.name,Re)}const De={name:"DvDecoration1",mixins:[i],props:{color:{type:Array,default:()=>[]}},data:()=>({ref:"decoration-1",svgWH:[200,50],svgScale:[1,1],rowNum:4,rowPoints:20,pointSideLength:2.5,halfPointSideLength:1.25,points:[],rects:[],defaultColor:["#fff","#0de7c2"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{afterAutoResizeMixinInit(){const{calcSVGData:e}=this;e()},calcSVGData(){const{calcPointsPosition:e,calcRectsPosition:t,calcScale:n}=this;e(),t(),n()},calcPointsPosition(){const{svgWH:e,rowNum:t,rowPoints:n}=this,[r,i]=e,o=r/(n+1),a=i/(t+1);let s=new Array(t).fill(0).map((e,t)=>new Array(n).fill(0).map((e,n)=>[o*(n+1),a*(t+1)]));this.points=s.reduce((e,t)=>[...e,...t],[])},calcRectsPosition(){const{points:e,rowPoints:t}=this,n=e[2*t-1],r=e[2*t-3];this.rects=[n,r]},calcScale(){const{width:e,height:t,svgWH:n}=this,[r,i]=n;this.svgScale=[e/r,t/i]},onResize(){const{calcSVGData:e}=this;e()},mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var ze=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-decoration-1"},[n("svg",{style:"transform:scale("+e.svgScale[0]+","+e.svgScale[1]+");",attrs:{width:e.svgWH[0]+"px",height:e.svgWH[1]+"px"}},[e._l(e.points,(function(t,r){return[Math.random()>.6?n("rect",{key:r,attrs:{fill:e.mergedColor[0],x:t[0]-e.halfPointSideLength,y:t[1]-e.halfPointSideLength,width:e.pointSideLength,height:e.pointSideLength}},[Math.random()>.6?n("animate",{attrs:{attributeName:"fill",values:e.mergedColor[0]+";transparent",dur:"1s",begin:2*Math.random(),repeatCount:"indefinite"}}):e._e()]):e._e()]})),e._v(" "),e.rects[0]?n("rect",{attrs:{fill:e.mergedColor[1],x:e.rects[0][0]-e.pointSideLength,y:e.rects[0][1]-e.pointSideLength,width:2*e.pointSideLength,height:2*e.pointSideLength}},[n("animate",{attrs:{attributeName:"width",values:"0;"+2*e.pointSideLength,dur:"2s",repeatCount:"indefinite"}}),e._v(" "),n("animate",{attrs:{attributeName:"height",values:"0;"+2*e.pointSideLength,dur:"2s",repeatCount:"indefinite"}}),e._v(" "),n("animate",{attrs:{attributeName:"x",values:e.rects[0][0]+";"+(e.rects[0][0]-e.pointSideLength),dur:"2s",repeatCount:"indefinite"}}),e._v(" "),n("animate",{attrs:{attributeName:"y",values:e.rects[0][1]+";"+(e.rects[0][1]-e.pointSideLength),dur:"2s",repeatCount:"indefinite"}})]):e._e(),e._v(" "),e.rects[1]?n("rect",{attrs:{fill:e.mergedColor[1],x:e.rects[1][0]-40,y:e.rects[1][1]-e.pointSideLength,width:40,height:2*e.pointSideLength}},[n("animate",{attrs:{attributeName:"width",values:"0;40;0",dur:"2s",repeatCount:"indefinite"}}),e._v(" "),n("animate",{attrs:{attributeName:"x",values:e.rects[1][0]+";"+(e.rects[1][0]-40)+";"+e.rects[1][0],dur:"2s",repeatCount:"indefinite"}})]):e._e()],2)])};ze._withStripped=!0;const Te=a({render:ze,staticRenderFns:[]},(function(e){e&&e("data-v-69241e60_0",{source:".dv-decoration-1 {\n  width: 100%;\n  height: 100%;\n}\n.dv-decoration-1 svg {\n  transform-origin: left top;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,WAAW;EACX,YAAY;AACd;AACA;EACE,0BAA0B;AAC5B",file:"main.vue",sourcesContent:[".dv-decoration-1 {\n  width: 100%;\n  height: 100%;\n}\n.dv-decoration-1 svg {\n  transform-origin: left top;\n}\n"]},media:void 0})}),De,void 0,!1,void 0,!1,l,void 0,void 0);function Ye(e){e.component(Te.name,Te)}const Ne={name:"DvDecoration2",mixins:[i],props:{color:{type:Array,default:()=>[]},reverse:{type:Boolean,default:!1}},data:()=>({ref:"decoration-2",x:0,y:0,w:0,h:0,defaultColor:["#3faacb","#fff"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()},reverse(){const{calcSVGData:e}=this;e()}},methods:{afterAutoResizeMixinInit(){const{calcSVGData:e}=this;e()},calcSVGData(){const{reverse:e,width:t,height:n}=this;e?(this.w=1,this.h=n,this.x=t/2,this.y=0):(this.w=t,this.h=1,this.x=0,this.y=n/2)},onResize(){const{calcSVGData:e}=this;e()},mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var Xe=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-decoration-2"},[n("svg",{attrs:{width:e.width+"px",height:e.height+"px"}},[n("rect",{attrs:{x:e.x,y:e.y,width:e.w,height:e.h,fill:e.mergedColor[0]}},[n("animate",{attrs:{attributeName:e.reverse?"height":"width",from:"0",to:e.reverse?e.height:e.width,dur:"6s",calcMode:"spline",keyTimes:"0;1",keySplines:".42,0,.58,1",repeatCount:"indefinite"}})]),e._v(" "),n("rect",{attrs:{x:e.x,y:e.y,width:"1",height:"1",fill:e.mergedColor[1]}},[n("animate",{attrs:{attributeName:e.reverse?"y":"x",from:"0",to:e.reverse?e.height:e.width,dur:"6s",calcMode:"spline",keyTimes:"0;1",keySplines:"0.42,0,0.58,1",repeatCount:"indefinite"}})])])])};Xe._withStripped=!0;const $e=a({render:Xe,staticRenderFns:[]},(function(e){e&&e("data-v-a2b21eaa_0",{source:".dv-decoration-2 {\n  display: flex;\n  width: 100%;\n  height: 100%;\n  justify-content: center;\n  align-items: center;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,aAAa;EACb,WAAW;EACX,YAAY;EACZ,uBAAuB;EACvB,mBAAmB;AACrB",file:"main.vue",sourcesContent:[".dv-decoration-2 {\n  display: flex;\n  width: 100%;\n  height: 100%;\n  justify-content: center;\n  align-items: center;\n}\n"]},media:void 0})}),Ne,void 0,!1,void 0,!1,l,void 0,void 0);function Qe(e){e.component($e.name,$e)}const Ue={name:"DvDecoration3",mixins:[i],props:{color:{type:Array,default:()=>[]}},data:()=>({ref:"decoration-3",svgWH:[300,35],svgScale:[1,1],rowNum:2,rowPoints:25,pointSideLength:7,halfPointSideLength:3.5,points:[],defaultColor:["#7acaec","transparent"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{afterAutoResizeMixinInit(){const{calcSVGData:e}=this;e()},calcSVGData(){const{calcPointsPosition:e,calcScale:t}=this;e(),t()},calcPointsPosition(){const{svgWH:e,rowNum:t,rowPoints:n}=this,[r,i]=e,o=r/(n+1),a=i/(t+1);let s=new Array(t).fill(0).map((e,t)=>new Array(n).fill(0).map((e,n)=>[o*(n+1),a*(t+1)]));this.points=s.reduce((e,t)=>[...e,...t],[])},calcScale(){const{width:e,height:t,svgWH:n}=this,[r,i]=n;this.svgScale=[e/r,t/i]},onResize(){const{calcSVGData:e}=this;e()},mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var Ve=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-decoration-3"},[n("svg",{style:"transform:scale("+e.svgScale[0]+","+e.svgScale[1]+");",attrs:{width:e.svgWH[0]+"px",height:e.svgWH[1]+"px"}},[e._l(e.points,(function(t,r){return[n("rect",{key:r,attrs:{fill:e.mergedColor[0],x:t[0]-e.halfPointSideLength,y:t[1]-e.halfPointSideLength,width:e.pointSideLength,height:e.pointSideLength}},[Math.random()>.6?n("animate",{attrs:{attributeName:"fill",values:""+e.mergedColor.join(";"),dur:Math.random()+1+"s",begin:2*Math.random(),repeatCount:"indefinite"}}):e._e()])]}))],2)])};Ve._withStripped=!0;const He=a({render:Ve,staticRenderFns:[]},(function(e){e&&e("data-v-2cd3ac93_0",{source:".dv-decoration-3 {\n  width: 100%;\n  height: 100%;\n}\n.dv-decoration-3 svg {\n  transform-origin: left top;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,WAAW;EACX,YAAY;AACd;AACA;EACE,0BAA0B;AAC5B",file:"main.vue",sourcesContent:[".dv-decoration-3 {\n  width: 100%;\n  height: 100%;\n}\n.dv-decoration-3 svg {\n  transform-origin: left top;\n}\n"]},media:void 0})}),Ue,void 0,!1,void 0,!1,l,void 0,void 0);function qe(e){e.component(He.name,He)}const Ze={name:"DvDecoration4",mixins:[i],props:{color:{type:Array,default:()=>[]},reverse:{type:Boolean,default:!1}},data:()=>({ref:"decoration-4",defaultColor:["rgba(255, 255, 255, 0.3)","rgba(255, 255, 255, 0.3)"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var Ke=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-decoration-4"},[n("div",{class:"container "+(e.reverse?"reverse":"normal"),style:e.reverse?"width:"+e.width+"px;height:5px":"width:5px;height:"+e.height+"px;"},[n("svg",{attrs:{width:e.reverse?e.width:5,height:e.reverse?5:e.height}},[n("polyline",{attrs:{stroke:e.mergedColor[0],points:e.reverse?"0, 2.5 "+e.width+", 2.5":"2.5, 0 2.5, "+e.height}}),e._v(" "),n("polyline",{staticClass:"bold-line",attrs:{stroke:e.mergedColor[1],"stroke-width":"3","stroke-dasharray":"20, 80","stroke-dashoffset":"-30",points:e.reverse?"0, 2.5 "+e.width+", 2.5":"2.5, 0 2.5, "+e.height}})])])])};Ke._withStripped=!0;const Je=a({render:Ke,staticRenderFns:[]},(function(e){e&&e("data-v-6a8df262_0",{source:".dv-decoration-4 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-decoration-4 .container {\n  display: flex;\n  overflow: hidden;\n  position: absolute;\n  flex: 1;\n}\n.dv-decoration-4 .normal {\n  animation: ani-height 3s ease-in-out infinite;\n  left: 50%;\n  margin-left: -2px;\n}\n.dv-decoration-4 .reverse {\n  animation: ani-width 3s ease-in-out infinite;\n  top: 50%;\n  margin-top: -2px;\n}\n@keyframes ani-height {\n0% {\n    height: 0%;\n}\n70% {\n    height: 100%;\n}\n100% {\n    height: 100%;\n}\n}\n@keyframes ani-width {\n0% {\n    width: 0%;\n}\n70% {\n    width: 100%;\n}\n100% {\n    width: 100%;\n}\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd;AACA;EACE,aAAa;EACb,gBAAgB;EAChB,kBAAkB;EAClB,OAAO;AACT;AACA;EACE,6CAA6C;EAC7C,SAAS;EACT,iBAAiB;AACnB;AACA;EACE,4CAA4C;EAC5C,QAAQ;EACR,gBAAgB;AAClB;AACA;AACE;IACE,UAAU;AACZ;AACA;IACE,YAAY;AACd;AACA;IACE,YAAY;AACd;AACF;AACA;AACE;IACE,SAAS;AACX;AACA;IACE,WAAW;AACb;AACA;IACE,WAAW;AACb;AACF",file:"main.vue",sourcesContent:[".dv-decoration-4 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-decoration-4 .container {\n  display: flex;\n  overflow: hidden;\n  position: absolute;\n  flex: 1;\n}\n.dv-decoration-4 .normal {\n  animation: ani-height 3s ease-in-out infinite;\n  left: 50%;\n  margin-left: -2px;\n}\n.dv-decoration-4 .reverse {\n  animation: ani-width 3s ease-in-out infinite;\n  top: 50%;\n  margin-top: -2px;\n}\n@keyframes ani-height {\n  0% {\n    height: 0%;\n  }\n  70% {\n    height: 100%;\n  }\n  100% {\n    height: 100%;\n  }\n}\n@keyframes ani-width {\n  0% {\n    width: 0%;\n  }\n  70% {\n    width: 100%;\n  }\n  100% {\n    width: 100%;\n  }\n}\n"]},media:void 0})}),Ze,void 0,!1,void 0,!1,l,void 0,void 0);function et(e){e.component(Je.name,Je)}const tt={name:"DvDecoration5",mixins:[i],props:{color:{type:Array,default:()=>[]}},data:()=>({ref:"decoration-5",line1Points:"",line2Points:"",line1Length:0,line2Length:0,defaultColor:["#3f96a5","#3f96a5"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{afterAutoResizeMixinInit(){const{calcSVGData:e}=this;e()},calcSVGData(){const{width:e,height:t}=this;let n=[[0,.2*t],[.18*e,.2*t],[.2*e,.4*t],[.25*e,.4*t],[.27*e,.6*t],[.72*e,.6*t],[.75*e,.4*t],[.8*e,.4*t],[.82*e,.2*t],[e,.2*t]],r=[[.3*e,.8*t],[.7*e,.8*t]];const i=R(n),o=R(r);n=n.map(e=>e.join(",")).join(" "),r=r.map(e=>e.join(",")).join(" "),this.line1Points=n,this.line2Points=r,this.line1Length=i,this.line2Length=o},onResize(){const{calcSVGData:e}=this;e()},mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var nt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-decoration-5"},[n("svg",{attrs:{width:e.width,height:e.height}},[n("polyline",{attrs:{fill:"transparent",stroke:e.mergedColor[0],"stroke-width":"3",points:e.line1Points}},[n("animate",{attrs:{attributeName:"stroke-dasharray",attributeType:"XML",from:"0, "+e.line1Length/2+", 0, "+e.line1Length/2,to:"0, 0, "+e.line1Length+", 0",dur:"1.2s",begin:"0s",calcMode:"spline",keyTimes:"0;1",keySplines:"0.4,1,0.49,0.98",repeatCount:"indefinite"}})]),e._v(" "),n("polyline",{attrs:{fill:"transparent",stroke:e.mergedColor[1],"stroke-width":"2",points:e.line2Points}},[n("animate",{attrs:{attributeName:"stroke-dasharray",attributeType:"XML",from:"0, "+e.line2Length/2+", 0, "+e.line2Length/2,to:"0, 0, "+e.line2Length+", 0",dur:"1.2s",begin:"0s",calcMode:"spline",keyTimes:"0;1",keySplines:".4,1,.49,.98",repeatCount:"indefinite"}})])])])};nt._withStripped=!0;const rt=a({render:nt,staticRenderFns:[]},(function(e){e&&e("data-v-301d5bb4_0",{source:".dv-decoration-5 {\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-decoration-5 {\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),tt,void 0,!1,void 0,!1,l,void 0,void 0);function it(e){e.component(rt.name,rt)}const ot={name:"DvDecoration6",mixins:[i],props:{color:{type:Array,default:()=>[]}},data:()=>({ref:"decoration-6",svgWH:[300,35],svgScale:[1,1],rowNum:1,rowPoints:40,rectWidth:7,halfRectWidth:3.5,points:[],heights:[],minHeights:[],randoms:[],defaultColor:["#7acaec","#7acaec"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{afterAutoResizeMixinInit(){const{calcSVGData:e}=this;e()},calcSVGData(){const{calcPointsPosition:e,calcScale:t}=this;e(),t()},calcPointsPosition(){const{svgWH:e,rowNum:n,rowPoints:r}=this,[i,o]=e,a=i/(r+1),s=o/(n+1);let l=new Array(n).fill(0).map((e,t)=>new Array(r).fill(0).map((e,n)=>[a*(n+1),s*(t+1)]));this.points=l.reduce((e,t)=>[...e,...t],[]);const d=this.heights=new Array(n*r).fill(0).map(e=>Math.random()>.8?t(.7*o,o):t(.2*o,.5*o));this.minHeights=new Array(n*r).fill(0).map((e,t)=>d[t]*Math.random()),this.randoms=new Array(n*r).fill(0).map(e=>Math.random()+1.5)},calcScale(){const{width:e,height:t,svgWH:n}=this,[r,i]=n;this.svgScale=[e/r,t/i]},onResize(){const{calcSVGData:e}=this;e()},mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var at=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-decoration-6"},[n("svg",{style:"transform:scale("+e.svgScale[0]+","+e.svgScale[1]+");",attrs:{width:e.svgWH[0]+"px",height:e.svgWH[1]+"px"}},[e._l(e.points,(function(t,r){return[n("rect",{key:r,attrs:{fill:e.mergedColor[Math.random()>.5?0:1],x:t[0]-e.halfRectWidth,y:t[1]-e.heights[r]/2,width:e.rectWidth,height:e.heights[r]}},[n("animate",{attrs:{attributeName:"y",values:t[1]-e.minHeights[r]/2+";"+(t[1]-e.heights[r]/2)+";"+(t[1]-e.minHeights[r]/2),dur:e.randoms[r]+"s",keyTimes:"0;0.5;1",calcMode:"spline",keySplines:"0.42,0,0.58,1;0.42,0,0.58,1",begin:"0s",repeatCount:"indefinite"}}),e._v(" "),n("animate",{attrs:{attributeName:"height",values:e.minHeights[r]+";"+e.heights[r]+";"+e.minHeights[r],dur:e.randoms[r]+"s",keyTimes:"0;0.5;1",calcMode:"spline",keySplines:"0.42,0,0.58,1;0.42,0,0.58,1",begin:"0s",repeatCount:"indefinite"}})])]}))],2)])};at._withStripped=!0;const st=a({render:at,staticRenderFns:[]},(function(e){e&&e("data-v-a29c4fc2_0",{source:".dv-decoration-6 {\n  width: 100%;\n  height: 100%;\n}\n.dv-decoration-6 svg {\n  transform-origin: left top;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,WAAW;EACX,YAAY;AACd;AACA;EACE,0BAA0B;AAC5B",file:"main.vue",sourcesContent:[".dv-decoration-6 {\n  width: 100%;\n  height: 100%;\n}\n.dv-decoration-6 svg {\n  transform-origin: left top;\n}\n"]},media:void 0})}),ot,void 0,!1,void 0,!1,l,void 0,void 0);function lt(e){e.component(st.name,st)}const dt={name:"DvDecoration7",props:{color:{type:Array,default:()=>[]}},data:()=>({defaultColor:["#1dc1f5","#1dc1f5"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var ct=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"dv-decoration-7"},[n("svg",{attrs:{width:"21px",height:"20px"}},[n("polyline",{attrs:{"stroke-width":"4",fill:"transparent",stroke:e.mergedColor[0],points:"10, 0 19, 10 10, 20"}}),e._v(" "),n("polyline",{attrs:{"stroke-width":"2",fill:"transparent",stroke:e.mergedColor[1],points:"2, 0 11, 10 2, 20"}})]),e._v(" "),e._t("default"),e._v(" "),n("svg",{attrs:{width:"21px",height:"20px"}},[n("polyline",{attrs:{"stroke-width":"4",fill:"transparent",stroke:e.mergedColor[0],points:"11, 0 2, 10 11, 20"}}),e._v(" "),n("polyline",{attrs:{"stroke-width":"2",fill:"transparent",stroke:e.mergedColor[1],points:"19, 0 10, 10 19, 20"}})])],2)};ct._withStripped=!0;const ut=a({render:ct,staticRenderFns:[]},(function(e){e&&e("data-v-b84d1f12_0",{source:".dv-decoration-7 {\n  display: flex;\n  width: 100%;\n  height: 100%;\n  justify-content: center;\n  align-items: center;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,aAAa;EACb,WAAW;EACX,YAAY;EACZ,uBAAuB;EACvB,mBAAmB;AACrB",file:"main.vue",sourcesContent:[".dv-decoration-7 {\n  display: flex;\n  width: 100%;\n  height: 100%;\n  justify-content: center;\n  align-items: center;\n}\n"]},media:void 0})}),dt,void 0,!1,void 0,!1,l,void 0,void 0);function ft(e){e.component(ut.name,ut)}const ht={name:"DvDecoration8",mixins:[i],props:{color:{type:Array,default:()=>[]},reverse:{type:Boolean,default:!1}},data:()=>({ref:"decoration-8",defaultColor:["#3f96a5","#3f96a5"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{xPos(e){const{reverse:t,width:n}=this;return t?n-e:e},mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var pt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-decoration-8"},[n("svg",{attrs:{width:e.width,height:e.height}},[n("polyline",{attrs:{stroke:e.mergedColor[0],"stroke-width":"2",fill:"transparent",points:e.xPos(0)+", 0 "+e.xPos(30)+", "+e.height/2}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],"stroke-width":"2",fill:"transparent",points:e.xPos(20)+", 0 "+e.xPos(50)+", "+e.height/2+" "+e.xPos(e.width)+", "+e.height/2}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[1],fill:"transparent","stroke-width":"3",points:e.xPos(0)+", "+(e.height-3)+", "+e.xPos(200)+", "+(e.height-3)}})])])};pt._withStripped=!0;const gt=a({render:pt,staticRenderFns:[]},(function(e){e&&e("data-v-53cf43a5_0",{source:".dv-decoration-8 {\n  display: flex;\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,aAAa;EACb,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-decoration-8 {\n  display: flex;\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),ht,void 0,!1,void 0,!1,l,void 0,void 0);function vt(e){e.component(gt.name,gt)}const mt={name:"DvDecoration9",mixins:[i],props:{color:{type:Array,default:()=>[]},dur:{type:Number,default:3}},data:()=>({ref:"decoration-9",polygonId:"decoration-9-polygon-"+r(),svgWH:[100,100],svgScale:[1,1],defaultColor:["rgba(3, 166, 224, 0.8)","rgba(3, 166, 224, 0.5)"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{afterAutoResizeMixinInit(){const{calcScale:e}=this;e()},calcScale(){const{width:e,height:t,svgWH:n}=this,[r,i]=n;this.svgScale=[e/r,t/i]},onResize(){const{calcScale:e}=this;e()},mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])},fade:Be},mounted(){const{mergeColor:e}=this;e()}};var At=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-decoration-9"},[n("svg",{style:"transform:scale("+e.svgScale[0]+","+e.svgScale[1]+");",attrs:{width:e.svgWH[0]+"px",height:e.svgWH[1]+"px"}},[n("defs",[n("polygon",{attrs:{id:e.polygonId,points:"15, 46.5, 21, 47.5, 21, 52.5, 15, 53.5"}})]),e._v(" "),n("circle",{attrs:{cx:"50",cy:"50",r:"45",fill:"transparent",stroke:e.mergedColor[1],"stroke-width":"10","stroke-dasharray":"80, 100, 30, 100"}},[n("animateTransform",{attrs:{attributeName:"transform",type:"rotate",values:"0 50 50;360 50 50",dur:e.dur+"s",repeatCount:"indefinite"}})],1),e._v(" "),n("circle",{attrs:{cx:"50",cy:"50",r:"45",fill:"transparent",stroke:e.mergedColor[0],"stroke-width":"6","stroke-dasharray":"50, 66, 100, 66"}},[n("animateTransform",{attrs:{attributeName:"transform",type:"rotate",values:"0 50 50;-360 50 50",dur:e.dur+"s",repeatCount:"indefinite"}})],1),e._v(" "),n("circle",{attrs:{cx:"50",cy:"50",r:"38",fill:"transparent",stroke:e.fade(e.mergedColor[1]||e.defaultColor[1],30),"stroke-width":"1","stroke-dasharray":"5, 1"}}),e._v(" "),e._l(new Array(20).fill(0),(function(t,r){return n("use",{key:r,attrs:{"xlink:href":"#"+e.polygonId,stroke:e.mergedColor[1],fill:Math.random()>.4?"transparent":e.mergedColor[0]}},[n("animateTransform",{attrs:{attributeName:"transform",type:"rotate",values:"0 50 50;360 50 50",dur:e.dur+"s",begin:r*e.dur/20+"s",repeatCount:"indefinite"}})],1)})),e._v(" "),n("circle",{attrs:{cx:"50",cy:"50",r:"26",fill:"transparent",stroke:e.fade(e.mergedColor[1]||e.defaultColor[1],30),"stroke-width":"1","stroke-dasharray":"5, 1"}})],2),e._v(" "),e._t("default")],2)};At._withStripped=!0;const Ct=a({render:At,staticRenderFns:[]},(function(e){e&&e("data-v-b47f91a8_0",{source:".dv-decoration-9 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.dv-decoration-9 svg {\n  position: absolute;\n  left: 0px;\n  top: 0px;\n  transform-origin: left top;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AACzB;AACA;EACE,kBAAkB;EAClB,SAAS;EACT,QAAQ;EACR,0BAA0B;AAC5B",file:"main.vue",sourcesContent:[".dv-decoration-9 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.dv-decoration-9 svg {\n  position: absolute;\n  left: 0px;\n  top: 0px;\n  transform-origin: left top;\n}\n"]},media:void 0})}),mt,void 0,!1,void 0,!1,l,void 0,void 0);function bt(e){e.component(Ct.name,Ct)}const yt={name:"DvDecoration10",mixins:[i],props:{color:{type:Array,default:()=>[]}},data(){const e=r();return{ref:"decoration-10",animationId1:"d10ani1"+e,animationId2:"d10ani2"+e,animationId3:"d10ani3"+e,animationId4:"d10ani4"+e,animationId5:"d10ani5"+e,animationId6:"d10ani6"+e,animationId7:"d10ani7"+e,defaultColor:["#00c2ff","rgba(0, 194, 255, 0.3)"],mergedColor:[]}},watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])}},mounted(){const{mergeColor:e}=this;e()}};var xt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-decoration-10"},[n("svg",{attrs:{width:e.width,height:e.height}},[n("polyline",{attrs:{stroke:e.mergedColor[1],"stroke-width":"2",points:"0, "+e.height/2+" "+e.width+", "+e.height/2}}),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],"stroke-width":"2",points:"5, "+e.height/2+" "+(.2*e.width-3)+", "+e.height/2,"stroke-dasharray":"0, "+.2*e.width,fill:"freeze"}},[n("animate",{attrs:{id:e.animationId2,attributeName:"stroke-dasharray",values:"0, "+.2*e.width+";"+.2*e.width+", 0;",dur:"3s",begin:e.animationId1+".end",fill:"freeze"}}),e._v(" "),n("animate",{attrs:{attributeName:"stroke-dasharray",values:.2*e.width+", 0;0, "+.2*e.width,dur:"0.01s",begin:e.animationId7+".end",fill:"freeze"}})]),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],"stroke-width":"2",points:.2*e.width+3+", "+e.height/2+" "+(.8*e.width-3)+", "+e.height/2,"stroke-dasharray":"0, "+.6*e.width}},[n("animate",{attrs:{id:e.animationId4,attributeName:"stroke-dasharray",values:"0, "+.6*e.width+";"+.6*e.width+", 0",dur:"3s",begin:e.animationId3+".end + 1s",fill:"freeze"}}),e._v(" "),n("animate",{attrs:{attributeName:"stroke-dasharray",values:.6*e.width+", 0;0, "+.6*e.width,dur:"0.01s",begin:e.animationId7+".end",fill:"freeze"}})]),e._v(" "),n("polyline",{attrs:{stroke:e.mergedColor[0],"stroke-width":"2",points:.8*e.width+3+", "+e.height/2+" "+(e.width-5)+", "+e.height/2,"stroke-dasharray":"0, "+.2*e.width}},[n("animate",{attrs:{id:e.animationId6,attributeName:"stroke-dasharray",values:"0, "+.2*e.width+";"+.2*e.width+", 0",dur:"3s",begin:e.animationId5+".end + 1s",fill:"freeze"}}),e._v(" "),n("animate",{attrs:{attributeName:"stroke-dasharray",values:.2*e.width+", 0;0, "+.3*e.width,dur:"0.01s",begin:e.animationId7+".end",fill:"freeze"}})]),e._v(" "),n("circle",{attrs:{cx:"2",cy:e.height/2,r:"2",fill:e.mergedColor[1]}},[n("animate",{attrs:{id:e.animationId1,attributeName:"fill",values:e.mergedColor[1]+";"+e.mergedColor[0],begin:"0s;"+e.animationId7+".end",dur:"0.3s",fill:"freeze"}})]),e._v(" "),n("circle",{attrs:{cx:.2*e.width,cy:e.height/2,r:"2",fill:e.mergedColor[1]}},[n("animate",{attrs:{id:e.animationId3,attributeName:"fill",values:e.mergedColor[1]+";"+e.mergedColor[0],begin:e.animationId2+".end",dur:"0.3s",fill:"freeze"}}),e._v(" "),n("animate",{attrs:{attributeName:"fill",values:e.mergedColor[1]+";"+e.mergedColor[1],dur:"0.01s",begin:e.animationId7+".end",fill:"freeze"}})]),e._v(" "),n("circle",{attrs:{cx:.8*e.width,cy:e.height/2,r:"2",fill:e.mergedColor[1]}},[n("animate",{attrs:{id:e.animationId5,attributeName:"fill",values:e.mergedColor[1]+";"+e.mergedColor[0],begin:e.animationId4+".end",dur:"0.3s",fill:"freeze"}}),e._v(" "),n("animate",{attrs:{attributeName:"fill",values:e.mergedColor[1]+";"+e.mergedColor[1],dur:"0.01s",begin:e.animationId7+".end",fill:"freeze"}})]),e._v(" "),n("circle",{attrs:{cx:e.width-2,cy:e.height/2,r:"2",fill:e.mergedColor[1]}},[n("animate",{attrs:{id:e.animationId7,attributeName:"fill",values:e.mergedColor[1]+";"+e.mergedColor[0],begin:e.animationId6+".end",dur:"0.3s",fill:"freeze"}}),e._v(" "),n("animate",{attrs:{attributeName:"fill",values:e.mergedColor[1]+";"+e.mergedColor[1],dur:"0.01s",begin:e.animationId7+".end",fill:"freeze"}})])])])};xt._withStripped=!0;const wt=a({render:xt,staticRenderFns:[]},(function(e){e&&e("data-v-23172a05_0",{source:".dv-decoration-10 {\n  width: 100%;\n  height: 100%;\n  display: flex;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,WAAW;EACX,YAAY;EACZ,aAAa;AACf",file:"main.vue",sourcesContent:[".dv-decoration-10 {\n  width: 100%;\n  height: 100%;\n  display: flex;\n}\n"]},media:void 0})}),yt,void 0,!1,void 0,!1,l,void 0,void 0);function kt(e){e.component(wt.name,wt)}const Et={name:"DvDecoration11",mixins:[i],props:{color:{type:Array,default:()=>[]}},data:()=>({ref:"decoration-11",defaultColor:["#1a98fc","#2cf7fe"],mergedColor:[]}),watch:{color(){const{mergeColor:e}=this;e()}},methods:{mergeColor(){const{color:e,defaultColor:t}=this;this.mergedColor=F(j(t,!0),e||[])},fade:Be},mounted(){const{mergeColor:e}=this;e()}};var Bt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-decoration-11"},[n("svg",{attrs:{width:e.width,height:e.height}},[n("polygon",{attrs:{fill:e.fade(e.mergedColor[1]||e.defaultColor[1],10),stroke:e.mergedColor[1],points:"20 10, 25 4, 55 4 60 10"}}),e._v(" "),n("polygon",{attrs:{fill:e.fade(e.mergedColor[1]||e.defaultColor[1],10),stroke:e.mergedColor[1],points:"20 "+(e.height-10)+", 25 "+(e.height-4)+", 55 "+(e.height-4)+" 60 "+(e.height-10)}}),e._v(" "),n("polygon",{attrs:{fill:e.fade(e.mergedColor[1]||e.defaultColor[1],10),stroke:e.mergedColor[1],points:e.width-20+" 10, "+(e.width-25)+" 4, "+(e.width-55)+" 4 "+(e.width-60)+" 10"}}),e._v(" "),n("polygon",{attrs:{fill:e.fade(e.mergedColor[1]||e.defaultColor[1],10),stroke:e.mergedColor[1],points:e.width-20+" "+(e.height-10)+", "+(e.width-25)+" "+(e.height-4)+", "+(e.width-55)+" "+(e.height-4)+" "+(e.width-60)+" "+(e.height-10)}}),e._v(" "),n("polygon",{attrs:{fill:e.fade(e.mergedColor[0]||e.defaultColor[0],20),stroke:e.mergedColor[0],points:"\n        20 10, 5 "+e.height/2+" 20 "+(e.height-10)+"\n        "+(e.width-20)+" "+(e.height-10)+" "+(e.width-5)+" "+e.height/2+" "+(e.width-20)+" 10\n      "}}),e._v(" "),n("polyline",{attrs:{fill:"transparent",stroke:e.fade(e.mergedColor[0]||e.defaultColor[0],70),points:"25 18, 15 "+e.height/2+" 25 "+(e.height-18)}}),e._v(" "),n("polyline",{attrs:{fill:"transparent",stroke:e.fade(e.mergedColor[0]||e.defaultColor[0],70),points:e.width-25+" 18, "+(e.width-15)+" "+e.height/2+" "+(e.width-25)+" "+(e.height-18)}})]),e._v(" "),n("div",{staticClass:"decoration-content"},[e._t("default")],2)])};Bt._withStripped=!0;const Pt=a({render:Bt,staticRenderFns:[]},(function(e){e&&e("data-v-53895fd4_0",{source:".dv-decoration-11 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n}\n.dv-decoration-11 .decoration-content {\n  position: absolute;\n  top: 0px;\n  left: 0px;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,aAAa;AACf;AACA;EACE,kBAAkB;EAClB,QAAQ;EACR,SAAS;EACT,WAAW;EACX,YAAY;EACZ,aAAa;EACb,mBAAmB;EACnB,uBAAuB;AACzB",file:"main.vue",sourcesContent:[".dv-decoration-11 {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n}\n.dv-decoration-11 .decoration-content {\n  position: absolute;\n  top: 0px;\n  left: 0px;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n"]},media:void 0})}),Et,void 0,!1,void 0,!1,l,void 0,void 0);function _t(e){e.component(Pt.name,Pt)}var St=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")};var Ot=function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},It=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.bezierCurveToPolyline=p,t.getBezierCurveLength=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;if(!e)return console.error("getBezierCurveLength: Missing parameters!"),!1;if(!(e instanceof Array))return console.error("getBezierCurveLength: Parameter bezierCurve must be an array!"),!1;if("number"!=typeof t)return console.error("getBezierCurveLength: Parameter precision must be a number!"),!1;var n=l(e,t),r=n.segmentPoints,i=u([r])[0],o=c(i);return o},t.default=void 0;var n=y(W),r=y(P),i=Math.sqrt,o=Math.pow,a=Math.ceil,s=Math.abs;function l(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5,n=e.length-1,i=e[0],o=e[n][2],a=e.slice(1),s=a.map((function(e,t){var n=0===t?i:a[t-1][2];return d.apply(void 0,[n].concat((0,r.default)(e)))})),l=new Array(n).fill(50),c=f(s,l),u=h(c,s,a,t);return u.segmentPoints.push(o),u}function d(e,t,n,r){return function(i){var a=1-i,s=o(a,3),l=o(a,2),d=o(i,3),c=o(i,2);return[e[0]*s+3*t[0]*i*l+3*n[0]*c*a+r[0]*d,e[1]*s+3*t[1]*i*l+3*n[1]*c*a+r[1]*d]}}function c(e){return e.reduce((function(e,t){return e+t}),0)}function u(e){return e.map((function(e,t){return new Array(e.length-1).fill(0).map((function(t,r){return a=e[r],s=e[r+1],l=(0,n.default)(a,2),d=l[0],c=l[1],u=(0,n.default)(s,2),f=u[0],h=u[1],i(o(d-f,2)+o(c-h,2));var a,s,l,d,c,u,f,h}))}))}function f(e,t){return e.map((function(e,n){var r=1/t[n];return new Array(t[n]).fill("").map((function(t,n){return e(n*r)}))}))}function h(e,t,n,r){var i=4,o=1,l=function(){var l=e.reduce((function(e,t){return e+t.length}),0);e.forEach((function(e,t){return e.push(n[t][2])}));var d=u(e),h=d.reduce((function(e,t){return e+t.length}),0),p=d.map((function(e){return c(e)})),g=c(p),v=g/h;if(function(e,t){return e.map((function(e){return e.map((function(e){return s(e-t)}))})).map((function(e){return c(e)})).reduce((function(e,t){return e+t}),0)}(d,v)<=r)return"break";l=a(v/r*l*1.1);var m=p.map((function(e){return a(e/g*l)}));e=f(t,m),l=e.reduce((function(e,t){return e+t.length}),0);var A=JSON.parse(JSON.stringify(e));A.forEach((function(e,t){return e.push(n[t][2])})),h=(d=u(A)).reduce((function(e,t){return e+t.length}),0),p=d.map((function(e){return c(e)})),g=c(p),v=g/h;var C=1/l/10;t.forEach((function(t,n){for(var r=m[n],o=new Array(r).fill("").map((function(e,t){return t/m[n]})),a=0;a<i;a++)for(var s=u([e[n]])[0].map((function(e){return e-v})),l=0,d=0;d<r;d++){if(0===d)return;l+=s[d-1],o[d]-=C*l,o[d]>1&&(o[d]=1),o[d]<0&&(o[d]=0),e[n][d]=t(o[d])}})),i*=4,o++};do{if("break"===l())break}while(i<=1025);return{segmentPoints:e=e.reduce((function(e,t){return e.concat(t)}),[]),cycles:o,rounds:i}}function p(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;if(!e)return console.error("bezierCurveToPolyline: Missing parameters!"),!1;if(!(e instanceof Array))return console.error("bezierCurveToPolyline: Parameter bezierCurve must be an array!"),!1;if("number"!=typeof t)return console.error("bezierCurveToPolyline: Parameter precision must be a number!"),!1;var n=l(e,t),r=n.segmentPoints;return r}var g=p;t.default=g}));C(It);It.bezierCurveToPolyline,It.getBezierCurveLength;var Wt=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=y(W),r=y(P);function i(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.25,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:.25,o=e.length;if(!(o<3||t>=o)){var a=t-1;a<0&&(a=n?o+a:0);var s=t+1;s>=o&&(s=n?s-o:o-1);var l=t+2;l>=o&&(l=n?l-o:o-1);var d=e[a],c=e[t],u=e[s],f=e[l];return[[c[0]+r*(u[0]-d[0]),c[1]+r*(u[1]-d[1])],[u[0]-i*(f[0]-c[0]),u[1]-i*(f[1]-c[1])]]}}function o(e,t){var n=e[0],r=e.slice(-1)[0];return e.push([a(r[1],r[2]),a(n[0],t),t]),e}function a(e,t){var r=(0,n.default)(e,2),i=r[0],o=r[1],a=(0,n.default)(t,2),s=a[0],l=a[1];return[s+(s-i),l+(l-o)]}var s=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.25,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.25;if(!(e instanceof Array))return console.error("polylineToBezierCurve: Parameter polyline must be an array!"),!1;if(e.length<=2)return console.error("polylineToBezierCurve: Converting to a curve requires at least 3 points!"),!1;var s=e[0],l=e.length-1,d=new Array(l).fill(0).map((function(o,s){return[].concat((0,r.default)(i(e,s,t,n,a)),[e[s+1]])}));return t&&o(d,s),d.unshift(e[0]),d};t.default=s}));C(Wt);var Lt=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"bezierCurveToPolyline",{enumerable:!0,get:function(){return It.bezierCurveToPolyline}}),Object.defineProperty(t,"getBezierCurveLength",{enumerable:!0,get:function(){return It.getBezierCurveLength}}),Object.defineProperty(t,"polylineToBezierCurve",{enumerable:!0,get:function(){return n.default}}),t.default=void 0;var n=y(Wt),r={bezierCurveToPolyline:It.bezierCurveToPolyline,getBezierCurveLength:It.getBezierCurveLength,polylineToBezierCurve:n.default};t.default=r}));C(Lt);var jt=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.drawPolylinePath=r,t.drawBezierCurvePath=i,t.default=void 0;var n=y(P);function r(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!e||t.length<2)return!1;r&&e.beginPath(),t.forEach((function(t,r){return t&&(0===r?e.moveTo.apply(e,(0,n.default)(t)):e.lineTo.apply(e,(0,n.default)(t)))})),i&&e.closePath()}function i(e,t){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(!e||!t)return!1;i&&e.beginPath(),r&&e.moveTo.apply(e,(0,n.default)(r)),t.forEach((function(t){return t&&e.bezierCurveTo.apply(e,(0,n.default)(t[0]).concat((0,n.default)(t[1]),(0,n.default)(t[2])))})),o&&e.closePath()}var o={drawPolylinePath:r,drawBezierCurvePath:i};t.default=o}));C(jt);jt.drawPolylinePath,jt.drawBezierCurvePath;var Mt=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.extendNewGraph=function(e,t){if(!e||!t)return void console.error("ExtendNewGraph Missing Parameters!");if(!t.shape)return void console.error("Required attribute of shape to extendNewGraph!");if(!t.validator)return void console.error("Required function of validator to extendNewGraph!");if(!t.draw)return void console.error("Required function of draw to extendNewGraph!");A.set(e,t)},t.default=t.text=t.bezierCurve=t.smoothline=t.polyline=t.regPolygon=t.sector=t.arc=t.ring=t.rect=t.ellipse=t.circle=void 0;var n=y(P),r=y(W),i=y(Lt),o=i.default.polylineToBezierCurve,a=i.default.bezierCurveToPolyline,s={shape:{rx:0,ry:0,r:0},validator:function(e){var t=e.shape,n=t.rx,r=t.ry,i=t.r;return"number"==typeof n&&"number"==typeof r&&"number"==typeof i||(console.error("Circle shape configuration is abnormal!"),!1)},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.rx,o=r.ry,a=r.r;n.arc(i,o,a>0?a:.01,0,2*Math.PI),n.fill(),n.stroke(),n.closePath()},hoverCheck:function(e,t){var n=t.shape,r=n.rx,i=n.ry,o=n.r;return(0,L.checkPointIsInCircle)(e,r,i,o)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.rx,o=n.ry;r.graphCenter=[i,o]},move:function(e,t){var n=e.movementX,r=e.movementY,i=t.shape;this.attr("shape",{rx:i.rx+n,ry:i.ry+r})}};t.circle=s;var l={shape:{rx:0,ry:0,hr:0,vr:0},validator:function(e){var t=e.shape,n=t.rx,r=t.ry,i=t.hr,o=t.vr;return"number"==typeof n&&"number"==typeof r&&"number"==typeof i&&"number"==typeof o||(console.error("Ellipse shape configuration is abnormal!"),!1)},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.rx,o=r.ry,a=r.hr,s=r.vr;n.ellipse(i,o,a>0?a:.01,s>0?s:.01,0,0,2*Math.PI),n.fill(),n.stroke(),n.closePath()},hoverCheck:function(e,t){var n=t.shape,r=n.rx,i=n.ry,o=n.hr,a=n.vr,s=Math.max(o,a),l=Math.min(o,a),d=Math.sqrt(s*s-l*l),c=[r-d,i],u=[r+d,i];return(0,L.getTwoPointDistance)(e,c)+(0,L.getTwoPointDistance)(e,u)<=2*s},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.rx,o=n.ry;r.graphCenter=[i,o]},move:function(e,t){var n=e.movementX,r=e.movementY,i=t.shape;this.attr("shape",{rx:i.rx+n,ry:i.ry+r})}};t.ellipse=l;var d={shape:{x:0,y:0,w:0,h:0},validator:function(e){var t=e.shape,n=t.x,r=t.y,i=t.w,o=t.h;return"number"==typeof n&&"number"==typeof r&&"number"==typeof i&&"number"==typeof o||(console.error("Rect shape configuration is abnormal!"),!1)},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.x,o=r.y,a=r.w,s=r.h;n.rect(i,o,a,s),n.fill(),n.stroke(),n.closePath()},hoverCheck:function(e,t){var n=t.shape,r=n.x,i=n.y,o=n.w,a=n.h;return(0,L.checkPointIsInRect)(e,r,i,o,a)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.x,o=n.y,a=n.w,s=n.h;r.graphCenter=[i+a/2,o+s/2]},move:function(e,t){var n=e.movementX,r=e.movementY,i=t.shape;this.attr("shape",{x:i.x+n,y:i.y+r})}};t.rect=d;var c={shape:{rx:0,ry:0,r:0},validator:function(e){var t=e.shape,n=t.rx,r=t.ry,i=t.r;return"number"==typeof n&&"number"==typeof r&&"number"==typeof i||(console.error("Ring shape configuration is abnormal!"),!1)},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.rx,o=r.ry,a=r.r;n.arc(i,o,a>0?a:.01,0,2*Math.PI),n.stroke(),n.closePath()},hoverCheck:function(e,t){var n=t.shape,r=t.style,i=n.rx,o=n.ry,a=n.r,s=r.lineWidth/2,l=a-s,d=a+s,c=(0,L.getTwoPointDistance)(e,[i,o]);return c>=l&&c<=d},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.rx,o=n.ry;r.graphCenter=[i,o]},move:function(e,t){var n=e.movementX,r=e.movementY,i=t.shape;this.attr("shape",{rx:i.rx+n,ry:i.ry+r})}};t.ring=c;var u={shape:{rx:0,ry:0,r:0,startAngle:0,endAngle:0,clockWise:!0},validator:function(e){var t=e.shape;return!["rx","ry","r","startAngle","endAngle"].find((function(e){return"number"!=typeof t[e]}))||(console.error("Arc shape configuration is abnormal!"),!1)},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.rx,o=r.ry,a=r.r,s=r.startAngle,l=r.endAngle,d=r.clockWise;n.arc(i,o,a>0?a:.001,s,l,!d),n.stroke(),n.closePath()},hoverCheck:function(e,t){var n=t.shape,r=t.style,i=n.rx,o=n.ry,a=n.r,s=n.startAngle,l=n.endAngle,d=n.clockWise,c=r.lineWidth/2,u=a-c,f=a+c;return!(0,L.checkPointIsInSector)(e,i,o,u,s,l,d)&&(0,L.checkPointIsInSector)(e,i,o,f,s,l,d)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.rx,o=n.ry;r.graphCenter=[i,o]},move:function(e,t){var n=e.movementX,r=e.movementY,i=t.shape;this.attr("shape",{rx:i.rx+n,ry:i.ry+r})}};t.arc=u;var f={shape:{rx:0,ry:0,r:0,startAngle:0,endAngle:0,clockWise:!0},validator:function(e){var t=e.shape;return!["rx","ry","r","startAngle","endAngle"].find((function(e){return"number"!=typeof t[e]}))||(console.error("Sector shape configuration is abnormal!"),!1)},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.rx,o=r.ry,a=r.r,s=r.startAngle,l=r.endAngle,d=r.clockWise;n.arc(i,o,a>0?a:.01,s,l,!d),n.lineTo(i,o),n.closePath(),n.stroke(),n.fill()},hoverCheck:function(e,t){var n=t.shape,r=n.rx,i=n.ry,o=n.r,a=n.startAngle,s=n.endAngle,l=n.clockWise;return(0,L.checkPointIsInSector)(e,r,i,o,a,s,l)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.rx,o=n.ry;r.graphCenter=[i,o]},move:function(e,t){var n=e.movementX,r=e.movementY,i=t.shape,o=i.rx,a=i.ry;this.attr("shape",{rx:o+n,ry:a+r})}};t.sector=f;var h={shape:{rx:0,ry:0,r:0,side:0},validator:function(e){var t=e.shape,n=t.side;return["rx","ry","r","side"].find((function(e){return"number"!=typeof t[e]}))?(console.error("RegPolygon shape configuration is abnormal!"),!1):!(n<3)||(console.error("RegPolygon at least trigon!"),!1)},draw:function(e,t){var n=e.ctx,r=t.shape,i=t.cache;n.beginPath();var o=r.rx,a=r.ry,s=r.r,l=r.side;if(!i.points||i.rx!==o||i.ry!==a||i.r!==s||i.side!==l){var d=(0,L.getRegularPolygonPoints)(o,a,s,l);Object.assign(i,{points:d,rx:o,ry:a,r:s,side:l})}var c=i.points;(0,jt.drawPolylinePath)(n,c),n.closePath(),n.stroke(),n.fill()},hoverCheck:function(e,t){var n=t.cache.points;return(0,L.checkPointIsInPolygon)(e,n)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.rx,o=n.ry;r.graphCenter=[i,o]},move:function(e,t){var n=e.movementX,i=e.movementY,o=t.shape,a=t.cache,s=o.rx,l=o.ry;a.rx+=n,a.ry+=i,this.attr("shape",{rx:s+n,ry:l+i}),a.points=a.points.map((function(e){var t=(0,r.default)(e,2),o=t[0],a=t[1];return[o+n,a+i]}))}};t.regPolygon=h;var p={shape:{points:[],close:!1},validator:function(e){return e.shape.points instanceof Array||(console.error("Polyline points should be an array!"),!1)},draw:function(e,t){var n=e.ctx,r=t.shape,i=t.style.lineWidth;n.beginPath();var o=r.points,a=r.close;1===i&&(o=(0,L.eliminateBlur)(o)),(0,jt.drawPolylinePath)(n,o),a?(n.closePath(),n.fill(),n.stroke()):n.stroke()},hoverCheck:function(e,t){var n=t.shape,r=t.style,i=n.points,o=n.close,a=r.lineWidth;return o?(0,L.checkPointIsInPolygon)(e,i):(0,L.checkPointIsNearPolyline)(e,i,a)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.points;r.graphCenter=i[0]},move:function(e,t){var n=e.movementX,i=e.movementY,o=t.shape.points.map((function(e){var t=(0,r.default)(e,2),o=t[0],a=t[1];return[o+n,a+i]}));this.attr("shape",{points:o})}};t.polyline=p;var g={shape:{points:[],close:!1},validator:function(e){return e.shape.points instanceof Array||(console.error("Smoothline points should be an array!"),!1)},draw:function(e,t){var n=e.ctx,r=t.shape,i=t.cache,s=r.points,l=r.close;if(!i.points||i.points.toString()!==s.toString()){var d=o(s,l),c=a(d);Object.assign(i,{points:(0,L.deepClone)(s,!0),bezierCurve:d,hoverPoints:c})}var u=i.bezierCurve;n.beginPath(),(0,jt.drawBezierCurvePath)(n,u.slice(1),u[0]),l?(n.closePath(),n.fill(),n.stroke()):n.stroke()},hoverCheck:function(e,t){var n=t.cache,r=t.shape,i=t.style,o=n.hoverPoints,a=r.close,s=i.lineWidth;return a?(0,L.checkPointIsInPolygon)(e,o):(0,L.checkPointIsNearPolyline)(e,o,s)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.points;r.graphCenter=i[0]},move:function(e,t){var i=e.movementX,o=e.movementY,a=t.shape,s=t.cache,l=a.points.map((function(e){var t=(0,r.default)(e,2),n=t[0],a=t[1];return[n+i,a+o]}));s.points=l;var d=(0,r.default)(s.bezierCurve[0],2),c=d[0],u=d[1],f=s.bezierCurve.slice(1);s.bezierCurve=[[c+i,u+o]].concat((0,n.default)(f.map((function(e){return e.map((function(e){var t=(0,r.default)(e,2),n=t[0],a=t[1];return[n+i,a+o]}))})))),s.hoverPoints=s.hoverPoints.map((function(e){var t=(0,r.default)(e,2),n=t[0],a=t[1];return[n+i,a+o]})),this.attr("shape",{points:l})}};t.smoothline=g;var v={shape:{points:[],close:!1},validator:function(e){return e.shape.points instanceof Array||(console.error("BezierCurve points should be an array!"),!1)},draw:function(e,t){var n=e.ctx,r=t.shape,i=t.cache,o=r.points,s=r.close;if(!i.points||i.points.toString()!==o.toString()){var l=a(o,20);Object.assign(i,{points:(0,L.deepClone)(o,!0),hoverPoints:l})}n.beginPath(),(0,jt.drawBezierCurvePath)(n,o.slice(1),o[0]),s?(n.closePath(),n.fill(),n.stroke()):n.stroke()},hoverCheck:function(e,t){var n=t.cache,r=t.shape,i=t.style,o=n.hoverPoints,a=r.close,s=i.lineWidth;return a?(0,L.checkPointIsInPolygon)(e,o):(0,L.checkPointIsNearPolyline)(e,o,s)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.points;r.graphCenter=i[0]},move:function(e,t){var i=e.movementX,o=e.movementY,a=t.shape,s=t.cache,l=a.points,d=(0,r.default)(l[0],2),c=d[0],u=d[1],f=l.slice(1),h=[[c+i,u+o]].concat((0,n.default)(f.map((function(e){return e.map((function(e){var t=(0,r.default)(e,2),n=t[0],a=t[1];return[n+i,a+o]}))}))));s.points=h,s.hoverPoints=s.hoverPoints.map((function(e){var t=(0,r.default)(e,2),n=t[0],a=t[1];return[n+i,a+o]})),this.attr("shape",{points:h})}};t.bezierCurve=v;var m={shape:{content:"",position:[],maxWidth:void 0,rowGap:0},validator:function(e){var t=e.shape,n=t.content,r=t.position,i=t.rowGap;return"string"!=typeof n?(console.error("Text content should be a string!"),!1):r instanceof Array?"number"==typeof i||(console.error("Text rowGap should be a number!"),!1):(console.error("Text position should be an array!"),!1)},draw:function(e,t){var i=e.ctx,o=t.shape,a=o.content,s=o.position,l=o.maxWidth,d=o.rowGap,c=i.textBaseline,u=i.font,f=parseInt(u.replace(/\D/g,"")),h=s,p=(0,r.default)(h,2),g=p[0],v=p[1],m=(a=a.split("\n")).length,A=f+d,C=m*A-d,b=0;"middle"===c&&(b=C/2,v+=f/2),"bottom"===c&&(b=C,v+=f),s=new Array(m).fill(0).map((function(e,t){return[g,v+t*A-b]})),i.beginPath(),a.forEach((function(e,t){i.fillText.apply(i,[e].concat((0,n.default)(s[t]),[l])),i.strokeText.apply(i,[e].concat((0,n.default)(s[t]),[l]))})),i.closePath()},hoverCheck:function(e,t){t.shape,t.style;return!1},setGraphCenter:function(e,t){var r=t.shape,i=t.style,o=r.position;i.graphCenter=(0,n.default)(o)},move:function(e,t){var n=e.movementX,i=e.movementY,o=t.shape,a=(0,r.default)(o.position,2),s=a[0],l=a[1];this.attr("shape",{position:[s+n,l+i]})}};t.text=m;var A=new Map([["circle",s],["ellipse",l],["rect",d],["ring",c],["arc",u],["sector",f],["regPolygon",h],["polyline",p],["smoothline",g],["bezierCurve",v],["text",m]]),C=A;t.default=C}));C(Mt);Mt.extendNewGraph,Mt.text,Mt.bezierCurve,Mt.smoothline,Mt.polyline,Mt.regPolygon,Mt.sector,Mt.arc,Mt.ring,Mt.rect,Mt.ellipse,Mt.circle;var Ft=b((function(e){var t=function(e){var t=Object.prototype,n=t.hasOwnProperty,r="function"==typeof Symbol?Symbol:{},i=r.iterator||"@@iterator",o=r.asyncIterator||"@@asyncIterator",a=r.toStringTag||"@@toStringTag";function s(e,t,n,r){var i=t&&t.prototype instanceof c?t:c,o=Object.create(i.prototype),a=new x(r||[]);return o._invoke=function(e,t,n){var r="suspendedStart";return function(i,o){if("executing"===r)throw new Error("Generator is already running");if("completed"===r){if("throw"===i)throw o;return k()}for(n.method=i,n.arg=o;;){var a=n.delegate;if(a){var s=C(a,n);if(s){if(s===d)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===r)throw r="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r="executing";var c=l(e,t,n);if("normal"===c.type){if(r=n.done?"completed":"suspendedYield",c.arg===d)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(r="completed",n.method="throw",n.arg=c.arg)}}}(e,n,a),o}function l(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}e.wrap=s;var d={};function c(){}function u(){}function f(){}var h={};h[i]=function(){return this};var p=Object.getPrototypeOf,g=p&&p(p(w([])));g&&g!==t&&n.call(g,i)&&(h=g);var v=f.prototype=c.prototype=Object.create(h);function m(e){["next","throw","return"].forEach((function(t){e[t]=function(e){return this._invoke(t,e)}}))}function A(e,t){var r;this._invoke=function(i,o){function a(){return new t((function(r,a){!function r(i,o,a,s){var d=l(e[i],e,o);if("throw"!==d.type){var c=d.arg,u=c.value;return u&&"object"==typeof u&&n.call(u,"__await")?t.resolve(u.__await).then((function(e){r("next",e,a,s)}),(function(e){r("throw",e,a,s)})):t.resolve(u).then((function(e){c.value=e,a(c)}),(function(e){return r("throw",e,a,s)}))}s(d.arg)}(i,o,r,a)}))}return r=r?r.then(a,a):a()}}function C(e,t){var n=e.iterator[t.method];if(void 0===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,C(e,t),"throw"===t.method))return d;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return d}var r=l(n,e.iterator,t.arg);if("throw"===r.type)return t.method="throw",t.arg=r.arg,t.delegate=null,d;var i=r.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function b(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function y(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function x(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(b,this),this.reset(!0)}function w(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var r=-1,o=function t(){for(;++r<e.length;)if(n.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:k}}function k(){return{value:void 0,done:!0}}return u.prototype=v.constructor=f,f.constructor=u,f[a]=u.displayName="GeneratorFunction",e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===u||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,f):(e.__proto__=f,a in e||(e[a]="GeneratorFunction")),e.prototype=Object.create(v),e},e.awrap=function(e){return{__await:e}},m(A.prototype),A.prototype[o]=function(){return this},e.AsyncIterator=A,e.async=function(t,n,r,i,o){void 0===o&&(o=Promise);var a=new A(s(t,n,r,i),o);return e.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},m(v),v[a]="Generator",v[i]=function(){return this},v.toString=function(){return"[object Generator]"},e.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},e.values=w,x.prototype={constructor:x,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(y),!e)for(var t in this)"t"===t.charAt(0)&&n.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(n,r){return a.type="throw",a.arg=e,t.next=n,r&&(t.method="next",t.arg=void 0),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],a=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var s=n.call(o,"catchLoc"),l=n.call(o,"finallyLoc");if(s&&l){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;r>=0;--r){var i=this.tryEntries[r];if(i.tryLoc<=this.prev&&n.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var a=o?o.completion:{};return a.type=e,a.arg=t,o?(this.method="next",this.next=o.finallyLoc,d):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),d},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),y(n),d}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;y(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:w(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=void 0),d}},e}(e.exports);try{regeneratorRuntime=t}catch(e){Function("r","regeneratorRuntime = r")(t)}}));function Rt(e,t,n,r,i,o,a){try{var s=e[o](a),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}var Gt=function(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function a(e){Rt(o,r,i,a,s,"next",e)}function s(e){Rt(o,r,i,a,s,"throw",e)}a(void 0)}))}},Dt=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=y(P),r=y(St),i=function e(t){(0,r.default)(this,e),this.colorProcessor(t);Object.assign(this,{fill:[0,0,0,1],stroke:[0,0,0,0],opacity:1,lineCap:null,lineJoin:null,lineDash:null,lineDashOffset:null,shadowBlur:0,shadowColor:[0,0,0,0],shadowOffsetX:0,shadowOffsetY:0,lineWidth:0,graphCenter:null,scale:null,rotate:null,translate:null,hoverCursor:"pointer",fontStyle:"normal",fontVarient:"normal",fontWeight:"normal",fontSize:10,fontFamily:"Arial",textAlign:"center",textBaseline:"middle",gradientColor:null,gradientType:"linear",gradientParams:null,gradientWith:"stroke",gradientStops:"auto",colors:null},t)};t.default=i,i.prototype.colorProcessor=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=t?Ee.getColorFromRgbValue:Ee.getRgbaValue,r=["fill","stroke","shadowColor"],i=Object.keys(e),o=i.filter((function(e){return r.find((function(t){return t===e}))}));o.forEach((function(t){return e[t]=n(e[t])}));var a=e.gradientColor,s=e.colors;if(a&&(e.gradientColor=a.map((function(e){return n(e)}))),s){var l=Object.keys(s);l.forEach((function(e){return s[e]=n(s[e])}))}},i.prototype.initStyle=function(e){!function(e,t){e.save();var r=t.graphCenter,i=t.rotate,o=t.scale,a=t.translate;if(!(r instanceof Array))return;e.translate.apply(e,(0,n.default)(r)),i&&e.rotate(i*Math.PI/180);o instanceof Array&&e.scale.apply(e,(0,n.default)(o));a&&e.translate.apply(e,(0,n.default)(a));e.translate(-r[0],-r[1])}(e,this),function(e,t){var r=t.fill,i=t.stroke,a=t.shadowColor,s=t.opacity;o.forEach((function(n){(n||"number"==typeof n)&&(e[n]=t[n])})),r=(0,n.default)(r),i=(0,n.default)(i),a=(0,n.default)(a),r[3]*=s,i[3]*=s,a[3]*=s,e.fillStyle=(0,Ee.getColorFromRgbValue)(r),e.strokeStyle=(0,Ee.getColorFromRgbValue)(i),e.shadowColor=(0,Ee.getColorFromRgbValue)(a);var l=t.lineDash,d=t.shadowBlur;l&&(l=l.map((function(e){return e>=0?e:0})),e.setLineDash(l));"number"==typeof d&&(e.shadowBlur=d>0?d:.001);var c=t.fontStyle,u=t.fontVarient,f=t.fontWeight,h=t.fontSize,p=t.fontFamily;e.font=c+" "+u+" "+f+" "+h+"px "+p}(e,this),function(e,t){if(!function(e){var t=e.gradientColor,n=e.gradientParams,r=e.gradientType,i=e.gradientWith,o=e.gradientStops;if(!t||!n)return!1;if(1===t.length)return console.warn("The gradient needs to provide at least two colors"),!1;if("linear"!==r&&"radial"!==r)return console.warn("GradientType only supports linear or radial, current value is "+r),!1;var a=n.length;if("linear"===r&&4!==a||"radial"===r&&6!==a)return console.warn("The expected length of gradientParams is "+("linear"===r?"4":"6")),!1;if("fill"!==i&&"stroke"!==i)return console.warn("GradientWith only supports fill or stroke, current value is "+i),!1;if("auto"!==o&&!(o instanceof Array))return console.warn("gradientStops only supports 'auto' or Number Array ([0, .5, 1]), current value is "+o),!1;return!0}(t))return;var r=t.gradientColor,i=t.gradientParams,o=t.gradientType,a=t.gradientWith,s=t.gradientStops,l=t.opacity;r=(r=r.map((function(e){var t=e[3]*l,r=(0,n.default)(e);return r[3]=t,r}))).map((function(e){return(0,Ee.getColorFromRgbValue)(e)})),"auto"===s&&(s=function(e){var t=1/(e.length-1);return e.map((function(e,n){return t*n}))}(r));var d=e["create".concat(o.slice(0,1).toUpperCase()+o.slice(1),"Gradient")].apply(e,(0,n.default)(i));s.forEach((function(e,t){return d.addColorStop(e,r[t])})),e["".concat(a,"Style")]=d}(e,this)};var o=["lineCap","lineJoin","lineDashOffset","shadowOffsetX","shadowOffsetY","lineWidth","textAlign","textBaseline"];i.prototype.restoreTransform=function(e){e.restore()},i.prototype.update=function(e){this.colorProcessor(e),Object.assign(this,e)},i.prototype.getStyle=function(){var e=(0,L.deepClone)(this,!0);return this.colorProcessor(e,!0),e}}));C(Dt);var zt=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.easeInOutBounce=t.easeOutBounce=t.easeInBounce=t.easeInOutElastic=t.easeOutElastic=t.easeInElastic=t.easeInOutBack=t.easeOutBack=t.easeInBack=t.easeInOutQuint=t.easeOutQuint=t.easeInQuint=t.easeInOutQuart=t.easeOutQuart=t.easeInQuart=t.easeInOutCubic=t.easeOutCubic=t.easeInCubic=t.easeInOutQuad=t.easeOutQuad=t.easeInQuad=t.easeInOutSine=t.easeOutSine=t.easeInSine=t.linear=void 0;var n=[[[0,1],"",[.33,.67]],[[1,0],[.67,.33]]];t.linear=n;var r=[[[0,1]],[[.538,.564],[.169,.912],[.88,.196]],[[1,0]]];t.easeInSine=r;var i=[[[0,1]],[[.444,.448],[.169,.736],[.718,.16]],[[1,0]]];t.easeOutSine=i;var o=[[[0,1]],[[.5,.5],[.2,1],[.8,0]],[[1,0]]];t.easeInOutSine=o;var a=[[[0,1]],[[.55,.584],[.231,.904],[.868,.264]],[[1,0]]];t.easeInQuad=a;var s=[[[0,1]],[[.413,.428],[.065,.816],[.76,.04]],[[1,0]]];t.easeOutQuad=s;var l=[[[0,1]],[[.5,.5],[.3,.9],[.7,.1]],[[1,0]]];t.easeInOutQuad=l;var d=[[[0,1]],[[.679,.688],[.366,.992],[.992,.384]],[[1,0]]];t.easeInCubic=d;var c=[[[0,1]],[[.321,.312],[.008,.616],[.634,.008]],[[1,0]]];t.easeOutCubic=c;var u=[[[0,1]],[[.5,.5],[.3,1],[.7,0]],[[1,0]]];t.easeInOutCubic=u;var f=[[[0,1]],[[.812,.74],[.611,.988],[1.013,.492]],[[1,0]]];t.easeInQuart=f;var h=[[[0,1]],[[.152,.244],[.001,.448],[.285,-.02]],[[1,0]]];t.easeOutQuart=h;var p=[[[0,1]],[[.5,.5],[.4,1],[.6,0]],[[1,0]]];t.easeInOutQuart=p;var g=[[[0,1]],[[.857,.856],[.714,1],[1,.712]],[[1,0]]];t.easeInQuint=g;var v=[[[0,1]],[[.108,.2],[.001,.4],[.214,-.012]],[[1,0]]];t.easeOutQuint=v;var m=[[[0,1]],[[.5,.5],[.5,1],[.5,0]],[[1,0]]];t.easeInOutQuint=m;var A=[[[0,1]],[[.667,.896],[.38,1.184],[.955,.616]],[[1,0]]];t.easeInBack=A;var C=[[[0,1]],[[.335,.028],[.061,.22],[.631,-.18]],[[1,0]]];t.easeOutBack=C;var b=[[[0,1]],[[.5,.5],[.4,1.4],[.6,-.4]],[[1,0]]];t.easeInOutBack=b;var y=[[[0,1]],[[.474,.964],[.382,.988],[.557,.952]],[[.619,1.076],[.565,1.088],[.669,1.08]],[[.77,.916],[.712,.924],[.847,.904]],[[.911,1.304],[.872,1.316],[.961,1.34]],[[1,0]]];t.easeInElastic=y;var x=[[[0,1]],[[.073,-.32],[.034,-.328],[.104,-.344]],[[.191,.092],[.11,.06],[.256,.08]],[[.31,-.076],[.26,-.068],[.357,-.076]],[[.432,.032],[.362,.028],[.683,-.004]],[[1,0]]];t.easeOutElastic=x;var w=[[[0,1]],[[.21,.94],[.167,.884],[.252,.98]],[[.299,1.104],[.256,1.092],[.347,1.108]],[[.5,.496],[.451,.672],[.548,.324]],[[.696,-.108],[.652,-.112],[.741,-.124]],[[.805,.064],[.756,.012],[.866,.096]],[[1,0]]];t.easeInOutElastic=w;var k=[[[0,1]],[[.148,1],[.075,.868],[.193,.848]],[[.326,1],[.276,.836],[.405,.712]],[[.6,1],[.511,.708],[.671,.348]],[[1,0]]];t.easeInBounce=k;var E=[[[0,1]],[[.357,.004],[.27,.592],[.376,.252]],[[.604,-.004],[.548,.312],[.669,.184]],[[.82,0],[.749,.184],[.905,.132]],[[1,0]]];t.easeOutBounce=E;var B=[[[0,1]],[[.102,1],[.05,.864],[.117,.86]],[[.216,.996],[.208,.844],[.227,.808]],[[.347,.996],[.343,.8],[.48,.292]],[[.635,.004],[.511,.676],[.656,.208]],[[.787,0],[.76,.2],[.795,.144]],[[.905,-.004],[.899,.164],[.944,.144]],[[1,0]]];t.easeInOutBounce=B;var P=new Map([["linear",n],["easeInSine",r],["easeOutSine",i],["easeInOutSine",o],["easeInQuad",a],["easeOutQuad",s],["easeInOutQuad",l],["easeInCubic",d],["easeOutCubic",c],["easeInOutCubic",u],["easeInQuart",f],["easeOutQuart",h],["easeInOutQuart",p],["easeInQuint",g],["easeOutQuint",v],["easeInOutQuint",m],["easeInBack",A],["easeOutBack",C],["easeInOutBack",b],["easeInElastic",y],["easeOutElastic",x],["easeInOutElastic",w],["easeInBounce",k],["easeOutBounce",E],["easeInOutBounce",B]]);t.default=P}));C(zt);zt.easeInOutBounce,zt.easeOutBounce,zt.easeInBounce,zt.easeInOutElastic,zt.easeOutElastic,zt.easeInElastic,zt.easeInOutBack,zt.easeOutBack,zt.easeInBack,zt.easeInOutQuint,zt.easeOutQuint,zt.easeInQuint,zt.easeInOutQuart,zt.easeOutQuart,zt.easeInQuart,zt.easeInOutCubic,zt.easeOutCubic,zt.easeInCubic,zt.easeInOutQuad,zt.easeOutQuad,zt.easeInQuad,zt.easeInOutSine,zt.easeOutSine,zt.easeInSine,zt.linear;var Tt=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.transition=o,t.injectNewCurve=function(e,t){if(!e||!t)return void console.error("InjectNewCurve Missing Parameters!");i.default.set(e,t)},t.default=void 0;var n=y(W),r=y(_),i=y(zt);function o(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:30,i=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(!a.apply(void 0,arguments))return!1;try{var o=s(e),c=l(o,r);return i&&"number"!=typeof n?u(t,n,c):d(t,n,c)}catch(e){return console.warn("Transition parameter may be abnormal!"),[n]}}function a(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:30;if(!e||!1===t||!1===n||!o)return console.error("transition: Missing Parameters!"),!1;if((0,r.default)(t)!==(0,r.default)(n))return console.error("transition: Inconsistent Status Types!"),!1;var a=(0,r.default)(n);return"string"!==a&&"boolean"!==a&&e.length?(i.default.has(e)||e instanceof Array||console.warn("transition: Transition curve not found, default curve will be used!"),!0):(console.error("transition: Unsupported Data Type of State!"),!1)}function s(e){return i.default.has(e)?i.default.get(e):e instanceof Array?e:i.default.get("linear")}function l(e,t){var r=1/(t-1);return new Array(t).fill(0).map((function(e,t){return t*r})).map((function(t){return function(e,t){var r=function(e,t){var n=e.length-1,r="",i="";e.findIndex((function(o,a){if(a!==n){r=o,i=e[a+1];var s=r[0][0],l=i[0][0];return t>=s&&t<l}}));var o=r[0],a=r[2]||r[0],s=i[1]||i[0],l=i[0];return[o,a,s,l]}(e,t),i=function(e,t){var n=e[0][0],r=e[3][0];return(t-n)/(r-n)}(r,t);return function(e,t){var r=(0,n.default)(e,4),i=(0,n.default)(r[0],2)[1],o=(0,n.default)(r[1],2)[1],a=(0,n.default)(r[2],2)[1],s=(0,n.default)(r[3],2)[1],l=Math.pow,d=1-t,c=i*l(d,3),u=3*o*t*l(d,2),f=3*a*l(t,2)*d,h=s*l(t,3);return 1-(c+u+f+h)}(r,i)}(e,t)}))}function d(e,t,n){var r="object";return"number"==typeof e&&(r="number"),e instanceof Array&&(r="array"),"number"===r?function(e,t,n){var r=t-e;return n.map((function(t){return e+r*t}))}(e,t,n):"array"===r?c(e,t,n):"object"===r?function(e,t,n){var r=Object.keys(t),i=r.map((function(t){return e[t]})),o=r.map((function(e){return t[e]}));return c(i,o,n).map((function(e){var t={};return e.forEach((function(e,n){return t[r[n]]=e})),t}))}(e,t,n):n.map((function(e){return t}))}function c(e,t,n){var r=t.map((function(t,n){return"number"==typeof t&&t-e[n]}));return n.map((function(n){return r.map((function(r,i){return!1===r?t[i]:e[i]+r*n}))}))}function u(e,t,n){var i=d(e,t,n),o=function(o){var a=e[o],s=t[o];if("object"!==(0,r.default)(s))return"continue";var l=u(a,s,n);i.forEach((function(e,t){return e[o]=l[t]}))};for(var a in t)o(a);return i}var f=o;t.default=f}));C(Tt);Tt.transition,Tt.injectNewCurve;var Yt=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=y(Ft),r=y(Gt),i=y(_),o=y(P),a=y(St),s=y(Dt),l=y(Tt),d=function e(t,n){(0,a.default)(this,e);var r={visible:!0,drag:!1,hover:!1,index:1,animationDelay:0,animationFrame:30,animationCurve:"linear",animationPause:!1,hoverRect:null,mouseEnter:null,mouseOuter:null,click:null};(n=(0,L.deepClone)(n,!0)).shape||(n.shape={}),n.style||(n.style={});var i=Object.assign({},t.shape,n.shape);Object.assign(r,n,{status:"static",animationRoot:[],animationKeys:[],animationFrameState:[],cache:{}}),Object.assign(this,t,r),this.shape=i,this.style=new s.default(n.style),this.addedProcessor()};function c(e){return new Promise((function(t){setTimeout(t,e)}))}t.default=d,d.prototype.addedProcessor=function(){"function"==typeof this.setGraphCenter&&this.setGraphCenter(null,this),"function"==typeof this.added&&this.added(this)},d.prototype.drawProcessor=function(e,t){var n=e.ctx;t.style.initStyle(n),"function"==typeof this.beforeDraw&&this.beforeDraw(this,e),t.draw(e,t),"function"==typeof this.drawed&&this.drawed(this,e),t.style.restoreTransform(n)},d.prototype.hoverCheckProcessor=function(e,t){var n=t.hoverRect,r=t.style,i=t.hoverCheck,a=r.graphCenter,s=r.rotate,l=r.scale,d=r.translate;return a&&(s&&(e=(0,L.getRotatePointPos)(-s,e,a)),l&&(e=(0,L.getScalePointPos)(l.map((function(e){return 1/e})),e,a)),d&&(e=(0,L.getTranslatePointPos)(d.map((function(e){return-1*e})),e))),n?L.checkPointIsInRect.apply(void 0,[e].concat((0,o.default)(n))):i(e,this)},d.prototype.moveProcessor=function(e){this.move(e,this),"function"==typeof this.beforeMove&&this.beforeMove(e,this),"function"==typeof this.setGraphCenter&&this.setGraphCenter(e,this),"function"==typeof this.moved&&this.moved(e,this)},d.prototype.attr=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;if(!e||void 0===t)return!1;var n="object"===(0,i.default)(this[e]);n&&(t=(0,L.deepClone)(t,!0));var r=this.render;"style"===e?this.style.update(t):n?Object.assign(this[e],t):this[e]=t,"index"===e&&r.sortGraphsByIndex(),r.drawAllGraph()},d.prototype.animation=function(){var e=(0,r.default)(n.default.mark((function e(t,i){var o,a,s,d,u,f,h,p,g,v=arguments;return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(o=v.length>2&&void 0!==v[2]&&v[2],"shape"===t||"style"===t){e.next=4;break}return console.error("Only supported shape and style animation!"),e.abrupt("return");case 4:if(i=(0,L.deepClone)(i,!0),"style"===t&&this.style.colorProcessor(i),a=this[t],s=Object.keys(i),d={},s.forEach((function(e){return d[e]=a[e]})),u=this.animationFrame,f=this.animationCurve,h=this.animationDelay,p=(0,l.default)(f,d,i,u,!0),this.animationRoot.push(a),this.animationKeys.push(s),this.animationFrameState.push(p),!o){e.next=17;break}return e.abrupt("return");case 17:if(!(h>0)){e.next=20;break}return e.next=20,c(h);case 20:return g=this.render,e.abrupt("return",new Promise(function(){var e=(0,r.default)(n.default.mark((function e(t){return n.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,g.launchAnimation();case 2:t();case 3:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()));case 22:case"end":return e.stop()}}),e,this)})));return function(t,n){return e.apply(this,arguments)}}(),d.prototype.turnNextAnimationFrame=function(e){var t=this.animationDelay,n=this.animationRoot,r=this.animationKeys,i=this.animationFrameState;this.animationPause||Date.now()-e<t||(n.forEach((function(e,t){r[t].forEach((function(n){e[n]=i[t][0][n]}))})),i.forEach((function(e,t){e.shift();var i=0===e.length;i&&(n[t]=null),i&&(r[t]=null)})),this.animationFrameState=i.filter((function(e){return e.length})),this.animationRoot=n.filter((function(e){return e})),this.animationKeys=r.filter((function(e){return e})))},d.prototype.animationEnd=function(){var e=this.animationFrameState,t=this.animationKeys,n=this.animationRoot,r=this.render;return n.forEach((function(n,r){var i=t[r],o=e[r].pop();i.forEach((function(e){return n[e]=o[e]}))})),this.animationFrameState=[],this.animationKeys=[],this.animationRoot=[],r.drawAllGraph()},d.prototype.pauseAnimation=function(){this.attr("animationPause",!0)},d.prototype.playAnimation=function(){var e=this.render;return this.attr("animationPause",!1),new Promise(function(){var t=(0,r.default)(n.default.mark((function t(r){return n.default.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.launchAnimation();case 2:r();case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},d.prototype.delProcessor=function(e){var t=this,n=e.graphs,r=n.findIndex((function(e){return e===t}));-1!==r&&("function"==typeof this.beforeDelete&&this.beforeDelete(this),n.splice(r,1,null),"function"==typeof this.deleted&&this.deleted(this))}}));C(Yt);var Nt=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=y(Ot),r=y(P),i=y(St),o=y(Ee),a=y(Lt),s=y(Mt),l=y(Yt);function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var c=function e(t){if((0,i.default)(this,e),t){var n=t.getContext("2d"),r=t.clientWidth,s=t.clientHeight,l=[r,s];t.setAttribute("width",r),t.setAttribute("height",s),this.ctx=n,this.area=l,this.animationStatus=!1,this.graphs=[],this.color=o.default,this.bezierCurve=a.default,t.addEventListener("mousedown",f.bind(this)),t.addEventListener("mousemove",h.bind(this)),t.addEventListener("mouseup",p.bind(this))}else console.error("CRender Missing parameters!")};function u(e,t){var n=this.graphs;!function(e){return e.find((function(e){return!e.animationPause&&e.animationFrameState.length}))}(n)?e():(n.forEach((function(e){return e.turnNextAnimationFrame(t)})),this.drawAllGraph(),requestAnimationFrame(u.bind(this,e,t)))}function f(e){var t=this.graphs.find((function(e){return"hover"===e.status}));t&&(t.status="active")}function h(e){var t=[e.offsetX,e.offsetY],n=this.graphs,r=n.find((function(e){return"active"===e.status||"drag"===e.status}));if(r){if(!r.drag)return;return"function"!=typeof r.move?void console.error("No move method is provided, cannot be dragged!"):(r.moveProcessor(e),void(r.status="drag"))}var i=n.find((function(e){return"hover"===e.status})),o=n.filter((function(e){return e.hover&&("function"==typeof e.hoverCheck||e.hoverRect)})).find((function(e){return e.hoverCheckProcessor(t,e)}));document.body.style.cursor=o?o.style.hoverCursor:"default";var a=!1,s=!1;if(i&&(a="function"==typeof i.mouseOuter),o&&(s="function"==typeof o.mouseEnter),o||i){if(!o&&i)return a&&i.mouseOuter(e,i),void(i.status="static");if(!o||o!==i)return o&&!i?(s&&o.mouseEnter(e,o),void(o.status="hover")):void(o&&i&&o!==i&&(a&&i.mouseOuter(e,i),i.status="static",s&&o.mouseEnter(e,o),o.status="hover"))}}function p(e){var t=this.graphs,n=t.find((function(e){return"active"===e.status})),r=t.find((function(e){return"drag"===e.status}));n&&"function"==typeof n.click&&n.click(e,n),t.forEach((function(e){return e&&(e.status="static")})),n&&(n.status="hover"),r&&(r.status="hover")}t.default=c,c.prototype.clearArea=function(){var e,t=this.area;(e=this.ctx).clearRect.apply(e,[0,0].concat((0,r.default)(t)))},c.prototype.add=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.name;if(t){var n=s.default.get(t);if(n){var r=new l.default(n,e);if(r.validator(r))return r.render=this,this.graphs.push(r),this.sortGraphsByIndex(),this.drawAllGraph(),r}else console.warn("No corresponding graph configuration found!")}else console.error("add Missing parameters!")},c.prototype.sortGraphsByIndex=function(){this.graphs.sort((function(e,t){return e.index>t.index?1:e.index===t.index?0:e.index<t.index?-1:void 0}))},c.prototype.delGraph=function(e){"function"==typeof e.delProcessor&&(e.delProcessor(this),this.graphs=this.graphs.filter((function(e){return e})),this.drawAllGraph())},c.prototype.delAllGraph=function(){var e=this;this.graphs.forEach((function(t){return t.delProcessor(e)})),this.graphs=this.graphs.filter((function(e){return e})),this.drawAllGraph()},c.prototype.drawAllGraph=function(){var e=this;this.clearArea(),this.graphs.filter((function(e){return e&&e.visible})).forEach((function(t){return t.drawProcessor(e,t)}))},c.prototype.launchAnimation=function(){var e=this;if(!this.animationStatus)return this.animationStatus=!0,new Promise((function(t){u.call(e,(function(){e.animationStatus=!1,t()}),Date.now())}))},c.prototype.clone=function(e){var t=e.style.getStyle(),r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(r,!0).forEach((function(t){(0,n.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(r).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({},e,{style:t});return delete r.render,r=(0,L.deepClone)(r,!0),this.add(r)}}));C(Nt);var Xt=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"CRender",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(t,"extendNewGraph",{enumerable:!0,get:function(){return Mt.extendNewGraph}}),t.default=void 0;var n=y(Nt),r=n.default;t.default=r})),$t=C(Xt),Qt=y(Ot),Ut=y(P);function Vt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ht(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Vt(n,!0).forEach((function(t){(0,Qt.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Vt(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var qt={shape:{rx:0,ry:0,ir:0,or:0,startAngle:0,endAngle:0,clockWise:!0},validator:function(e){var t=e.shape;return!["rx","ry","ir","or","startAngle","endAngle"].find((function(e){return"number"!=typeof t[e]}))||(console.error("Pie shape configuration is abnormal!"),!1)},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.rx,o=r.ry,a=r.ir,s=r.or,l=r.startAngle,d=r.endAngle,c=r.clockWise;i=parseInt(i)+.5,o=parseInt(o)+.5,n.arc(i,o,a>0?a:0,l,d,!c);var u=(0,L.getCircleRadianPoint)(i,o,s,d).map((function(e){return parseInt(e)+.5})),f=(0,L.getCircleRadianPoint)(i,o,a,l).map((function(e){return parseInt(e)+.5}));n.lineTo.apply(n,(0,Ut.default)(u)),n.arc(i,o,s>0?s:0,d,l,c),n.lineTo.apply(n,(0,Ut.default)(f)),n.closePath(),n.stroke(),n.fill()}},Zt={shape:{rx:0,ry:0,r:0,startAngle:0,endAngle:0,gradientStartAngle:null,gradientEndAngle:null},validator:function(e){var t=e.shape;return!["rx","ry","r","startAngle","endAngle"].find((function(e){return"number"!=typeof t[e]}))||(console.error("AgArc shape configuration is abnormal!"),!1)},draw:function(e,t){var n=e.ctx,r=t.shape,i=t.style.gradient;1===(i=i.map((function(e){return(0,Ee.getColorFromRgbValue)(e)}))).length&&(i=[i[0],i[0]]);var o=i.length-1,a=r.gradientStartAngle,s=r.gradientEndAngle,l=r.startAngle,d=r.endAngle,c=r.r,u=r.rx,f=r.ry;null===a&&(a=l),null===s&&(s=d);var h=(s-a)/o;h===2*Math.PI&&(h=2*Math.PI-.001);for(var p=0;p<o;p++){n.beginPath();var g=(0,L.getCircleRadianPoint)(u,f,c,l+h*p),v=(0,L.getCircleRadianPoint)(u,f,c,l+h*(p+1)),m=(0,M.getLinearGradientColor)(n,g,v,[i[p],i[p+1]]),A=l+h*p,C=l+h*(p+1),b=!1;if(C>d&&(C=d,b=!0),n.arc(u,f,c,A,C),n.strokeStyle=m,n.stroke(),b)break}}},Kt={shape:{number:[],content:"",position:[0,0],toFixed:0},validator:function(e){var t=e.shape,n=t.number,r=t.content,i=t.position;return n instanceof Array&&"string"==typeof r&&i instanceof Array||(console.error("NumberText shape configuration is abnormal!"),!1)},draw:function(e,t){var n=e.ctx,r=t.shape,i=r.number,o=r.content,a=r.toFixed,s=r.rowGap,l=o.split("{nt}"),d=l.length-1,c="";l.forEach((function(e,t){var n=i[t];t===d&&(n=""),"number"==typeof n&&(n=n.toFixed(a)),c+=e+(n||"")})),Mt.text.draw({ctx:n},{shape:Ht({},r,{content:c,rowGap:s||0})})}},Jt={shape:{x:0,y:0,w:0,h:0},validator:function(e){var t=e.shape,n=t.x,r=t.y,i=t.w,o=t.h;return"number"==typeof n&&"number"==typeof r&&"number"==typeof i&&"number"==typeof o||(console.error("lineIcon shape configuration is abnormal!"),!1)},draw:function(e,t){var n=e.ctx,r=t.shape;n.beginPath();var i=r.x,o=r.y,a=r.w,s=r.h/2;n.strokeStyle=n.fillStyle,n.moveTo(i,o+s),n.lineTo(i+a,o+s),n.lineWidth=1,n.stroke(),n.beginPath();var l=s-10;l<=0&&(l=3),n.arc(i+a/2,o+s,l,0,2*Math.PI),n.lineWidth=5,n.stroke(),n.fillStyle="#fff",n.fill()},hoverCheck:function(e,t){var n=t.shape,r=n.x,i=n.y,o=n.w,a=n.h;return(0,L.checkPointIsInRect)(e,r,i,o,a)},setGraphCenter:function(e,t){var n=t.shape,r=t.style,i=n.x,o=n.y,a=n.w,s=n.h;r.graphCenter=[i+a/2,o+s/2]}};(0,Xt.extendNewGraph)("pie",qt),(0,Xt.extendNewGraph)("agArc",Zt),(0,Xt.extendNewGraph)("numberText",Kt),(0,Xt.extendNewGraph)("lineIcon",Jt);var en=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.colorConfig=void 0;t.colorConfig=["#37a2da","#32c5e9","#67e0e3","#9fe6b8","#ffdb5c","#ff9f7f","#fb7293","#e062ae","#e690d1","#e7bcf3","#9d96f5","#8378ea","#96bfff"]}));C(en);en.colorConfig;var tn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.gridConfig=void 0;t.gridConfig={left:"10%",right:"10%",top:60,bottom:60,style:{fill:"rgba(0, 0, 0, 0)"},rLevel:-30,animationCurve:"easeOutCubic",animationFrame:30}}));C(tn);tn.gridConfig;var nn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.yAxisConfig=t.xAxisConfig=void 0;t.xAxisConfig={name:"",show:!0,position:"bottom",nameGap:15,nameLocation:"end",nameTextStyle:{fill:"#333",fontSize:10},min:"20%",max:"20%",interval:null,minInterval:null,maxInterval:null,boundaryGap:null,splitNumber:5,axisLine:{show:!0,style:{stroke:"#333",lineWidth:1}},axisTick:{show:!0,style:{stroke:"#333",lineWidth:1}},axisLabel:{show:!0,formatter:null,style:{fill:"#333",fontSize:10,rotate:0}},splitLine:{show:!1,style:{stroke:"#d4d4d4",lineWidth:1}},rLevel:-20,animationCurve:"easeOutCubic",animationFrame:50};t.yAxisConfig={name:"",show:!0,position:"left",nameGap:15,nameLocation:"end",nameTextStyle:{fill:"#333",fontSize:10},min:"20%",max:"20%",interval:null,minInterval:null,maxInterval:null,boundaryGap:null,splitNumber:5,axisLine:{show:!0,style:{stroke:"#333",lineWidth:1}},axisTick:{show:!0,style:{stroke:"#333",lineWidth:1}},axisLabel:{show:!0,formatter:null,style:{fill:"#333",fontSize:10,rotate:0}},splitLine:{show:!0,style:{stroke:"#d4d4d4",lineWidth:1}},rLevel:-20,animationCurve:"easeOutCubic",animationFrame:50}}));C(nn);nn.yAxisConfig,nn.xAxisConfig;var rn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.titleConfig=void 0;t.titleConfig={show:!0,text:"",offset:[0,-20],style:{fill:"#333",fontSize:17,fontWeight:"bold",textAlign:"center",textBaseline:"bottom"},rLevel:20,animationCurve:"easeOutCubic",animationFrame:50}}));C(rn);rn.titleConfig;var on=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.lineConfig=void 0;t.lineConfig={show:!0,name:"",stack:"",smooth:!1,xAxisIndex:0,yAxisIndex:0,data:[],lineStyle:{lineWidth:1},linePoint:{show:!0,radius:2,style:{fill:"#fff",lineWidth:1}},lineArea:{show:!1,gradient:[],style:{opacity:.5}},label:{show:!1,position:"top",offset:[0,-10],formatter:null,style:{fontSize:10}},rLevel:10,animationCurve:"easeOutCubic",animationFrame:50}}));C(on);on.lineConfig;var an=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.barConfig=void 0;t.barConfig={show:!0,name:"",stack:"",shapeType:"normal",echelonOffset:10,barWidth:"auto",barGap:"30%",barCategoryGap:"20%",xAxisIndex:0,yAxisIndex:0,data:[],backgroundBar:{show:!1,width:"auto",style:{fill:"rgba(200, 200, 200, .4)"}},label:{show:!1,position:"top",offset:[0,-10],formatter:null,style:{fontSize:10}},gradient:{color:[],local:!0},barStyle:{},independentColor:!1,independentColors:[],rLevel:0,animationCurve:"easeOutCubic",animationFrame:50}}));C(an);an.barConfig;var sn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.pieConfig=void 0;var n={show:!0,name:"",radius:"50%",center:["50%","50%"],startAngle:-Math.PI/2,roseType:!1,roseSort:!0,roseIncrement:"auto",data:[],insideLabel:{show:!1,formatter:"{percent}%",style:{fontSize:10,fill:"#fff",textAlign:"center",textBaseline:"middle"}},outsideLabel:{show:!0,formatter:"{name}",style:{fontSize:11},labelLineBendGap:"20%",labelLineEndLength:50,labelLineStyle:{lineWidth:1}},pieStyle:{},percentToFixed:0,rLevel:10,animationDelayGap:60,animationCurve:"easeOutCubic",startAnimationCurve:"easeOutBack",animationFrame:50};t.pieConfig=n}));C(sn);sn.pieConfig;var ln=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.radarAxisConfig=void 0;var n={show:!0,center:["50%","50%"],radius:"65%",startAngle:-Math.PI/2,splitNum:5,polygon:!1,axisLabel:{show:!0,labelGap:15,color:[],style:{fill:"#333"}},axisLine:{show:!0,color:[],style:{stroke:"#999",lineWidth:1}},splitLine:{show:!0,color:[],style:{stroke:"#d4d4d4",lineWidth:1}},splitArea:{show:!1,color:["#f5f5f5","#e6e6e6"],style:{}},rLevel:-10,animationCurve:"easeOutCubic",animationFrane:50};t.radarAxisConfig=n}));C(ln);ln.radarAxisConfig;var dn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.radarConfig=void 0;t.radarConfig={show:!0,name:"",data:[],radarStyle:{lineWidth:1},point:{show:!0,radius:2,style:{fill:"#fff"}},label:{show:!0,offset:[0,0],labelGap:5,formatter:null,style:{fontSize:10}},rLevel:10,animationCurve:"easeOutCubic",animationFrane:50}}));C(dn);dn.radarConfig;var cn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.gaugeConfig=void 0;var n={show:!0,name:"",radius:"60%",center:["50%","50%"],startAngle:-Math.PI/4*5,endAngle:Math.PI/4,min:0,max:100,splitNum:5,arcLineWidth:15,data:[],dataItemStyle:{},axisTick:{show:!0,tickLength:6,style:{stroke:"#999",lineWidth:1}},axisLabel:{show:!0,data:[],formatter:null,labelGap:5,style:{}},pointer:{show:!0,valueIndex:0,style:{scale:[1,1],fill:"#fb7293"}},details:{show:!1,formatter:null,offset:[0,0],valueToFixed:0,position:"center",style:{fontSize:20,fontWeight:"bold",textAlign:"center",textBaseline:"middle"}},backgroundArc:{show:!0,style:{stroke:"#e0e0e0"}},rLevel:10,animationCurve:"easeOutCubic",animationFrame:50};t.gaugeConfig=n}));C(cn);cn.gaugeConfig;var un=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.legendConfig=void 0;t.legendConfig={show:!0,orient:"horizontal",left:"auto",right:"auto",top:"auto",bottom:"auto",itemGap:10,iconWidth:25,iconHeight:10,selectAble:!0,data:[],textStyle:{fontFamily:"Arial",fontSize:13,fill:"#000"},iconStyle:{},textUnselectedStyle:{fontFamily:"Arial",fontSize:13,fill:"#999"},iconUnselectedStyle:{fill:"#999"},rLevel:20,animationCurve:"easeOutCubic",animationFrame:50}}));C(un);un.legendConfig;var fn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.changeDefaultConfig=function(e,t){if(!n["".concat(e,"Config")])return void console.warn("Change default config Error - Invalid key!");(0,M.deepMerge)(n["".concat(e,"Config")],t)},Object.defineProperty(t,"colorConfig",{enumerable:!0,get:function(){return en.colorConfig}}),Object.defineProperty(t,"gridConfig",{enumerable:!0,get:function(){return tn.gridConfig}}),Object.defineProperty(t,"xAxisConfig",{enumerable:!0,get:function(){return nn.xAxisConfig}}),Object.defineProperty(t,"yAxisConfig",{enumerable:!0,get:function(){return nn.yAxisConfig}}),Object.defineProperty(t,"titleConfig",{enumerable:!0,get:function(){return rn.titleConfig}}),Object.defineProperty(t,"lineConfig",{enumerable:!0,get:function(){return on.lineConfig}}),Object.defineProperty(t,"barConfig",{enumerable:!0,get:function(){return an.barConfig}}),Object.defineProperty(t,"pieConfig",{enumerable:!0,get:function(){return sn.pieConfig}}),Object.defineProperty(t,"radarAxisConfig",{enumerable:!0,get:function(){return ln.radarAxisConfig}}),Object.defineProperty(t,"radarConfig",{enumerable:!0,get:function(){return dn.radarConfig}}),Object.defineProperty(t,"gaugeConfig",{enumerable:!0,get:function(){return cn.gaugeConfig}}),Object.defineProperty(t,"legendConfig",{enumerable:!0,get:function(){return un.legendConfig}}),t.keys=void 0;var n={colorConfig:en.colorConfig,gridConfig:tn.gridConfig,xAxisConfig:nn.xAxisConfig,yAxisConfig:nn.yAxisConfig,titleConfig:rn.titleConfig,lineConfig:on.lineConfig,barConfig:an.barConfig,pieConfig:sn.pieConfig,radarAxisConfig:ln.radarAxisConfig,radarConfig:dn.radarConfig,gaugeConfig:cn.gaugeConfig,legendConfig:un.legendConfig};t.keys=["color","title","legend","xAxis","yAxis","grid","radarAxis","line","bar","pie","radar","gauge"]}));C(fn);fn.changeDefaultConfig,fn.keys;var hn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.mergeColor=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=(0,L.deepClone)(fn.colorConfig,!0),r=t.color,i=t.series;i||(i=[]);r||(r=[]);if(t.color=r=(0,M.deepMerge)(n,r),!i.length)return;var o=r.length;i.forEach((function(e,t){e.color||(e.color=r[t%o])}));var a=i.filter((function(e){return"pie"===e.type}));a.forEach((function(e){return e.data.forEach((function(e,t){return e.color=r[t%o]}))}));var s=i.filter((function(e){return"gauge"===e.type}));s.forEach((function(e){return e.data.forEach((function(e,t){return e.color=r[t%o]}))}));var l=i.filter((function(e){var t=e.type,n=e.independentColor;return"bar"===t&&n}));l.forEach((function(e){e.independentColors||(e.independentColors=r)}))}}));C(hn);hn.mergeColor;var pn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.doUpdate=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.chart,n=e.series,r=e.key,i=e.getGraphConfig,a=e.getStartGraphConfig,s=e.beforeChange,l=e.beforeUpdate,d=e.afterAddGraph;t[r]?t[r].update(n):t[r]=new o({chart:t,key:r,getGraphConfig:i,getStartGraphConfig:a,beforeChange:s,beforeUpdate:l,afterAddGraph:d},n)},t.Updater=void 0;var n=y(P),r=y(_),i=y(St),o=function e(t,n){(0,i.default)(this,e);var r=t.chart,o=t.key;"function"==typeof t.getGraphConfig?(r[o]||(this.graphs=r[o]=[]),Object.assign(this,t),this.update(n)):console.warn("Updater need function getGraphConfig!")};function a(e,t){Object.keys(t).forEach((function(n){"shape"===n||"style"===n?e.animation(n,t[n],!0):e[n]=t[n]}))}t.Updater=o,o.prototype.update=function(e){var t=this,i=this.graphs,o=this.beforeUpdate;if(function(e,t){var n=e.graphs,r=e.chart.render,i=n.length,o=t.length;if(i>o){n.splice(o).forEach((function(e){return e.forEach((function(e){return r.delGraph(e)}))}))}}(this,e),e.length){var s=(0,r.default)(o);e.forEach((function(e,r){"function"===s&&o(i,e,r,t);var l=i[r];l?function(e,t,r,i){var o=i.getGraphConfig,s=i.chart.render,l=i.beforeChange,d=o(t,i);(function(e,t,r){var i=e.length,o=t.length;if(o>i){var a=e.slice(-1)[0],s=new Array(o-i).fill(0).map((function(e){return r.clone(a)}));e.push.apply(e,(0,n.default)(s))}else if(o<i){e.splice(o).forEach((function(e){return r.delGraph(e)}))}})(e,d,s),e.forEach((function(e,t){var n=d[t];"function"==typeof l&&l(e,n),a(e,n)}))}(l,e,0,t):function(e,t,n,r){var i=r.getGraphConfig,o=r.getStartGraphConfig,s=r.chart.render,l=null;"function"==typeof o&&(l=o(t,r));var d=i(t,r);if(!d.length)return;l?(e[n]=l.map((function(e){return s.add(e)})),e[n].forEach((function(e,t){a(e,d[t])}))):e[n]=d.map((function(e){return s.add(e)}));var c=r.afterAddGraph;"function"==typeof c&&c(e[n])}(i,e,r,t)}))}}}));C(pn);pn.doUpdate,pn.Updater;var gn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.title=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=[];t.title&&(n[0]=(0,M.deepMerge)((0,L.deepClone)(fn.titleConfig,!0),t.title));(0,pn.doUpdate)({chart:e,series:n,key:"title",getGraphConfig:r})};var n=y(W);function r(e,t){var r=fn.titleConfig.animationCurve,i=fn.titleConfig.animationFrame,o=fn.titleConfig.rLevel,a=function(e,t){var r=e.offset,i=e.text,o=t.chart.gridArea,a=o.x,s=o.y,l=o.w,d=(0,n.default)(r,2),c=d[0],u=d[1];return{content:i,position:[a+l/2+c,s+u]}}(e,t),s=function(e){return e.style}(e);return[{name:"text",index:o,visible:e.show,animationCurve:r,animationFrame:i,shape:a,style:s}]}}));C(gn);gn.title;var vn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.grid=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.grid;n=(0,M.deepMerge)((0,L.deepClone)(fn.gridConfig,!0),n||{}),(0,pn.doUpdate)({chart:e,series:[n],key:"grid",getGraphConfig:o})};var n=y(W),r=y(Ot);function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function o(e,t){var o=e.animationCurve,s=e.animationFrame,l=e.rLevel,d=function(e,t){var r=(0,n.default)(t.chart.render.area,2),i=r[0],o=r[1],s=a(e.left,i),l=a(e.right,i),d=a(e.top,o),c=a(e.bottom,o);return{x:s,y:d,w:i-s-l,h:o-d-c}}(e,t),c=function(e){return e.style}(e);return t.chart.gridArea=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(n,!0).forEach((function(t){(0,r.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},d),[{name:"rect",index:l,animationCurve:o,animationFrame:s,shape:d,style:c}]}function a(e,t){return"number"==typeof e?e:"string"!=typeof e?0:t*parseInt(e)/100}}));C(vn);vn.grid;var mn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.axis=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.xAxis,r=t.yAxis,i=t.series,o=[];n&&r&&i&&(o=w(o=x(o=b(o=C(o=A(o=p(o=h(o=(o=f(o=u(n,r))).filter((function(e){return e.show}))),i)),e))),e));(0,pn.doUpdate)({chart:e,series:o,key:"axisLine",getGraphConfig:k}),(0,pn.doUpdate)({chart:e,series:o,key:"axisTick",getGraphConfig:S}),(0,pn.doUpdate)({chart:e,series:o,key:"axisLabel",getGraphConfig:O}),(0,pn.doUpdate)({chart:e,series:o,key:"axisName",getGraphConfig:j}),(0,pn.doUpdate)({chart:e,series:o,key:"splitLine",getGraphConfig:G}),e.axisData=o};var n=y(_),r=y(W),i=y(Ot),o=y(P);function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(n,!0).forEach((function(t){(0,i.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var l={xAxisConfig:fn.xAxisConfig,yAxisConfig:fn.yAxisConfig},d=Math.abs,c=Math.pow;function u(e,t){var n,r,i=[],a=[];e instanceof Array?(n=i).push.apply(n,(0,o.default)(e)):i.push(e);t instanceof Array?(r=a).push.apply(r,(0,o.default)(t)):a.push(t);return i.splice(2),a.splice(2),i=i.map((function(e,t){return s({},e,{index:t,axis:"x"})})),a=a.map((function(e,t){return s({},e,{index:t,axis:"y"})})),[].concat((0,o.default)(i),(0,o.default)(a))}function f(e){var t=e.filter((function(e){return"x"===e.axis})),n=e.filter((function(e){return"y"===e.axis}));return t=t.map((function(e){return(0,M.deepMerge)((0,L.deepClone)(fn.xAxisConfig),e)})),n=n.map((function(e){return(0,M.deepMerge)((0,L.deepClone)(fn.yAxisConfig),e)})),[].concat((0,o.default)(t),(0,o.default)(n))}function h(e){var t=e.filter((function(e){return"value"===e.data})),n=e.filter((function(e){return"value"!==e.data}));return t.forEach((function(e){"boolean"!=typeof e.boundaryGap&&(e.boundaryGap=!1)})),n.forEach((function(e){"boolean"!=typeof e.boundaryGap&&(e.boundaryGap=!0)})),[].concat((0,o.default)(t),(0,o.default)(n))}function p(e,t){var i=e.filter((function(e){return"value"===e.data})),a=e.filter((function(e){return e.data instanceof Array}));return i=function(e,t){return e.map((function(e){var i=function(e,t){var i=e.min,o=e.max,a=e.axis,s=(0,r.default)(t,2),c=s[0],u=s[1],f=(0,n.default)(i),h=(0,n.default)(o);v(i)||(i=l[a+"AxisConfig"].min,f="string");v(o)||(o=l[a+"AxisConfig"].max,h="string");if("string"===f){var p=g(i=parseInt(c-d(c*parseFloat(i)/100)));i=parseFloat((i/p-.1).toFixed(1))*p}if("string"===h){var m=g(o=parseInt(u+d(u*parseFloat(o)/100)));o=parseFloat((o/m+.1).toFixed(1))*m}return[i,o]}(e,function(e,t){if(0===(t=t.filter((function(e){var t=e.show,n=e.type;return!1!==t&&"pie"!==n}))).length)return[0,0];var n=e.index,r=e.axis;t=function(e){var t=(0,L.deepClone)(e,!0);return e.forEach((function(n,r){var i=(0,M.mergeSameStackData)(n,e);t[r].data=i})),t}(t);var i=r+"Axis",a=t.filter((function(e){return e[i]===n}));a.length||(a=t);return function(e){if(!e)return;var t=Math.min.apply(Math,(0,o.default)(e.map((function(e){var t=e.data;return Math.min.apply(Math,(0,o.default)((0,M.filterNonNumber)(t)))})))),n=Math.max.apply(Math,(0,o.default)(e.map((function(e){var t=e.data;return Math.max.apply(Math,(0,o.default)((0,M.filterNonNumber)(t)))}))));return[t,n]}(a)}(e,t)),a=(0,r.default)(i,2),c=a[0],u=a[1],f=function(e,t,n){var r=n.interval,i=n.minInterval,o=n.maxInterval,a=n.splitNumber,s=n.axis,d=l[s+"AxisConfig"];"number"!=typeof r&&(r=d.interval);"number"!=typeof i&&(i=d.minInterval);"number"!=typeof o&&(o=d.maxInterval);"number"!=typeof a&&(a=d.splitNumber);if("number"==typeof r)return r;var c=parseInt((t-e)/(a-1));c.toString().length>1&&(c=parseInt(c.toString().replace(/\d$/,"0")));0===c&&(c=1);return"number"==typeof i&&c<i?i:"number"==typeof o&&c>o?o:c}(c,u,e),h=e.axisLabel.formatter,p=[];return s({},e,{maxValue:(p=(p=c<0&&u>0?function(e,t,n){var r=[],i=[],a=0,s=0;do{r.push(a-=n)}while(a>e);do{i.push(s+=n)}while(s<t);return[].concat((0,o.default)(r.reverse()),[0],(0,o.default)(i))}(c,u,f):function(e,t,n){var r=[e],i=e;do{r.push(i+=n)}while(i<t);return r}(c,u,f)).map((function(e){return parseFloat(e.toFixed(2))}))).slice(-1)[0],minValue:p[0],label:m(p,h)})}))}(i,t),a=function(e){return e.map((function(e){return s({},e,{label:m(e.data,e.axisLabel.formatter)})}))}(a),[].concat((0,o.default)(i),(0,o.default)(a))}function g(e){var t=d(e).toString(),n=t.length,r=t.replace(/0*$/g,"").indexOf("0"),i=n-1;return-1!==r&&(i-=r),c(10,i)}function v(e){var t=(0,n.default)(e);return"string"===t&&/^\d+%$/.test(e)||"number"===t}function m(e,t){return t?("string"==typeof t&&(e=e.map((function(e){return t.replace("{value}",e)}))),"function"==typeof t&&(e=e.map((function(e,n){return t({value:e,index:n})}))),e):e}function A(e){var t=e.filter((function(e){return"x"===e.axis})),n=e.filter((function(e){return"y"===e.axis}));return t[0]&&!t[0].position&&(t[0].position=fn.xAxisConfig.position),t[1]&&!t[1].position&&(t[1].position="bottom"===t[0].position?"top":"bottom"),n[0]&&!n[0].position&&(n[0].position=fn.yAxisConfig.position),n[1]&&!n[1].position&&(n[1].position="left"===n[0].position?"right":"left"),[].concat((0,o.default)(t),(0,o.default)(n))}function C(e,t){var n=t.gridArea,r=n.x,i=n.y,o=n.w,a=n.h;return e=e.map((function(e){var t=e.position,n=[];return"left"===t?n=[[r,i],[r,i+a]].reverse():"right"===t?n=[[r+o,i],[r+o,i+a]].reverse():"top"===t?n=[[r,i],[r+o,i]]:"bottom"===t&&(n=[[r,i+a],[r+o,i+a]]),s({},e,{linePosition:n})}))}function b(e,t){return e.map((function(e){var t=e.axis,n=e.linePosition,i=e.position,o=e.label,a=e.boundaryGap;"boolean"!=typeof a&&(a=l[t+"AxisConfig"].boundaryGap);var d=o.length,c=(0,r.default)(n,2),u=(0,r.default)(c[0],2),f=u[0],h=u[1],p=(0,r.default)(c[1],2),g=p[0],v=p[1],m=("x"===t?g-f:v-h)/(a?d:d-1),A=new Array(d).fill(0).map((function(e,n){return"x"===t?[f+m*(a?n+.5:n),h]:[f,h+m*(a?n+.5:n)]})),C=function(e,t,n,i,o){var a="x"===e?1:0,s=5;"x"===e&&"top"===n&&(s=-5);"y"===e&&"left"===n&&(s=-5);var l=i.map((function(e){var t=(0,L.deepClone)(e);return t[a]+=s,[(0,L.deepClone)(e),t]}));return t?(a="x"===e?0:1,s=o/2,l.forEach((function(e){var t=(0,r.default)(e,2),n=t[0],i=t[1];n[a]+=s,i[a]+=s})),l):l}(t,a,i,A,m);return s({},e,{tickPosition:A,tickLinePosition:C,tickGap:m})}))}function x(e,t){return e.map((function(e){var t=e.nameGap,n=e.nameLocation,i=e.position,a=e.linePosition,l=(0,r.default)(a,2),d=l[0],c=l[1],u=(0,o.default)(d);"end"===n&&(u=(0,o.default)(c)),"center"===n&&(u[0]=(d[0]+c[0])/2,u[1]=(d[1]+c[1])/2);var f=0;"top"===i&&"center"===n&&(f=1),"bottom"===i&&"center"===n&&(f=1),"left"===i&&"center"!==n&&(f=1),"right"===i&&"center"!==n&&(f=1);var h=t;return"top"===i&&"end"!==n&&(h*=-1),"left"===i&&"start"!==n&&(h*=-1),"bottom"===i&&"start"===n&&(h*=-1),"right"===i&&"end"===n&&(h*=-1),u[f]+=h,s({},e,{namePosition:u})}))}function w(e,t){var n=t.gridArea,i=n.w,a=n.h;return e.map((function(e){var t=e.tickLinePosition,n=e.position,l=e.boundaryGap,d=0,c=i;"top"!==n&&"bottom"!==n||(d=1),"top"!==n&&"bottom"!==n||(c=a),"right"!==n&&"bottom"!==n||(c*=-1);var u=t.map((function(e){var t=(0,r.default)(e,1)[0],n=(0,o.default)(t);return n[d]+=c,[(0,o.default)(t),n]}));return l||u.shift(),s({},e,{splitLinePosition:u})}))}function k(e){var t=e.animationCurve,n=e.animationFrame;return[{name:"polyline",index:e.rLevel,visible:e.axisLine.show,animationCurve:t,animationFrame:n,shape:E(e),style:B(e)}]}function E(e){return{points:e.linePosition}}function B(e){return e.axisLine.style}function S(e){var t=e.animationCurve,n=e.animationFrame,r=e.rLevel,i=function(e){return e.tickLinePosition.map((function(e){return{points:e}}))}(e),o=function(e){return e.axisTick.style}(e);return i.map((function(i){return{name:"polyline",index:r,visible:e.axisTick.show,animationCurve:t,animationFrame:n,shape:i,style:o}}))}function O(e){var t=e.animationCurve,n=e.animationFrame,r=e.rLevel,i=function(e){var t=e.label,n=e.tickPosition,r=e.position;return n.map((function(e,n){return{position:I(e,r),content:t[n].toString()}}))}(e),o=function(e,t){var n=e.position,r=e.axisLabel.style,i=function(e){if("left"===e)return{textAlign:"right",textBaseline:"middle"};if("right"===e)return{textAlign:"left",textBaseline:"middle"};if("top"===e)return{textAlign:"center",textBaseline:"bottom"};if("bottom"===e)return{textAlign:"center",textBaseline:"top"}}(n);return r=(0,M.deepMerge)(i,r),t.map((function(e){var t=e.position;return s({},r,{graphCenter:t})}))}(e,i);return i.map((function(i,a){return{name:"text",index:r,visible:e.axisLabel.show,animationCurve:t,animationFrame:n,shape:i,style:o[a],setGraphCenter:function(){}}}))}function I(e,t){var n=0,r=10;return"top"!==t&&"bottom"!==t||(n=1),"top"!==t&&"left"!==t||(r=-10),(e=(0,L.deepClone)(e))[n]+=r,e}function j(e){var t=e.animationCurve,n=e.animationFrame;return[{name:"text",index:e.rLevel,animationCurve:t,animationFrame:n,shape:F(e),style:R(e)}]}function F(e){return{content:e.name,position:e.namePosition}}function R(e){var t=e.nameLocation,n=e.position,r=e.nameTextStyle,i=function(e,t){if("top"===e&&"start"===t||"bottom"===e&&"start"===t||"left"===e&&"center"===t)return{textAlign:"right",textBaseline:"middle"};if("top"===e&&"end"===t||"bottom"===e&&"end"===t||"right"===e&&"center"===t)return{textAlign:"left",textBaseline:"middle"};if("top"===e&&"center"===t||"left"===e&&"end"===t||"right"===e&&"end"===t)return{textAlign:"center",textBaseline:"bottom"};if("bottom"===e&&"center"===t||"left"===e&&"start"===t||"right"===e&&"start"===t)return{textAlign:"center",textBaseline:"top"}}(n,t);return(0,M.deepMerge)(i,r)}function G(e){var t=e.animationCurve,n=e.animationFrame,r=e.rLevel,i=function(e){return e.splitLinePosition.map((function(e){return{points:e}}))}(e),o=function(e){return e.splitLine.style}(e);return i.map((function(i){return{name:"polyline",index:r,visible:e.splitLine.show,animationCurve:t,animationFrame:n,shape:i,style:o}}))}}));C(mn);mn.axis;var An=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.line=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.xAxis,r=t.yAxis,i=t.series,o=[];n&&r&&i&&(o=u(o=(0,M.initNeedSeries)(i,fn.lineConfig,"line"),e));(0,pn.doUpdate)({chart:e,series:o,key:"lineArea",getGraphConfig:f,getStartGraphConfig:v,beforeUpdate:m,beforeChange:A}),(0,pn.doUpdate)({chart:e,series:o,key:"line",getGraphConfig:C,getStartGraphConfig:w,beforeUpdate:m,beforeChange:A}),(0,pn.doUpdate)({chart:e,series:o,key:"linePoint",getGraphConfig:k,getStartGraphConfig:E}),(0,pn.doUpdate)({chart:e,series:o,key:"lineLabel",getGraphConfig:B})};var n=y(_),r=y(W),i=y(P),o=y(Ot),a=y(Lt);function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(n,!0).forEach((function(t){(0,o.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var d=a.default.polylineToBezierCurve,c=a.default.getBezierCurveLength;function u(e,t){var n=t.axisData;return e.map((function(t){var r=(0,M.mergeSameStackData)(t,e);r=function(e,t){var n=e.data;return t.map((function(e,t){return"number"==typeof n[t]?e:null}))}(t,r);var i=function(e,t){var n=e.xAxisIndex,r=e.yAxisIndex,i=t.find((function(e){var t=e.axis,r=e.index;return"x"===t&&r===n})),o=t.find((function(e){var t=e.axis,n=e.index;return"y"===t&&n===r}));return[i,o]}(t,n),o=function(e,t){var n=t.findIndex((function(e){return"value"===e.data})),r=t[n],i=t[1-n],o=r.linePosition,a=r.axis,s=i.tickPosition,l=s.length,d="x"===a?0:1,c=o[0][d],u=o[1][d]-c,f=r.maxValue,h=r.minValue,p=f-h;return new Array(l).fill(0).map((function(t,n){var r=e[n];if("number"!=typeof r)return null;var i=(r-h)/p;return 0===p&&(i=0),i*u+c})).map((function(e,t){if(t>=l||"number"!=typeof e)return null;var n=[e,s[t][1-d]];return 0===d||n.reverse(),n}))}(r,i),a=function(e){var t=e.find((function(e){return"value"===e.data})),n=t.axis,r=t.linePosition,i=t.minValue,o=t.maxValue,a="x"===n?0:1,s=r[0][a];if(i<0&&o>0){var l=o-i,d=Math.abs(r[0][a]-r[1][a]),c=Math.abs(i)/l*d;"y"===n&&(c*=-1),s+=c}return{changeIndex:a,changeValue:s}}(i);return l({},t,{linePosition:o.filter((function(e){return e})),lineFillBottomPos:a})}))}function f(e){var t=e.animationCurve,n=e.animationFrame,r=e.lineFillBottomPos,i=e.rLevel;return[{name:b(e),index:i,animationCurve:t,animationFrame:n,visible:e.lineArea.show,lineFillBottomPos:r,shape:h(e),style:p(e),drawed:g}]}function h(e){return{points:e.linePosition}}function p(e){var t=e.lineArea,n=e.color,r=t.gradient,o=t.style,a=[o.fill||n],s=(0,M.deepMerge)(a,r);1===s.length&&s.push(s[0]);var d=function(e){var t=e.lineFillBottomPos,n=e.linePosition,r=t.changeIndex,o=t.changeValue,a=n.map((function(e){return e[r]})),s=Math.max.apply(Math,(0,i.default)(a)),l=Math.min.apply(Math,(0,i.default)(a)),d=s;1===r&&(d=l);return 1===r?[0,d,0,o]:[d,0,o,0]}(e);return o=l({},o,{stroke:"rgba(0, 0, 0, 0)"}),(0,M.deepMerge)({gradientColor:s,gradientParams:d,gradientType:"linear",gradientWith:"fill"},o)}function g(e,t){var n=e.lineFillBottomPos,r=e.shape,o=t.ctx,a=r.points,s=n.changeIndex,l=n.changeValue,d=(0,i.default)(a[a.length-1]),c=(0,i.default)(a[0]);d[s]=l,c[s]=l,o.lineTo.apply(o,(0,i.default)(d)),o.lineTo.apply(o,(0,i.default)(c)),o.closePath(),o.fill()}function v(e){var t=f(e)[0],n=l({},t.style);return n.opacity=0,t.style=n,[t]}function m(e,t,n,r){var i=e[n];if(i){var o=b(t),a=r.chart.render;o!==i[0].name&&(i.forEach((function(e){return a.delGraph(e)})),e[n]=null)}}function A(e,t){var n=t.shape.points,r=e.shape.points,o=r.length,a=n.length;if(a>o){var s=r.slice(-1)[0],l=new Array(a-o).fill(0).map((function(e){return(0,i.default)(s)}));r.push.apply(r,(0,i.default)(l))}else a<o&&r.splice(a)}function C(e){var t=e.animationCurve,n=e.animationFrame,r=e.rLevel;return[{name:b(e),index:r+1,animationCurve:t,animationFrame:n,shape:h(e),style:x(e)}]}function b(e){return e.smooth?"smoothline":"polyline"}function x(e){var t=e.lineStyle,n=e.color,r=e.smooth,i=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!t)return(0,M.getPolylineLength)(e);var n=d(e);return c(n)}(e.linePosition,r);return(0,M.deepMerge)({stroke:n,lineDash:[i,0]},t)}function w(e){var t=e.lineStyle.lineDash,n=C(e)[0],r=n.style.lineDash;return r=t?[0,0]:(0,i.default)(r).reverse(),n.style.lineDash=r,[n]}function k(e){var t=e.animationCurve,n=e.animationFrame,i=e.rLevel,o=function(e){var t=e.linePosition,n=e.linePoint.radius;return t.map((function(e){var t=(0,r.default)(e,2),i=t[0],o=t[1];return{r:n,rx:i,ry:o}}))}(e),a=function(e){var t=e.color,n=e.linePoint.style;return(0,M.deepMerge)({stroke:t},n)}(e);return o.map((function(r){return{name:"circle",index:i+2,visible:e.linePoint.show,animationCurve:t,animationFrame:n,shape:r,style:a}}))}function E(e){var t=k(e);return t.forEach((function(e){e.shape.r=.1})),t}function B(e){var t=e.animationCurve,o=e.animationFrame,a=e.rLevel,s=function(e){var t=function(e){var t=e.data,r=e.label.formatter;if(t=t.filter((function(e){return"number"==typeof e})).map((function(e){return e.toString()})),!r)return t;var i=(0,n.default)(r);return"string"===i?t.map((function(e){return r.replace("{value}",e)})):"function"===i?t.map((function(e,t){return r({value:e,index:t})})):t}(e),o=function(e){var t=e.linePosition,n=e.lineFillBottomPos,o=e.label,a=o.position,s=o.offset,l=n.changeIndex,d=n.changeValue;return t.map((function(e){if("bottom"===a&&((e=(0,i.default)(e))[l]=d),"center"===a){var t=(0,i.default)(e);t[l]=d,n=e,o=t,c=(0,r.default)(n,2),u=c[0],f=c[1],h=(0,r.default)(o,2),p=h[0],g=h[1],e=[(u+p)/2,(f+g)/2]}var n,o,c,u,f,h,p,g,v,m,A,C,b,y,x,w;return v=e,m=s,A=(0,r.default)(v,2),C=A[0],b=A[1],y=(0,r.default)(m,2),x=y[0],w=y[1],[C+x,b+w]}))}(e);return t.map((function(e,t){return{content:e,position:o[t]}}))}(e),l=function(e){var t=e.color,n=e.label.style;return(0,M.deepMerge)({fill:t},n)}(e);return s.map((function(n,r){return{name:"text",index:a+3,visible:e.label.show,animationCurve:t,animationFrame:o,shape:n,style:l}}))}}));C(An);An.line;var Cn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.bar=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.xAxis,r=t.yAxis,i=t.series,o=[];n&&r&&i&&(o=c(o=d(o=l(o=(0,M.initNeedSeries)(i,fn.barConfig,"bar"),e))));(0,pn.doUpdate)({chart:e,series:o.slice(-1),key:"backgroundBar",getGraphConfig:f}),o.reverse(),(0,pn.doUpdate)({chart:e,series:o,key:"bar",getGraphConfig:h,getStartGraphConfig:m,beforeUpdate:A}),(0,pn.doUpdate)({chart:e,series:o,key:"barLabel",getGraphConfig:C})};var n=y(_),r=y(Ot),i=y(W),o=y(P);function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?a(n,!0).forEach((function(t){(0,r.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):a(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function l(e,t){var n=t.axisData;return e.forEach((function(e){var t=e.xAxisIndex,r=e.yAxisIndex;"number"!=typeof t&&(t=0),"number"!=typeof r&&(r=0);var i=[n.find((function(e){var n=e.axis,r=e.index;return"".concat(n).concat(r)==="x".concat(t)})),n.find((function(e){var t=e.axis,n=e.index;return"".concat(t).concat(n)==="y".concat(r)}))],o=i.findIndex((function(e){return"value"===e.data}));e.valueAxis=i[o],e.labelAxis=i[1-o]})),e}function d(e,t){return function(e){var t=e.map((function(e){var t=e.labelAxis;return t.axis+t.index}));return(t=(0,o.default)(new Set(t))).map((function(t){return e.filter((function(e){var n=e.labelAxis;return n.axis+n.index===t}))}))}(e).forEach((function(e){!function(e){var t=function(e){var t=[];return e.forEach((function(e){var n=e.stack;n&&t.push(n)})),(0,o.default)(new Set(t))}(e);t=t.map((function(e){return{stack:e,index:-1}}));var n=0;e.forEach((function(e){var r=e.stack;if(r){var i=t.find((function(e){return e.stack===r}));-1===i.index&&(i.index=n,n++),e.barIndex=i.index}else e.barIndex=n,n++}))}(e),function(e){var t=(0,o.default)(new Set(e.map((function(e){return e.barIndex})))).length;e.forEach((function(e){return e.barNum=t}))}(e),function(e){var t=e.slice(-1)[0],n=t.barCategoryGap,r=t.labelAxis.tickGap,i=0;i="number"==typeof n?n:(1-parseInt(n)/100)*r;e.forEach((function(e){return e.barCategoryWidth=i}))}(e),function(e){var t=e.slice(-1)[0],n=t.barCategoryWidth,r=t.barWidth,o=t.barGap,a=t.barNum,s=[];"number"==typeof r||"auto"!==r?s=function(e,t,n){var r=0,i=0;r="number"==typeof t?t:parseInt(t)/100*e;i="number"==typeof n?n:parseInt(n)/100*r;return[r,i]}(n,r,o):"auto"===r&&(s=function(e,t,n,r){var i=0,o=0,a=e/r;if("number"==typeof n)i=a-(o=n);else{var s=10+parseInt(n)/10;o=0===s?-(i=2*a):a-(i=a/s*10)}return[i,o]}(n,0,o,a));var l=s,d=(0,i.default)(l,2),c=d[0],u=d[1];e.forEach((function(e){e.barWidth=c,e.barGap=u}))}(e),function(e){var t=e.slice(-1)[0],n=t.barGap,r=t.barWidth,i=t.barNum,o=(n+r)*i-n;e.forEach((function(e){return e.barAllWidthAndGap=o}))}(e)})),e}function c(e,t){return e=function(e){return e.forEach((function(e){var t=e.data,n=e.barLabelAxisPos,r=e.barValueAxisPos,i=t.filter((function(e){return"number"==typeof e})).length;n.length>i&&(n.splice(i),r.splice(i))})),e}(e=function(e){return e.map((function(e){var t=e.barLabelAxisPos;return e.data.forEach((function(e,n){"number"!=typeof e&&(t[n]=null)})),s({},e,{barLabelAxisPos:t.filter((function(e){return null!==e}))})}))}(e=function(e){return e.map((function(e){var t=e.labelAxis,n=e.barAllWidthAndGap,r=e.barGap,i=e.barWidth,o=e.barIndex,a=t.tickGap,l=t.tickPosition,d="x"===t.axis?0:1;return s({},e,{barLabelAxisPos:l.map((function(e,t){return l[t][d]-a/2+(a-n)/2+(o+.5)*i+o*r}))})}))}(e=function(e){return e.map((function(t){var n=(0,M.mergeSameStackData)(t,e);n=function(e,t){var n=e.data;return t.map((function(e,t){return"number"==typeof n[t]?e:null})).filter((function(e){return null!==e}))}(t,n);var r=t.valueAxis,i=r.axis,o=r.minValue,a=r.maxValue,l=r.linePosition,d=u(o,a,o<0?0:o,l,i);return s({},t,{barValueAxisPos:n.map((function(e){return u(o,a,e,l,i)})).map((function(e){return[d,e]}))})}))}(e))))}function u(e,t,n,r,i){if("number"!=typeof n)return null;var o=t-e,a="x"===i?0:1,s=(n-e)/o;return 0===o&&(s=0),s*(r[1][a]-r[0][a])+r[0][a]}function f(e){var t=e.animationCurve,n=e.animationFrame,r=e.rLevel,i=function(e){var t=e.labelAxis,n=e.valueAxis,r=t.tickPosition,i=n.axis,o=n.linePosition,a=function(e){var t=e.barAllWidthAndGap,n=e.barCategoryWidth,r=e.backgroundBar.width;return"number"==typeof r?r:"auto"===r?t:parseInt(r)/100*n}(e),s=a/2,l="x"===i?0:1,d=r.map((function(e){return e[1-l]})),c=[o[0][l],o[1][l]],u=c[0],f=c[1];return d.map((function(e){return"x"===i?{x:u,y:e-s,w:f-u,h:a}:{x:e-s,y:f,w:a,h:u-f}}))}(e),o=function(e){return e.backgroundBar.style}(e);return i.map((function(i){return{name:"rect",index:r,visible:e.backgroundBar.show,animationCurve:t,animationFrame:n,shape:i,style:o}}))}function h(e){var t=e.barLabelAxisPos,n=e.animationCurve,r=e.animationFrame,i=e.rLevel,o=p(e);return t.map((function(t,a){return{name:o,index:i,animationCurve:n,animationFrame:r,shape:g(e,a),style:v(e,a)}}))}function p(e){var t=e.shapeType;return"leftEchelon"===t||"rightEchelon"===t?"polyline":"rect"}function g(e,t){var n=e.shapeType;return"leftEchelon"===n?function(e,t){var n=e.barValueAxisPos,r=e.barLabelAxisPos,o=e.barWidth,a=e.echelonOffset,s=(0,i.default)(n[t],2),l=s[0],d=s[1],c=r[t],u=o/2,f=e.valueAxis.axis,h=[];"x"===f?(h[0]=[d,c-u],h[1]=[d,c+u],h[2]=[l,c+u],h[3]=[l+a,c-u],d-l<a&&h.splice(3,1)):(h[0]=[c-u,d],h[1]=[c+u,d],h[2]=[c+u,l],h[3]=[c-u,l-a],l-d<a&&h.splice(3,1));return{points:h,close:!0}}(e,t):"rightEchelon"===n?function(e,t){var n=e.barValueAxisPos,r=e.barLabelAxisPos,o=e.barWidth,a=e.echelonOffset,s=(0,i.default)(n[t],2),l=s[0],d=s[1],c=r[t],u=o/2,f=e.valueAxis.axis,h=[];"x"===f?(h[0]=[d,c+u],h[1]=[d,c-u],h[2]=[l,c-u],h[3]=[l+a,c+u],d-l<a&&h.splice(2,1)):(h[0]=[c+u,d],h[1]=[c-u,d],h[2]=[c-u,l],h[3]=[c+u,l-a],l-d<a&&h.splice(2,1));return{points:h,close:!0}}(e,t):function(e,t){var n=e.barValueAxisPos,r=e.barLabelAxisPos,o=e.barWidth,a=(0,i.default)(n[t],2),s=a[0],l=a[1],d=r[t],c=e.valueAxis.axis,u={};"x"===c?(u.x=s,u.y=d-o/2,u.w=l-s,u.h=o):(u.x=d-o/2,u.y=l,u.w=o,u.h=s-l);return u}(e,t)}function v(e,t){var n=e.barStyle,r=e.gradient,o=e.color,a=e.independentColor,s=e.independentColors,l=[n.fill||o],d=(0,M.deepMerge)(l,r.color);if(a){var c=s[t%s.length];d=c instanceof Array?c:[c]}1===d.length&&d.push(d[0]);var u=function(e,t){var n=e.barValueAxisPos,r=e.barLabelAxisPos,o=e.data,a=e.valueAxis,s=a.linePosition,l=a.axis,d=(0,i.default)(n[t],2),c=d[0],u=d[1],f=r[t],h=o[t],p=(0,i.default)(s,2),g=p[0],v=p[1],m="x"===l?0:1,A=u;e.gradient.local||(A=h<0?g[m]:v[m]);return"y"===l?[f,A,f,c]:[A,f,c,f]}(e,t);return(0,M.deepMerge)({gradientColor:d,gradientParams:u,gradientType:"linear",gradientWith:"fill"},n)}function m(e){var t=h(e),n=e.shapeType;return t.forEach((function(t){var r=t.shape;r="leftEchelon"===n?function(e,t){var n=t.valueAxis.axis,r=(e=(0,L.deepClone)(e)).points,i="x"===n?0:1,o=r[2][i];return r.forEach((function(e){return e[i]=o})),e}(r,e):"rightEchelon"===n?function(e,t){var n=t.valueAxis.axis,r=(e=(0,L.deepClone)(e)).points,i="x"===n?0:1,o=r[2][i];return r.forEach((function(e){return e[i]=o})),e}(r,e):function(e,t){var n=t.valueAxis.axis,r=e.x,i=e.y,o=e.w,a=e.h;"x"===n?o=0:(i+=a,a=0);return{x:r,y:i,w:o,h:a}}(r,e),t.shape=r})),t}function A(e,t,n,r){var i=r.chart.render,o=p(t);e[n]&&e[n][0].name!==o&&(e[n].forEach((function(e){return i.delGraph(e)})),e[n]=null)}function C(e){var t=e.animationCurve,r=e.animationFrame,o=e.rLevel,a=function(e){var t=function(e){var t=e.data,r=e.label.formatter;if(t=t.filter((function(e){return"number"==typeof e})).map((function(e){return e.toString()})),!r)return t;var i=(0,n.default)(r);return"string"===i?t.map((function(e){return r.replace("{value}",e)})):"function"===i?t.map((function(e,t){return r({value:e,index:t})})):t}(e);return function(e){var t=e.label,n=e.barValueAxisPos,r=e.barLabelAxisPos,o=t.position,a=t.offset,s=e.valueAxis.axis;return n.map((function(e,t){var n,l,d,c,u,f,h,p,g=(0,i.default)(e,2),v=g[0],m=g[1],A=r[t],C=[m,A];return"bottom"===o&&(C=[v,A]),"center"===o&&(C=[(v+m)/2,A]),"y"===s&&C.reverse(),n=C,l=a,d=(0,i.default)(n,2),c=d[0],u=d[1],f=(0,i.default)(l,2),h=f[0],p=f[1],[c+h,u+p]}))}(e).map((function(e,n){return{position:e,content:t[n]}}))}(e),s=function(e){var t=e.color,n=e.label.style,r=e.gradient.color;r.length&&(t=r[0]);return n=(0,M.deepMerge)({fill:t},n)}(e);return a.map((function(n){return{name:"text",index:o,visible:e.label.show,animationCurve:t,animationFrame:r,shape:n,style:s}}))}}));C(Cn);Cn.bar;var bn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.pie=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.series;n||(n=[]);var r=(0,M.initNeedSeries)(n,sn.pieConfig,"pie");r=v(r=g(r=p(r=h(r=u(r=c(r=l(r=s(r,e),e))))))),(0,pn.doUpdate)({chart:e,series:r,key:"pie",getGraphConfig:x,getStartGraphConfig:w,beforeChange:k}),(0,pn.doUpdate)({chart:e,series:r,key:"pieInsideLabel",getGraphConfig:S}),(0,pn.doUpdate)({chart:e,series:r,key:"pieOutsideLabelLine",getGraphConfig:j,getStartGraphConfig:F}),(0,pn.doUpdate)({chart:e,series:r,key:"pieOutsideLabel",getGraphConfig:D,getStartGraphConfig:z})};var n=y(Ot),r=y(_),i=y(W),o=y(P);function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e,t){var n=t.render.area;return e.forEach((function(e){var t=e.center;t=t.map((function(e,t){return"number"==typeof e?e:parseInt(e)/100*n[t]})),e.center=t})),e}function l(e,t){var n=Math.min.apply(Math,(0,o.default)(t.render.area))/2;return e.forEach((function(e){var t=e.radius,r=e.data;t=d(t,n),r.forEach((function(e){var r=e.radius;r||(r=t),r=d(r,n),e.radius=r})),e.radius=t})),e}function d(e,t){return e instanceof Array||(e=[0,e]),e=e.map((function(e){return"number"==typeof e?e:parseInt(e)/100*t}))}function c(e,t){return e.filter((function(e){return e.roseType})).forEach((function(e){var t=e.radius,n=e.data,r=e.roseSort,i=function(e){var t=e.radius,n=e.roseIncrement;if("number"==typeof n)return n;if("auto"===n){var r=e.data,i=r.reduce((function(e,t){var n=t.radius;return[].concat((0,o.default)(e),(0,o.default)(n))}),[]),a=Math.min.apply(Math,(0,o.default)(i));return.6*(Math.max.apply(Math,(0,o.default)(i))-a)/(r.length-1||1)}return parseInt(n)/100*t[1]}(e),a=(0,o.default)(n);(n=function(e){return e.sort((function(e,t){var n=e.value,r=t.value;return n===r?0:n>r?-1:n<r?1:void 0}))}(n)).forEach((function(e,n){e.radius[1]=t[1]-i*n})),r?n.reverse():e.data=a,e.roseIncrement=i})),e}function u(e){return e.forEach((function(e){var t=e.data,n=e.percentToFixed,r=function(e){return(0,M.mulAdd)(e.map((function(e){return e.value})))}(t);t.forEach((function(e){var t=e.value;e.percent=t/r*100,e.percentForLabel=f(t/r*100,n)}));var i=(0,M.mulAdd)(t.slice(0,-1).map((function(e){return e.percent})));t.slice(-1)[0].percent=100-i,t.slice(-1)[0].percentForLabel=f(100-i,n)})),e}function f(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=e.toString(),r=n.split("."),i=r[1]||"0",o=i.slice(0,t);return r[1]=o,parseFloat(r.join("."))}function h(e){return e.forEach((function(e){var t=e.startAngle,n=e.data;n.forEach((function(e,r){var o=function(e,t){var n=2*Math.PI,r=e.slice(0,t+1),i=(0,M.mulAdd)(r.map((function(e){return e.percent}))),o=e[t].percent;return[n*(i-o)/100,n*i/100]}(n,r),a=(0,i.default)(o,2),s=a[0],l=a[1];e.startAngle=t+s,e.endAngle=t+l}))})),e}function p(e){return e.forEach((function(e){e.data.forEach((function(t){t.insideLabelPos=function(e,t){var n=e.center,r=t.startAngle,a=t.endAngle,s=(0,i.default)(t.radius,2),l=s[0],d=s[1],c=(l+d)/2,u=(r+a)/2;return L.getCircleRadianPoint.apply(void 0,(0,o.default)(n).concat([c,u]))}(e,t)}))})),e}function g(e){return e.forEach((function(e){var t=e.data,n=e.center;t.forEach((function(e){var t=e.startAngle,r=e.endAngle,i=e.radius,a=(t+r)/2,s=L.getCircleRadianPoint.apply(void 0,(0,o.default)(n).concat([i[1],a]));e.edgeCenterPos=s}))})),e}function v(e){return e.forEach((function(e){var t=A(e),n=A(e,!1);t=C(t),n=C(n),b(t,e),b(n,e,!1)})),e}function m(e){var t=e.outsideLabel.labelLineBendGap,n=function(e){var t=e.data.map((function(e){var t=(0,i.default)(e.radius,2);t[0];return t[1]}));return Math.max.apply(Math,(0,o.default)(t))}(e);return"number"!=typeof t&&(t=parseInt(t)/100*n),t+n}function A(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=e.data,r=e.center,i=r[0];return n.filter((function(e){var n=e.edgeCenterPos[0];return t?n<=i:n>i}))}function C(e){return e.sort((function(e,t){var n=(0,i.default)(e.edgeCenterPos,2),r=(n[0],n[1]),o=(0,i.default)(t.edgeCenterPos,2),a=(o[0],o[1]);return r>a?1:r<a?-1:r===a?0:void 0})),e}function b(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=t.center,i=t.outsideLabel,a=m(t);e.forEach((function(e){var t=e.edgeCenterPos,s=e.startAngle,l=e.endAngle,d=i.labelLineEndLength,c=(s+l)/2,u=L.getCircleRadianPoint.apply(void 0,(0,o.default)(r).concat([a,c])),f=(0,o.default)(u);f[0]+=d*(n?-1:1),e.labelLine=[t,u,f],e.labelLineLength=(0,M.getPolylineLength)(e.labelLine),e.align={textAlign:"left",textBaseline:"middle"},n&&(e.align.textAlign="right")}))}function x(e){var t=e.data,n=e.animationCurve,r=e.animationFrame,i=e.rLevel;return t.map((function(t,o){return{name:"pie",index:i,animationCurve:n,animationFrame:r,shape:E(e,o),style:B(e,o)}}))}function w(e){var t=e.animationDelayGap,n=e.startAnimationCurve,r=x(e);return r.forEach((function(e,r){e.animationCurve=n,e.animationDelay=r*t,e.shape.or=e.shape.ir})),r}function k(e){e.animationDelay=0}function E(e,t){var n=e.center,r=e.data[t],i=r.radius;return{startAngle:r.startAngle,endAngle:r.endAngle,ir:i[0],or:i[1],rx:n[0],ry:n[1]}}function B(e,t){var n=e.pieStyle,r=e.data[t].color;return(0,M.deepMerge)({fill:r},n)}function S(e){var t=e.animationCurve,n=e.animationFrame,r=e.data,i=e.rLevel;return r.map((function(r,o){return{name:"text",index:i,visible:e.insideLabel.show,animationCurve:t,animationFrame:n,shape:O(e,o),style:I(e)}}))}function O(e,t){var n=e.insideLabel,i=e.data,o=n.formatter,a=i[t],s=(0,r.default)(o),l="";return"string"===s&&(l=(l=(l=o.replace("{name}",a.name)).replace("{percent}",a.percentForLabel)).replace("{value}",a.value)),"function"===s&&(l=o(a)),{content:l,position:a.insideLabelPos}}function I(e,t){return e.insideLabel.style}function j(e){var t=e.animationCurve,n=e.animationFrame,r=e.data,i=e.rLevel;return r.map((function(r,o){return{name:"polyline",index:i,visible:e.outsideLabel.show,animationCurve:t,animationFrame:n,shape:R(e,o),style:G(e,o)}}))}function F(e){var t=e.data,n=j(e);return n.forEach((function(e,n){e.style.lineDash=[0,t[n].labelLineLength]})),n}function R(e,t){return{points:e.data[t].labelLine}}function G(e,t){var n=e.outsideLabel,r=e.data,i=n.labelLineStyle,o=r[t].color;return(0,M.deepMerge)({stroke:o,lineDash:[r[t].labelLineLength,0]},i)}function D(e){var t=e.animationCurve,n=e.animationFrame,r=e.data,i=e.rLevel;return r.map((function(r,o){return{name:"text",index:i,visible:e.outsideLabel.show,animationCurve:t,animationFrame:n,shape:T(e,o),style:Y(e,o)}}))}function z(e){var t=e.data,n=D(e);return n.forEach((function(e,n){e.shape.position=t[n].labelLine[1]})),n}function T(e,t){var n=e.outsideLabel,i=e.data,o=n.formatter,a=i[t],s=a.labelLine,l=a.name,d=a.percentForLabel,c=a.value,u=(0,r.default)(o),f="";return"string"===u&&(f=(f=(f=o.replace("{name}",l)).replace("{percent}",d)).replace("{value}",c)),"function"===u&&(f=o(i[t])),{content:f,position:s[2]}}function Y(e,t){var r=e.outsideLabel,i=e.data[t],o=i.color,s=i.align,l=r.style;return(0,M.deepMerge)(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(r,!0).forEach((function(t){(0,n.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(r).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({fill:o},s),l)}}));C(bn);bn.pie;var yn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.radarAxis=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.radar,r=[];n&&(r=[r=f(r=u(r=c(r=d(r=l(r=s(n),e),e))))]);var i=r;r.length&&!r[0].show&&(i=[]);(0,pn.doUpdate)({chart:e,series:i,key:"radarAxisSplitArea",getGraphConfig:h,beforeUpdate:v,beforeChange:m}),(0,pn.doUpdate)({chart:e,series:i,key:"radarAxisSplitLine",getGraphConfig:A,beforeUpdate:x,beforeChange:w}),(0,pn.doUpdate)({chart:e,series:i,key:"radarAxisLine",getGraphConfig:k}),(0,pn.doUpdate)({chart:e,series:i,key:"radarAxisLable",getGraphConfig:_}),e.radarAxis=r[0]};var n=y(W),r=y(Ot),i=y(P);function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(n,!0).forEach((function(t){(0,r.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(n).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function s(e){return(0,M.deepMerge)((0,L.deepClone)(fn.radarAxisConfig),e)}function l(e,t){var n=t.render.area,r=e.center;return e.centerPos=r.map((function(e,t){return"number"==typeof e?e:parseInt(e)/100*n[t]})),e}function d(e,t){var n=t.render.area,r=e.splitNum,o=e.radius,a=Math.min.apply(Math,(0,i.default)(n))/2;"number"!=typeof o&&(o=parseInt(o)/100*a);var s=o/r;return e.ringRadius=new Array(r).fill(0).map((function(e,t){return s*(t+1)})),e.radius=o,e}function c(e){var t=e.indicator,n=e.centerPos,r=e.radius,o=e.startAngle,a=2*Math.PI,s=t.length,l=a/s,d=new Array(s).fill(0).map((function(e,t){return l*t+o}));return e.axisLineAngles=d,e.axisLinePosition=d.map((function(e){return L.getCircleRadianPoint.apply(void 0,(0,i.default)(n).concat([r,e]))})),e}function u(e){var t=e.ringRadius,n=t[0]/2;return e.areaRadius=t.map((function(e){return e-n})),e}function f(e){var t=e.axisLineAngles,n=e.centerPos,r=e.radius,o=e.axisLabel;return r+=o.labelGap,e.axisLabelPosition=t.map((function(e){return L.getCircleRadianPoint.apply(void 0,(0,i.default)(n).concat([r,e]))})),e}function h(e){var t=e.areaRadius,n=e.polygon,r=e.animationCurve,i=e.animationFrame,o=e.rLevel,a=n?"regPolygon":"ring";return t.map((function(t,n){return{name:a,index:o,visible:e.splitArea.show,animationCurve:r,animationFrame:i,shape:p(e,n),style:g(e,n)}}))}function p(e,t){var n=e.polygon,r=e.areaRadius,i=e.indicator,o=e.centerPos,a=i.length,s={rx:o[0],ry:o[1],r:r[t]};return n&&(s.side=a),s}function g(e,t){var n=e.splitArea,r=e.ringRadius,o=e.axisLineAngles,s=e.polygon,l=e.centerPos,d=n.color,c=n.style;c=a({fill:"rgba(0, 0, 0, 0)"},c);var u=r[0]-0;if(s){var f=L.getCircleRadianPoint.apply(void 0,(0,i.default)(l).concat([r[0],o[0]])),h=L.getCircleRadianPoint.apply(void 0,(0,i.default)(l).concat([r[0],o[1]]));u=(0,M.getPointToLineDistance)(l,f,h)}if(c=(0,M.deepMerge)((0,L.deepClone)(c,!0),{lineWidth:u}),!d.length)return c;var p=d.length;return(0,M.deepMerge)(c,{stroke:d[t%p]})}function v(e,t,n,r){var i=e[n];if(i){var o=r.chart.render;(t.polygon?"regPolygon":"ring")!==i[0].name&&(i.forEach((function(e){return o.delGraph(e)})),e[n]=null)}}function m(e,t){var n=t.shape.side;"number"==typeof n&&(e.shape.side=n)}function A(e){var t=e.ringRadius,n=e.polygon,r=e.animationCurve,i=e.animationFrame,o=e.rLevel,a=n?"regPolygon":"ring";return t.map((function(t,n){return{name:a,index:o,animationCurve:r,animationFrame:i,visible:e.splitLine.show,shape:C(e,n),style:b(e,n)}}))}function C(e,t){var n=e.ringRadius,r=e.centerPos,i=e.indicator,o=e.polygon,a={rx:r[0],ry:r[1],r:n[t]},s=i.length;return o&&(a.side=s),a}function b(e,t){var n=e.splitLine,r=n.color,i=n.style;if(i=a({fill:"rgba(0, 0, 0, 0)"},i),!r.length)return i;var o=r.length;return(0,M.deepMerge)(i,{stroke:r[t%o]})}function x(e,t,n,r){var i=e[n];if(i){var o=r.chart.render;(t.polygon?"regPolygon":"ring")!==i[0].name&&(i.forEach((function(e){return o.delGraph(e)})),e[n]=null)}}function w(e,t){var n=t.shape.side;"number"==typeof n&&(e.shape.side=n)}function k(e){var t=e.axisLinePosition,n=e.animationCurve,r=e.animationFrame,i=e.rLevel;return t.map((function(t,o){return{name:"polyline",index:i,visible:e.axisLine.show,animationCurve:n,animationFrame:r,shape:E(e,o),style:B(e,o)}}))}function E(e,t){return{points:[e.centerPos,e.axisLinePosition[t]]}}function B(e,t){var n=e.axisLine,r=n.color,i=n.style;if(!r.length)return i;var o=r.length;return(0,M.deepMerge)(i,{stroke:r[t%o]})}function _(e){var t=e.axisLabelPosition,n=e.animationCurve,r=e.animationFrame,i=e.rLevel;return t.map((function(t,o){return{name:"text",index:i,visible:e.axisLabel.show,animationCurve:n,animationFrame:r,shape:S(e,o),style:O(e,o)}}))}function S(e,t){var n=e.axisLabelPosition;return{content:e.indicator[t].name,position:n[t]}}function O(e,t){var r=e.axisLabel,i=(0,n.default)(e.centerPos,2),o=i[0],a=i[1],s=e.axisLabelPosition,l=r.color,d=r.style,c=(0,n.default)(s[t],2),u=c[0]>o?"left":"right",f=c[1]>a?"top":"bottom";if(d=(0,M.deepMerge)({textAlign:u,textBaseline:f},d),!l.length)return d;var h=l.length;return(0,M.deepMerge)(d,{fill:l[t%h]})}}));C(yn);yn.radarAxis;var xn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.radar=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.series;n||(n=[]);var r=(0,M.initNeedSeries)(n,fn.radarConfig,"radar");r=d(r=l(r=s(r,e),e),e),(0,pn.doUpdate)({chart:e,series:r,key:"radar",getGraphConfig:c,getStartGraphConfig:u,beforeChange:p}),(0,pn.doUpdate)({chart:e,series:r,key:"radarPoint",getGraphConfig:g,getStartGraphConfig:v}),(0,pn.doUpdate)({chart:e,series:r,key:"radarLabel",getGraphConfig:C})};var n=y(Ot),r=y(_),i=y(W),o=y(P);function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e,t){var n=t.radarAxis;if(!n)return[];var r=n.indicator,i=n.axisLineAngles,a=n.radius,s=n.centerPos;return e.forEach((function(e){var t=e.data;e.dataRadius=[],e.radarPosition=r.map((function(n,r){var l=n.max,d=n.min,c=t[r];"number"!=typeof l&&(l=c),"number"!=typeof d&&(d=0),"number"!=typeof c&&(c=d);var u=(c-d)/(l-d)*a;return e.dataRadius[r]=u,L.getCircleRadianPoint.apply(void 0,(0,o.default)(s).concat([u,i[r]]))}))})),e}function l(e,t){var n=t.radarAxis;if(!n)return[];var r=n.centerPos,i=n.axisLineAngles;return e.forEach((function(e){var t=e.dataRadius,n=e.label.labelGap;e.labelPosition=t.map((function(e,t){return L.getCircleRadianPoint.apply(void 0,(0,o.default)(r).concat([e+n,i[t]]))}))})),e}function d(e,t){var n=t.radarAxis;if(!n)return[];var r=(0,i.default)(n.centerPos,2),o=r[0],a=r[1];return e.forEach((function(e){var t=e.labelPosition.map((function(e){var t=(0,i.default)(e,2),n=t[0],r=t[1];return{textAlign:n>o?"left":"right",textBaseline:r>a?"top":"bottom"}}));e.labelAlign=t})),e}function c(e){var t=e.animationCurve,n=e.animationFrame;return[{name:"polyline",index:e.rLevel,animationCurve:t,animationFrame:n,shape:f(e),style:h(e)}]}function u(e,t){var n=t.chart.radarAxis.centerPos,r=c(e)[0],i=r.shape.points.length,a=new Array(i).fill(0).map((function(e){return(0,o.default)(n)}));return r.shape.points=a,[r]}function f(e){return{points:e.radarPosition,close:!0}}function h(e){var t=e.radarStyle,n=e.color,r=(0,Ee.getRgbaValue)(n);r[3]=.5;var i={stroke:n,fill:(0,Ee.getColorFromRgbValue)(r)};return(0,M.deepMerge)(i,t)}function p(e,t){var n=t.shape,r=e.shape.points,i=r.length,a=n.points.length;if(a>i){var s=r.slice(-1)[0],l=new Array(a-i).fill(0).map((function(e){return(0,o.default)(s)}));r.push.apply(r,(0,o.default)(l))}else a<i&&r.splice(a)}function g(e){var t=e.radarPosition,n=e.animationCurve,r=e.animationFrame,i=e.rLevel;return t.map((function(t,o){return{name:"circle",index:i,animationCurve:n,animationFrame:r,visible:e.point.show,shape:m(e,o),style:A(e)}}))}function v(e){var t=g(e);return t.forEach((function(e){return e.shape.r=.01})),t}function m(e,t){var n=e.radarPosition,r=e.point.radius,i=n[t];return{rx:i[0],ry:i[1],r:r}}function A(e,t){var n=e.point,r=e.color,i=n.style;return(0,M.deepMerge)({stroke:r},i)}function C(e){var t=e.labelPosition,n=e.animationCurve,r=e.animationFrame,i=e.rLevel;return t.map((function(t,o){return{name:"text",index:i,visible:e.label.show,animationCurve:n,animationFrame:r,shape:b(e,o),style:x(e,o)}}))}function b(e,t){var n,o,a,s,l,d,c,u,f=e.labelPosition,h=e.label,p=e.data,g=h.offset,v=h.formatter,m=(n=f[t],o=g,a=(0,i.default)(n,2),s=a[0],l=a[1],d=(0,i.default)(o,2),c=d[0],u=d[1],[s+c,l+u]),A=p[t]?p[t].toString():"0",C=(0,r.default)(v);return"string"===C&&(A=v.replace("{value}",A)),"function"===C&&(A=v(A)),{content:A,position:m}}function x(e,t){var r=e.label,i=e.color,o=e.labelAlign,s=r.style,l=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(r,!0).forEach((function(t){(0,n.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(r).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({fill:i},o[t]);return(0,M.deepMerge)(l,s)}}));C(xn);xn.radar;var wn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.gauge=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.series;n||(n=[]);var r=(0,M.initNeedSeries)(n,cn.gaugeConfig,"gauge");r=m(r=v(r=g(r=p(r=h(r=f(r=u(r=c(r=d(r=l(r,e),e),e)))))))),(0,pn.doUpdate)({chart:e,series:r,key:"gaugeAxisTick",getGraphConfig:A}),(0,pn.doUpdate)({chart:e,series:r,key:"gaugeAxisLabel",getGraphConfig:x}),(0,pn.doUpdate)({chart:e,series:r,key:"gaugeBackgroundArc",getGraphConfig:E,getStartGraphConfig:O}),(0,pn.doUpdate)({chart:e,series:r,key:"gaugeArc",getGraphConfig:I,getStartGraphConfig:R,beforeChange:G}),(0,pn.doUpdate)({chart:e,series:r,key:"gaugePointer",getGraphConfig:D,getStartGraphConfig:Y}),(0,pn.doUpdate)({chart:e,series:r,key:"gaugeDetails",getGraphConfig:N})};var n=y(Ot),r=y(_),i=y(W),o=y(P);function a(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(r,!0).forEach((function(t){(0,n.default)(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(r).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function l(e,t){var n=t.render.area;return e.forEach((function(e){var t=e.center;t=t.map((function(e,t){return"number"==typeof e?e:parseInt(e)/100*n[t]})),e.center=t})),e}function d(e,t){var n=t.render.area,r=Math.min.apply(Math,(0,o.default)(n))/2;return e.forEach((function(e){var t=e.radius;"number"!=typeof t&&(t=parseInt(t)/100*r),e.radius=t})),e}function c(e,t){var n=t.render.area,r=Math.min.apply(Math,(0,o.default)(n))/2;return e.forEach((function(e){var t=e.radius,n=e.data,i=e.arcLineWidth;n.forEach((function(e){var n=e.radius,o=e.lineWidth;n||(n=t),"number"!=typeof n&&(n=parseInt(n)/100*r),e.radius=n,o||(o=i),e.lineWidth=o}))})),e}function u(e,t){return e.forEach((function(e){var t=e.startAngle,n=e.endAngle,r=e.data,i=e.min,o=e.max,a=n-t,s=o-i;r.forEach((function(e){var n=e.value,r=Math.abs((n-i)/s*a);e.startAngle=t,e.endAngle=t+r}))})),e}function f(e,t){return e.forEach((function(e){e.data.forEach((function(e){var t=e.color,n=e.gradient;n&&n.length||(n=t),n instanceof Array||(n=[n]),e.gradient=n}))})),e}function h(e,t){return e.forEach((function(e){var t=e.startAngle,n=e.endAngle,r=e.splitNum,i=e.center,a=e.radius,s=e.arcLineWidth,l=e.axisTick,d=l.tickLength,c=l.style.lineWidth,u=n-t,f=a-s/2,h=f-d,p=u/(r-1),g=2*Math.PI*a*u/(2*Math.PI),v=Math.ceil(c/2)/g*u;e.tickAngles=[],e.tickInnerRadius=[],e.tickPosition=new Array(r).fill(0).map((function(n,a){var s=t+p*a;return 0===a&&(s+=v),a===r-1&&(s-=v),e.tickAngles[a]=s,e.tickInnerRadius[a]=h,[L.getCircleRadianPoint.apply(void 0,(0,o.default)(i).concat([f,s])),L.getCircleRadianPoint.apply(void 0,(0,o.default)(i).concat([h,s]))]}))})),e}function p(e,t){return e.forEach((function(e){var t=e.center,n=e.tickInnerRadius,r=e.tickAngles,a=e.axisLabel.labelGap,s=r.map((function(e,i){return L.getCircleRadianPoint.apply(void 0,(0,o.default)(t).concat([n[i]-a,r[i]]))})),l=s.map((function(e){var n=(0,i.default)(e,2),r=n[0],o=n[1];return{textAlign:r>t[0]?"right":"left",textBaseline:o>t[1]?"bottom":"top"}}));e.labelPosition=s,e.labelAlign=l})),e}function g(e,t){return e.forEach((function(e){var t=e.axisLabel,n=e.min,i=e.max,o=e.splitNum,a=t.data,s=t.formatter,l=(i-n)/(o-1),d=new Array(o).fill(0).map((function(e,t){return parseInt(n+l*t)})),c=(0,r.default)(s);a=(0,M.deepMerge)(d,a).map((function(e,t){var n=e;return"string"===c&&(n=s.replace("{value}",e)),"function"===c&&(n=s({value:e,index:t})),n})),t.data=a})),e}function v(e,t){return e.forEach((function(e){var t=e.data,n=e.details,r=e.center,a=n.position,s=n.offset,l=t.map((function(e){var t,n,l,d,c,u,f,h,p=e.startAngle,g=e.endAngle,v=e.radius,m=null;return"center"===a?m=r:"start"===a?m=L.getCircleRadianPoint.apply(void 0,(0,o.default)(r).concat([v,p])):"end"===a&&(m=L.getCircleRadianPoint.apply(void 0,(0,o.default)(r).concat([v,g]))),t=m,n=s,l=(0,i.default)(t,2),d=l[0],c=l[1],u=(0,i.default)(n,2),f=u[0],h=u[1],[d+f,c+h]}));e.detailsPosition=l})),e}function m(e,t){return e.forEach((function(e){var t=e.data,n=e.details.formatter,i=(0,r.default)(n),o=t.map((function(e){var t=e.value;return"string"===i&&(t=(t=n.replace("{value}","{nt}")).replace("{name}",e.name)),"function"===i&&(t=n(e)),t.toString()}));e.detailsContent=o})),e}function A(e){var t=e.tickPosition,n=e.animationCurve,r=e.animationFrame,i=e.rLevel;return t.map((function(t,o){return{name:"polyline",index:i,visible:e.axisTick.show,animationCurve:n,animationFrame:r,shape:C(e,o),style:b(e)}}))}function C(e,t){return{points:e.tickPosition[t]}}function b(e,t){return e.axisTick.style}function x(e){var t=e.labelPosition,n=e.animationCurve,r=e.animationFrame,i=e.rLevel;return t.map((function(t,o){return{name:"text",index:i,visible:e.axisLabel.show,animationCurve:n,animationFrame:r,shape:w(e,o),style:k(e,o)}}))}function w(e,t){var n=e.labelPosition;return{content:e.axisLabel.data[t].toString(),position:n[t]}}function k(e,t){var n=e.labelAlign,r=e.axisLabel.style;return(0,M.deepMerge)(s({},n[t]),r)}function E(e){var t=e.animationCurve,n=e.animationFrame;return[{name:"arc",index:e.rLevel,visible:e.backgroundArc.show,animationCurve:t,animationFrame:n,shape:B(e),style:S(e)}]}function B(e){var t=e.startAngle,n=e.endAngle,r=e.center,i=e.radius;return{rx:r[0],ry:r[1],r:i,startAngle:t,endAngle:n}}function S(e){var t=e.backgroundArc,n=e.arcLineWidth,r=t.style;return(0,M.deepMerge)({lineWidth:n},r)}function O(e){var t=E(e)[0],n=s({},t.shape);return n.endAngle=t.shape.startAngle,t.shape=n,[t]}function I(e){var t=e.data,n=e.animationCurve,r=e.animationFrame,i=e.rLevel;return t.map((function(t,o){return{name:"agArc",index:i,animationCurve:n,animationFrame:r,shape:j(e,o),style:F(e,o)}}))}function j(e,t){var n=e.data,r=e.center,i=e.endAngle,o=n[t],a=o.radius,s=o.startAngle,l=o.endAngle;return o.localGradient&&(i=l),{rx:r[0],ry:r[1],r:a,startAngle:s,endAngle:l,gradientEndAngle:i}}function F(e,t){var n=e.data,r=e.dataItemStyle,i=n[t],o=i.lineWidth,a=i.gradient;return a=a.map((function(e){return(0,Ee.getRgbaValue)(e)})),(0,M.deepMerge)({lineWidth:o,gradient:a},r)}function R(e){var t=I(e);return t.map((function(e){var t=s({},e.shape);t.endAngle=e.shape.startAngle,e.shape=t})),t}function G(e,t){var n=e.style.gradient,r=n.length,i=t.style.gradient.length;if(r>i)n.splice(i);else{var a=n.slice(-1)[0];n.push.apply(n,(0,o.default)(new Array(i-r).fill(0).map((function(e){return(0,o.default)(a)}))))}}function D(e){var t=e.animationCurve,n=e.animationFrame,r=e.center;return[{name:"polyline",index:e.rLevel,visible:e.pointer.show,animationCurve:t,animationFrame:n,shape:z(e),style:T(e),setGraphCenter:function(e,t){t.style.graphCenter=r}}]}function z(e){var t,n,r,o,a,s,l,d,c=e.center;return{points:(t=c,n=(0,i.default)(t,2),r=n[0],o=n[1],a=[r,o-40],s=[r+5,o],l=[r,o+10],d=[r-5,o],[a,s,l,d]),close:!0}}function T(e){var t=e.startAngle,n=e.endAngle,r=e.min,i=e.max,o=e.data,a=e.pointer,s=e.center,l=a.valueIndex,d=a.style,c=((o[l]?o[l].value:0)-r)/(i-r)*(n-t)+t+Math.PI/2;return(0,M.deepMerge)({rotate:(0,M.radianToAngle)(c),scale:[1,1],graphCenter:s},d)}function Y(e){var t=e.startAngle,n=D(e)[0];return n.style.rotate=(0,M.radianToAngle)(t+Math.PI/2),[n]}function N(e){var t=e.detailsPosition,n=e.animationCurve,r=e.animationFrame,i=e.rLevel,o=e.details.show;return t.map((function(t,a){return{name:"numberText",index:i,visible:o,animationCurve:n,animationFrame:r,shape:X(e,a),style:$(e,a)}}))}function X(e,t){var n=e.detailsPosition,r=e.detailsContent,i=e.data,o=e.details,a=n[t],s=r[t];return{number:[i[t].value],content:s,position:a,toFixed:o.valueToFixed}}function $(e,t){var n=e.details,r=e.data,i=n.style,o=r[t].color;return(0,M.deepMerge)({fill:o},i)}}));C(wn);wn.gauge;var kn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.legend=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.legend;n=n?[n=l(n=s(n=a(n=o(n=(0,M.deepMerge)((0,L.deepClone)(fn.legendConfig,!0),n)),t,e),e),e)]:[];(0,pn.doUpdate)({chart:e,series:n,key:"legendIcon",getGraphConfig:u}),(0,pn.doUpdate)({chart:e,series:n,key:"legendText",getGraphConfig:p})};var n=y(Ot),r=y(W),i=y(_);function o(e){var t=e.data;return e.data=t.map((function(e){var t=(0,i.default)(e);return"string"===t?{name:e}:"object"===t?e:{name:""}})),e}function a(e,t,n){var r=t.series,i=n.legendStatus,o=e.data.filter((function(e){var t=e.name,n=r.find((function(e){var n=e.name;return t===n}));return!!n&&(e.color||(e.color=n.color),e.icon||(e.icon=n.type),e)}));return i&&i.length===e.data.length||(i=new Array(e.data.length).fill(!0)),o.forEach((function(e,t){return e.status=i[t]})),e.data=o,n.legendStatus=i,e}function s(e,t){var n=t.render.ctx,r=e.data,i=e.textStyle,o=e.textUnselectedStyle;return r.forEach((function(e){var t=e.status,r=e.name;e.textWidth=function(e,t,n){return e.font=function(e){var t=e.fontFamily,n=e.fontSize;return"".concat(n,"px ").concat(t)}(n),e.measureText(t).width}(n,r,t?i:o)})),e}function l(e,t){return"vertical"===e.orient?function(e,t){var n=function(e,t){var n=e.left,r=e.right,i=t.render.area[0],o=[n,r].findIndex((function(e){return"auto"!==e}));if(-1===o)return[!0,i-10];var a=[n,r][o];return"number"!=typeof a&&(a=parseInt(a)/100*i),[Boolean(o),a]}(e,t),i=(0,r.default)(n,2),o=i[0],a=i[1],s=function(e,t){var n=e.iconHeight,r=e.itemGap,i=e.data,o=e.top,a=e.bottom,s=t.render.area[1],l=i.length,d=l*n+(l-1)*r,c=[o,a].findIndex((function(e){return"auto"!==e}));if(-1===c)return(s-d)/2;var u=[o,a][c];return"number"!=typeof u&&(u=parseInt(u)/100*s),1===c&&(u=s-u-d),u}(e,t);!function(e,t){var n=e.data,r=e.iconWidth,i=e.iconHeight,o=e.itemGap,a=i/2;n.forEach((function(e,n){var s=e.textWidth,l=(i+o)*n+a,d=t?0-r:0,c=t?d-5-s:r+5;e.iconPosition=[d,l],e.textPosition=[c,l]}))}(e,o);var l={textAlign:"left",textBaseline:"middle"};e.data.forEach((function(e){var t=e.textPosition,n=e.iconPosition;e.textPosition=c(t,[a,s]),e.iconPosition=c(n,[a,s]),e.align=l}))}(e,t):function(e,t){var n=e.iconHeight,r=e.itemGap,i=function(e,t){var n=e.data,r=e.iconWidth,i=t.render.area[0],o=0,a=[[]];return n.forEach((function(t,n){var s=d(o,n,e);s+r+5+t.textWidth>=i&&(s=d(o=n,n,e),a.push([])),t.iconPosition=[s,0],t.textPosition=[s+r+5,0],a.slice(-1)[0].push(t)})),a}(e,t),o=i.map((function(n){return function(e,t,n){var r=t.left,i=t.right,o=t.iconWidth,a=t.itemGap,s=n.render.area[0],l=e.length,d=(0,M.mulAdd)(e.map((function(e){return e.textWidth})))+l*(5+o)+(l-1)*a,c=[r,i].findIndex((function(e){return"auto"!==e}));return-1===c?(s-d)/2:0===c?"number"==typeof r?r:parseInt(r)/100*s:("number"!=typeof i&&(i=parseInt(i)/100*s),s-(d+i))}(n,e,t)})),a=function(e,t){var n=e.top,r=e.bottom,i=e.iconHeight,o=t.render.area[1],a=[n,r].findIndex((function(e){return"auto"!==e})),s=i/2;if(-1===a){var l=t.gridArea,d=l.y,c=l.h;return d+c+45-s}return 0===a?"number"==typeof n?n-s:parseInt(n)/100*o-s:("number"!=typeof r&&(r=parseInt(r)/100*o),o-r-s)}(e,t),s={textAlign:"left",textBaseline:"middle"};i.forEach((function(e,t){return e.forEach((function(e){var i=e.iconPosition,l=e.textPosition,d=o[t],u=a+t*(r+n);e.iconPosition=c(i,[d,u]),e.textPosition=c(l,[d,u]),e.align=s}))}))}(e,t),e}function d(e,t,n){var r=n.data,i=n.iconWidth,o=n.itemGap,a=r.slice(e,t);return(0,M.mulAdd)(a.map((function(e){return e.textWidth})))+(t-e)*(o+5+i)}function c(e,t){var n=(0,r.default)(e,2),i=n[0],o=n[1],a=(0,r.default)(t,2);return[i+a[0],o+a[1]]}function u(e,t){var r=e.data,i=e.selectAble,o=e.animationCurve,a=e.animationFrame,s=e.rLevel;return r.map((function(r,l){return(0,n.default)({name:"line"===r.icon?"lineIcon":"rect",index:s,visible:e.show,hover:i,click:i,animationCurve:o,animationFrame:a,shape:f(e,l),style:h(e,l)},"click",A(e,l,t))}))}function f(e,t){var n=e.data,i=e.iconWidth,o=e.iconHeight,a=(0,r.default)(n[t].iconPosition,2);return{x:a[0],y:a[1]-o/2,w:i,h:o}}function h(e,t){var n=e.data,r=e.iconStyle,i=e.iconUnselectedStyle,o=n[t],a=o.status,s=o.color,l=a?r:i;return(0,M.deepMerge)({fill:s},l)}function p(e,t){var n=e.data,r=e.selectAble,i=e.animationCurve,o=e.animationFrame,a=e.rLevel;return n.map((function(n,s){return{name:"text",index:a,visible:e.show,hover:r,animationCurve:i,animationFrame:o,hoverRect:m(e,s),shape:g(e,s),style:v(e,s),click:A(e,s,t)}}))}function g(e,t){var n=e.data[t],r=n.textPosition;return{content:n.name,position:r}}function v(e,t){var n=e.textStyle,r=e.textUnselectedStyle,i=e.data[t],o=i.status,a=i.align,s=o?n:r;return(0,M.deepMerge)((0,L.deepClone)(s,!0),a)}function m(e,t){var n=e.textStyle,i=e.textUnselectedStyle,o=e.data[t],a=o.status,s=(0,r.default)(o.textPosition,2),l=s[0],d=s[1],c=o.textWidth,u=(a?n:i).fontSize;return[l,d-u/2,c,u]}function A(e,t,n){var r=e.data[t].name;return function(){var e=n.chart,i=e.legendStatus,o=e.option,a=!i[t];o.series.find((function(e){return e.name===r})).show=a,i[t]=a,n.chart.setOption(o)}}}));C(kn);kn.legend;var En=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"mergeColor",{enumerable:!0,get:function(){return hn.mergeColor}}),Object.defineProperty(t,"title",{enumerable:!0,get:function(){return gn.title}}),Object.defineProperty(t,"grid",{enumerable:!0,get:function(){return vn.grid}}),Object.defineProperty(t,"axis",{enumerable:!0,get:function(){return mn.axis}}),Object.defineProperty(t,"line",{enumerable:!0,get:function(){return An.line}}),Object.defineProperty(t,"bar",{enumerable:!0,get:function(){return Cn.bar}}),Object.defineProperty(t,"pie",{enumerable:!0,get:function(){return bn.pie}}),Object.defineProperty(t,"radarAxis",{enumerable:!0,get:function(){return yn.radarAxis}}),Object.defineProperty(t,"radar",{enumerable:!0,get:function(){return xn.radar}}),Object.defineProperty(t,"gauge",{enumerable:!0,get:function(){return wn.gauge}}),Object.defineProperty(t,"legend",{enumerable:!0,get:function(){return kn.legend}})}));C(En);var Bn=b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=y(_),r=y(St),i=y(Xt),o=function e(t){if((0,r.default)(this,e),!t)return console.error("Charts Missing parameters!"),!1;var n=t.clientWidth,o=t.clientHeight,a=document.createElement("canvas");a.setAttribute("width",n),a.setAttribute("height",o),t.appendChild(a);var s={container:t,canvas:a,render:new i.default(a),option:null};Object.assign(this,s)};t.default=o,o.prototype.setOption=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e||"object"!==(0,n.default)(e))return console.error("setOption Missing parameters!"),!1;t&&this.render.graphs.forEach((function(e){return e.animationEnd()}));var r=(0,L.deepClone)(e,!0);(0,En.mergeColor)(this,r),(0,En.grid)(this,r),(0,En.axis)(this,r),(0,En.radarAxis)(this,r),(0,En.title)(this,r),(0,En.bar)(this,r),(0,En.line)(this,r),(0,En.pie)(this,r),(0,En.radar)(this,r),(0,En.gauge)(this,r),(0,En.legend)(this,r),this.option=e,this.render.launchAnimation()},o.prototype.resize=function(){var e=this.container,t=this.canvas,n=this.render,r=this.option,i=e.clientWidth,o=e.clientHeight;t.setAttribute("width",i),t.setAttribute("height",o),n.area=[i,o],this.setOption(r)}}));C(Bn);var Pn=C(b((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"changeDefaultConfig",{enumerable:!0,get:function(){return fn.changeDefaultConfig}}),t.default=void 0;var n=y(Bn).default;t.default=n})));const _n={name:"DvCharts",mixins:[i],props:{option:{type:Object,default:()=>({})}},data(){const e=r();return{ref:"charts-container-"+e,chartRef:"chart-"+e,chart:null}},watch:{option(){let{chart:e,option:t}=this;e&&(t||(t={}),e.setOption(t,!0))}},methods:{afterAutoResizeMixinInit(){const{initChart:e}=this;e()},initChart(){const{$refs:e,chartRef:t,option:n}=this,r=this.chart=new Pn(e[t]);n&&r.setOption(n)},onResize(){const{chart:e}=this;e&&e.resize()}}};var Sn=function(){var e=this.$createElement,t=this._self._c||e;return t("div",{ref:this.ref,staticClass:"dv-charts-container"},[t("div",{ref:this.chartRef,staticClass:"charts-canvas-container"})])};Sn._withStripped=!0;const On=a({render:Sn,staticRenderFns:[]},(function(e){e&&e("data-v-1f446fe6_0",{source:".dv-charts-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-charts-container .charts-canvas-container {\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;AACd;AACA;EACE,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-charts-container {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n.dv-charts-container .charts-canvas-container {\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),_n,void 0,!1,void 0,!1,l,void 0,void 0);function In(e){e.component(On.name,On)}const Wn={name:"DvDigitalFlop",props:{config:{type:Object,default:()=>({})}},data:()=>({renderer:null,defaultConfig:{number:[],content:"",toFixed:0,textAlign:"center",rowGap:0,style:{fontSize:30,fill:"#3de7c9"},animationCurve:"easeOutCubic",animationFrame:50},mergedConfig:null,graph:null}),watch:{config(){const{update:e}=this;e()}},methods:{init(){const{initRender:e,mergeConfig:t,initGraph:n}=this;e(),t(),n()},initRender(){const{$refs:e}=this;this.renderer=new $t(e["digital-flop"])},mergeConfig(){const{defaultConfig:e,config:t}=this;this.mergedConfig=F(j(e,!0),t||{})},initGraph(){const{getShape:e,getStyle:t,renderer:n,mergedConfig:r}=this,{animationCurve:i,animationFrame:o}=r,a=e(),s=t();this.graph=n.add({name:"numberText",animationCurve:i,animationFrame:o,shape:a,style:s})},getShape(){const{number:e,content:t,toFixed:n,textAlign:r,rowGap:i}=this.mergedConfig,[o,a]=this.renderer.area,s=[o/2,a/2];return"left"===r&&(s[0]=0),"right"===r&&(s[0]=o),{number:e,content:t,toFixed:n,position:s,rowGap:i}},getStyle(){const{style:e,textAlign:t}=this.mergedConfig;return F(e,{textAlign:t,textBaseline:"middle"})},update(){const{mergeConfig:e,mergeShape:t,getShape:n,getStyle:r,graph:i,mergedConfig:o}=this;if(i.animationEnd(),e(),!i)return;const{animationCurve:a,animationFrame:s}=o,l=n(),d=r();t(i,l),i.animationCurve=a,i.animationFrame=s,i.animation("style",d,!0),i.animation("shape",l)},mergeShape(e,t){e.shape.number.length!==t.number.length&&(e.shape.number=t.number)}},mounted(){const{init:e}=this;e()}};var Ln=function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"dv-digital-flop"},[t("canvas",{ref:"digital-flop"})])};Ln._withStripped=!0;const jn=a({render:Ln,staticRenderFns:[]},(function(e){e&&e("data-v-4570caa9_0",{source:".dv-digital-flop canvas {\n  width: 100%;\n  height: 100%;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,WAAW;EACX,YAAY;AACd",file:"main.vue",sourcesContent:[".dv-digital-flop canvas {\n  width: 100%;\n  height: 100%;\n}\n"]},media:void 0})}),Wn,void 0,!1,void 0,!1,l,void 0,void 0);const Mn={name:"DvActiveRingChart",components:{dvDigitalFlop:jn},props:{config:{type:Object,default:()=>({})}},data:()=>({defaultConfig:{radius:"50%",activeRadius:"55%",data:[{name:"",value:0}],lineWidth:20,activeTimeGap:3e3,color:[],digitalFlopStyle:{fontSize:25,fill:"#fff"},digitalFlopToFixed:0,animationCurve:"easeOutCubic",animationFrame:50,showOriginValue:!1},mergedConfig:null,chart:null,activeIndex:0,animationHandler:""}),computed:{digitalFlop(){const{mergedConfig:e,activeIndex:t}=this;if(!e)return{};const{digitalFlopStyle:n,digitalFlopToFixed:r,data:i,showOriginValue:o}=e,a=i.map(({value:e})=>e);let s;if(o)s=a[t];else{const e=a.reduce((e,t)=>e+t,0);s=parseFloat(a[t]/e*100)||0}return{content:o?"{nt}":"{nt}%",number:[s],style:n,toFixed:r}},ringName(){const{mergedConfig:e,activeIndex:t}=this;return e?e.data[t].name:""},fontSize(){const{mergedConfig:e}=this;return e?`font-size: ${e.digitalFlopStyle.fontSize}px;`:""}},watch:{config(){const{animationHandler:e,mergeConfig:t,setRingOption:n}=this;clearTimeout(e),this.activeIndex=0,t(),n()}},methods:{init(){const{initChart:e,mergeConfig:t,setRingOption:n}=this;e(),t(),n()},initChart(){const{$refs:e}=this;this.chart=new Pn(e["active-ring-chart"])},mergeConfig(){const{defaultConfig:e,config:t}=this;this.mergedConfig=F(j(e,!0),t||{})},setRingOption(){const{getRingOption:e,chart:t,ringAnimation:n}=this,r=e();t.setOption(r,!0),n()},getRingOption(){const{mergedConfig:e,getRealRadius:t}=this,n=t();return e.data.forEach(e=>{e.radius=n}),{series:[{type:"pie",...e,outsideLabel:{show:!1}}],color:e.color}},getRealRadius(e=!1){const{mergedConfig:t,chart:n}=this,{radius:r,activeRadius:i,lineWidth:o}=t,a=Math.min(...n.render.area)/2,s=o/2;let l=e?i:r;return"number"!=typeof l&&(l=parseInt(l)/100*a),[l-s,l+s]},ringAnimation(){let{activeIndex:e,getRingOption:t,chart:n,getRealRadius:r}=this;const i=r(),o=r(!0),a=t(),{data:s}=a.series[0];s.forEach((t,n)=>{t.radius=n===e?o:i}),n.setOption(a,!0);const{activeTimeGap:l}=a.series[0];this.animationHandler=setTimeout(t=>{e+=1,e>=s.length&&(e=0),this.activeIndex=e,this.ringAnimation()},l)}},mounted(){const{init:e}=this;e()},beforeDestroy(){const{animationHandler:e}=this;clearTimeout(e)}};var Fn=function(){var e=this.$createElement,t=this._self._c||e;return t("div",{staticClass:"dv-active-ring-chart"},[t("div",{ref:"active-ring-chart",staticClass:"active-ring-chart-container"}),this._v(" "),t("div",{staticClass:"active-ring-info"},[t("dv-digital-flop",{attrs:{config:this.digitalFlop}}),this._v(" "),t("div",{staticClass:"active-ring-name",style:this.fontSize},[this._v(this._s(this.ringName))])],1)])};Fn._withStripped=!0;const Rn=a({render:Fn,staticRenderFns:[]},(function(e){e&&e("data-v-7319e037_0",{source:".dv-active-ring-chart {\n  position: relative;\n}\n.dv-active-ring-chart .active-ring-chart-container {\n  width: 100%;\n  height: 100%;\n}\n.dv-active-ring-chart .active-ring-info {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  left: 0px;\n  top: 0px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n.dv-active-ring-chart .active-ring-info .dv-digital-flop {\n  width: 100px;\n  height: 30px;\n}\n.dv-active-ring-chart .active-ring-info .active-ring-name {\n  width: 100px;\n  height: 30px;\n  color: #fff;\n  text-align: center;\n  vertical-align: middle;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;AACpB;AACA;EACE,WAAW;EACX,YAAY;AACd;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,SAAS;EACT,QAAQ;EACR,aAAa;EACb,sBAAsB;EACtB,uBAAuB;EACvB,mBAAmB;AACrB;AACA;EACE,YAAY;EACZ,YAAY;AACd;AACA;EACE,YAAY;EACZ,YAAY;EACZ,WAAW;EACX,kBAAkB;EAClB,sBAAsB;EACtB,uBAAuB;EACvB,gBAAgB;EAChB,mBAAmB;AACrB",file:"main.vue",sourcesContent:[".dv-active-ring-chart {\n  position: relative;\n}\n.dv-active-ring-chart .active-ring-chart-container {\n  width: 100%;\n  height: 100%;\n}\n.dv-active-ring-chart .active-ring-info {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  left: 0px;\n  top: 0px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n.dv-active-ring-chart .active-ring-info .dv-digital-flop {\n  width: 100px;\n  height: 30px;\n}\n.dv-active-ring-chart .active-ring-info .active-ring-name {\n  width: 100px;\n  height: 30px;\n  color: #fff;\n  text-align: center;\n  vertical-align: middle;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n}\n"]},media:void 0})}),Mn,void 0,!1,void 0,!1,l,void 0,void 0);function Gn(e){e.component(Rn.name,Rn)}const Dn={name:"DvCapsuleChart",props:{config:{type:Object,default:()=>({})}},data:()=>({defaultConfig:{data:[],colors:["#37a2da","#32c5e9","#67e0e3","#9fe6b8","#ffdb5c","#ff9f7f","#fb7293"],unit:"",showValue:!1},mergedConfig:null,capsuleLength:[],capsuleValue:[],labelData:[],labelDataLength:[]}),watch:{config(){const{calcData:e}=this;e()}},methods:{calcData(){const{mergeConfig:e,calcCapsuleLengthAndLabelData:t}=this;e(),t()},mergeConfig(){let{config:e,defaultConfig:t}=this;this.mergedConfig=F(j(t,!0),e||{})},calcCapsuleLengthAndLabelData(){const{data:e}=this.mergedConfig;if(!e.length)return;const t=e.map(({value:e})=>e),n=Math.max(...t);this.capsuleValue=t,this.capsuleLength=t.map(e=>n?e/n:0);const r=n/5,i=Array.from(new Set(new Array(6).fill(0).map((e,t)=>Math.ceil(t*r))));this.labelData=i,this.labelDataLength=Array.from(i).map(e=>n?e/n:0)}},mounted(){const{calcData:e}=this;e()}};var zn=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"dv-capsule-chart"},[e.mergedConfig?[n("div",{staticClass:"label-column"},[e._l(e.mergedConfig.data,(function(t){return n("div",{key:t.name},[e._v(e._s(t.name))])})),e._v(" "),n("div",[e._v(" ")])],2),e._v(" "),n("div",{staticClass:"capsule-container"},[e._l(e.capsuleLength,(function(t,r){return n("div",{key:r,staticClass:"capsule-item"},[n("div",{staticClass:"capsule-item-column",style:"width: "+100*t+"%; background-color: "+e.mergedConfig.colors[r%e.mergedConfig.colors.length]+";"},[e.mergedConfig.showValue?n("div",{staticClass:"capsule-item-value"},[e._v(e._s(e.capsuleValue[r]))]):e._e()])])})),e._v(" "),n("div",{staticClass:"unit-label"},e._l(e.labelData,(function(t,r){return n("div",{key:t+r},[e._v(e._s(t))])})),0)],2),e._v(" "),e.mergedConfig.unit?n("div",{staticClass:"unit-text"},[e._v(e._s(e.mergedConfig.unit))]):e._e()]:e._e()],2)};zn._withStripped=!0;const Tn=a({render:zn,staticRenderFns:[]},(function(e){e&&e("data-v-6f678c1a_0",{source:".dv-capsule-chart {\n  position: relative;\n  display: flex;\n  flex-direction: row;\n  box-sizing: border-box;\n  padding: 10px;\n  color: #fff;\n}\n.dv-capsule-chart .label-column {\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  box-sizing: border-box;\n  padding-right: 10px;\n  text-align: right;\n  font-size: 12px;\n}\n.dv-capsule-chart .label-column div {\n  height: 20px;\n  line-height: 20px;\n}\n.dv-capsule-chart .capsule-container {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n.dv-capsule-chart .capsule-item {\n  box-shadow: 0 0 3px #999;\n  height: 10px;\n  margin: 5px 0px;\n  border-radius: 5px;\n}\n.dv-capsule-chart .capsule-item .capsule-item-column {\n  position: relative;\n  height: 8px;\n  margin-top: 1px;\n  border-radius: 5px;\n  transition: all 0.3s;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n.dv-capsule-chart .capsule-item .capsule-item-column .capsule-item-value {\n  font-size: 12px;\n  transform: translateX(100%);\n}\n.dv-capsule-chart .unit-label {\n  height: 20px;\n  font-size: 12px;\n  position: relative;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.dv-capsule-chart .unit-text {\n  text-align: right;\n  display: flex;\n  align-items: flex-end;\n  font-size: 12px;\n  line-height: 20px;\n  margin-left: 10px;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,aAAa;EACb,mBAAmB;EACnB,sBAAsB;EACtB,aAAa;EACb,WAAW;AACb;AACA;EACE,aAAa;EACb,sBAAsB;EACtB,8BAA8B;EAC9B,sBAAsB;EACtB,mBAAmB;EACnB,iBAAiB;EACjB,eAAe;AACjB;AACA;EACE,YAAY;EACZ,iBAAiB;AACnB;AACA;EACE,OAAO;EACP,aAAa;EACb,sBAAsB;EACtB,8BAA8B;AAChC;AACA;EACE,wBAAwB;EACxB,YAAY;EACZ,eAAe;EACf,kBAAkB;AACpB;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,eAAe;EACf,kBAAkB;EAClB,oBAAoB;EACpB,aAAa;EACb,yBAAyB;EACzB,mBAAmB;AACrB;AACA;EACE,eAAe;EACf,2BAA2B;AAC7B;AACA;EACE,YAAY;EACZ,eAAe;EACf,kBAAkB;EAClB,aAAa;EACb,8BAA8B;EAC9B,mBAAmB;AACrB;AACA;EACE,iBAAiB;EACjB,aAAa;EACb,qBAAqB;EACrB,eAAe;EACf,iBAAiB;EACjB,iBAAiB;AACnB",file:"main.vue",sourcesContent:[".dv-capsule-chart {\n  position: relative;\n  display: flex;\n  flex-direction: row;\n  box-sizing: border-box;\n  padding: 10px;\n  color: #fff;\n}\n.dv-capsule-chart .label-column {\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  box-sizing: border-box;\n  padding-right: 10px;\n  text-align: right;\n  font-size: 12px;\n}\n.dv-capsule-chart .label-column div {\n  height: 20px;\n  line-height: 20px;\n}\n.dv-capsule-chart .capsule-container {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n.dv-capsule-chart .capsule-item {\n  box-shadow: 0 0 3px #999;\n  height: 10px;\n  margin: 5px 0px;\n  border-radius: 5px;\n}\n.dv-capsule-chart .capsule-item .capsule-item-column {\n  position: relative;\n  height: 8px;\n  margin-top: 1px;\n  border-radius: 5px;\n  transition: all 0.3s;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n.dv-capsule-chart .capsule-item .capsule-item-column .capsule-item-value {\n  font-size: 12px;\n  transform: translateX(100%);\n}\n.dv-capsule-chart .unit-label {\n  height: 20px;\n  font-size: 12px;\n  position: relative;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.dv-capsule-chart .unit-text {\n  text-align: right;\n  display: flex;\n  align-items: flex-end;\n  font-size: 12px;\n  line-height: 20px;\n  margin-left: 10px;\n}\n"]},media:void 0})}),Dn,void 0,!1,void 0,!1,l,void 0,void 0);function Yn(e){e.component(Tn.name,Tn)}const Nn={name:"DvWaterLevelPond",props:{config:Object,default:()=>({})},data:()=>({gradientId:"water-level-pond-"+r(),defaultConfig:{data:[],shape:"rect",waveNum:3,waveHeight:40,waveOpacity:.4,colors:["#3DE7C9","#00BAFF"],formatter:"{value}%"},mergedConfig:{},renderer:null,svgBorderGradient:[],details:"",waves:[],animation:!1}),computed:{radius(){const{shape:e}=this.mergedConfig;return"round"===e?"50%":"rect"===e?"0":"roundRect"===e?"10px":"0"},shape(){const{shape:e}=this.mergedConfig;return e||"rect"}},watch:{config(){const{calcData:e,renderer:t}=this;t.delAllGraph(),this.waves=[],setTimeout(e,0)}},methods:{init(){const{initRender:e,config:t,calcData:n}=this;e(),t&&n()},initRender(){const{$refs:e}=this;this.renderer=new $t(e["water-pond-level"])},calcData(){const{mergeConfig:e,calcSvgBorderGradient:t,calcDetails:n}=this;e(),t(),n();const{addWave:r,animationWave:i}=this;r(),i()},mergeConfig(){const{config:e,defaultConfig:t}=this;this.mergedConfig=F(j(t,!0),e)},calcSvgBorderGradient(){const{colors:e}=this.mergedConfig,t=100/(e.length-1);this.svgBorderGradient=e.map((e,n)=>[t*n,e])},calcDetails(){const{data:e,formatter:t}=this.mergedConfig;if(!e.length)return void(this.details="");const n=Math.max(...e);this.details=t.replace("{value}",n)},addWave(){const{renderer:e,getWaveShapes:t,getWaveStyle:n,drawed:r}=this,i=t(),o=n();this.waves=i.map(t=>e.add({name:"smoothline",animationFrame:300,shape:t,style:o,drawed:r}))},getWaveShapes(){const{mergedConfig:e,renderer:t,mergeOffset:n}=this,{waveNum:r,waveHeight:i,data:o}=e,[a,s]=t.area,l=4*r+4,d=a/r/2;return o.map(e=>{let t=new Array(l).fill(0).map((t,n)=>{const r=(1-e/100)*s;return[a-d*n,n%2==0?r:r-i]});return t=t.map(e=>n(e,[2*d,0])),{points:t}})},mergeOffset:([e,t],[n,r])=>[e+n,t+r],getWaveStyle(){const{renderer:e,mergedConfig:t}=this,n=e.area[1];return{gradientColor:t.colors,gradientType:"linear",gradientParams:[0,0,0,n],gradientWith:"fill",opacity:t.waveOpacity,translate:[0,0]}},drawed({shape:{points:e}},{ctx:t,area:n}){const r=e[0],i=e.slice(-1)[0],o=n[1];t.lineTo(i[0],o),t.lineTo(r[0],o),t.closePath(),t.fill()},async animationWave(e=1){const{waves:t,renderer:n,animation:r}=this;if(r)return;this.animation=!0;const i=n.area[0];t.forEach(e=>{e.attr("style",{translate:[0,0]}),e.animation("style",{translate:[i,0]},!0)}),await n.launchAnimation(),this.animation=!1,n.graphs.length&&this.animationWave(e+1)}},mounted(){const{init:e}=this;e()},beforeDestroy(){const{renderer:e}=this;e.delAllGraph(),this.waves=[]}};var Xn=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"dv-water-pond-level"},[e.renderer?n("svg",[n("defs",[n("linearGradient",{attrs:{id:e.gradientId,x1:"0%",y1:"0%",x2:"0%",y2:"100%"}},e._l(e.svgBorderGradient,(function(e){return n("stop",{key:e[0],attrs:{offset:e[0],"stop-color":e[1]}})})),1)],1),e._v(" "),e.renderer?n("text",{attrs:{stroke:"url(#"+e.gradientId+")",fill:"url(#"+e.gradientId+")",x:e.renderer.area[0]/2+8,y:e.renderer.area[1]/2+8}},[e._v("\n      "+e._s(e.details)+"\n    ")]):e._e(),e._v(" "),e.shape&&"round"!==e.shape?n("rect",{attrs:{x:"2",y:"2",rx:"roundRect"===e.shape?10:0,ry:"roundRect"===e.shape?10:0,width:e.renderer.area[0]+12,height:e.renderer.area[1]+12,stroke:"url(#"+e.gradientId+")"}}):n("ellipse",{attrs:{cx:e.renderer.area[0]/2+8,cy:e.renderer.area[1]/2+8,rx:e.renderer.area[0]/2+5,ry:e.renderer.area[1]/2+5,stroke:"url(#"+e.gradientId+")"}})]):e._e(),e._v(" "),n("canvas",{ref:"water-pond-level",style:"border-radius: "+e.radius+";"})])};Xn._withStripped=!0;const $n=a({render:Xn,staticRenderFns:[]},(function(e){e&&e("data-v-be672a2c_0",{source:".dv-water-pond-level {\n  position: relative;\n}\n.dv-water-pond-level svg {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0px;\n  left: 0px;\n}\n.dv-water-pond-level text {\n  font-size: 25px;\n  font-weight: bold;\n  text-anchor: middle;\n  dominant-baseline: middle;\n}\n.dv-water-pond-level ellipse,\n.dv-water-pond-level rect {\n  fill: none;\n  stroke-width: 3;\n}\n.dv-water-pond-level canvas {\n  margin-top: 8px;\n  margin-left: 8px;\n  width: calc(100% - 16px);\n  height: calc(100% - 16px);\n  box-sizing: border-box;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;AACpB;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,QAAQ;EACR,SAAS;AACX;AACA;EACE,eAAe;EACf,iBAAiB;EACjB,mBAAmB;EACnB,yBAAyB;AAC3B;AACA;;EAEE,UAAU;EACV,eAAe;AACjB;AACA;EACE,eAAe;EACf,gBAAgB;EAChB,wBAAwB;EACxB,yBAAyB;EACzB,sBAAsB;AACxB",file:"main.vue",sourcesContent:[".dv-water-pond-level {\n  position: relative;\n}\n.dv-water-pond-level svg {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0px;\n  left: 0px;\n}\n.dv-water-pond-level text {\n  font-size: 25px;\n  font-weight: bold;\n  text-anchor: middle;\n  dominant-baseline: middle;\n}\n.dv-water-pond-level ellipse,\n.dv-water-pond-level rect {\n  fill: none;\n  stroke-width: 3;\n}\n.dv-water-pond-level canvas {\n  margin-top: 8px;\n  margin-left: 8px;\n  width: calc(100% - 16px);\n  height: calc(100% - 16px);\n  box-sizing: border-box;\n}\n"]},media:void 0})}),Nn,void 0,!1,void 0,!1,l,void 0,void 0);function Qn(e){e.component($n.name,$n)}const Un={name:"DvPercentPond",props:{config:{type:Object,default:()=>({})}},data(){const e=r();return{gradientId1:"percent-pond-gradientId1-"+e,gradientId2:"percent-pond-gradientId2-"+e,width:0,height:0,defaultConfig:{value:0,colors:["#3DE7C9","#00BAFF"],borderWidth:3,borderGap:3,lineDash:[5,1],textColor:"#fff",borderRadius:5,localGradient:!1,formatter:"{value}%"},mergedConfig:null}},computed:{rectWidth(){const{mergedConfig:e,width:t}=this;if(!e)return 0;const{borderWidth:n}=e;return t-n},rectHeight(){const{mergedConfig:e,height:t}=this;if(!e)return 0;const{borderWidth:n}=e;return t-n},points(){const{mergedConfig:e,width:t,height:n}=this,r=n/2;if(!e)return`0, ${r} 0, ${r}`;const{borderWidth:i,borderGap:o,value:a}=e;return`\n        ${i+o}, ${r}\n        ${i+o+(t-2*(i+o))/100*a}, ${r+.001}\n      `},polylineWidth(){const{mergedConfig:e,height:t}=this;if(!e)return 0;const{borderWidth:n,borderGap:r}=e;return t-2*(n+r)},linearGradient(){const{mergedConfig:e}=this;if(!e)return[];const{colors:t}=e,n=100/(t.length-1);return t.map((e,t)=>[n*t,e])},polylineGradient(){const{gradientId1:e,gradientId2:t,mergedConfig:n}=this;return n&&n.localGradient?e:t},gradient2XPos(){const{mergedConfig:e}=this;if(!e)return"100%";const{value:t}=e;return 200-t+"%"},details(){const{mergedConfig:e}=this;if(!e)return"";const{value:t,formatter:n}=e;return n.replace("{value}",t)}},watch:{config(){const{mergeConfig:e}=this;e()}},methods:{async init(){const{initWH:e,config:t,mergeConfig:n}=this;await e(),t&&n()},async initWH(){const{$nextTick:e,$refs:t}=this;await e();const{clientWidth:n,clientHeight:r}=t["percent-pond"];this.width=n,this.height=r},mergeConfig(){const{config:e,defaultConfig:t}=this;this.mergedConfig=F(j(t,!0),e||{})}},mounted(){const{init:e}=this;e()}};var Vn=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"percent-pond",staticClass:"dv-percent-pond"},[n("svg",[n("defs",[n("linearGradient",{attrs:{id:e.gradientId1,x1:"0%",y1:"0%",x2:"100%",y2:"0%"}},e._l(e.linearGradient,(function(e){return n("stop",{key:e[0],attrs:{offset:e[0]+"%","stop-color":e[1]}})})),1),e._v(" "),n("linearGradient",{attrs:{id:e.gradientId2,x1:"0%",y1:"0%",x2:e.gradient2XPos,y2:"0%"}},e._l(e.linearGradient,(function(e){return n("stop",{key:e[0],attrs:{offset:e[0]+"%","stop-color":e[1]}})})),1)],1),e._v(" "),n("rect",{attrs:{x:e.mergedConfig?e.mergedConfig.borderWidth/2:"0",y:e.mergedConfig?e.mergedConfig.borderWidth/2:"0",rx:e.mergedConfig?e.mergedConfig.borderRadius:"0",ry:e.mergedConfig?e.mergedConfig.borderRadius:"0",fill:"transparent","stroke-width":e.mergedConfig?e.mergedConfig.borderWidth:"0",stroke:"url(#"+e.gradientId1+")",width:e.rectWidth>0?e.rectWidth:0,height:e.rectHeight>0?e.rectHeight:0}}),e._v(" "),n("polyline",{attrs:{"stroke-width":e.polylineWidth,"stroke-dasharray":e.mergedConfig?e.mergedConfig.lineDash.join(","):"0",stroke:"url(#"+e.polylineGradient+")",points:e.points}}),e._v(" "),n("text",{attrs:{stroke:e.mergedConfig?e.mergedConfig.textColor:"#fff",fill:e.mergedConfig?e.mergedConfig.textColor:"#fff",x:e.width/2,y:e.height/2}},[e._v("\n      "+e._s(e.details)+"\n    ")])])])};Vn._withStripped=!0;const Hn=a({render:Vn,staticRenderFns:[]},(function(e){e&&e("data-v-05a0166f_0",{source:".dv-percent-pond {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n}\n.dv-percent-pond svg {\n  position: absolute;\n  left: 0px;\n  top: 0px;\n  width: 100%;\n  height: 100%;\n}\n.dv-percent-pond polyline {\n  transition: all 0.3s;\n}\n.dv-percent-pond text {\n  font-size: 25px;\n  font-weight: bold;\n  text-anchor: middle;\n  dominant-baseline: middle;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,aAAa;EACb,sBAAsB;AACxB;AACA;EACE,kBAAkB;EAClB,SAAS;EACT,QAAQ;EACR,WAAW;EACX,YAAY;AACd;AACA;EACE,oBAAoB;AACtB;AACA;EACE,eAAe;EACf,iBAAiB;EACjB,mBAAmB;EACnB,yBAAyB;AAC3B",file:"main.vue",sourcesContent:[".dv-percent-pond {\n  position: relative;\n  display: flex;\n  flex-direction: column;\n}\n.dv-percent-pond svg {\n  position: absolute;\n  left: 0px;\n  top: 0px;\n  width: 100%;\n  height: 100%;\n}\n.dv-percent-pond polyline {\n  transition: all 0.3s;\n}\n.dv-percent-pond text {\n  font-size: 25px;\n  font-weight: bold;\n  text-anchor: middle;\n  dominant-baseline: middle;\n}\n"]},media:void 0})}),Un,void 0,!1,void 0,!1,l,void 0,void 0);function qn(e){e.component(Hn.name,Hn)}const Zn={name:"DvFlylineChart",mixins:[i],props:{config:{type:Object,default:()=>({})},dev:{type:Boolean,default:!1}},data(){const e=r();return{ref:"dv-flyline-chart",unique:Math.random(),maskId:"flyline-mask-id-"+e,maskCircleId:"mask-circle-id-"+e,gradientId:"gradient-id-"+e,gradient2Id:"gradient2-id-"+e,defaultConfig:{centerPoint:[0,0],points:[],lineWidth:1,orbitColor:"rgba(103, 224, 227, .2)",flylineColor:"#ffde93",k:-.5,curvature:5,flylineRadius:100,duration:[20,30],relative:!0,bgImgUrl:"",text:{offset:[0,15],color:"#ffdb5c",fontSize:12},halo:{show:!0,duration:30,color:"#fb7293",radius:120},centerPointImg:{width:40,height:40,url:""},pointsImg:{width:15,height:15,url:""}},mergedConfig:null,paths:[],lengths:[],times:[],texts:[]}},watch:{config(){const{calcData:e}=this;e()}},methods:{afterAutoResizeMixinInit(){const{calcData:e}=this;e()},onResize(){const{calcData:e}=this;e()},async calcData(){const{mergeConfig:e,createFlylinePaths:t,calcLineLengths:n}=this;e(),t(),await n();const{calcTimes:r,calcTexts:i}=this;r(),i()},mergeConfig(){let{config:e,defaultConfig:t}=this;const n=F(j(t,!0),e||{}),{points:r}=n;n.points=r.map(e=>e instanceof Array?{position:e,text:""}:e),this.mergedConfig=n},createFlylinePaths(){const{getPath:e,mergedConfig:t,width:n,height:r}=this;let{centerPoint:i,points:o,relative:a}=t;o=o.map(({position:e})=>e),a&&(i=[n*i[0],r*i[1]],o=o.map(([e,t])=>[n*e,r*t])),this.paths=o.map(t=>e(i,t))},getPath(e,t){const{getControlPoint:n}=this;return[t,n(e,t),e]},getControlPoint([e,t],[r,i]){const{getKLinePointByx:o,mergedConfig:a}=this,{curvature:s,k:l}=a,[d,c]=[(e+r)/2,(t+i)/2],u=n([e,t],[r,i])/s,f=u/2;let[h,p]=[d,c];do{h+=f,p=o(l,[d,c],h)[1]}while(n([d,c],[h,p])<u);return[h,p]},getKLinePointByx:(e,[t,n],r)=>[r,n-e*t+e*r],async calcLineLengths(){const{$nextTick:e,paths:t,$refs:n}=this;await e(),this.lengths=t.map((e,t)=>n["path"+t][0].getTotalLength())},calcTimes(){const{duration:e,points:n}=this.mergedConfig;this.times=n.map(n=>t(...e)/10)},calcTexts(){const{points:e}=this.mergedConfig;this.texts=e.map(({text:e})=>e)},consoleClickPos({offsetX:e,offsetY:t}){const{width:n,height:r,dev:i}=this;if(!i)return;const o=(e/n).toFixed(2),a=(t/r).toFixed(2);console.warn(`dv-flyline-chart DEV: \n Click Position is [${e}, ${t}] \n Relative Position is [${o}, ${a}]`)}}};var Kn=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"dv-flyline-chart",staticClass:"dv-flyline-chart",style:"background-image: url("+(e.mergedConfig?e.mergedConfig.bgImgUrl:"")+")",on:{click:e.consoleClickPos}},[e.mergedConfig?n("svg",{attrs:{width:e.width,height:e.height}},[n("defs",[n("radialGradient",{attrs:{id:e.gradientId,cx:"50%",cy:"50%",r:"50%"}},[n("stop",{attrs:{offset:"0%","stop-color":"#fff","stop-opacity":"1"}}),e._v(" "),n("stop",{attrs:{offset:"100%","stop-color":"#fff","stop-opacity":"0"}})],1),e._v(" "),n("radialGradient",{attrs:{id:e.gradient2Id,cx:"50%",cy:"50%",r:"50%"}},[n("stop",{attrs:{offset:"0%","stop-color":"#fff","stop-opacity":"0"}}),e._v(" "),n("stop",{attrs:{offset:"100%","stop-color":"#fff","stop-opacity":"1"}})],1),e._v(" "),e.paths[0]?n("circle",{attrs:{id:"circle"+e.paths[0].toString(),cx:e.paths[0][2][0],cy:e.paths[0][2][1]}},[n("animate",{attrs:{attributeName:"r",values:"1;"+e.mergedConfig.halo.radius,dur:e.mergedConfig.halo.duration/10+"s",repeatCount:"indefinite"}}),e._v(" "),n("animate",{attrs:{attributeName:"opacity",values:"1;0",dur:e.mergedConfig.halo.duration/10+"s",repeatCount:"indefinite"}})]):e._e()],1),e._v(" "),e.paths[0]?n("image",{attrs:{"xlink:href":e.mergedConfig.centerPointImg.url,width:e.mergedConfig.centerPointImg.width,height:e.mergedConfig.centerPointImg.height,x:e.paths[0][2][0]-e.mergedConfig.centerPointImg.width/2,y:e.paths[0][2][1]-e.mergedConfig.centerPointImg.height/2}}):e._e(),e._v(" "),n("mask",{attrs:{id:"maskhalo"+e.paths[0].toString()}},[e.paths[0]?n("use",{attrs:{"xlink:href":"#circle"+e.paths[0].toString(),fill:"url(#"+e.gradient2Id+")"}}):e._e()]),e._v(" "),e.paths[0]&&e.mergedConfig.halo.show?n("use",{attrs:{"xlink:href":"#circle"+e.paths[0].toString(),fill:e.mergedConfig.halo.color,mask:"url(#maskhalo"+e.paths[0].toString()+")"}}):e._e(),e._v(" "),e._l(e.paths,(function(t,r){return n("g",{key:r},[n("defs",[n("path",{ref:"path"+r,refInFor:!0,attrs:{id:"path"+t.toString(),d:"M"+t[0].toString()+" Q"+t[1].toString()+" "+t[2].toString(),fill:"transparent"}})]),e._v(" "),n("use",{attrs:{"xlink:href":"#path"+t.toString(),"stroke-width":e.mergedConfig.lineWidth,stroke:e.mergedConfig.orbitColor}}),e._v(" "),e.lengths[r]?n("use",{attrs:{"xlink:href":"#path"+t.toString(),"stroke-width":e.mergedConfig.lineWidth,stroke:e.mergedConfig.flylineColor,mask:"url(#mask"+e.unique+t.toString()+")"}},[n("animate",{attrs:{attributeName:"stroke-dasharray",from:"0, "+e.lengths[r],to:e.lengths[r]+", 0",dur:e.times[r]||0,repeatCount:"indefinite"}})]):e._e(),e._v(" "),n("mask",{attrs:{id:"mask"+e.unique+t.toString()}},[n("circle",{attrs:{cx:"0",cy:"0",r:e.mergedConfig.flylineRadius,fill:"url(#"+e.gradientId+")"}},[n("animateMotion",{attrs:{dur:e.times[r]||0,path:"M"+t[0].toString()+" Q"+t[1].toString()+" "+t[2].toString(),rotate:"auto",repeatCount:"indefinite"}})],1)]),e._v(" "),n("image",{attrs:{"xlink:href":e.mergedConfig.pointsImg.url,width:e.mergedConfig.pointsImg.width,height:e.mergedConfig.pointsImg.height,x:t[0][0]-e.mergedConfig.pointsImg.width/2,y:t[0][1]-e.mergedConfig.pointsImg.height/2}}),e._v(" "),n("text",{style:"fontSize:"+e.mergedConfig.text.fontSize+"px;",attrs:{fill:e.mergedConfig.text.color,x:t[0][0]+e.mergedConfig.text.offset[0],y:t[0][1]+e.mergedConfig.text.offset[1]}},[e._v("\n        "+e._s(e.texts[r])+"\n      ")])])}))],2):e._e()])};Kn._withStripped=!0;const Jn=a({render:Kn,staticRenderFns:[]},(function(e){e&&e("data-v-1edfcf29_0",{source:".dv-flyline-chart {\n  display: flex;\n  flex-direction: column;\n  background-size: 100% 100%;\n}\n.dv-flyline-chart polyline {\n  transition: all 0.3s;\n}\n.dv-flyline-chart text {\n  text-anchor: middle;\n  dominant-baseline: middle;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,aAAa;EACb,sBAAsB;EACtB,0BAA0B;AAC5B;AACA;EACE,oBAAoB;AACtB;AACA;EACE,mBAAmB;EACnB,yBAAyB;AAC3B",file:"main.vue",sourcesContent:[".dv-flyline-chart {\n  display: flex;\n  flex-direction: column;\n  background-size: 100% 100%;\n}\n.dv-flyline-chart polyline {\n  transition: all 0.3s;\n}\n.dv-flyline-chart text {\n  text-anchor: middle;\n  dominant-baseline: middle;\n}\n"]},media:void 0})}),Zn,void 0,!1,void 0,!1,l,void 0,void 0);function er(e){e.component(Jn.name,Jn)}const tr={name:"DvFlylineChartEnhanced",mixins:[i],props:{config:{type:Object,default:()=>({})},dev:{type:Boolean,default:!1}},data(){const e=r();return{ref:"dv-flyline-chart-enhanced",unique:Math.random(),flylineGradientId:"flyline-gradient-id-"+e,haloGradientId:"halo-gradient-id-"+e,defaultConfig:{points:[],lines:[],halo:{show:!1,duration:[20,30],color:"#fb7293",radius:120},text:{show:!1,offset:[0,15],color:"#ffdb5c",fontSize:12},icon:{show:!1,src:"",width:15,height:15},line:{width:1,color:"#ffde93",orbitColor:"rgba(103, 224, 227, .2)",duration:[20,30],radius:100},bgImgSrc:"",k:-.5,curvature:5,relative:!0},flylines:[],flylineLengths:[],flylinePoints:[],mergedConfig:null}},watch:{config(){const{calcData:e}=this;e()}},methods:{afterAutoResizeMixinInit(){const{calcData:e}=this;e()},onResize(){const{calcData:e}=this;e()},async calcData(){const{mergeConfig:e,calcflylinePoints:t,calcLinePaths:n}=this;e(),t(),n();const{calcLineLengths:r}=this;await r()},mergeConfig(){let{config:e,defaultConfig:t}=this;const n=F(j(t,!0),e||{}),{points:r,lines:i,halo:o,text:a,icon:s,line:l}=n;n.points=r.map(e=>(e.halo=F(j(o,!0),e.halo||{}),e.text=F(j(a,!0),e.text||{}),e.icon=F(j(s,!0),e.icon||{}),e)),n.lines=i.map(e=>F(j(l,!0),e)),this.mergedConfig=n},calcflylinePoints(){const{mergedConfig:e,width:n,height:r}=this,{relative:i,points:o}=e;this.flylinePoints=o.map((e,o)=>{const{coordinate:[a,s],halo:l,icon:d,text:c}=e;i&&(e.coordinate=[a*n,s*r]),e.halo.time=t(...l.duration)/10;const{width:u,height:f}=d;e.icon.x=e.coordinate[0]-u/2,e.icon.y=e.coordinate[1]-f/2;const[h,p]=c.offset;return e.text.x=e.coordinate[0]+h,e.text.y=e.coordinate[1]+p,e.key=`${e.coordinate.toString()}${o}`,e})},calcLinePaths(){const{getPath:e,mergedConfig:n}=this,{points:r,lines:i}=n;this.flylines=i.map(n=>{const{source:i,target:o,duration:a}=n,s=r.find(({name:e})=>e===i).coordinate,l=r.find(({name:e})=>e===o).coordinate,d=e(s,l).map(e=>e.map(e=>parseFloat(e.toFixed(10)))),c=`M${d[0].toString()} Q${d[1].toString()} ${d[2].toString()}`,u="path"+d.toString(),f=t(...a)/10;return{...n,path:d,key:u,d:c,time:f}})},getPath(e,t){const{getControlPoint:n}=this;return[e,n(e,t),t]},getControlPoint([e,t],[r,i]){const{getKLinePointByx:o,mergedConfig:a}=this,{curvature:s,k:l}=a,[d,c]=[(e+r)/2,(t+i)/2],u=n([e,t],[r,i])/s,f=u/2;let[h,p]=[d,c];do{h+=f,p=o(l,[d,c],h)[1]}while(n([d,c],[h,p])<u);return[h,p]},getKLinePointByx:(e,[t,n],r)=>[r,n-e*t+e*r],async calcLineLengths(){const{$nextTick:e,flylines:t,$refs:n}=this;await e(),this.flylineLengths=t.map(({key:e})=>n[e][0].getTotalLength())},consoleClickPos({offsetX:e,offsetY:t}){const{width:n,height:r,dev:i}=this;if(!i)return;const o=(e/n).toFixed(2),a=(t/r).toFixed(2);console.warn(`dv-flyline-chart-enhanced DEV: \n Click Position is [${e}, ${t}] \n Relative Position is [${o}, ${a}]`)}}};var nr=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-flyline-chart-enhanced",style:"background-image: url("+(e.mergedConfig?e.mergedConfig.bgImgSrc:"")+")",on:{click:e.consoleClickPos}},[e.flylines.length?n("svg",{attrs:{width:e.width,height:e.height}},[n("defs",[n("radialGradient",{attrs:{id:e.flylineGradientId,cx:"50%",cy:"50%",r:"50%"}},[n("stop",{attrs:{offset:"0%","stop-color":"#fff","stop-opacity":"1"}}),e._v(" "),n("stop",{attrs:{offset:"100%","stop-color":"#fff","stop-opacity":"0"}})],1),e._v(" "),n("radialGradient",{attrs:{id:e.haloGradientId,cx:"50%",cy:"50%",r:"50%"}},[n("stop",{attrs:{offset:"0%","stop-color":"#fff","stop-opacity":"0"}}),e._v(" "),n("stop",{attrs:{offset:"100%","stop-color":"#fff","stop-opacity":"1"}})],1)],1),e._v(" "),e._l(e.flylinePoints,(function(t){return n("g",{key:t.key+Math.random()},[n("defs",[t.halo.show?n("circle",{attrs:{id:"halo"+e.unique+t.key,cx:t.coordinate[0],cy:t.coordinate[1]}},[n("animate",{attrs:{attributeName:"r",values:"1;"+t.halo.radius,dur:t.halo.time+"s",repeatCount:"indefinite"}}),e._v(" "),n("animate",{attrs:{attributeName:"opacity",values:"1;0",dur:t.halo.time+"s",repeatCount:"indefinite"}})]):e._e()]),e._v(" "),n("mask",{attrs:{id:"mask"+e.unique+t.key}},[t.halo.show?n("use",{attrs:{"xlink:href":"#halo"+e.unique+t.key,fill:"url(#"+e.haloGradientId+")"}}):e._e()]),e._v(" "),t.halo.show?n("use",{attrs:{"xlink:href":"#halo"+e.unique+t.key,fill:t.halo.color,mask:"url(#mask"+e.unique+t.key+")"}}):e._e(),e._v(" "),t.icon.show?n("image",{attrs:{"xlink:href":t.icon.src,width:t.icon.width,height:t.icon.height,x:t.icon.x,y:t.icon.y}}):e._e(),e._v(" "),t.text.show?n("text",{style:"fontSize:"+t.text.fontSize+"px;color:"+t.text.color,attrs:{fill:t.text.color,x:t.text.x,y:t.text.y}},[e._v("\n        "+e._s(t.name)+"\n      ")]):e._e()])})),e._v(" "),e._l(e.flylines,(function(t,r){return n("g",{key:t.key+Math.random()},[n("defs",[n("path",{ref:t.key,refInFor:!0,attrs:{id:t.key,d:t.d,fill:"transparent"}})]),e._v(" "),n("use",{attrs:{"xlink:href":"#"+t.key,"stroke-width":t.width,stroke:t.orbitColor}}),e._v(" "),n("mask",{attrs:{id:"mask"+e.unique+t.key}},[n("circle",{attrs:{cx:"0",cy:"0",r:t.radius,fill:"url(#"+e.flylineGradientId+")"}},[n("animateMotion",{attrs:{dur:t.time,path:t.d,rotate:"auto",repeatCount:"indefinite"}})],1)]),e._v(" "),e.flylineLengths[r]?n("use",{attrs:{"xlink:href":"#"+t.key,"stroke-width":t.width,stroke:t.color,mask:"url(#mask"+e.unique+t.key+")"}},[n("animate",{attrs:{attributeName:"stroke-dasharray",from:"0, "+e.flylineLengths[r],to:e.flylineLengths[r]+", 0",dur:t.time,repeatCount:"indefinite"}})]):e._e()])}))],2):e._e()])};nr._withStripped=!0;const rr=a({render:nr,staticRenderFns:[]},(function(e){e&&e("data-v-a7b8c35c_0",{source:".dv-flyline-chart-enhanced {\n  display: flex;\n  flex-direction: column;\n  background-size: 100% 100%;\n}\n.dv-flyline-chart-enhanced text {\n  text-anchor: middle;\n  dominant-baseline: middle;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,aAAa;EACb,sBAAsB;EACtB,0BAA0B;AAC5B;AACA;EACE,mBAAmB;EACnB,yBAAyB;AAC3B",file:"main.vue",sourcesContent:[".dv-flyline-chart-enhanced {\n  display: flex;\n  flex-direction: column;\n  background-size: 100% 100%;\n}\n.dv-flyline-chart-enhanced text {\n  text-anchor: middle;\n  dominant-baseline: middle;\n}\n"]},media:void 0})}),tr,void 0,!1,void 0,!1,l,void 0,void 0);function ir(e){e.component(rr.name,rr)}const or={name:"DvConicalColumnChart",mixins:[i],props:{config:{type:Object,default:()=>({})}},data:()=>({ref:"conical-column-chart",defaultConfig:{data:[],img:[],fontSize:12,imgSideLength:30,columnColor:"rgba(0, 194, 255, 0.4)",textColor:"#fff",showValue:!1},mergedConfig:null,column:[]}),watch:{config(){const{calcData:e}=this;e()}},methods:{afterAutoResizeMixinInit(){const{calcData:e}=this;e()},onResize(){const{calcData:e}=this;e()},calcData(){const{mergeConfig:e,initData:t,calcSVGPath:n}=this;e(),t(),n()},mergeConfig(){const{defaultConfig:e,config:t}=this;this.mergedConfig=F(j(e,!0),t||{})},initData(){const{mergedConfig:e}=this;let{data:t}=e;t=j(t,!0),t.sort(({value:e},{value:t})=>e>t?-1:e<t?1:e===t?0:void 0);const n=t[0]?t[0].value:10;t=t.map(e=>({...e,percent:e.value/n})),e.data=t},calcSVGPath(){const{mergedConfig:e,width:t,height:n}=this,{imgSideLength:r,fontSize:i,data:o}=e,a=t/(o.length+1),s=n-r-i-5,l=n-i-5;this.column=o.map((e,t)=>{const{percent:n}=e,r=a*(t+1),o=a*t,d=l-s*n,c=s*n*.6+d,u=`\n          M${o}, ${l}\n          Q${r}, ${c} ${r},${d}\n          M${r},${d}\n          Q${r}, ${c} ${a*(t+2)},${l}\n          L${o}, ${l}\n          Z\n        `,f=(l+d)/2+i/2;return{...e,d:u,x:r,y:d,textY:f}})}}};var ar=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-conical-column-chart"},[n("svg",{attrs:{width:e.width,height:e.height}},e._l(e.column,(function(t,r){return n("g",{key:r},[n("path",{attrs:{d:t.d,fill:e.mergedConfig.columnColor}}),e._v(" "),n("text",{style:"fontSize:"+e.mergedConfig.fontSize+"px",attrs:{fill:e.mergedConfig.textColor,x:t.x,y:e.height-4}},[e._v("\n        "+e._s(t.name)+"\n      ")]),e._v(" "),e.mergedConfig.img.length?n("image",{attrs:{"xlink:href":e.mergedConfig.img[r%e.mergedConfig.img.length],width:e.mergedConfig.imgSideLength,height:e.mergedConfig.imgSideLength,x:t.x-e.mergedConfig.imgSideLength/2,y:t.y-e.mergedConfig.imgSideLength}}):e._e(),e._v(" "),e.mergedConfig.showValue?n("text",{style:"fontSize:"+e.mergedConfig.fontSize+"px",attrs:{fill:e.mergedConfig.textColor,x:t.x,y:t.textY}},[e._v("\n        "+e._s(t.value)+"\n      ")]):e._e()])})),0)])};ar._withStripped=!0;const sr=a({render:ar,staticRenderFns:[]},(function(e){e&&e("data-v-382f06c7_0",{source:".dv-conical-column-chart {\n  width: 100%;\n  height: 100%;\n}\n.dv-conical-column-chart text {\n  text-anchor: middle;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,WAAW;EACX,YAAY;AACd;AACA;EACE,mBAAmB;AACrB",file:"main.vue",sourcesContent:[".dv-conical-column-chart {\n  width: 100%;\n  height: 100%;\n}\n.dv-conical-column-chart text {\n  text-anchor: middle;\n}\n"]},media:void 0})}),or,void 0,!1,void 0,!1,l,void 0,void 0);function lr(e){e.component(sr.name,sr)}function dr(e){e.component(jn.name,jn)}const cr={name:"DvScrollBoard",mixins:[i],props:{config:{type:Object,default:()=>({})}},data:()=>({ref:"scroll-board",defaultConfig:{header:[],data:[],rowNum:5,headerBGC:"#00BAFF",oddRowBGC:"#003B51",evenRowBGC:"#0A2732",waitTime:2e3,headerHeight:35,columnWidth:[],align:[],index:!1,indexHeader:"#",carousel:"single"},mergedConfig:null,header:[],rowsData:[],rows:[],widths:[],heights:[],avgHeight:0,aligns:[],animationIndex:0,animationHandler:"",updater:0}),watch:{config(){const{stopAnimation:e,calcData:t}=this;e(),t()}},methods:{afterAutoResizeMixinInit(){const{calcData:e}=this;e()},onResize(){const{mergedConfig:e,calcWidths:t,calcHeights:n}=this;e&&(t(),n())},calcData(){const{mergeConfig:e,calcHeaderData:t,calcRowsData:n}=this;e(),t(),n();const{calcWidths:r,calcHeights:i,calcAligns:o}=this;r(),i(),o();const{animation:a}=this;a(!0)},mergeConfig(){let{config:e,defaultConfig:t}=this;this.mergedConfig=F(j(t,!0),e||{})},calcHeaderData(){let{header:e,index:t,indexHeader:n}=this.mergedConfig;e.length?(e=[...e],t&&e.unshift(n),this.header=e):this.header=[]},calcRowsData(){let{data:e,index:t,headerBGC:n,rowNum:r}=this.mergedConfig;t&&(e=e.map((e,t)=>{e=[...e];const r=`<span class="index" style="background-color: ${n};">${t+1}</span>`;return e.unshift(r),e})),e=e.map((e,t)=>({ceils:e,rowIndex:t}));const i=e.length;i>r&&i<2*r&&(e=[...e,...e]),e=e.map((e,t)=>({...e,scroll:t})),this.rowsData=e,this.rows=e},calcWidths(){const{width:e,mergedConfig:t,rowsData:n}=this,{columnWidth:r,header:i}=t,o=r.reduce((e,t)=>e+t,0);let a=0;n[0]?a=n[0].ceils.length:i.length&&(a=i.length);const s=(e-o)/(a-r.length),l=new Array(a).fill(s);this.widths=F(l,r)},calcHeights(e=!1){const{height:t,mergedConfig:n,header:r}=this,{headerHeight:i,rowNum:o,data:a}=n;let s=t;r.length&&(s-=i);const l=s/o;this.avgHeight=l,e||(this.heights=new Array(a.length).fill(l))},calcAligns(){const{header:e,mergedConfig:t}=this,n=e.length;let r=new Array(n).fill("left");const{align:i}=t;this.aligns=F(r,i)},async animation(e=!1){let{avgHeight:t,animationIndex:n,mergedConfig:r,rowsData:i,animation:o,updater:a}=this;const{waitTime:s,carousel:l,rowNum:d}=r,c=i.length;if(d>=c)return;if(e&&(await new Promise(e=>setTimeout(e,s)),a!==this.updater))return;const u="single"===l?1:d;let f=i.slice(n);if(f.push(...i.slice(0,n)),this.rows=f,this.heights=new Array(c).fill(t),await new Promise(e=>setTimeout(e,300)),a!==this.updater)return;this.heights.splice(0,u,...new Array(u).fill(0)),n+=u;const h=n-c;h>=0&&(n=h),this.animationIndex=n,this.animationHandler=setTimeout(o,s-300)},stopAnimation(){const{animationHandler:e,updater:t}=this;this.updater=(t+1)%999999,e&&clearTimeout(e)},emitEvent(e,t,n,r){const{ceils:i,rowIndex:o}=n;this.$emit("click",{row:i,ceil:r,rowIndex:o,columnIndex:t})}},destroyed(){const{stopAnimation:e}=this;e()}};var ur=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-scroll-board"},[e.header.length&&e.mergedConfig?n("div",{staticClass:"header",style:"background-color: "+e.mergedConfig.headerBGC+";"},e._l(e.header,(function(t,r){return n("div",{key:t+r,staticClass:"header-item",style:"\n        height: "+e.mergedConfig.headerHeight+"px;\n        line-height: "+e.mergedConfig.headerHeight+"px;\n        width: "+e.widths[r]+"px;\n      ",attrs:{align:e.aligns[r]},domProps:{innerHTML:e._s(t)}})})),0):e._e(),e._v(" "),e.mergedConfig?n("div",{staticClass:"rows",style:"height: "+(e.height-(e.header.length?e.mergedConfig.headerHeight:0))+"px;"},e._l(e.rows,(function(t,r){return n("div",{key:t.toString()+t.scroll,staticClass:"row-item",style:"\n        height: "+e.heights[r]+"px;\n        line-height: "+e.heights[r]+"px;\n        background-color: "+e.mergedConfig[t.rowIndex%2==0?"evenRowBGC":"oddRowBGC"]+";\n      "},e._l(t.ceils,(function(i,o){return n("div",{key:i+r+o,staticClass:"ceil",style:"width: "+e.widths[o]+"px;",attrs:{align:e.aligns[o]},domProps:{innerHTML:e._s(i)},on:{click:function(n){return e.emitEvent(r,o,t,i)}}})})),0)})),0):e._e()])};ur._withStripped=!0;const fr=a({render:ur,staticRenderFns:[]},(function(e){e&&e("data-v-1aad958a_0",{source:".dv-scroll-board {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  color: #fff;\n}\n.dv-scroll-board .text {\n  padding: 0 10px;\n  box-sizing: border-box;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.dv-scroll-board .header {\n  display: flex;\n  flex-direction: row;\n  font-size: 15px;\n}\n.dv-scroll-board .header .header-item {\n  padding: 0 10px;\n  box-sizing: border-box;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  transition: all 0.3s;\n}\n.dv-scroll-board .rows {\n  overflow: hidden;\n}\n.dv-scroll-board .rows .row-item {\n  display: flex;\n  font-size: 14px;\n  transition: all 0.3s;\n}\n.dv-scroll-board .rows .ceil {\n  padding: 0 10px;\n  box-sizing: border-box;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.dv-scroll-board .rows .index {\n  border-radius: 3px;\n  padding: 0px 3px;\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,kBAAkB;EAClB,WAAW;EACX,YAAY;EACZ,WAAW;AACb;AACA;EACE,eAAe;EACf,sBAAsB;EACtB,mBAAmB;EACnB,gBAAgB;EAChB,uBAAuB;AACzB;AACA;EACE,aAAa;EACb,mBAAmB;EACnB,eAAe;AACjB;AACA;EACE,eAAe;EACf,sBAAsB;EACtB,mBAAmB;EACnB,gBAAgB;EAChB,uBAAuB;EACvB,oBAAoB;AACtB;AACA;EACE,gBAAgB;AAClB;AACA;EACE,aAAa;EACb,eAAe;EACf,oBAAoB;AACtB;AACA;EACE,eAAe;EACf,sBAAsB;EACtB,mBAAmB;EACnB,gBAAgB;EAChB,uBAAuB;AACzB;AACA;EACE,kBAAkB;EAClB,gBAAgB;AAClB",file:"main.vue",sourcesContent:[".dv-scroll-board {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  color: #fff;\n}\n.dv-scroll-board .text {\n  padding: 0 10px;\n  box-sizing: border-box;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.dv-scroll-board .header {\n  display: flex;\n  flex-direction: row;\n  font-size: 15px;\n}\n.dv-scroll-board .header .header-item {\n  padding: 0 10px;\n  box-sizing: border-box;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  transition: all 0.3s;\n}\n.dv-scroll-board .rows {\n  overflow: hidden;\n}\n.dv-scroll-board .rows .row-item {\n  display: flex;\n  font-size: 14px;\n  transition: all 0.3s;\n}\n.dv-scroll-board .rows .ceil {\n  padding: 0 10px;\n  box-sizing: border-box;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.dv-scroll-board .rows .index {\n  border-radius: 3px;\n  padding: 0px 3px;\n}\n"]},media:void 0})}),cr,void 0,!1,void 0,!1,l,void 0,void 0);function hr(e){e.component(fr.name,fr)}const pr={name:"DvScrollRankingBoard",mixins:[i],props:{config:{type:Object,default:()=>({})}},data:()=>({ref:"scroll-ranking-board",defaultConfig:{data:[],rowNum:5,waitTime:2e3,carousel:"single",unit:"",sort:!0},mergedConfig:null,rowsData:[],rows:[],heights:[],animationIndex:0,animationHandler:"",updater:0}),watch:{config(){const{stopAnimation:e,calcData:t}=this;e(),t()}},methods:{afterAutoResizeMixinInit(){const{calcData:e}=this;e()},onResize(){const{mergedConfig:e,calcHeights:t}=this;e&&t(!0)},calcData(){const{mergeConfig:e,calcRowsData:t}=this;e(),t();const{calcHeights:n}=this;n();const{animation:r}=this;r(!0)},mergeConfig(){let{config:e,defaultConfig:t}=this;this.mergedConfig=F(j(t,!0),e||{})},calcRowsData(){let{data:e,rowNum:t,sort:n}=this.mergedConfig;n&&e.sort(({value:e},{value:t})=>e>t?-1:e<t?1:e===t?0:void 0);const r=e.map(({value:e})=>e),i=Math.max(...r)||0;e=e.map((e,t)=>({...e,ranking:t+1,percent:e.value/i*100}));const o=e.length;o>t&&o<2*t&&(e=[...e,...e]),e=e.map((e,t)=>({...e,scroll:t})),this.rowsData=e,this.rows=e},calcHeights(e=!1){const{height:t,mergedConfig:n}=this,{rowNum:r,data:i}=n,o=t/r;this.avgHeight=o,e||(this.heights=new Array(i.length).fill(o))},async animation(e=!1){let{avgHeight:t,animationIndex:n,mergedConfig:r,rowsData:i,animation:o,updater:a}=this;const{waitTime:s,carousel:l,rowNum:d}=r,c=i.length;if(d>=c)return;if(e&&(await new Promise(e=>setTimeout(e,s)),a!==this.updater))return;const u="single"===l?1:d;let f=i.slice(n);if(f.push(...i.slice(0,n)),this.rows=f,this.heights=new Array(c).fill(t),await new Promise(e=>setTimeout(e,300)),a!==this.updater)return;this.heights.splice(0,u,...new Array(u).fill(0)),n+=u;const h=n-c;h>=0&&(n=h),this.animationIndex=n,this.animationHandler=setTimeout(o,s-300)},stopAnimation(){const{animationHandler:e,updater:t}=this;this.updater=(t+1)%999999,e&&clearTimeout(e)}},destroyed(){const{stopAnimation:e}=this;e()}};var gr=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:e.ref,staticClass:"dv-scroll-ranking-board"},e._l(e.rows,(function(t,r){return n("div",{key:t.toString()+t.scroll,staticClass:"row-item",style:"height: "+e.heights[r]+"px;"},[n("div",{staticClass:"ranking-info"},[n("div",{staticClass:"rank"},[e._v("No."+e._s(t.ranking))]),e._v(" "),n("div",{staticClass:"info-name",domProps:{innerHTML:e._s(t.name)}}),e._v(" "),n("div",{staticClass:"ranking-value"},[e._v(e._s(t.value+e.mergedConfig.unit))])]),e._v(" "),n("div",{staticClass:"ranking-column"},[n("div",{staticClass:"inside-column",style:"width: "+t.percent+"%;"},[n("div",{staticClass:"shine"})])])])})),0)};gr._withStripped=!0;const vr=a({render:gr,staticRenderFns:[]},(function(e){e&&e("data-v-4fe9e817_0",{source:".dv-scroll-ranking-board {\n  width: 100%;\n  height: 100%;\n  color: #fff;\n  overflow: hidden;\n}\n.dv-scroll-ranking-board .row-item {\n  transition: all 0.3s;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  overflow: hidden;\n}\n.dv-scroll-ranking-board .ranking-info {\n  display: flex;\n  width: 100%;\n  font-size: 13px;\n}\n.dv-scroll-ranking-board .ranking-info .rank {\n  width: 40px;\n  color: #1370fb;\n}\n.dv-scroll-ranking-board .ranking-info .info-name {\n  flex: 1;\n}\n.dv-scroll-ranking-board .ranking-column {\n  border-bottom: 2px solid rgba(19, 112, 251, 0.5);\n  margin-top: 5px;\n}\n.dv-scroll-ranking-board .ranking-column .inside-column {\n  position: relative;\n  height: 6px;\n  background-color: #1370fb;\n  margin-bottom: 2px;\n  border-radius: 1px;\n  overflow: hidden;\n}\n.dv-scroll-ranking-board .ranking-column .shine {\n  position: absolute;\n  left: 0%;\n  top: 2px;\n  height: 2px;\n  width: 50px;\n  transform: translateX(-100%);\n  background: radial-gradient(#28f8ff 5%, transparent 80%);\n  animation: shine 3s ease-in-out infinite alternate;\n}\n@keyframes shine {\n80% {\n    left: 0%;\n    transform: translateX(-100%);\n}\n100% {\n    left: 100%;\n    transform: translateX(0%);\n}\n}\n",map:{version:3,sources:["main.vue"],names:[],mappings:"AAAA;EACE,WAAW;EACX,YAAY;EACZ,WAAW;EACX,gBAAgB;AAClB;AACA;EACE,oBAAoB;EACpB,aAAa;EACb,sBAAsB;EACtB,uBAAuB;EACvB,gBAAgB;AAClB;AACA;EACE,aAAa;EACb,WAAW;EACX,eAAe;AACjB;AACA;EACE,WAAW;EACX,cAAc;AAChB;AACA;EACE,OAAO;AACT;AACA;EACE,gDAAgD;EAChD,eAAe;AACjB;AACA;EACE,kBAAkB;EAClB,WAAW;EACX,yBAAyB;EACzB,kBAAkB;EAClB,kBAAkB;EAClB,gBAAgB;AAClB;AACA;EACE,kBAAkB;EAClB,QAAQ;EACR,QAAQ;EACR,WAAW;EACX,WAAW;EACX,4BAA4B;EAC5B,wDAAwD;EACxD,kDAAkD;AACpD;AACA;AACE;IACE,QAAQ;IACR,4BAA4B;AAC9B;AACA;IACE,UAAU;IACV,yBAAyB;AAC3B;AACF",file:"main.vue",sourcesContent:[".dv-scroll-ranking-board {\n  width: 100%;\n  height: 100%;\n  color: #fff;\n  overflow: hidden;\n}\n.dv-scroll-ranking-board .row-item {\n  transition: all 0.3s;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  overflow: hidden;\n}\n.dv-scroll-ranking-board .ranking-info {\n  display: flex;\n  width: 100%;\n  font-size: 13px;\n}\n.dv-scroll-ranking-board .ranking-info .rank {\n  width: 40px;\n  color: #1370fb;\n}\n.dv-scroll-ranking-board .ranking-info .info-name {\n  flex: 1;\n}\n.dv-scroll-ranking-board .ranking-column {\n  border-bottom: 2px solid rgba(19, 112, 251, 0.5);\n  margin-top: 5px;\n}\n.dv-scroll-ranking-board .ranking-column .inside-column {\n  position: relative;\n  height: 6px;\n  background-color: #1370fb;\n  margin-bottom: 2px;\n  border-radius: 1px;\n  overflow: hidden;\n}\n.dv-scroll-ranking-board .ranking-column .shine {\n  position: absolute;\n  left: 0%;\n  top: 2px;\n  height: 2px;\n  width: 50px;\n  transform: translateX(-100%);\n  background: radial-gradient(#28f8ff 5%, transparent 80%);\n  animation: shine 3s ease-in-out infinite alternate;\n}\n@keyframes shine {\n  80% {\n    left: 0%;\n    transform: translateX(-100%);\n  }\n  100% {\n    left: 100%;\n    transform: translateX(0%);\n  }\n}\n"]},media:void 0})}),pr,void 0,!1,void 0,!1,l,void 0,void 0);function mr(e){e.component(vr.name,vr)}e.use((function(e){e.use(p),e.use(A),e.use(T),e.use($),e.use(H),e.use(J),e.use(re),e.use(se),e.use(ue),e.use(ge),e.use(Ce),e.use(we),e.use(Oe),e.use(je),e.use(Ge),e.use(Ye),e.use(Qe),e.use(qe),e.use(et),e.use(it),e.use(lt),e.use(ft),e.use(vt),e.use(bt),e.use(kt),e.use(_t),e.use(In),e.use(Gn),e.use(Yn),e.use(Qn),e.use(qn),e.use(er),e.use(ir),e.use(lr),e.use(dr),e.use(hr),e.use(mr)}))}));