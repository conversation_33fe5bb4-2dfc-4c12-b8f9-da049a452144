define(["./when-54c2dc71","./Check-6c0211bc","./Math-fc8cecf5","./Cartesian2-bddc1162","./Transforms-6f81ad4c","./RuntimeError-2109023a","./WebGLConstants-76bb35d1","./ComponentDatatype-6d99a1ee","./GeometryAttribute-700c1da0","./GeometryAttributes-4fcfcf40","./AttributeCompression-9fc99391","./GeometryPipeline-e6a15a43","./EncodedCartesian3-4df2eabb","./IndexDatatype-53503fee","./IntersectionTests-7f3bcd5c","./Plane-b6058d9b","./PrimitivePipeline-6fc2e482","./WebMercatorProjection-df58d479","./createTaskProcessorWorker"],function(e,t,i,r,n,c,a,o,s,m,b,d,f,P,p,u,y,C,l){"use strict";return l(function(e,t){return e=y.PrimitivePipeline.unpackCombineGeometryParameters(e),e=y.PrimitivePipeline.combineGeometry(e),y.PrimitivePipeline.packCombineGeometryResults(e,t)})});
