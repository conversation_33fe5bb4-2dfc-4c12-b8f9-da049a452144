!function(t){var e={};function n(i){if(e[i])return e[i].exports;var o=e[i]={i:i,l:!1,exports:{}};return t[i].call(o.exports,o,o.exports,n),o.l=!0,o.exports}n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(i,o,function(e){return t[e]}.bind(null,o));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=13)}([function(t,e,n){"use strict";(t.exports={}).forEach=function(t,e){for(var n=0;n<t.length;n++){var i=e(t[n]);if(i)return i}}},function(t,e,n){"use strict";var i=t.exports={};i.isIE=function(t){return(-1!==(e=navigator.userAgent.toLowerCase()).indexOf("msie")||-1!==e.indexOf("trident")||-1!==e.indexOf(" edge/"))&&(!t||t===function(){var t=3,e=document.createElement("div"),n=e.getElementsByTagName("i");do{e.innerHTML="\x3c!--[if gt IE "+ ++t+"]><i></i><![endif]--\x3e"}while(n[0]);return t>4?t:void 0}());var e},i.isLegacyOpera=function(){return!!window.opera}},function(t,e,n){"use strict";var i=n(0).forEach,o=n(3),r=n(4),a=n(5),s=n(6),l=n(7),c=n(1),d=n(8),u=n(10),h=n(11),f=n(12);function p(t){return Array.isArray(t)||void 0!==t.length}function v(t){if(Array.isArray(t))return t;var e=[];return i(t,(function(t){e.push(t)})),e}function g(t){return t&&1===t.nodeType}function m(t,e,n){var i=t[e];return null==i&&void 0!==n?n:i}t.exports=function(t){var e;if((t=t||{}).idHandler)e={get:function(e){return t.idHandler.get(e,!0)},set:t.idHandler.set};else{var n=a(),y=s({idGenerator:n,stateHandler:u});e=y}var b=t.reporter;b||(b=l(!1===b));var w=m(t,"batchProcessor",d({reporter:b})),x={};x.callOnAdd=!!m(t,"callOnAdd",!0),x.debug=!!m(t,"debug",!1);var E,S=r(e),T=o({stateHandler:u}),A=m(t,"strategy","object"),O={reporter:b,batchProcessor:w,stateHandler:u,idHandler:e};if("scroll"===A&&(c.isLegacyOpera()?(b.warn("Scroll strategy is not supported on legacy Opera. Changing to object strategy."),A="object"):c.isIE(9)&&(b.warn("Scroll strategy is not supported on IE9. Changing to object strategy."),A="object")),"scroll"===A)E=f(O);else{if("object"!==A)throw new Error("Invalid strategy name: "+A);E=h(O)}var k={};return{listenTo:function(t,n,o){function r(t){var e=S.get(t);i(e,(function(e){e(t)}))}function a(t,e,n){S.add(e,n),t&&n(e)}if(o||(o=n,n=t,t={}),!n)throw new Error("At least one element required.");if(!o)throw new Error("Listener required.");if(g(n))n=[n];else{if(!p(n))return b.error("Invalid arguments. Must be a DOM element or a collection of DOM elements.");n=v(n)}var s=0,l=m(t,"callOnAdd",x.callOnAdd),c=m(t,"onReady",(function(){})),d=m(t,"debug",x.debug);i(n,(function(t){u.getState(t)||(u.initState(t),e.set(t));var h=e.get(t);if(d&&b.log("Attaching listener to element",h,t),!T.isDetectable(t))return d&&b.log(h,"Not detectable."),T.isBusy(t)?(d&&b.log(h,"System busy making it detectable"),a(l,t,o),k[h]=k[h]||[],void k[h].push((function(){++s===n.length&&c()}))):(d&&b.log(h,"Making detectable..."),T.markBusy(t,!0),E.makeDetectable({debug:d},t,(function(t){if(d&&b.log(h,"onElementDetectable"),u.getState(t)){T.markAsDetectable(t),T.markBusy(t,!1),E.addListener(t,r),a(l,t,o);var e=u.getState(t);if(e&&e.startSize){var f=t.offsetWidth,p=t.offsetHeight;e.startSize.width===f&&e.startSize.height===p||r(t)}k[h]&&i(k[h],(function(t){t()}))}else d&&b.log(h,"Element uninstalled before being detectable.");delete k[h],++s===n.length&&c()})));d&&b.log(h,"Already detecable, adding listener."),a(l,t,o),s++})),s===n.length&&c()},removeListener:S.removeListener,removeAllListeners:S.removeAllListeners,uninstall:function(t){if(!t)return b.error("At least one element is required.");if(g(t))t=[t];else{if(!p(t))return b.error("Invalid arguments. Must be a DOM element or a collection of DOM elements.");t=v(t)}i(t,(function(t){S.removeAllListeners(t),E.uninstall(t),u.cleanState(t)}))}}}},function(t,e,n){"use strict";t.exports=function(t){var e=t.stateHandler.getState;return{isDetectable:function(t){var n=e(t);return n&&!!n.isDetectable},markAsDetectable:function(t){e(t).isDetectable=!0},isBusy:function(t){return!!e(t).busy},markBusy:function(t,n){e(t).busy=!!n}}}},function(t,e,n){"use strict";t.exports=function(t){var e={};function n(n){var i=t.get(n);return void 0===i?[]:e[i]||[]}return{get:n,add:function(n,i){var o=t.get(n);e[o]||(e[o]=[]),e[o].push(i)},removeListener:function(t,e){for(var i=n(t),o=0,r=i.length;o<r;++o)if(i[o]===e){i.splice(o,1);break}},removeAllListeners:function(t){var e=n(t);e&&(e.length=0)}}}},function(t,e,n){"use strict";t.exports=function(){var t=1;return{generate:function(){return t++}}}},function(t,e,n){"use strict";t.exports=function(t){var e=t.idGenerator,n=t.stateHandler.getState;return{get:function(t){var e=n(t);return e&&void 0!==e.id?e.id:null},set:function(t){var i=n(t);if(!i)throw new Error("setId required the element to have a resize detection state.");var o=e.generate();return i.id=o,o}}}},function(t,e,n){"use strict";t.exports=function(t){function e(){}var n={log:e,warn:e,error:e};if(!t&&window.console){var i=function(t,e){t[e]=function(){var t=console[e];if(t.apply)t.apply(console,arguments);else for(var n=0;n<arguments.length;n++)t(arguments[n])}};i(n,"log"),i(n,"warn"),i(n,"error")}return n}},function(t,e,n){"use strict";var i=n(9);function o(){var t={},e=0,n=0,i=0;return{add:function(o,r){r||(r=o,o=0),o>n?n=o:o<i&&(i=o),t[o]||(t[o]=[]),t[o].push(r),e++},process:function(){for(var e=i;e<=n;e++)for(var o=t[e],r=0;r<o.length;r++){(0,o[r])()}},size:function(){return e}}}t.exports=function(t){var e=(t=t||{}).reporter,n=i.getOption(t,"async",!0),r=i.getOption(t,"auto",!0);r&&!n&&(e&&e.warn("Invalid options combination. auto=true and async=false is invalid. Setting async=true."),n=!0);var a,s=o(),l=!1;function c(){for(l=!0;s.size();){var t=s;s=o(),t.process()}l=!1}function d(){var t;t=c,a=setTimeout(t,0)}return{add:function(t,e){!l&&r&&n&&0===s.size()&&d(),s.add(t,e)},force:function(t){l||(void 0===t&&(t=n),a&&(clearTimeout(a),a=null),t?d():c())}}}},function(t,e,n){"use strict";(t.exports={}).getOption=function(t,e,n){var i=t[e];if(null==i&&void 0!==n)return n;return i}},function(t,e,n){"use strict";var i="_erd";function o(t){return t[i]}t.exports={initState:function(t){return t[i]={},o(t)},getState:o,cleanState:function(t){delete t[i]}}},function(t,e,n){"use strict";var i=n(1);t.exports=function(t){var e=(t=t||{}).reporter,n=t.batchProcessor,o=t.stateHandler.getState;if(!e)throw new Error("Missing required dependency: reporter.");function r(t){return o(t).object}return{makeDetectable:function(t,r,a){a||(a=r,r=t,t=null),(t=t||{}).debug,i.isIE(8)?a(r):function(t,r){var a="display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none; padding: 0; margin: 0; opacity: 0; z-index: -1000; pointer-events: none;",s=!1,l=window.getComputedStyle(t),c=t.offsetWidth,d=t.offsetHeight;function u(){function n(){if("static"===l.position){t.style.position="relative";var n=function(t,e,n,i){var o=n[i];"auto"!==o&&"0"!==function(t){return t.replace(/[^-\d\.]/g,"")}(o)&&(t.warn("An element that is positioned static has style."+i+"="+o+" which is ignored due to the static positioning. The element will need to be positioned relative, so the style."+i+" will be set to 0. Element: ",e),e.style[i]=0)};n(e,t,l,"top"),n(e,t,l,"right"),n(e,t,l,"bottom"),n(e,t,l,"left")}}""!==l.position&&(n(),s=!0);var c=document.createElement("object");c.style.cssText=a,c.tabIndex=-1,c.type="text/html",c.setAttribute("aria-hidden","true"),c.onload=function(){s||n(),function t(e,n){e.contentDocument?n(e.contentDocument):setTimeout((function(){t(e,n)}),100)}(this,(function(e){r(t)}))},i.isIE()||(c.data="about:blank"),t.appendChild(c),o(t).object=c,i.isIE()&&(c.data="about:blank")}o(t).startSize={width:c,height:d},n?n.add(u):u()}(r,a)},addListener:function(t,e){if(!r(t))throw new Error("Element is not detectable by this strategy.");function n(){e(t)}i.isIE(8)?(o(t).object={proxy:n},t.attachEvent("onresize",n)):r(t).contentDocument.defaultView.addEventListener("resize",n)},uninstall:function(t){i.isIE(8)?t.detachEvent("onresize",o(t).object.proxy):t.removeChild(r(t)),delete o(t).object}}}},function(t,e,n){"use strict";var i=n(0).forEach;t.exports=function(t){var e=(t=t||{}).reporter,n=t.batchProcessor,o=t.stateHandler.getState,r=(t.stateHandler.hasState,t.idHandler);if(!n)throw new Error("Missing required dependency: batchProcessor");if(!e)throw new Error("Missing required dependency: reporter.");var a=function(){var t=document.createElement("div");t.style.cssText="position: absolute; width: 1000px; height: 1000px; visibility: hidden; margin: 0; padding: 0;";var e=document.createElement("div");e.style.cssText="position: absolute; width: 500px; height: 500px; overflow: scroll; visibility: none; top: -1500px; left: -1500px; visibility: hidden; margin: 0; padding: 0;",e.appendChild(t),document.body.insertBefore(e,document.body.firstChild);var n=500-e.clientWidth,i=500-e.clientHeight;return document.body.removeChild(e),{width:n,height:i}}(),s="erd_scroll_detection_container";function l(t,n,i){if(t.addEventListener)t.addEventListener(n,i);else{if(!t.attachEvent)return e.error("[scroll] Don't know how to add event listeners.");t.attachEvent("on"+n,i)}}function c(t,n,i){if(t.removeEventListener)t.removeEventListener(n,i);else{if(!t.detachEvent)return e.error("[scroll] Don't know how to remove event listeners.");t.detachEvent("on"+n,i)}}function d(t){return o(t).container.childNodes[0].childNodes[0].childNodes[0]}function u(t){return o(t).container.childNodes[0].childNodes[0].childNodes[1]}return function(t,e){if(!document.getElementById(t)){var n=e+"_animation",i="/* Created by the element-resize-detector library. */\n";i+="."+e+" > div::-webkit-scrollbar { display: none; }\n\n",i+="."+(e+"_animation_active")+" { -webkit-animation-duration: 0.1s; animation-duration: 0.1s; -webkit-animation-name: "+n+"; animation-name: "+n+"; }\n",i+="@-webkit-keyframes "+n+" { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }\n",function(e,n){n=n||function(t){document.head.appendChild(t)};var i=document.createElement("style");i.innerHTML=e,i.id=t,n(i)}(i+="@keyframes "+n+" { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }")}}("erd_scroll_detection_scrollbar_style",s),{makeDetectable:function(t,c,h){function f(){if(t.debug){var n=Array.prototype.slice.call(arguments);if(n.unshift(r.get(c),"Scroll: "),e.log.apply)e.log.apply(null,n);else for(var i=0;i<n.length;i++)e.log(n[i])}}function p(t){var e=o(t).container.childNodes[0],n=window.getComputedStyle(e);return!n.width||-1===n.width.indexOf("px")}function v(){var t=window.getComputedStyle(c),e={};return e.position=t.position,e.width=c.offsetWidth,e.height=c.offsetHeight,e.top=t.top,e.right=t.right,e.bottom=t.bottom,e.left=t.left,e.widthCSS=t.width,e.heightCSS=t.height,e}function g(){if(f("storeStyle invoked."),o(c)){var t=v();o(c).style=t}else f("Aborting because element has been uninstalled")}function m(t,e,n){o(t).lastWidth=e,o(t).lastHeight=n}function y(){return 2*a.width+1}function b(){return 2*a.height+1}function w(t){return t+10+y()}function x(t){return t+10+b()}function E(t,e,n){var i=d(t),o=u(t),r=w(e),a=x(n),s=function(t){return 2*t+y()}(e),l=function(t){return 2*t+b()}(n);i.scrollLeft=r,i.scrollTop=a,o.scrollLeft=s,o.scrollTop=l}function S(){var t=o(c).container;if(!t){(t=document.createElement("div")).className=s,t.style.cssText="visibility: hidden; display: inline; width: 0px; height: 0px; z-index: -1; overflow: hidden; margin: 0; padding: 0;",o(c).container=t,function(t){t.className+=" "+s+"_animation_active"}(t),c.appendChild(t);var e=function(){o(c).onRendered&&o(c).onRendered()};l(t,"animationstart",e),o(c).onAnimationStart=e}return t}function T(){if(f("Injecting elements"),o(c)){!function(){var t=o(c).style;if("static"===t.position){c.style.position="relative";var n=function(t,e,n,i){var o=n[i];"auto"!==o&&"0"!==function(t){return t.replace(/[^-\d\.]/g,"")}(o)&&(t.warn("An element that is positioned static has style."+i+"="+o+" which is ignored due to the static positioning. The element will need to be positioned relative, so the style."+i+" will be set to 0. Element: ",e),e.style[i]=0)};n(e,c,t,"top"),n(e,c,t,"right"),n(e,c,t,"bottom"),n(e,c,t,"left")}}();var t=o(c).container;t||(t=S());var n,i,r,d,u=a.width,h=a.height,p="position: absolute; flex: none; overflow: hidden; z-index: -1; visibility: hidden; "+("left: "+(n=(n=-(1+u))?n+"px":"0")+"; top: "+(i=(i=-(1+h))?i+"px":"0")+"; right: "+(d=(d=-u)?d+"px":"0")+"; bottom: "+(r=(r=-h)?r+"px":"0")+";"),v=document.createElement("div"),g=document.createElement("div"),m=document.createElement("div"),y=document.createElement("div"),b=document.createElement("div"),w=document.createElement("div");v.dir="ltr",v.style.cssText="position: absolute; flex: none; overflow: hidden; z-index: -1; visibility: hidden; width: 100%; height: 100%; left: 0px; top: 0px;",v.className=s,g.className=s,g.style.cssText=p,m.style.cssText="position: absolute; flex: none; overflow: scroll; z-index: -1; visibility: hidden; width: 100%; height: 100%;",y.style.cssText="position: absolute; left: 0; top: 0;",b.style.cssText="position: absolute; flex: none; overflow: scroll; z-index: -1; visibility: hidden; width: 100%; height: 100%;",w.style.cssText="position: absolute; width: 200%; height: 200%;",m.appendChild(y),b.appendChild(w),g.appendChild(m),g.appendChild(b),v.appendChild(g),t.appendChild(v),l(m,"scroll",x),l(b,"scroll",E),o(c).onExpandScroll=x,o(c).onShrinkScroll=E}else f("Aborting because element has been uninstalled");function x(){o(c).onExpand&&o(c).onExpand()}function E(){o(c).onShrink&&o(c).onShrink()}}function A(){function a(t,e,n){var i=function(t){return d(t).childNodes[0]}(t),o=w(e),r=x(n);i.style.width=o+"px",i.style.height=r+"px"}function s(i){var s=c.offsetWidth,d=c.offsetHeight;f("Storing current size",s,d),m(c,s,d),n.add(0,(function(){if(o(c))if(l()){if(t.debug){var n=c.offsetWidth,i=c.offsetHeight;n===s&&i===d||e.warn(r.get(c),"Scroll: Size changed before updating detector elements.")}a(c,s,d)}else f("Aborting because element container has not been initialized");else f("Aborting because element has been uninstalled")})),n.add(1,(function(){o(c)?l()?E(c,s,d):f("Aborting because element container has not been initialized"):f("Aborting because element has been uninstalled")})),i&&n.add(2,(function(){o(c)?l()?i():f("Aborting because element container has not been initialized"):f("Aborting because element has been uninstalled")}))}function l(){return!!o(c).container}function h(){f("notifyListenersIfNeeded invoked");var t=o(c);return void 0===o(c).lastNotifiedWidth&&t.lastWidth===t.startSize.width&&t.lastHeight===t.startSize.height?f("Not notifying: Size is the same as the start size, and there has been no notification yet."):t.lastWidth===t.lastNotifiedWidth&&t.lastHeight===t.lastNotifiedHeight?f("Not notifying: Size already notified"):(f("Current size not notified, notifying..."),t.lastNotifiedWidth=t.lastWidth,t.lastNotifiedHeight=t.lastHeight,void i(o(c).listeners,(function(t){t(c)})))}function v(){if(f("Scroll detected."),p(c))f("Scroll event fired while unrendered. Ignoring...");else{var t=c.offsetWidth,e=c.offsetHeight;t!==o(c).lastWidth||e!==o(c).lastHeight?(f("Element size changed."),s(h)):f("Element size has not changed ("+t+"x"+e+").")}}if(f("registerListenersAndPositionElements invoked."),o(c)){o(c).onRendered=function(){if(f("startanimation triggered."),p(c))f("Ignoring since element is still unrendered...");else{f("Element rendered.");var t=d(c),e=u(c);0!==t.scrollLeft&&0!==t.scrollTop&&0!==e.scrollLeft&&0!==e.scrollTop||(f("Scrollbars out of sync. Updating detector elements..."),s(h))}},o(c).onExpand=v,o(c).onShrink=v;var g=o(c).style;a(c,g.width,g.height)}else f("Aborting because element has been uninstalled")}function O(){if(f("finalizeDomMutation invoked."),o(c)){var t=o(c).style;m(c,t.width,t.height),E(c,t.width,t.height)}else f("Aborting because element has been uninstalled")}function k(){h(c)}function z(){var t;f("Installing..."),o(c).listeners=[],t=v(),o(c).startSize={width:t.width,height:t.height},f("Element start size",o(c).startSize),n.add(0,g),n.add(1,T),n.add(2,A),n.add(3,O),n.add(4,k)}h||(h=c,c=t,t=null),t=t||{},f("Making detectable..."),!function(t){return!function(t){return t===t.ownerDocument.body||t.ownerDocument.body.contains(t)}(t)||null===window.getComputedStyle(t)}(c)?z():(f("Element is detached"),S(),f("Waiting until element is attached..."),o(c).onRendered=function(){f("Element is now attached"),z()})},addListener:function(t,e){if(!o(t).listeners.push)throw new Error("Cannot add listener to an element that is not detectable.");o(t).listeners.push(e)},uninstall:function(t){var e=o(t);e&&(e.onExpandScroll&&c(d(t),"scroll",e.onExpandScroll),e.onShrinkScroll&&c(u(t),"scroll",e.onShrinkScroll),e.onAnimationStart&&c(e.container,"animationstart",e.onAnimationStart),e.container&&t.removeChild(e.container))}}}},function(t,e,n){"use strict";var i;n.r(e),function(t){t[t.TOP=0]="TOP",t[t.BOTTOM=1]="BOTTOM",t[t.LEFT=2]="LEFT",t[t.RIGHT=3]="RIGHT",t[t.ONSTAGE=4]="ONSTAGE"}(i||(i={}));var o=function(){function t(t){this.color="#ffffff",this.active=!0,this.pos=i.TOP,this.canvas=t;var e=t.getContext("2d");if(!e)throw new Error("Canvas 2D context not found, please check it is running in Browser environment.");this.ctx=e,this.allocate()}return t.prototype.draw=function(){this.updatePosition(),(this.active||this.pos===i.ONSTAGE)&&(this.pos!==i.LEFT&&this.pos!==i.RIGHT&&this.pos!==i.BOTTOM?(this.y+=this.vy,this.x+=this.vx,this.ctx.globalAlpha=this.alpha,this.ctx.beginPath(),this.ctx.arc(this.x,this.y,this.radius,0,2*Math.PI,!1),this.ctx.closePath(),this.ctx.fillStyle=this.color,this.ctx.fill()):this.allocate())},t.prototype.updatePosition=function(){this.y<-this.radius?this.pos=i.TOP:this.y>this.canvas.height+this.radius?this.pos=i.BOTTOM:this.x<-this.radius?this.pos=i.LEFT:this.x>this.canvas.width+this.radius?this.pos=i.RIGHT:this.pos=i.ONSTAGE},t.prototype.allocate=function(){this.x=Math.random()*this.canvas.width,this.y=Math.random()*-this.canvas.height,this.vy=1+3*Math.random(),this.vx=.5-Math.random(),this.radius=1+2*Math.random(),this.alpha=.5+.5*Math.random()},t}(),r=n(2),a=n.n(r),s=function(){return(s=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},l={color:"#ffffff",volumn:300},c=function(){function t(t,e){void 0===t&&(t="body"),this.active=!1,this.initialised=!1,this.animationId=0,this.resizeDetector=a()({strategy:"scroll"});var n="string"==typeof t?document.querySelector(t):t;if(!n)throw new Error("can not find container by specified selector");this.container=n,this.config=s(s({},l),e),this.buildScene()}return t.prototype.play=function(){var t=this;this.initialised||this.buildScene(),this.active=!0,this.snowflakes.forEach((function(t){return t.active=!0})),this.animationId||(this.animationId=requestAnimationFrame((function(){return t.updateFrame()})))},t.prototype.pause=function(){this.active=!1,this.snowflakes.forEach((function(t){return t.active=!1}))},t.prototype.toggle=function(){this.active?this.pause():this.play()},t.prototype.buildScene=function(){var t=this,e=document.createElement("canvas");e.style.position="absolute",e.style.left="0",e.style.top="0",e.style.pointerEvents="none",e.width=this.container.clientWidth,e.height=this.container.clientHeight,this.container.appendChild(e),this.canvas=e;var n=e.getContext("2d");if(!n)throw new Error("Canvas 2D context not found, please check it is running in Browser environment.");this.ctx=n,this.snowflakes=[];for(var i=0;i<this.config.volumn;i++){var r=new o(this.canvas);r.color=this.config.color,this.snowflakes.push(r)}this.resizeDetector.listenTo(this.container,(function(){t.onResize()})),this.initialised=!0},t.prototype.destroyScene=function(){var t;null===(t=this.canvas)||void 0===t||t.remove(),this.resizeDetector.uninstall(this.container),this.initialised=!1},t.prototype.updateFrame=function(){var t=this;this.canvas&&this.ctx&&(this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height),this.snowflakes.forEach((function(t){t.draw()})),!this.active&&this.snowflakes.every((function(t){return t.pos!==i.ONSTAGE}))?(this.animationId=0,this.destroyScene()):this.animationId=requestAnimationFrame((function(){return t.updateFrame()})))},t.prototype.onResize=function(){this.canvas&&this.ctx&&(this.canvas.width=this.container.clientWidth,this.canvas.height=this.container.clientHeight)},t}();"undefined"!=typeof window&&(window.SnowScene=c)}]);
//# sourceMappingURL=snowflakes.bundle.min.js.map