<html lang="en">
<head>
  <style class="vjs-styles-defaults">
    .video-js {
      width: 300px;
      height: 150px;
    }

    .vjs-fluid {
      padding-top: 56.25%;
    }
  </style>
  <meta charset="UTF-8" />
  <meta name="referrer" content="no-referrer" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link href="./static/js/element-ui.css" rel="stylesheet" />
  <link
          href="static/js/animate.min.css"
          rel="stylesheet"
  />
  <link href="static/js/css/video-js.min.css" rel="stylesheet" />
  <link href="static/js/Cesium/Widgets/widgets.css" rel="stylesheet" />
  <link href="static/js/css/common.css" rel="stylesheet" />
  <script type="text/javascript" src="static/js/adapter.min.js"></script>
  <script type="text/javascript" src="static/js/webrtcstreamer.js"></script>
  <title>可视化预览</title>
  <style>
    /*!!!!!!!不要删除下面这句话 作用为替换css文件*/

    /* .el-image__inner {
width: 100%;
height: 100%;
} */
    .baseCss {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
    }

    html,
    body,
    p,
    ul {
      margin: 0;
      padding: 0;
    }

    @font-face {
      font-family: "FZHZGBJW";
      src: url(static/js/font/FZHZGBJW.TTF);
    }

    @font-face {
      font-family: "DIGITALDREAMFAT";
      src: url("static/js/font/DIGITALDREAMFAT.woff2") format("woff2"),
      url("static/js/font/DIGITALDREAMFAT.woff") format("woff"),
      url("static/js/font/DIGITALDREAMFAT.ttf") format("truetype");
    }

    #body {
      width: 100%;
      height: 100vh;
      /* background-color: #0D1D31; */
      background-size: 100% 100% !important;
      user-select: none;
      /* background-image: linear-gradient(rgb(41, 40, 50) 13px, transparent 0),
      linear-gradient(90deg, rgba(146, 134, 134, 0.623) 2px, transparent 0);
    background-size: 15px 15px, 15px 15px; */
    }

    ::-webkit-scrollbar {
      width: 5px;
      height: 5px;
      background-color: aliceblue;
    }

    ::-webkit-scrollbar-track {
      background: rgba(50, 50, 50, 0.01);
    }

    ::-webkit-scrollbar-thumb {
      background-color: rgba(50, 50, 50, 0.6);
    }

    .chart-item {
      width: 600px;
      height: 397px;
      position: absolute;
      /* top: 30px; */
      /* left: 30px; */
      background-repeat: no-repeat !important;
      background-size: 100% 100% !important;
    }

    .chart-item .chart-box {
      display: inline-block;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .main {
      width: 100%;
      height: 100%;
    }

    #background {
      height: 100%;
      width: 100%;
      position: absolute;
      z-index: 0;
    }

    li {
      list-style: none;
    }

    .full {
      width: 100% !important;
      height: 100% !important;
    }

    .el-dialog {
      z-index: 9999 !important;

      border: 0;
    }

    .el-dialog__headerbtn {
      font-size: 40px;
    }

    .willResizeClass {
      width: 90%;
      height: 90%;
      position: absolute;
      left: 50%;
      top: 55%;
      transform: translate(-50%, -50%);
    }

    .fade-enter-active,
    .fade-leave-active {
      transition: opacity 0.5s;
    }

    .fade-enter,
    .fade-leave-to {
      opacity: 0;
    }

    .amap-logo {
      display: none;
      opacity: 0 !important;
    }

    .amap-copyright {
      opacity: 0;
    }

    .l7-right {
      display: none;
    }

    .cesium-viewer-bottom {
      display: none;
    }

    .background {
      width: 100%;
      height: 100%;
      position: absolute;
      background-size: 100% 100% !important;
      top: 0;
      z-index: -1;
    }

    .x-spreadsheet-sheet {
      pointer-events: none;
    }

    .x-spreadsheet-toolbar {
      display: none;
    }

    .el-carousel__container {
      height: 100% !important;
    }

    [v-cloak] {
      display: none !important;
    }

    .l7-left {
      display: none;
    }


  </style>
  <style type="text/css">
    .dv-border-box-12 {
      position: relative;
      width: 100%;
      height: 100%;
    }
    .dv-border-box-12 .dv-border-svg-container {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0px;
      left: 0px;
    }
    .dv-border-box-12 .border-box-content {
      position: relative;
      width: 100%;
      height: 100%;
    }
    /* 添加滚动动画样式 */
    .scroll-list {
      position: relative;
    }

    .scroll-content {
      animation: scrollUp 20s linear infinite;
    }

    .scroll-content:hover {
      animation-play-state: paused;
    }

    @keyframes scrollUp {
      0% {
        transform: translateY(0);
      }
      100% {
        transform: translateY(-50%);
      }
    }
    .analysis-dialog {
      background: rgba(0,19,52,0.9) !important;
    }
    .analysis-dialog .el-dialog__title {
      color: #fff;
    }
    .analysis-dialog .el-dialog__header {
      border-bottom: 1px solid rgba(124,231,253,0.3);
    }
    .analysis-dialog .el-dialog__headerbtn .el-dialog__close {
      color: #fff;
    }

    .analysis-dialog .el-button--primary:hover {
      background-color: #409eff !important;
      border-color: #409eff !important;
    }

    .analysis-dialog .el-button--primary:focus {
      background-color: #188df0 !important;
      border-color: #188df0 !important;
    }

    .analysis-dialog .el-date-editor {
      background-color: transparent;
    }

    .analysis-dialog .el-date-editor .el-range-input {
      color: #fff;
      background-color: transparent;
    }

    .analysis-dialog .el-date-editor .el-range-separator {
      color: #fff;
    }

    .analysis-dialog .el-button--primary[loading] {
      background-color: #188df0;
      border-color: #188df0;
      opacity: 0.8;
    }


    .history-dialog {
      background: rgba(0,19,52,0.9) !important;
    }
    .history-dialog .el-dialog__title {
      color: #fff;
    }
    .history-dialog .el-dialog__header {
      border-bottom: 1px solid rgba(124,231,253,0.3);
    }
    .history-dialog .el-dialog__headerbtn .el-dialog__close {
      color: #fff;
    }

    .history-dialog .el-button--primary:hover {
      background-color: #409eff !important;
      border-color: #409eff !important;
    }

    .history-dialog .el-button--primary:focus {
      background-color: #188df0 !important;
      border-color: #188df0 !important;
    }

    .history-dialog .el-date-editor {
      background-color: transparent;
    }

    .history-dialog .el-date-editor .el-range-input {
      color: #fff;
      background-color: transparent;
    }

    .history-dialog .el-date-editor .el-range-separator {
      color: #fff;
    }

    .history-dialog .el-button--primary[loading] {
      background-color: #188df0;
      border-color: #188df0;
      opacity: 0.8;
    }


    .history-dialog .el-table,
    .history-dialog .el-table__header-wrapper,
    .history-dialog .el-table__body-wrapper {
      background-color: transparent !important;
    }

    .history-dialog .el-table::before {
      display: none;  /* 移除表格底部边框 */
    }

    /* 修复表格背景和文字颜色 */
    .history-dialog .el-table td,
    .history-dialog .el-table th {
      background-color: transparent !important;
    }

    .history-dialog .el-table--striped .el-table__body tr.el-table__row--striped td {
      background-color: rgba(0, 60, 160, 0.3) !important;
    }

    /* 表格内的所有文字颜色 */
    .history-dialog .el-table {
      color: #fff !important;
    }

    .history-dialog .el-table th,
    .history-dialog .el-table td,
    .history-dialog .el-table tr {
      color: #fff !important;
    }

    .history-dialog .el-table th, .el-table tr{
      background-color: transparent !important;
    }


    .history-dialog .el-select .el-input__inner {
      background-color: transparent !important;
      color: #fff !important;
      border-color: rgba(124,231,253,0.3) !important;
    }

    /* 下拉面板样式 */
    .el-select-dropdown.el-popper {
      background-color: rgba(0,19,52,0.95) !important;
      border: 1px solid rgba(124,231,253,0.3) !important;
    }

    .el-select-dropdown.el-popper .el-select-dropdown__item {
      background-color: transparent !important;
      color: #fff !important;
    }

    .el-select-dropdown.el-popper .el-select-dropdown__item.hover,
    .el-select-dropdown.el-popper .el-select-dropdown__item:hover {
      background-color: rgba(0, 60, 160, 0.3) !important;
    }

    .el-select-dropdown.el-popper .el-select-dropdown__item.selected {
      color: #188df0 !important;
      background-color: rgba(24, 141, 240, 0.1) !important;
    }



    .history-dialog .el-pagination {
      margin-top: 20px;
    }

    .history-dialog .el-pagination button {
      background-color: transparent !important;
      color: #fff !important;
    }

    .history-dialog .el-pagination .el-pager li {
      background-color: transparent !important;
      color: #fff !important;
      border: 1px solid rgba(124,231,253,0.3);
    }

    .history-dialog .el-pagination .el-pager li.active {
      color: #188df0 !important;
      border-color: #188df0;
    }

    .history-dialog .el-pagination .el-pager li:hover {
      color: #188df0 !important;
    }

    .history-dialog .el-pagination .btn-prev,
    .history-dialog .el-pagination .btn-next {
      background-color: transparent !important;
      color: #fff !important;
      border: 1px solid rgba(124,231,253,0.3);
    }

    .history-dialog .el-pagination .btn-prev:hover,
    .history-dialog .el-pagination .btn-next:hover {
      color: #188df0 !important;
    }

    .history-dialog .el-pagination .el-pagination__total,
    .history-dialog .el-pagination .el-pagination__jump {
      color: #fff !important;
    }

    /* 分页器输入框样式 */
    .history-dialog .el-pagination .el-input__inner {
      background-color: transparent !important;
      color: #fff !important;
      border-color: rgba(124,231,253,0.3) !important;
    }

    .history-dialog .el-pagination .el-input__inner:focus {
      border-color: #188df0 !important;
    }

    /* 下拉框箭头颜色 */
    .history-dialog .el-select .el-input .el-select__caret {
      color: #fff !important;
    }

    /* 确保下拉面板的箭头样式正确 */
    .el-popper .popper__arrow,
    .el-popper .popper__arrow::after {
      border-bottom-color: rgba(0,19,52,0.95) !important;
    }

    /* 禁用状态的样式 */
    .history-dialog .el-pagination button:disabled {
      background-color: transparent !important;
      color: rgba(255, 255, 255, 0.4) !important;
    }

    .history-dialog .el-select .el-input.is-disabled .el-input__inner {
      background-color: transparent !important;
      color: rgba(255, 255, 255, 0.4) !important;
    }

    .el-form-item__label{
      color: #fff !important;
    }

    /*# sourceURL=main.vue */
    /*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm1haW4udnVlIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0Usa0JBQWtCO0VBQ2xCLFdBQVc7RUFDWCxZQUFZO0FBQ2Q7QUFDQTtFQUNFLGtCQUFrQjtFQUNsQixXQUFXO0VBQ1gsWUFBWTtFQUNaLFFBQVE7RUFDUixTQUFTO0FBQ1g7QUFDQTtFQUNFLGtCQUFrQjtFQUNsQixXQUFXO0VBQ1gsWUFBWTtBQUNkIiwiZmlsZSI6Im1haW4udnVlIiwic291cmNlc0NvbnRlbnQiOlsiLmR2LWJvcmRlci1ib3gtMTIge1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG59XG4uZHYtYm9yZGVyLWJveC0xMiAuZHYtYm9yZGVyLXN2Zy1jb250YWluZXIge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDEwMCU7XG4gIHRvcDogMHB4O1xuICBsZWZ0OiAwcHg7XG59XG4uZHYtYm9yZGVyLWJveC0xMiAuYm9yZGVyLWJveC1jb250ZW50IHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xufVxuIl19 */
  </style>
</head>

<body>
<div
        id="body"
        element-loading-background="rgba(0, 0, 0, 0.8)"
        element-loading-text="拼命加载中"
        class=""
        style="zoom: 1"
>
  <div class="echartsList">
    <ul :style="ulStyle">
      <li
              id="1dc1a6d0-dda6-47f3-8552-2d4e11e8861achart-item"
              class="chart-item"
              style="
              zoom: 1;
              will-change: auto;
              width: 389px;
              height: 57px;
              transform: translate3d(1484px, 835px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="1dc1a6d0-dda6-47f3-8552-2d4e11e8861a"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="1dc1a6d0-dda6-47f3-8552-2d4e11e8861a"
                    style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  "
            >
              <span>| 运顺皮带机头</span>
            </p>
          </div>
        </div>
      </li>

      <li
              id="1dc1a6d0-dda6-47f3-8552-2d4e11e8861achart-item"
              class="chart-item"
              style="
              zoom: 1;
              will-change: auto;
              width: 389px;
              height: 167px;
              transform: translate3d(1484px, 885px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="1dc1a6d0-dda6-47f3-8552-2d4e11e8861a"
                  class="main animate__animated animate__slideInLeft"
          >
            <!--            <object-->
            <!--                    type="application/x-vlc-plugin"-->
            <!--                    id="vlc22"-->
            <!--                    events="True"-->
            <!--                    width="370"-->
            <!--                    controls="no"-->
            <!--                    height="150"-->
            <!--                    pluginspage="http://www.videolan.org"-->
            <!--                    codebase="http://downloads.videolan.org/pub/videolan/vlc-webplugins/2.0.6/npapi-vlc-2.0.6.tar.xz"-->
            <!--            >-->
            <!--              <param-->
            <!--                      name="mrl"-->
            <!--                      value="rtsp://137.12.60.232:1166/00122561536326390101?DstCode=01&ServiceType=1&ClientType=1&StreamID=1&SrcTP=2&DstTP=2&SrcPP=1&DstPP=1&MediaTransMode=0&BroadcastType=0&SV=3&Token=4Xaa7JF9k0rqyUU/BiBCLT0raELR13PzRvebUxXKW/w=&DomainCode=c73414d736ae4d2e80a11b77d18da128&UserId=2&"-->
            <!--              />-->
            <!--              <param name="volume" value="50" />-->
            <!--              <param name="autoplay" value="true" />-->
            <!--              <param name="loop" value="false" />-->
            <!--              <param name="fullscreen" value="true" />-->
            <!--            </object>-->
            <embed type="application/x-vlc-plugin" src="rtsp://137.12.60.232:1166/00122561536326390101?DstCode=01&ServiceType=1&ClientType=1&StreamID=1&SrcTP=2&DstTP=2&SrcPP=1&DstPP=1&MediaTransMode=0&BroadcastType=0&SV=3&Token=XVBT6d58wu+djjwQaWiUerfRNBFfs9FhYhlYqTBZVR8=&DomainCode=c73414d736ae4d2e80a11b77d18da128&UserId=2&" width="370" height="160" controls="0">

          </div>
        </div>
      </li>
      <li
              id="ba2b9f99-da17-4c03-a040-29c4624350fechart-item"
              class="chart-item"
              style="
              zoom: 1;
              will-change: auto;
              width: 389px;
              height: 57px;
              transform: translate3d(1484px, 593px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="ba2b9f99-da17-4c03-a040-29c4624350fe"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="ba2b9f99-da17-4c03-a040-29c4624350fe"
                    style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  "
            >
              <span>| 运顺皮带机驱动处</span>
            </p>
          </div>
        </div>
      </li>

      <li
              id="ba2b9f99-da17-4c03-a040-29c4624350fechart-item"
              class="chart-item"
              style="
              zoom: 1;
              will-change: auto;
              width: 389px;

              transform: translate3d(1484px, 646px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="ba2b9f99-da17-4c03-a040-29c4624350fe"
                  class="main animate__animated animate__slideInLeft"
          >
            <!--            <object-->
            <!--                    type="application/x-vlc-plugin"-->
            <!--                    id="vlc233"-->
            <!--                    events="True"-->
            <!--                    width="370"-->
            <!--                    controls="no"-->
            <!--                    height="150"-->
            <!--                    pluginspage="http://www.videolan.org"-->
            <!--                    codebase="http://downloads.videolan.org/pub/videolan/vlc-webplugins/2.0.6/npapi-vlc-2.0.6.tar.xz"-->
            <!--            >-->
            <!--              <param-->
            <!--                      name="mrl"-->
            <!--                      value="rtsp://137.12.60.225:1166/00122561532469220101?DstCode=01&ServiceType=1&ClientType=1&StreamID=1&SrcTP=2&DstTP=2&SrcPP=1&DstPP=1&MediaTransMode=0&BroadcastType=0&SV=3&Token=+XMW+Ywt4ISsxmMTPvOQasrq/yeMZFNEplegNMP3EVQ=&DomainCode=c73414d736ae4d2e80a11b77d18da128&UserId=2&"-->
            <!--              />-->
            <!--              <param name="volume" value="50" />-->
            <!--              <param name="autoplay" value="true" />-->
            <!--              <param name="loop" value="false" />-->
            <!--              <param name="fullscreen" value="true" />-->
            <!--            </object>-->

            <embed type="application/x-vlc-plugin" src="rtsp://137.12.60.225:1166/00122561532469220101?DstCode=01&ServiceType=1&ClientType=1&StreamID=1&SrcTP=2&DstTP=2&SrcPP=1&DstPP=1&MediaTransMode=0&BroadcastType=0&SV=3&Token=xMD0nVt6uQgcFlyQ+5fGVPViR4tCtz00+HewMpE2Q7Q=&DomainCode=c73414d736ae4d2e80a11b77d18da128&UserId=2&c" width="370" height="160" controls="0">
          </div>
        </div>
      </li>
      <li
              id="6a474af4-e9c8-4c7f-af83-c0d4c71ce106chart-item"
              class="chart-item"
              style="
              zoom: 1;
              will-change: auto;
              width: 389px;
              height: 57px;
              transform: translate3d(1484px, 357px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="6a474af4-e9c8-4c7f-af83-c0d4c71ce106"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="6a474af4-e9c8-4c7f-af83-c0d4c71ce106"
                    style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  "
            >
              <span>| 迎头1</span>
            </p>
          </div>
        </div>
      </li>

      <li
              id="6a474af4-e9c8-4c7f-af83-c0d4c71ce106chart-item"
              class="chart-item"
              style="
              zoom: 1;
              will-change: auto;
              width: 389px;

              transform: translate3d(1492px, 400px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="6a474af4-e9c8-4c7f-af83-c0d4c71ce106"
                  class="main animate__animated animate__slideInLeft"
          >
            <!--            <object-->
            <!--                    type="application/x-vlc-plugin"-->
            <!--                    id="vlc11"-->
            <!--                    events="True"-->
            <!--                    width="370"-->
            <!--                    controls="no"-->
            <!--                    height="150"-->
            <!--                    pluginspage="http://www.videolan.org"-->
            <!--                    codebase="http://downloads.videolan.org/pub/videolan/vlc-webplugins/2.0.6/npapi-vlc-2.0.6.tar.xz"-->
            <!--            >-->
            <!--              <param-->
            <!--                      name="mrl"-->
            <!--                      value="rtsp://137.12.60.232:1156/00122561531972820101?DstCode=01&ServiceType=1&ClientType=1&StreamID=1&SrcTP=2&DstTP=2&SrcPP=1&DstPP=1&MediaTransMode=0&BroadcastType=0&SV=3&Token=SJbTNiKFq1AuHbAFp++f9LFgm9b+OE1b9jhfUqUQsjQ=&DomainCode=c73414d736ae4d2e80a11b77d18da128&UserId=2&-->
            <!--"-->
            <!--              />-->
            <!--              <param name="autoplay" value="true" />-->
            <!--              <param name="loop" value="false" />-->
            <!--              <param name="fullscreen" value="true" />-->
            <!--            </object>-->

            <embed type="application/x-vlc-plugin" src="rtsp://137.12.60.225:1166/00122561531972820101?DstCode=01&ServiceType=1&ClientType=1&StreamID=1&SrcTP=2&DstTP=2&SrcPP=1&DstPP=1&MediaTransMode=0&BroadcastType=0&SV=3&Token=673NlfDPtb8b/HDTdWMjfG5RIaDyIktI3LdmzIJfrXc=&DomainCode=c73414d736ae4d2e80a11b77d18da128&UserId=2&
" width="370" height="160" controls="0">

          </div>
        </div>
      </li>
      <li
              id="dd2897d5-da8a-4b9e-8d5e-f02c785a3644chart-item"
              class="chart-item"
              style="
              zoom: 1;
              will-change: auto;
              width: 389px;
              height: 57px;
              transform: translate3d(1484px, 114px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="dd2897d5-da8a-4b9e-8d5e-f02c785a3644"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="dd2897d5-da8a-4b9e-8d5e-f02c785a3644"
                    style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  "
            >
              <span>| 迎头2</span>
            </p>
          </div>
        </div>
      </li>

      <li
              id="af1d2c34-95f8-4ade-b67a-5f4df4e9aaeachart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 380px;

              transform: translate3d(1484px, 160px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="af1d2c34-95f8-4ade-b67a-5f4df4e9aaea"
                  class="main animate__animated animate__slideInLeft"
          >
            <!--            <object-->
            <!--                    type="application/x-vlc-plugin"-->
            <!--                    id="vlc"-->
            <!--                    events="True"-->
            <!--                    width="360"-->
            <!--                    controls="no"-->
            <!--                    height="150"-->
            <!--                    pluginspage="http://www.videolan.org"-->
            <!--                    codebase="http://downloads.videolan.org/pub/videolan/vlc-webplugins/2.0.6/npapi-vlc-2.0.6.tar.xz"-->
            <!--            >-->
            <!--              <param-->
            <!--                      name="mrl"-->
            <!--                      value="rtsp://137.12.60.232:1156/00122561536013320101?DstCode=01&ServiceType=1&ClientType=1&StreamID=1&SrcTP=2&DstTP=2&SrcPP=1&DstPP=1&MediaTransMode=0&BroadcastType=0&SV=3&Token=gaDSu4whXts90D5Eeg5QKsIZisenXtOVgDJ7xifk4X0=&DomainCode=c73414d736ae4d2e80a11b77d18da128&UserId=2&"-->
            <!--              />-->

            <!--              <param name="autoplay" value="true" />-->
            <!--              <param name="showcontrols" value="false">-->
            <!--              <param name="fullscreen" value="true" />-->
            <!--            </object>-->
            <embed type="application/x-vlc-plugin" src="rtsp://137.12.60.234:1166/00122561536013320101?DstCode=01&ServiceType=1&ClientType=1&StreamID=1&SrcTP=2&DstTP=2&SrcPP=1&DstPP=1&MediaTransMode=0&BroadcastType=0&SV=3&Token=iSbSAYEeNEqxKxsiSl9YQRwKuEoZ7GMgMrPZf/H9VwI=&DomainCode=c73414d736ae4d2e80a11b77d18da128&UserId=2&" width="370" height="160" controls="0">

          </div>
        </div>
      </li>
      <!-- <li
            id="52f2528b-6830-4651-864d-afba8d9eb1f1chart-item"
            class="chart-item"
            style="
              zoom: 1;
              will-change: auto;
              width: 214px;
              height: 56px;
              transform: translate3d(1247px, 891px, 0px);
              z-index: 1016;
              background-color: transparent;
            "
          >
            <div
              class="chart-box animate__animated background selfAnimate_none"
              style="background: url('') no-repeat; transform: rotate(0deg)"
            >
              <div
                id="52f2528b-6830-4651-864d-afba8d9eb1f1"
                class="main animate__animated animate__slideInLeft"
              >
                <p
                  class="baseCss"
                  id="52f2528b-6830-4651-864d-afba8d9eb1f1"
                  style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  "
                >
                  <span>| 开机率 75%</span>
                </p>
              </div>
            </div>
          </li> -->

      <li
              id="b76fed82-ac78-4267-96e5-c3cb0845b6cbchart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 389px;
              height: 57px;
              transform: translate3d(34px, 388px, 0px);
              z-index: 1010;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="b76fed82-ac78-4267-96e5-c3cb0845b6cb"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="b76fed82-ac78-4267-96e5-c3cb0845b6cb"
                    style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  "
            >
              <span>| 顶板检测数据</span>
            </p>
          </div>
        </div>
      </li>

      <li
              id="b76fed82-ac78-4267-96e5-c3cb0845b6cbchart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 75%;
              transform: translate3d(34px, 419px, 0px);
              z-index: 1010;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="
                background: url('') no-repeat;
                transform: rotate(0deg);
                width: 100%;
              "
        >
          <div
                  id="b76fed82-ac78-4267-96e5-c3cb0845b6cb"
                  class="main animate__animated animate__slideInLeft"
          >
            <div class="chart-box" style="width: 100%">
              <div class="main" style="display: flex; ">
                <!-- 左侧图表 -->
                <div style="width: 777px; ">
                  <div
                          ref="leftBarChart"
                          style="height: 200px"
                          :style="{
                    transform: `scale(${zoom})`,
                    transformOrigin: 'left top'
                  }"
                  ></div>
                </div>

                <!-- 右侧图表 -->
                <div style="width: 47%; margin-right: -2%">
                  <div
                          ref="rightBarChart"
                          style="height: 200px"
                          :style="{
                    transform: `scale(${zoom})`,
                    transformOrigin: 'left top'
                  }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </li>

      <li
              id="1977da78-fbc7-4f2e-93de-1463c9012df3chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 389px;
              height: 57px;
              transform: translate3d(1004px, 96px, 0px);
              z-index: 1009;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="1977da78-fbc7-4f2e-93de-1463c9012df3"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="1977da78-fbc7-4f2e-93de-1463c9012df3"
                    style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  "
            >
              <span>| 安全监控数据</span>
            </p>
          </div>
        </div>
      </li>

      <li
              id="1977da78-fbc7-4f2e-93de-1463c9012df3chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 454px;

              transform: translate3d(1004px, 143px, 0px);
              z-index: 1009;

              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="1977da78-fbc7-4f2e-93de-1463c9012df3"
                  class="main animate__animated animate__slideInLeft"
          >
            <!-- 监控数据面板 -->
            <div
                    style="
                    display: flex;
                    flex-direction: column;
                    gap: 20px;
                    padding: 15px;
                    text-align: center;
                    font-size: 22px;
                  "
            >
              <!-- 第一行 -->
              <div
                      style="
                      display: flex;
                      gap: 20px;
                      justify-content: space-between;
                    "
              >
                <!-- 温度 -->
                <div
                        style="
                        width: 32%;
                        background: rgba(0, 60, 160, 0.3);
                        border-radius: 4px;
                        padding: 10px;
                      "
                >
                  <div
                          style="
                          display: flex;
                          align-items: center;
                          gap: 5px;
                          margin-bottom: 18px;
                        "
                  >
                    <i style="color: #7ce7fd; font-size: 22px">🌡️</i>
                    <span style="color: #fff; font-size: 22px">温度</span>
                  </div>
                  <div
                          style="
                          color: #ffeb3b;
                          font-size: 24px;
                          font-family: DIGITALDREAMFAT;
                        "
                  >
                    {{temperature}}<span
                          style="font-size: 22px; margin-left: 2px"
                  >℃</span
                  >
                  </div>
                </div>

                <!-- 粉尘 -->
                <div
                        style="
                        width: 32%;
                        background: rgba(0, 60, 160, 0.3);
                        border-radius: 4px;
                        padding: 10px;
                      "
                >
                  <div
                          style="
                          display: flex;
                          align-items: center;
                          gap: 5px;
                          margin-bottom: 18px;
                        "
                  >
                    <i style="color: #7ce7fd">💨</i>
                    <span style="color: #fff; font-size: 22px">粉尘</span>
                  </div>
                  <div
                          style="
                          color: #ffeb3b;
                          font-size: 24px;
                          font-family: DIGITALDREAMFAT;
                        "
                  >
                    {{dust}}<span style="font-size: 14px; margin-left: 2px"
                  >mg/m³</span
                  >
                  </div>
                </div>

                <!-- 风速 -->
                <div
                        style="
                        width: 32%;
                        background: rgba(0, 60, 160, 0.3);
                        border-radius: 4px;
                        padding: 10px;
                      "
                >
                  <div
                          style="
                          display: flex;
                          align-items: center;
                          gap: 5px;
                          margin-bottom: 18px;
                        "
                  >
                    <i style="color: #7ce7fd">🌪️</i>
                    <span style="color: #fff; font-size: 22px">风速</span>
                  </div>
                  <div
                          style="
                          color: #ffeb3b;
                          font-size: 24px;
                          font-family: DIGITALDREAMFAT;
                        "
                  >
                    {{windSpeed}}<span
                          style="font-size: 14px; margin-left: 2px"
                  >m/s</span
                  >
                  </div>
                </div>
              </div>

              <!-- 第二行 -->
              <div
                      style="
                      display: flex;
                      gap: 20px;
                      justify-content: space-between;
                    "
              >
                <!-- T1 -->
                <div
                        style="
                        width: 32%;
                        background: rgba(0, 60, 160, 0.3);
                        border-radius: 4px;
                        padding: 10px;
                      "
                >
                  <div
                          style="
                          display: flex;
                          align-items: center;
                          gap: 5px;
                          margin-bottom: 18px;
                        "
                  >
                    <i style="color: #7ce7fd">T1</i>
                  </div>
                  <div
                          style="
                          color: #ffeb3b;
                          font-size: 24px;
                          font-family: DIGITALDREAMFAT;
                        "
                  >
                    {{t1Amount}}<span
                          style="font-size: 14px; margin-left: 2px"
                  >%</span
                  >
                  </div>
                </div>

                <!-- T2 -->
                <div
                        style="
                        width: 32%;
                        background: rgba(0, 60, 160, 0.3);
                        border-radius: 4px;
                        padding: 10px;
                      "
                >
                  <div
                          style="
                          display: flex;
                          align-items: center;
                          gap: 5px;
                          margin-bottom: 18px;
                        "
                  >
                    <i style="color: #7ce7fd">T2</i>
                  </div>
                  <div
                          style="
                          color: #ffeb3b;
                          font-size: 24px;
                          font-family: DIGITALDREAMFAT;
                        "
                  >
                    {{t2Amount}}<span
                          style="font-size: 14px; margin-left: 2px"
                  >%</span
                  >
                  </div>
                </div>

                <!-- T进 -->
                <div
                        style="
                        width: 32%;
                        background: rgba(0, 60, 160, 0.3);
                        border-radius: 4px;
                        padding: 10px;
                      "
                >
                  <div
                          style="
                          display: flex;
                          align-items: center;
                          gap: 5px;
                          margin-bottom: 18px;
                        "
                  >
                    <i style="color: #7ce7fd">T进</i>
                  </div>
                  <div
                          style="
                          color: #ffeb3b;
                          font-size: 24px;
                          font-family: DIGITALDREAMFAT;
                        "
                  >
                    {{t3Amount}}<span
                          style="font-size: 14px; margin-left: 2px"
                  >%</span
                  >
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </li>

      <li
              id="772e7d79-39ac-4f00-81c4-eeb728ff4178chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 389px;
              height: 57px;
              transform: translate3d(518px, 96px, 0px);
              z-index: 1008;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="772e7d79-39ac-4f00-81c4-eeb728ff4178"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="772e7d79-39ac-4f00-81c4-eeb728ff4178"
                    style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  "
            >
              <span>| 进尺数据</span>
            </p>
          </div>
        </div>
      </li>

      <li
              id="772e7d79-39ac-4f00-81c4-eeb728ff4178chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 438px;
              transform: translate3d(518px, 143px, 0px);
              z-index: 1008;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div style="display: flex; gap: 20px; padding: 15px">
            <!-- 进尺数据面板 -->
            <div style="width: 100%;">
              <!-- 三班进尺数据 -->
              <div style="
              display: flex;
              gap: 15px;
              margin-bottom: 15px;
            ">
                <!-- 早班进尺 -->
                <div style="
                flex: 1;
                background: rgba(0, 60, 160, 0.3);
                border-radius: 4px;
                padding: 12px;
              ">
                  <div style="
                  display: flex;
                  align-items: center;
                  gap: 5px;
                  margin-bottom: 8px;
                ">
                    <span style="color: #7ce7fd; font-size: 20px">早班进尺</span>
                  </div>
                  <div style="
                  color: #ffeb3b;
                  font-size: 24px;
                  font-family: DIGITALDREAMFAT;
                  text-align: center;
                ">
                    {{morningProgress}}<span style="font-size: 14px; margin-left: 3px">cm</span>
                  </div>
                </div>

                <!-- 中班进尺 -->
                <div style="
                flex: 1;
                background: rgba(0, 60, 160, 0.3);
                border-radius: 4px;
                padding: 12px;
              ">
                  <div style="
                  display: flex;
                  align-items: center;
                  gap: 5px;
                  margin-bottom: 8px;
                ">
                    <span style="color: #7ce7fd; font-size: 20px">中班进尺</span>
                  </div>
                  <div style="
                  color: #ffeb3b;
                  font-size: 24px;
                  font-family: DIGITALDREAMFAT;
                  text-align: center;
                ">
                    {{dayProgress}}<span style="font-size: 14px; margin-left: 3px">cm</span>
                  </div>
                </div>

                <!-- 晚班进尺 -->
                <div style="
                flex: 1;
                background: rgba(0, 60, 160, 0.3);
                border-radius: 4px;
                padding: 12px;
              ">
                  <div style="
                  display: flex;
                  align-items: center;
                  gap: 5px;
                  margin-bottom: 8px;
                ">
                    <span style="color: #7ce7fd; font-size: 20px">晚班进尺</span>
                  </div>
                  <div style="
                  color: #ffeb3b;
                  font-size: 24px;
                  font-family: DIGITALDREAMFAT;
                  text-align: center;
                ">
                    {{nightProgress}}<span style="font-size: 14px; margin-left: 3px">cm</span>
                  </div>
                </div>
              </div>

              <!-- 当日和总进尺 -->
              <div style="
              display: flex;
              gap: 15px;
            ">
                <!-- 当日进尺 -->
                <div style="
                flex: 1;
                background: rgba(0, 60, 160, 0.3);
                border-radius: 4px;
                padding: 15px;
              ">
                  <div style="
                  display: flex;
                  align-items: center;
                  gap: 5px;
                  margin-bottom: 10px;
                ">
                    <i style="color: #7ce7fd">📊</i>
                    <span style="color: #7ce7fd; font-size: 22px">当日进尺</span>
                  </div>
                  <div style="
                  color: #ffeb3b;
                  font-size: 28px;
                  font-family: DIGITALDREAMFAT;
                  text-align: center;
                ">
                    {{todayTotalProgress}}<span style="font-size: 16px; margin-left: 5px">cm</span>
                  </div>
                </div>

                <!-- 总进尺 -->
                <div style="
                flex: 1;
                background: rgba(0, 60, 160, 0.3);
                border-radius: 4px;
                padding: 15px;
              ">
                  <div style="
                  display: flex;
                  align-items: center;
                  gap: 5px;
                  margin-bottom: 10px;
                ">
                    <i style="color: #7ce7fd">📈</i>
                    <span style="color: #7ce7fd; font-size: 22px">总进尺</span>
                  </div>
                  <div style="
                  color: #ffeb3b;
                  font-size: 28px;
                  font-family: DIGITALDREAMFAT;
                  text-align: center;
                ">
                    {{totalProgress}}<span style="font-size: 16px; margin-left: 5px">cm</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </div>
      </li>
      <li
              id="6eb4e86e-c4bd-45a0-be4b-c2a92a02d828chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 389px;
              height: 57px;
              transform: translate3d(34px, 96px, 0px);
              z-index: 1007;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="6eb4e86e-c4bd-45a0-be4b-c2a92a02d828"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="6eb4e86e-c4bd-45a0-be4b-c2a92a02d828"
                    style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  "
            >
              <span>| 人员定位数据</span>
            </p>
          </div>
        </div>
      </li>

      <li
              id="6eb4e86e-c4bd-45a0-be4b-c2a92a02d828chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 389px;
              height: 57px;
              transform: translate3d(34px, 137px, 0px);
              z-index: 1007;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="6eb4e86e-c4bd-45a0-be4b-c2a92a02d828"
                  class="main animate__animated animate__slideInLeft"
          >
            <div
                    style="
                    width: 100%;
                    height: 1px;
                    background: rgba(255, 255, 255, 0.3);
                    margin: 10px 0;
                  "
            ></div>
          </div>
        </div>
      </li>

      <li
              id="6eb4e86e-c4bd-45a0-be4b-c2a92a02d828chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 427px;

              transform: translate3d(34px, 147px, 0px);
              z-index: 1007;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="6eb4e86e-c4bd-45a0-be4b-c2a92a02d828"
                  class="main animate__animated animate__slideInLeft"
          >
            <!-- 人员定位面板 -->
            <div
                    style="
                    display: flex;
                    padding: 15px;
                    height: 100%;
                    color: #fff;
                  "
            >
              <!-- 左侧:人员图片和数量 -->
              <!-- 左侧:人员图片和数量 -->
              <div
                      style="
                      flex: 1;
                      display: flex;
                      flex-direction: column;
                      align-items: center;
                    "
              >
                <!-- 图片容器,使用relative定位以便放置当前人数 -->
                <div style="position: relative; margin-bottom: 15px">
                  <img
                          src="static/ren.png"
                          style="width: 120px; height: 161px; margin-left: -22px"
                  />

                  <!-- 当前人数显示,使用absolute定位到右上角 -->
                  <div
                          style="
                          position: absolute;
                          top: -10px;
                          right: -20px;
                          text-align: center;
                        "
                  >
                    <div
                            style="
                            color: #7ce7fd;
                            font-size: 15px;
                            margin-bottom: 5px;
                          "
                    >
                      当前人数
                    </div>
                    <div
                            style="
                            background: rgba(0, 0, 0, 0.3);
                            border: 1px solid #7ce7fd;
                            padding: 2px 15px;
                            border-radius: 4px;
                            display: flex;
                            align-items: center;
                          "
                    >
                          <span
                                  style="
                              color: #7ce7fd;
                              font-size: 17px;
                              font-family: DIGITALDREAMFAT;
                            "
                          >{{currentPersonnel}}</span
                          >
                    </div>
                  </div>
                </div>

                <!-- 定员人数显示,位于图片下方 -->
                <div style="color: #7ce7fd; font-size: 16px">
                  定员人数:
                  <span style="color: #fff">{{totalPersonnel}}</span>
                </div>
              </div>

              <!-- 分隔线 -->
              <div
                      style="
                      width: 2px;
                      background: rgba(124, 231, 253, 0.3);
                      margin: 0 20px;
                      height: 200px;
                    "
              ></div>
              <div style="flex: 3">
                <!-- 表头 -->
                <div
                        style="
                        display: flex;
                        padding: 8px 0;
                        background: rgba(124, 231, 253, 0.1);
                        margin-bottom: 10px;
                      "
                >
                  <div
                          style="
                          flex: 1;
                          text-align: center;
                          color: #7ce7fd;
                          font-weight: bold;
                        "
                  >
                    人员
                  </div>
                  <div
                          style="
                          width: 1px;
                          background: rgba(124, 231, 253, 0.3);
                          margin: 0 15px;
                        "
                  ></div>
                  <div
                          style="
                          flex: 1;
                          text-align: center;
                          color: #7ce7fd;
                          font-weight: bold;
                        "
                  >
                    位置
                  </div>
                </div>

                <!-- 滚动列表容器 -->
                <div
                        class="scroll-list"
                        style="height: 143px; overflow: hidden"
                >
                  <div class="scroll-content">
                    <!-- 列表项会被克隆并循环滚动 -->
                    <div
                            v-for="item in personnelList"
                            :key="item.id"
                            style="margin-bottom: 10px"
                    >
                      <div style="display: flex; padding: 8px 0">
                        <div style="flex: 1; text-align: center">
                          {{item.name}}
                        </div>
                        <div
                                style="
                                width: 1px;
                                background: rgba(124, 231, 253, 0.3);
                                margin: 0 15px;
                              "
                        ></div>
                        <div style="flex: 1; text-align: center">
                          {{item.location}}
                        </div>
                      </div>
                      <div
                              style="
                              height: 1px;
                              background: rgba(124, 231, 253, 0.3);
                            "
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </li>




      <li
              id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74achart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 1452px;
              height: 66px;
              transform: translate3d(1218px, 976px, 0px);
              z-index: 2000;
              background-color: transparent;
            "

              v-if="isShowLeft"
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74a"
                  class="main animate__animated animate__slideInLeft"
          >
            <img src="static/gif_02_left.gif" style="    width: 12%;
    height: 73px ;">
          </div>
        </div>
      </li>


      <li
              id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74achart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 41%;
    height: 93px;
    transform: translate3d(34px, 910px, 0px);
              z-index: 2000;
              background-color: transparent;
            "
              v-if="isShowRight"
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74a"
                  class="main animate__animated animate__slideInLeft"
          >
            <img src="static/gif_02_right.gif" style="       width: 99%;
    height: 96px;
       ;">
          </div>
        </div>
      </li>

      <!--      <li-->
      <!--              id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74achart-item"-->
      <!--              class="chart-item"-->
      <!--              style="-->
      <!--              zoom: 0.997396;-->
      <!--              will-change: auto;-->
      <!--              width: 1452px;-->
      <!--              height: 188px;-->
      <!--              transform: translate3d(11px, 891px, 0px);-->
      <!--              z-index: 2;-->
      <!--              background-color: transparent;-->
      <!--            "-->
      <!--      >-->
      <!--        <div-->
      <!--                class="chart-box animate__animated background selfAnimate_none"-->
      <!--                style="background: url('') no-repeat; transform: rotate(0deg)"-->
      <!--        >-->
      <!--          <div-->
      <!--                  id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74a"-->
      <!--                  class="main animate__animated animate__slideInLeft"-->
      <!--          >-->
      <!--            <div full="" id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74a">-->
      <!--              <div class="dv-border-box-12">-->
      <!--                <svg-->
      <!--                        width="1452"-->
      <!--                        height="188"-->
      <!--                        class="dv-border-svg-container"-->
      <!--                >-->
      <!--                  <defs>-->
      <!--                    <filter-->
      <!--                            id="borderr-box-12-filterId-faebe3e7-94c3-424a-9f72-d435dd51ce44"-->
      <!--                            height="150%"-->
      <!--                            width="150%"-->
      <!--                            x="-25%"-->
      <!--                            y="-25%"-->
      <!--                    >-->
      <!--                      <feMorphology-->
      <!--                              operator="dilate"-->
      <!--                              radius="1"-->
      <!--                              in="SourceAlpha"-->
      <!--                              result="thicken"-->
      <!--                      ></feMorphology>-->
      <!--                      <feGaussianBlur-->
      <!--                              in="thicken"-->
      <!--                              stdDeviation="2"-->
      <!--                              result="blurred"-->
      <!--                      ></feGaussianBlur>-->
      <!--                      <feFlood-->
      <!--                              flood-color="rgba(124,231,253,0.7)"-->
      <!--                              result="glowColor"-->
      <!--                      >-->
      <!--                        <animate-->
      <!--                                attributeName="flood-color"-->
      <!--                                values="-->
      <!--            rgba(124,231,253,0.7);-->
      <!--            rgba(124,231,253,0.3);-->
      <!--            rgba(124,231,253,0.7);-->
      <!--          "-->
      <!--                                dur="3s"-->
      <!--                                begin="0s"-->
      <!--                                repeatCount="indefinite"-->
      <!--                        ></animate>-->
      <!--                      </feFlood>-->
      <!--                      <feComposite-->
      <!--                              in="glowColor"-->
      <!--                              in2="blurred"-->
      <!--                              operator="in"-->
      <!--                              result="softGlowColored"-->
      <!--                      ></feComposite>-->
      <!--                      <feMerge>-->
      <!--                        <feMergeNode in="softGlowColored"></feMergeNode>-->
      <!--                        <feMergeNode in="SourceGraphic"></feMergeNode>-->
      <!--                      </feMerge>-->
      <!--                    </filter>-->
      <!--                  </defs>-->
      <!--                  <path-->
      <!--                          fill="transparent"-->
      <!--                          stroke-width="2"-->
      <!--                          stroke="#2e6099"-->
      <!--                          d="-->
      <!--      M15 5 L 1437 5 Q 1447 5, 1447 15-->
      <!--      L 1447 173 Q 1447 183, 1437 183-->
      <!--      L 15, 183 Q 5 183 5 173 L 5 15-->
      <!--      Q 5 5 15 5-->
      <!--    "-->
      <!--                  ></path>-->
      <!--                  <path-->
      <!--                          stroke-width="2"-->
      <!--                          fill="transparent"-->
      <!--                          stroke-linecap="round"-->
      <!--                          filter="url(#borderr-box-12-filterId-faebe3e7-94c3-424a-9f72-d435dd51ce44)"-->
      <!--                          d="M 20 5 L 15 5 Q 5 5 5 15 L 5 20"-->
      <!--                          stroke="#7ce7fd"-->
      <!--                  ></path>-->
      <!--                  <path-->
      <!--                          stroke-width="2"-->
      <!--                          fill="transparent"-->
      <!--                          stroke-linecap="round"-->
      <!--                          filter="url(#borderr-box-12-filterId-faebe3e7-94c3-424a-9f72-d435dd51ce44)"-->
      <!--                          d="M 1432 5 L 1437 5 Q 1447 5 1447 15 L 1447 20"-->
      <!--                          stroke="#7ce7fd"-->
      <!--                  ></path>-->
      <!--                  <path-->
      <!--                          stroke-width="2"-->
      <!--                          fill="transparent"-->
      <!--                          stroke-linecap="round"-->
      <!--                          filter="url(#borderr-box-12-filterId-faebe3e7-94c3-424a-9f72-d435dd51ce44)"-->
      <!--                          d="-->
      <!--      M 1432 183 L 1437 183-->
      <!--      Q 1447 183 1447 173-->
      <!--      L 1447 168-->
      <!--    "-->
      <!--                          stroke="#7ce7fd"-->
      <!--                  ></path>-->
      <!--                  <path-->
      <!--                          stroke-width="2"-->
      <!--                          fill="transparent"-->
      <!--                          stroke-linecap="round"-->
      <!--                          filter="url(#borderr-box-12-filterId-faebe3e7-94c3-424a-9f72-d435dd51ce44)"-->
      <!--                          d="-->
      <!--      M 20 183 L 15 183-->
      <!--      Q 5 183 5 173-->
      <!--      L 5 168-->
      <!--    "-->
      <!--                          stroke="#7ce7fd"-->
      <!--                  ></path>-->
      <!--                </svg>-->
      <!--                <div class="border-box-content"></div>-->
      <!--              </div>-->
      <!--              <resize-observer></resize-observer>-->
      <!--            </div>-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </li>-->
      <li
              id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74achart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 1452px;
    height: 234px;
    transform: translate3d(9px, 836px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74a"
                  class="main animate__animated animate__slideInLeft"
          >
            <div full="" id="eb68c4d4-2375-4bf5-8c03-4603ea4fb74a">
              <img src="static/biankuang.png" style="width: 100%;height: 237px;"/>
            </div>
          </div>
      </li>
      <li
              id="199e4a4f-0740-40b1-8615-84c74d27ba2dchart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 418px;
              height: 238px;
              transform: translate3d(1469px, 835px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="199e4a4f-0740-40b1-8615-84c74d27ba2d"
                  class="main animate__animated animate__slideInLeft"
          >
            <div full="" id="199e4a4f-0740-40b1-8615-84c74d27ba2d">
              <div class="dv-border-box-12">
                <svg
                        width="418"
                        height="238"
                        class="dv-border-svg-container"
                >
                  <defs>
                    <filter
                            id="borderr-box-12-filterId-99bf85dd-9d0c-4247-b97c-7b192d069dbb"
                            height="150%"
                            width="150%"
                            x="-25%"
                            y="-25%"
                    >
                      <feMorphology
                              operator="dilate"
                              radius="1"
                              in="SourceAlpha"
                              result="thicken"
                      ></feMorphology>
                      <feGaussianBlur
                              in="thicken"
                              stdDeviation="2"
                              result="blurred"
                      ></feGaussianBlur>
                      <feFlood
                              flood-color="rgba(124,231,253,0.7)"
                              result="glowColor"
                      >
                        <animate
                                attributeName="flood-color"
                                values="
            rgba(124,231,253,0.7);
            rgba(124,231,253,0.3);
            rgba(124,231,253,0.7);
          "
                                dur="3s"
                                begin="0s"
                                repeatCount="indefinite"
                        ></animate>
                      </feFlood>
                      <feComposite
                              in="glowColor"
                              in2="blurred"
                              operator="in"
                              result="softGlowColored"
                      ></feComposite>
                      <feMerge>
                        <feMergeNode in="softGlowColored"></feMergeNode>
                        <feMergeNode in="SourceGraphic"></feMergeNode>
                      </feMerge>
                    </filter>
                  </defs>
                  <path
                          fill="transparent"
                          stroke-width="2"
                          stroke="#2e6099"
                          d="
      M15 5 L 403 5 Q 413 5, 413 15
      L 413 223 Q 413 233, 403 233
      L 15, 233 Q 5 233 5 223 L 5 15
      Q 5 5 15 5
    "
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-99bf85dd-9d0c-4247-b97c-7b192d069dbb)"
                          d="M 20 5 L 15 5 Q 5 5 5 15 L 5 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-99bf85dd-9d0c-4247-b97c-7b192d069dbb)"
                          d="M 398 5 L 403 5 Q 413 5 413 15 L 413 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-99bf85dd-9d0c-4247-b97c-7b192d069dbb)"
                          d="
      M 398 233 L 403 233
      Q 413 233 413 223
      L 413 218
    "
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-99bf85dd-9d0c-4247-b97c-7b192d069dbb)"
                          d="
      M 20 233 L 15 233
      Q 5 233 5 223
      L 5 218
    "
                          stroke="#7ce7fd"
                  ></path>
                </svg>
                <div class="border-box-content"></div>
              </div>
              <resize-observer></resize-observer>
            </div>
          </div>
        </div>
      </li>
      <li
              id="3a117ee0-5f4e-4753-a0c6-84c7a7b34845chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 418px;
              height: 251px;
              transform: translate3d(1469px, 578px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="3a117ee0-5f4e-4753-a0c6-84c7a7b34845"
                  class="main animate__animated animate__slideInLeft"
          >
            <div full="" id="3a117ee0-5f4e-4753-a0c6-84c7a7b34845">
              <div class="dv-border-box-12">
                <svg
                        width="418"
                        height="251"
                        class="dv-border-svg-container"
                >
                  <defs>
                    <filter
                            id="borderr-box-12-filterId-f48d04da-7336-49ef-88c0-f7f7dc597cc9"
                            height="150%"
                            width="150%"
                            x="-25%"
                            y="-25%"
                    >
                      <feMorphology
                              operator="dilate"
                              radius="1"
                              in="SourceAlpha"
                              result="thicken"
                      ></feMorphology>
                      <feGaussianBlur
                              in="thicken"
                              stdDeviation="2"
                              result="blurred"
                      ></feGaussianBlur>
                      <feFlood
                              flood-color="rgba(124,231,253,0.7)"
                              result="glowColor"
                      >
                        <animate
                                attributeName="flood-color"
                                values="
            rgba(124,231,253,0.7);
            rgba(124,231,253,0.3);
            rgba(124,231,253,0.7);
          "
                                dur="3s"
                                begin="0s"
                                repeatCount="indefinite"
                        ></animate>
                      </feFlood>
                      <feComposite
                              in="glowColor"
                              in2="blurred"
                              operator="in"
                              result="softGlowColored"
                      ></feComposite>
                      <feMerge>
                        <feMergeNode in="softGlowColored"></feMergeNode>
                        <feMergeNode in="SourceGraphic"></feMergeNode>
                      </feMerge>
                    </filter>
                  </defs>
                  <path
                          fill="transparent"
                          stroke-width="2"
                          stroke="#2e6099"
                          d="
      M15 5 L 403 5 Q 413 5, 413 15
      L 413 236 Q 413 246, 403 246
      L 15, 246 Q 5 246 5 236 L 5 15
      Q 5 5 15 5
    "
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-f48d04da-7336-49ef-88c0-f7f7dc597cc9)"
                          d="M 20 5 L 15 5 Q 5 5 5 15 L 5 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-f48d04da-7336-49ef-88c0-f7f7dc597cc9)"
                          d="M 398 5 L 403 5 Q 413 5 413 15 L 413 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-f48d04da-7336-49ef-88c0-f7f7dc597cc9)"
                          d="
      M 398 246 L 403 246
      Q 413 246 413 236
      L 413 231
    "
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-f48d04da-7336-49ef-88c0-f7f7dc597cc9)"
                          d="
      M 20 246 L 15 246
      Q 5 246 5 236
      L 5 231
    "
                          stroke="#7ce7fd"
                  ></path>
                </svg>
                <div class="border-box-content"></div>
              </div>
              <resize-observer></resize-observer>
            </div>
          </div>
        </div>
      </li>
      <li
              id="ff4855bc-7350-4449-bcf9-753b016668cdchart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 418px;
              height: 233px;
              transform: translate3d(1470px, 340px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="ff4855bc-7350-4449-bcf9-753b016668cd"
                  class="main animate__animated animate__slideInLeft"
          >
            <div full="" id="ff4855bc-7350-4449-bcf9-753b016668cd">
              <div class="dv-border-box-12">
                <svg
                        width="418"
                        height="233"
                        class="dv-border-svg-container"
                >
                  <defs>
                    <filter
                            id="borderr-box-12-filterId-160d16e5-c5ef-4dde-beb2-1d37579468f4"
                            height="150%"
                            width="150%"
                            x="-25%"
                            y="-25%"
                    >
                      <feMorphology
                              operator="dilate"
                              radius="1"
                              in="SourceAlpha"
                              result="thicken"
                      ></feMorphology>
                      <feGaussianBlur
                              in="thicken"
                              stdDeviation="2"
                              result="blurred"
                      ></feGaussianBlur>
                      <feFlood
                              flood-color="rgba(124,231,253,0.7)"
                              result="glowColor"
                      >
                        <animate
                                attributeName="flood-color"
                                values="
            rgba(124,231,253,0.7);
            rgba(124,231,253,0.3);
            rgba(124,231,253,0.7);
          "
                                dur="3s"
                                begin="0s"
                                repeatCount="indefinite"
                        ></animate>
                      </feFlood>
                      <feComposite
                              in="glowColor"
                              in2="blurred"
                              operator="in"
                              result="softGlowColored"
                      ></feComposite>
                      <feMerge>
                        <feMergeNode in="softGlowColored"></feMergeNode>
                        <feMergeNode in="SourceGraphic"></feMergeNode>
                      </feMerge>
                    </filter>
                  </defs>
                  <path
                          fill="transparent"
                          stroke-width="2"
                          stroke="#2e6099"
                          d="
      M15 5 L 403 5 Q 413 5, 413 15
      L 413 218 Q 413 228, 403 228
      L 15, 228 Q 5 228 5 218 L 5 15
      Q 5 5 15 5
    "
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-160d16e5-c5ef-4dde-beb2-1d37579468f4)"
                          d="M 20 5 L 15 5 Q 5 5 5 15 L 5 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-160d16e5-c5ef-4dde-beb2-1d37579468f4)"
                          d="M 398 5 L 403 5 Q 413 5 413 15 L 413 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-160d16e5-c5ef-4dde-beb2-1d37579468f4)"
                          d="
      M 398 228 L 403 228
      Q 413 228 413 218
      L 413 213
    "
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-160d16e5-c5ef-4dde-beb2-1d37579468f4)"
                          d="
      M 20 228 L 15 228
      Q 5 228 5 218
      L 5 213
    "
                          stroke="#7ce7fd"
                  ></path>
                </svg>
                <div class="border-box-content"></div>
              </div>
              <resize-observer></resize-observer>
            </div>
          </div>
        </div>
      </li>

      <li
              id="11a2c74b-6653-49fd-bfe7-114888d5d84bchart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 1452px;
              height: 215px;
              transform: translate3d(11px, 388px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="11a2c74b-6653-49fd-bfe7-114888d5d84b"
                  class="main animate__animated animate__slideInLeft"
          >
            <div full="" id="11a2c74b-6653-49fd-bfe7-114888d5d84b">
              <div class="dv-border-box-12">
                <svg
                        width="1452"
                        height="215"
                        class="dv-border-svg-container"
                >
                  <defs>
                    <filter
                            id="borderr-box-12-filterId-037a36e8-854c-4e5c-a096-191f650c29dc"
                            height="150%"
                            width="150%"
                            x="-25%"
                            y="-25%"
                    >
                      <feMorphology
                              operator="dilate"
                              radius="1"
                              in="SourceAlpha"
                              result="thicken"
                      ></feMorphology>
                      <feGaussianBlur
                              in="thicken"
                              stdDeviation="2"
                              result="blurred"
                      ></feGaussianBlur>
                      <feFlood
                              flood-color="rgba(124,231,253,0.7)"
                              result="glowColor"
                      >
                        <animate
                                attributeName="flood-color"
                                values="
            rgba(124,231,253,0.7);
            rgba(124,231,253,0.3);
            rgba(124,231,253,0.7);
          "
                                dur="3s"
                                begin="0s"
                                repeatCount="indefinite"
                        ></animate>
                      </feFlood>
                      <feComposite
                              in="glowColor"
                              in2="blurred"
                              operator="in"
                              result="softGlowColored"
                      ></feComposite>
                      <feMerge>
                        <feMergeNode in="softGlowColored"></feMergeNode>
                        <feMergeNode in="SourceGraphic"></feMergeNode>
                      </feMerge>
                    </filter>
                  </defs>
                  <path
                          fill="transparent"
                          stroke-width="2"
                          stroke="#2e6099"
                          d="
      M15 5 L 1437 5 Q 1447 5, 1447 15
      L 1447 200 Q 1447 210, 1437 210
      L 15, 210 Q 5 210 5 200 L 5 15
      Q 5 5 15 5
    "
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-037a36e8-854c-4e5c-a096-191f650c29dc)"
                          d="M 20 5 L 15 5 Q 5 5 5 15 L 5 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-037a36e8-854c-4e5c-a096-191f650c29dc)"
                          d="M 1432 5 L 1437 5 Q 1447 5 1447 15 L 1447 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-037a36e8-854c-4e5c-a096-191f650c29dc)"
                          d="
      M 1432 210 L 1437 210
      Q 1447 210 1447 200
      L 1447 195
    "
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-037a36e8-854c-4e5c-a096-191f650c29dc)"
                          d="
      M 20 210 L 15 210
      Q 5 210 5 200
      L 5 195
    "
                          stroke="#7ce7fd"
                  ></path>
                </svg>
                <div class="border-box-content"></div>
              </div>
              <resize-observer></resize-observer>
            </div>
          </div>
        </div>
      </li>
      <li
              id="d6d9b04f-8c8e-491a-b539-acbc47f4428fchart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 418px;
              height: 220px;
              transform: translate3d(1469px, 114px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="d6d9b04f-8c8e-491a-b539-acbc47f4428f"
                  class="main animate__animated animate__slideInLeft"
          >
            <div full="" id="d6d9b04f-8c8e-491a-b539-acbc47f4428f">
              <div class="dv-border-box-12">
                <svg
                        width="418"
                        height="220"
                        class="dv-border-svg-container"
                >
                  <defs>
                    <filter
                            id="borderr-box-12-filterId-71d0c2ef-aa31-4baa-85b6-82696a3be93d"
                            height="150%"
                            width="150%"
                            x="-25%"
                            y="-25%"
                    >
                      <feMorphology
                              operator="dilate"
                              radius="1"
                              in="SourceAlpha"
                              result="thicken"
                      ></feMorphology>
                      <feGaussianBlur
                              in="thicken"
                              stdDeviation="2"
                              result="blurred"
                      ></feGaussianBlur>
                      <feFlood
                              flood-color="rgba(124,231,253,0.7)"
                              result="glowColor"
                      >
                        <animate
                                attributeName="flood-color"
                                values="
            rgba(124,231,253,0.7);
            rgba(124,231,253,0.3);
            rgba(124,231,253,0.7);
          "
                                dur="3s"
                                begin="0s"
                                repeatCount="indefinite"
                        ></animate>
                      </feFlood>
                      <feComposite
                              in="glowColor"
                              in2="blurred"
                              operator="in"
                              result="softGlowColored"
                      ></feComposite>
                      <feMerge>
                        <feMergeNode in="softGlowColored"></feMergeNode>
                        <feMergeNode in="SourceGraphic"></feMergeNode>
                      </feMerge>
                    </filter>
                  </defs>
                  <path
                          fill="transparent"
                          stroke-width="2"
                          stroke="#2e6099"
                          d="
      M15 5 L 403 5 Q 413 5, 413 15
      L 413 205 Q 413 215, 403 215
      L 15, 215 Q 5 215 5 205 L 5 15
      Q 5 5 15 5
    "
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-71d0c2ef-aa31-4baa-85b6-82696a3be93d)"
                          d="M 20 5 L 15 5 Q 5 5 5 15 L 5 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-71d0c2ef-aa31-4baa-85b6-82696a3be93d)"
                          d="M 398 5 L 403 5 Q 413 5 413 15 L 413 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-71d0c2ef-aa31-4baa-85b6-82696a3be93d)"
                          d="
      M 398 215 L 403 215
      Q 413 215 413 205
      L 413 200
    "
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-71d0c2ef-aa31-4baa-85b6-82696a3be93d)"
                          d="
      M 20 215 L 15 215
      Q 5 215 5 205
      L 5 200
    "
                          stroke="#7ce7fd"
                  ></path>
                </svg>
                <div class="border-box-content"></div>
              </div>
              <resize-observer></resize-observer>
            </div>
          </div>
        </div>
      </li>
      <li
              id="bba8d50c-e8da-4ffd-b881-09a21619f8aachart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 479px;
              height: 300px;
              transform: translate3d(980px, 93px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="bba8d50c-e8da-4ffd-b881-09a21619f8aa"
                  class="main animate__animated animate__slideInLeft"
          >
            <div full="" id="bba8d50c-e8da-4ffd-b881-09a21619f8aa">
              <div class="dv-border-box-12">
                <svg
                        width="479"
                        height="300"
                        class="dv-border-svg-container"
                >
                  <defs>
                    <filter
                            id="borderr-box-12-filterId-dbfd60ee-119b-4beb-9c76-f28552a4b4fd"
                            height="150%"
                            width="150%"
                            x="-25%"
                            y="-25%"
                    >
                      <feMorphology
                              operator="dilate"
                              radius="1"
                              in="SourceAlpha"
                              result="thicken"
                      ></feMorphology>
                      <feGaussianBlur
                              in="thicken"
                              stdDeviation="2"
                              result="blurred"
                      ></feGaussianBlur>
                      <feFlood
                              flood-color="rgba(124,231,253,0.7)"
                              result="glowColor"
                      >
                        <animate
                                attributeName="flood-color"
                                values="
            rgba(124,231,253,0.7);
            rgba(124,231,253,0.3);
            rgba(124,231,253,0.7);
          "
                                dur="3s"
                                begin="0s"
                                repeatCount="indefinite"
                        ></animate>
                      </feFlood>
                      <feComposite
                              in="glowColor"
                              in2="blurred"
                              operator="in"
                              result="softGlowColored"
                      ></feComposite>
                      <feMerge>
                        <feMergeNode in="softGlowColored"></feMergeNode>
                        <feMergeNode in="SourceGraphic"></feMergeNode>
                      </feMerge>
                    </filter>
                  </defs>
                  <path
                          fill="transparent"
                          stroke-width="2"
                          stroke="#2e6099"
                          d="
      M15 5 L 464 5 Q 474 5, 474 15
      L 474 285 Q 474 295, 464 295
      L 15, 295 Q 5 295 5 285 L 5 15
      Q 5 5 15 5
    "
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-dbfd60ee-119b-4beb-9c76-f28552a4b4fd)"
                          d="M 20 5 L 15 5 Q 5 5 5 15 L 5 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-dbfd60ee-119b-4beb-9c76-f28552a4b4fd)"
                          d="M 459 5 L 464 5 Q 474 5 474 15 L 474 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-dbfd60ee-119b-4beb-9c76-f28552a4b4fd)"
                          d="
      M 459 295 L 464 295
      Q 474 295 474 285
      L 474 280
    "
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-dbfd60ee-119b-4beb-9c76-f28552a4b4fd)"
                          d="
      M 20 295 L 15 295
      Q 5 295 5 285
      L 5 280
    "
                          stroke="#7ce7fd"
                  ></path>
                </svg>
                <div class="border-box-content"></div>
              </div>
              <resize-observer></resize-observer>
            </div>
          </div>
        </div>
      </li>
      <li
              id="1ee3cd2b-e1cf-49c1-aaf6-508d46e95854chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 460px;
              height: 300px;
              transform: translate3d(502px, 93px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="1ee3cd2b-e1cf-49c1-aaf6-508d46e95854"
                  class="main animate__animated animate__slideInLeft"
          >
            <div full="" id="1ee3cd2b-e1cf-49c1-aaf6-508d46e95854">
              <div class="dv-border-box-12">
                <svg
                        width="460"
                        height="300"
                        class="dv-border-svg-container"
                >
                  <defs>
                    <filter
                            id="borderr-box-12-filterId-b68af16d-fcf2-4f47-9e5d-56616c8a2e76"
                            height="150%"
                            width="150%"
                            x="-25%"
                            y="-25%"
                    >
                      <feMorphology
                              operator="dilate"
                              radius="1"
                              in="SourceAlpha"
                              result="thicken"
                      ></feMorphology>
                      <feGaussianBlur
                              in="thicken"
                              stdDeviation="2"
                              result="blurred"
                      ></feGaussianBlur>
                      <feFlood
                              flood-color="rgba(124,231,253,0.7)"
                              result="glowColor"
                      >
                        <animate
                                attributeName="flood-color"
                                values="
            rgba(124,231,253,0.7);
            rgba(124,231,253,0.3);
            rgba(124,231,253,0.7);
          "
                                dur="3s"
                                begin="0s"
                                repeatCount="indefinite"
                        ></animate>
                      </feFlood>
                      <feComposite
                              in="glowColor"
                              in2="blurred"
                              operator="in"
                              result="softGlowColored"
                      ></feComposite>
                      <feMerge>
                        <feMergeNode in="softGlowColored"></feMergeNode>
                        <feMergeNode in="SourceGraphic"></feMergeNode>
                      </feMerge>
                    </filter>
                  </defs>
                  <path
                          fill="transparent"
                          stroke-width="2"
                          stroke="#2e6099"
                          d="
      M15 5 L 445 5 Q 455 5, 455 15
      L 455 285 Q 455 295, 445 295
      L 15, 295 Q 5 295 5 285 L 5 15
      Q 5 5 15 5
    "
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-b68af16d-fcf2-4f47-9e5d-56616c8a2e76)"
                          d="M 20 5 L 15 5 Q 5 5 5 15 L 5 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-b68af16d-fcf2-4f47-9e5d-56616c8a2e76)"
                          d="M 440 5 L 445 5 Q 455 5 455 15 L 455 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-b68af16d-fcf2-4f47-9e5d-56616c8a2e76)"
                          d="
      M 440 295 L 445 295
      Q 455 295 455 285
      L 455 280
    "
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-b68af16d-fcf2-4f47-9e5d-56616c8a2e76)"
                          d="
      M 20 295 L 15 295
      Q 5 295 5 285
      L 5 280
    "
                          stroke="#7ce7fd"
                  ></path>
                </svg>
                <div class="border-box-content"></div>
              </div>
              <resize-observer></resize-observer>
            </div>
          </div>
        </div>
      </li>
      <li
              id="1318c137-ecd1-4914-a26a-84e274d9f38cchart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 470px;
              height: 300px;
              transform: translate3d(11px, 93px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="1318c137-ecd1-4914-a26a-84e274d9f38c"
                  class="main animate__animated animate__slideInLeft"
          >
            <div full="" id="1318c137-ecd1-4914-a26a-84e274d9f38c">
              <div class="dv-border-box-12">
                <svg
                        width="470"
                        height="300"
                        class="dv-border-svg-container"
                >
                  <defs>
                    <filter
                            id="borderr-box-12-filterId-f3752cfb-06fb-46f3-a31d-b0bfe99a7410"
                            height="150%"
                            width="150%"
                            x="-25%"
                            y="-25%"
                    >
                      <feMorphology
                              operator="dilate"
                              radius="1"
                              in="SourceAlpha"
                              result="thicken"
                      ></feMorphology>
                      <feGaussianBlur
                              in="thicken"
                              stdDeviation="2"
                              result="blurred"
                      ></feGaussianBlur>
                      <feFlood
                              flood-color="rgba(124,231,253,0.7)"
                              result="glowColor"
                      >
                        <animate
                                attributeName="flood-color"
                                values="
            rgba(124,231,253,0.7);
            rgba(124,231,253,0.3);
            rgba(124,231,253,0.7);
          "
                                dur="3s"
                                begin="0s"
                                repeatCount="indefinite"
                        ></animate>
                      </feFlood>
                      <feComposite
                              in="glowColor"
                              in2="blurred"
                              operator="in"
                              result="softGlowColored"
                      ></feComposite>
                      <feMerge>
                        <feMergeNode in="softGlowColored"></feMergeNode>
                        <feMergeNode in="SourceGraphic"></feMergeNode>
                      </feMerge>
                    </filter>
                  </defs>
                  <path
                          fill="transparent"
                          stroke-width="2"
                          stroke="#2e6099"
                          d="
      M15 5 L 455 5 Q 465 5, 465 15
      L 465 285 Q 465 295, 455 295
      L 15, 295 Q 5 295 5 285 L 5 15
      Q 5 5 15 5
    "
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-f3752cfb-06fb-46f3-a31d-b0bfe99a7410)"
                          d="M 20 5 L 15 5 Q 5 5 5 15 L 5 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-f3752cfb-06fb-46f3-a31d-b0bfe99a7410)"
                          d="M 450 5 L 455 5 Q 465 5 465 15 L 465 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-f3752cfb-06fb-46f3-a31d-b0bfe99a7410)"
                          d="
      M 450 295 L 455 295
      Q 465 295 465 285
      L 465 280
    "
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-f3752cfb-06fb-46f3-a31d-b0bfe99a7410)"
                          d="
      M 20 295 L 15 295
      Q 5 295 5 285
      L 5 280
    "
                          stroke="#7ce7fd"
                  ></path>
                </svg>
                <div class="border-box-content"></div>
              </div>
              <resize-observer></resize-observer>
            </div>
          </div>
        </div>
      </li>
      <li
              id="caf8eaee-5f33-45c4-bca8-62a9d0652b58chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 150px;
              height: 100px;
              transform: translate3d(1639px, 18px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="caf8eaee-5f33-45c4-bca8-62a9d0652b58"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="caf8eaee-5f33-45c4-bca8-62a9d0652b58"
                    style="
                    justify-content: center;
                    font-size: 20px;
                    color: rgb(255, 255, 255);
                    font-family: 'Microsoft YaHei';
                    font-style: normal;
                    font-weight: 500;
                  "
            >
              <span>用户：admin</span>
            </p>
          </div>
        </div>
      </li>
      <li
              id="12de53f8-adc6-4fbb-a194-938028a34633chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 203px;
              height: 68px;
              transform: translate3d(1422px, 34px, 0px);
              z-index: 1004;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="12de53f8-adc6-4fbb-a194-938028a34633"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="12de53f8-adc6-4fbb-a194-938028a34633"
                    style="
                    justify-content: center;
                    font-size: 20px;
                    color: rgb(255, 255, 255);
                    font-family: 'Microsoft YaHei';
                    font-style: normal;
                    font-weight: 500;
                  "
                    @click="showAnalysisDialog"
            >
              <span>智能分析</span>
            </p>
          </div>
        </div>
      </li>
      <li
              id="e541dbc9-1616-4df9-8c84-308e0e0b84a2chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 178px;
              height: 57px;
              transform: translate3d(1437px, 34px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="
                background: url('static/img4.png') no-repeat;
                transform: rotate(0deg);
              "
        >
          <div
                  id="e541dbc9-1616-4df9-8c84-308e0e0b84a2"
                  class="main animate__animated animate__slideInLeft"
          >
            <div
                    class="el-image full"
                    id="e541dbc9-1616-4df9-8c84-308e0e0b84a2"
            >
              <div class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
              <!---->
            </div>
          </div>
        </div>
      </li>
      <li
              id="39b45544-869f-4a2e-bd1b-a53b95e8d83achart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 150px;
              height: 30px;
              transform: translate3d(353px, 53px, 0px);
              z-index: 999;
              background-color: transparent;
            "
              @click="showHistoryDialog"
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="39b45544-869f-4a2e-bd1b-a53b95e8d83a"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="39b45544-869f-4a2e-bd1b-a53b95e8d83a"
                    style="
                    justify-content: center;
                    font-size: 20px;
                    color: rgb(255, 255, 255);
                    font-family: 'Microsoft YaHei';
                    font-style: normal;
                    font-weight: 500;
                  "
            >
              <span>历史查询</span>
            </p>
          </div>
        </div>
      </li>
      <li
              id="afaa652f-f955-48a2-a4fe-94c9120bf14cchart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 162px;
              height: 61px;
              transform: translate3d(353px, 34px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="
                background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKwAAAA+CAYAAAC2n/CQAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAABttJREFUeNrsnXlsFFUcx39vWgrdHtAUoSCFQku15dCQAIVAIkaihsMQY4whEhKVAClXIKKJB5qIMVFDFDQmqBCM/mNEQEPiFYRyKtCYiJRi6UGVQivQu7vdGX9v5s3udI/SSnGn2+8n+fbNe29mOzvznbfv2OxPGIZBAPQXNFwCAMMCcIdIjFYxq/iIvenGPsNeVjHrr57sfHz7XNxptLAxZQnrHGslS+A2wrBOhAu1VLX8H7IOswpwKwd4l8DlfM6SfZbdrHmsUtZW1pssb+jOmaevkiEEGfLx1GSqtkX4tnwczDKh9hXBMjMVXdMu2/wICTnrYnTdDqS6I8/bwllmplymR9jW1bZZ5sjrznxvykJfu/uyQN4IycttI3zbfj/W+4zy/lV6YtucATPoqmE9xNqkWtstrLOssCuQ/2kZ3fPJebp353k0UfHewvoHJyzj1mN3lxZGI9ViOVsfq6UK21YtWGB/LVgXzN+ybC+XFY/+sTZ0kCWf63dY37M+Y01h/cz6iPUi6yZu8QDqw+pJWoHqJ7pikFU7f8zKyw9nRxpk/caaznpX5VexzquuQhPrO9ZY3O44bmH1QZqHky9Znpa6BmoouxSTExSeJEqcP5m0wtFD1cOztObR7BXZB2v+CNm1g7WR9S1rFyublaXq5jv6uyBOW9gdrMKOm03UcKEyZidotHrJt+8M+fafJeNqI6k+amn1grGvVi8alxThkJ9YU1mdIeWzcbvj1LBF60uWcbLc0HVqKK80R3+xRv+9lnw7D5N+tlpmk+xBVtVjOZGGmTdYh0LKjuF2x6Fhi9aVBPqt/1TUUGdbh3vOlh8c/zel5N9znIzKellSKAdZlUvGf3Dp8fFDK56Y4Nz7GdYPrBaVLsftjrM+bNHaEg+P+s1+a/PVBmq+Uu/KkzYqrpnSFt9PNHO8pgZZi1lryFq2lVSXPz1xAWkixVDzqYZGGY6XGcJKjvIvUlQrHom0bvr+Q7vpZmVEKU9gpUepG8RKjVI3WN6nKHXJ6v1FIlW9biTS1flEYhhFXlUUqi6av9Ic+dW3O4hPjNRvbW9spoaLVa5/2vT9pUQnK0g8MpnPOutuLvqq4snc4PQYcBvvs2pZ+/vKsObHpmnW/vI92bpGMnZz93TuRKIpo4ly7wrW+XWiDl21edb8sZzjFdLMfj+RTzfzpFa6hL2K5e20jhVqxcu5mtUh64wuC8UGWXWixUvCr66bvaKjujKiyRvIC8MqM/fh/yNafeorRtbql1xJMvfr5Lo2X+DrR8FVNN5Pnjufi1DHkaFuGf8xfPze+FhzP7XqZp8Hea3zFxQ8B/uczPetG4HXs5tPM+vrdHwNynC8pqqL0oXTO/3Wx0t+DqVlDZet9xesB1kn+2xay9fe0f+e3SPlJFhEA/vbMMYt8rHi+oVKbhsEpYzIlN2YA2rW5mJfTmsB0KfIufzW+utyU34MHlQpDAvcbdr2G+Z8ep5saYvWlXhgWODeLouuU31ZJXmbW2V2puzTFq0tSYBhgWvx+3x0rayC/F6fzC5WswcwLHAvnTywrzsXGHOtgmGB6/G2tvX6GBgW9CtgWADDAgDDAgDDAhgWABgWABgWwLAAwLAAwLAAhgUAhgUAhgUwLAAwLAAwLIBhAYBhAYBhAQwLAAwLQEQScQmAW3CEiw3DDr+KFhb0/y7ByMI8GjIsDVcHxKp1lSGqZKiqsJBVoYaVv3NUkTwsnUZOyqfM/BzSEhNwNUGfk5TqoZEFedGqPyYrVFUqBUNWhRv2xHtz5A/NTma9xmpLHZFJo6ZNovTsLFxh0Gdk5o2jUfcVUHKGGdrhMlmBA21kvIQHQg4JhKwSRpSfhp+54WguCdpmCLFQ/qR1280maqr+m9qvN+KKg/+EZ3gGpY/JosHcupJBfjbfdk5fFn6jSe0iA//tovColYd40DWvW8PazNh4bJFpXE1MkMZtudJAN8qrAr9dD8AtB0qDEilj4jhKYcOq2A+nOV2p+fRf1S4yIs4brA3qU79OKVdOELCeZcNW9ciwpmk3HUtmw25mwz7PLW5yDIIjO8pCX7v7skAe4efdEH6+mdOXOL9D8+p2JA8Z0FoGtp5qXTUzsPULFBLY2p7W6tE87Km3Z8vfRdwyffPxPZxuYy1EuwF6yT7WmoQOf41j/LSetVW1sDKY9XOskkhGtenVwsEvb836k5NFzrJpr5zCrXAZYw+4PsZatuqryvBHXjXI36q2u+W2V7rOvD4DDnEZs/ZVuvn0niIrgKGMDHmUtYJ1rqcHY2k2vnFrdEDZP12t+qt6bw6EYcH/zdesYrJCePaaHs0SAOCaKTJcAgDDAnCH+FeAAQAsEkQsm37GdQAAAABJRU5ErkJggg==')
                  no-repeat;
                transform: rotate(0deg);
              "
        >
          <div
                  id="afaa652f-f955-48a2-a4fe-94c9120bf14c"
                  class="main animate__animated animate__slideInLeft"
          >
            <div
                    class="el-image full"
                    id="afaa652f-f955-48a2-a4fe-94c9120bf14c"
            >
              <div class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
              <!---->
            </div>
          </div>
        </div>
      </li>
      <li
              id="cac3936f-f947-48b6-a1ff-47536d0a57d3chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 237px;
              height: 64px;
              transform: translate3d(115px, 38px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="cac3936f-f947-48b6-a1ff-47536d0a57d3"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="cac3936f-f947-48b6-a1ff-47536d0a57d3"
                    style="
                    justify-content: center;
                    font-size: 20px;
                    color: rgb(255, 255, 255);
                    font-family: 'Microsoft YaHei';
                    font-style: normal;
                    font-weight: 500;
                  "
            >
              <span>{{time}}</span>
            </p>
          </div>
        </div>
      </li>
      <li
              id="6f74bd98-c73d-495e-ac74-56be6e1e6af1chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 122px;
              height: 58px;
              transform: translate3d(0px, 42px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="6f74bd98-c73d-495e-ac74-56be6e1e6af1"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="6f74bd98-c73d-495e-ac74-56be6e1e6af1"
                    style="
                    justify-content: center;
                    font-size: 20px;
                    color: rgb(29, 246, 251);
                    font-family: 'Microsoft YaHei';
                    font-style: normal;
                    font-weight: 500;
                  "
                    v-if="isStatus"
            >
              <span>通讯正常</span>
            </p>
            <p
                    class="baseCss"
                    id="6f74bd98-c73d-495e-ac74-56be6e1e6af1"
                    style="
                    justify-content: center;
                    font-size: 20px;
                    color: rgb(241,0,0);
                    font-family: 'Microsoft YaHei';
                    font-style: normal;
                    font-weight: 500;
                  "
                    v-if="!isStatus"
            >
              <span>通讯异常</span>
            </p>
          </div>
        </div>
      </li>
      <li
              id="ddb15622-a0cc-4b99-ab8d-9c874c253190chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 778px;
              height: 56px;
              transform: translate3d(541px, 0px, 0px);
              z-index: 1006;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="ddb15622-a0cc-4b99-ab8d-9c874c253190"
                  class="main animate__animated animate__slideInLeft"
                  style="display: flex"
          >
            <!-- 添加logo图片 -->
            <img
                    src="static/logo_03.png"
                    style="
                    width: 161px;
                    margin-left: 70px;
                    margin-top: -7px;
                  "
            />

            <!-- 添加分隔竖线 -->
            <span style="color: #fff; font-size: 30px; margin: 0 5px"
            >|</span
            >

            <!-- 原有标题文字 -->
            <p
                    class="baseCss"
                    id="ddb15622-a0cc-4b99-ab8d-9c874c253190"
                    style="
                    justify-content: center;
                    font-size: 32px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 200;
                    margin: 0;
                  "
            >
              <span>潘二矿18425智能化掘进工作面</span>
            </p>
          </div>
        </div>
      </li>
      <li
              id="b9c531fd-307a-4622-be11-22e6d27e2489chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 660px;
              height: 55px;
              transform: translate3d(1256px, 0px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="
                background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAmwAAABBCAYAAABsOPjkAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAkJJREFUeNrs3T1KA0EYgOFdSWEhahewE6+QVk9irb1dTuAZ3BPoNbT1CmIn2ClYiIjjLiQQMfE3yX6TfR4YBJNmByKvs5OdMqVUAAAQ15opAAAQbAAACDYAAMEGAIBgAwBAsAEACDYAAAQbAIBgAwBAsAEAINgAAAQbAABL0fvqxX5VfDgZ/vn2ujJlZOqyHhf1eP3ujY+nA7MFQChW2OiKg3qc1GPbVAAg2CCuvXoMRz8BQLBBUM0KW7PStm8qABBsEFezd/NwNHqmAwDBBnE1q2z2tQEQntUFum68r635BvRN84ut4bVZASAUK2xgXxsAwZUppZkvTnkOW2nKaMmZKeAPnuqxWY83UwHk7Fe3RNd3B8X9kUljudyi5B82Jv7OvZgOoBPBBpChJtQeiol9igCCDSCe8T7F83pcTXuDI8mAyHzpAOjSP6ievwcINoAMeP4eINgAMuBcWUCwAWTA8/eAbNjHQS6OTQEAgg2C8u09AATbHPSr9i9kEQ/0dV15XRcArCp72AAAgpv3LdHU0nUs+ozTttaDFr0WttPSdd356AHAz1lhAwAQbAAACDYAAMEGAIBgAwBAsAEACDYAAAQbAIBgAwAglHmfdFCu6DxVK3pdThwAgAyUKc0+TapffTpqqnRoNwDAcrklCgAg2AAAEGwAAIINAADBBgCAYAMAEGwAAAg2AADBBgCAYAMAQLABAAg2AAAEGwAAgg0AQLABACDYAAAEGwAAgg0AgFnKlJJZAAAIzAobAEBw7wIMABehQezClmtoAAAAAElFTkSuQmCC')
                  no-repeat;
                transform: rotate(0deg);
              "
        >
          <div
                  id="b9c531fd-307a-4622-be11-22e6d27e2489"
                  class="main animate__animated animate__slideInLeft"
          >
            <div
                    class="el-image full"
                    id="b9c531fd-307a-4622-be11-22e6d27e2489"
            >
              <div class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
              <!---->
            </div>
          </div>
        </div>
      </li>
      <li
              id="e57e6d8e-6dee-4cfc-9440-5a95e6bd56dbchart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 610px;
              height: 52px;
              transform: translate3d(0px, 0px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="
                background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAnsAAABBCAYAAACpSmJ1AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAhdJREFUeNrs3U1Kw1AUgNGmBHEg6KzgTN1Cp7oNXUG6BlfgGux2nLqOQoa6gdeowerEmkASct85EETw712o+RpfY5FSWgAAENPSCAAAxB4AAGIPAACxBwCA2AMAQOwBACD2AADEHgAAYg8AALEHAIDYAwBA7AEA0FFpBABEdf742uV8eN8cd6bGHJ1erauf79fVohB7ANA2YXNsmuPGKIhI7AGQs+s29C6MArEHALHcNseDcyFiDwDinfvsz0PsAUBA9ueRX+x1eKUSAMyZ/XnkGXtGAEAG7M8j69h7NgaAbx83m39vjjOjoIeNETCR6q/YA+DLSftW6NHb29PaEBjNanv8Y0rPQgA+2c8FhFR6BgLk4MiL0eznAuLGnhEAmf8OdL81QOwBBOR+a4DYAwjK/jxA7AEEZX8eIPYAAntpDwCxBxCFOw8AYg8AGM1/boY7tLqyrhzWtfRwAwCIy5U9AJjW5UTfdzfw168mWtfQ1+DSROsq+n6iK3sAAIGJPQAAsQcAgNgDAEDsAQAg9gAAEHsAAIg9AACxBwBAJP6DBgBMaxd0Xdug6ypm9wOnlDzMAABmaHVI6l9BV1eHKPVnXACAwMQeAIDYAwBA7AEAIPYAABB7AACIPQAAxB4AgNgDAEDsAQAg9gAAEHsAAIg9AADEHgAAYg8AQOwBACD2AAAQewAAjKxIKZkCAEBQruwBAAS2F2AAKUUvqfIn+64AAAAASUVORK5CYII=')
                  no-repeat;
                transform: rotate(0deg);
              "
        >
          <div
                  id="e57e6d8e-6dee-4cfc-9440-5a95e6bd56db"
                  class="main animate__animated animate__slideInLeft"
          >
            <div
                    class="el-image full"
                    id="e57e6d8e-6dee-4cfc-9440-5a95e6bd56db"
            >
              <div class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
              <!---->
            </div>
          </div>
        </div>
      </li>

      <!-- 底部 -->
      <li
              id="eee905c7-bdc3-4ec8-add2-74bc16fe1775chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 172px;
              height: 54px;
              transform: translate3d(569px, 597px, 0px);
              z-index: 1013;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="eee905c7-bdc3-4ec8-add2-74bc16fe1775"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="eee905c7-bdc3-4ec8-add2-74bc16fe1775"
                    style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  "
            >
              <span>| 掘锚一体机</span>
            </p>
          </div>
        </div>
      </li>

      <li
              id="eee905c7-bdc3-4ec8-add2-74bc16fe1775chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 328px;
              top: 12px;
              transform: translate3d(569px, 617px, 0px);
              z-index: 1013;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="eee905c7-bdc3-4ec8-add2-74bc16fe1775"
                  class="main animate__animated animate__slideInLeft"
          >
            <div
                    class="data-panel"
                    style="
                    padding: 15px 0px;
                    display: flex;
                    justify-content: space-between;
                  "
            >
              <!-- 左侧数据列 -->
              <div class="left-column" style="width: 53%">
                <div v-for="item in leftColumnConfig"
                     :key="item.code"
                     class="data-row"
                     style="display: flex; justify-content: space-between; color: #ffffff; font-size: 17px;"
                     :style="{ marginBottom: item.isLast ? '0' : '10px' }">
                  <span>{{item.name}}</span>
                  <span style="color: #00ffff">{{monitorDataLeft[item.key] || 0}}{{item.unit}}</span>
                </div>
              </div>

              <!-- 右侧数据列 -->
              <!--              <div class="right-column" style="width: 68%; margin-left: 9px">-->
              <!--                <div v-for="item in rightColumnConfig"-->
              <!--                     :key="item.code"-->
              <!--                     class="data-row"-->
              <!--                     style="display: flex; justify-content: space-around; color: #ffffff; font-size: 16px;"-->
              <!--                     :style="{ marginBottom: item.isLast ? '0' : '10px' }">-->
              <!--                  <span>{{item.name}}{{item.showColon ? ':' : ''}}</span>-->
              <!--                  <span :style="{ color: getStatusColorAll(item, monitorDataRight[item.key]) }">-->
              <!--                        {{getStatusTextAll(item, monitorDataRight[item.key])}}-->
              <!--                      </span>-->
              <!--                </div>-->
              <!--              </div>-->
              <div class="right-column" style="width: 60%;">
                <div v-for="item in rightColumnConfig"
                     :key="item.code"
                     class="data-row"
                     style="display: flex; color: #ffffff; font-size: 17px;"
                     :style="{ marginBottom: item.isLast ? '0' : '10px' }">

                  <span style="width: 120px; text-align: right">{{item.name}}{{item.showColon ? ':' : ''}}</span>

                  <span style="margin-left: 10px;width: 70px" :style="{ color: getStatusColorAll(item, monitorDataRight[item.key]) }">
      {{getStatusTextAll(item, monitorDataRight[item.key])}}{{item.unit}}
    </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </li>

      <!--      <li-->
      <!--              id="ad2aeee7-b088-40df-a386-984be902e710chart-item"-->
      <!--              class="chart-item"-->
      <!--              style="-->
      <!--              zoom: 0.997396;-->
      <!--              will-change: auto;-->
      <!--              width: 307px;-->
      <!--              height: 245px;-->
      <!--              transform: translate3d(730px, 631px, 0px);-->
      <!--              z-index: 2;-->
      <!--              background-color: transparent;-->
      <!--            "-->
      <!--      >-->
      <!--        <div-->
      <!--                class="chart-box animate__animated background selfAnimate_none"-->
      <!--                style="background: url('') no-repeat; transform: rotate(0deg)"-->
      <!--        >-->
      <!--          <div-->
      <!--                  id="ad2aeee7-b088-40df-a386-984be902e710"-->
      <!--                  class="main animate__animated animate__slideInLeft"-->
      <!--          >-->
      <!--            <div full="" id="ad2aeee7-b088-40df-a386-984be902e710">-->
      <!--              <div class="dv-border-box-12">-->
      <!--                <svg-->
      <!--                        width="307"-->
      <!--                        height="245"-->
      <!--                        class="dv-border-svg-container"-->
      <!--                >-->
      <!--                  <defs>-->
      <!--                    <filter-->
      <!--                            id="borderr-box-12-filterId-7d16738e-111e-4d5d-8dc6-4f14a472e263"-->
      <!--                            height="150%"-->
      <!--                            width="150%"-->
      <!--                            x="-25%"-->
      <!--                            y="-25%"-->
      <!--                    >-->
      <!--                      <feMorphology-->
      <!--                              operator="dilate"-->
      <!--                              radius="1"-->
      <!--                              in="SourceAlpha"-->
      <!--                              result="thicken"-->
      <!--                      ></feMorphology>-->
      <!--                      <feGaussianBlur-->
      <!--                              in="thicken"-->
      <!--                              stdDeviation="2"-->
      <!--                              result="blurred"-->
      <!--                      ></feGaussianBlur>-->
      <!--                      <feFlood-->
      <!--                              flood-color="rgba(124,231,253,0.7)"-->
      <!--                              result="glowColor"-->
      <!--                      >-->
      <!--                        <animate-->
      <!--                                attributeName="flood-color"-->
      <!--                                values="-->
      <!--              rgba(124,231,253,0.7);-->
      <!--              rgba(124,231,253,0.3);-->
      <!--              rgba(124,231,253,0.7);-->
      <!--            "-->
      <!--                                dur="3s"-->
      <!--                                begin="0s"-->
      <!--                                repeatCount="indefinite"-->
      <!--                        ></animate>-->
      <!--                      </feFlood>-->
      <!--                      <feComposite-->
      <!--                              in="glowColor"-->
      <!--                              in2="blurred"-->
      <!--                              operator="in"-->
      <!--                              result="softGlowColored"-->
      <!--                      ></feComposite>-->
      <!--                      <feMerge>-->
      <!--                        <feMergeNode in="softGlowColored"></feMergeNode>-->
      <!--                        <feMergeNode in="SourceGraphic"></feMergeNode>-->
      <!--                      </feMerge>-->
      <!--                    </filter>-->
      <!--                  </defs>-->
      <!--                  <path-->
      <!--                          fill="transparent"-->
      <!--                          stroke-width="2"-->
      <!--                          stroke="#2e6099"-->
      <!--                          d="-->
      <!--        M15 5 L 292 5 Q 302 5, 302 15-->
      <!--        L 302 230 Q 302 240, 292 240-->
      <!--        L 15, 240 Q 5 240 5 230 L 5 15-->
      <!--        Q 5 5 15 5-->
      <!--      "-->
      <!--                  ></path>-->
      <!--                  <path-->
      <!--                          stroke-width="2"-->
      <!--                          fill="transparent"-->
      <!--                          stroke-linecap="round"-->
      <!--                          filter="url(#borderr-box-12-filterId-7d16738e-111e-4d5d-8dc6-4f14a472e263)"-->
      <!--                          d="M 20 5 L 15 5 Q 5 5 5 15 L 5 20"-->
      <!--                          stroke="#7ce7fd"-->
      <!--                  ></path>-->
      <!--                  <path-->
      <!--                          stroke-width="2"-->
      <!--                          fill="transparent"-->
      <!--                          stroke-linecap="round"-->
      <!--                          filter="url(#borderr-box-12-filterId-7d16738e-111e-4d5d-8dc6-4f14a472e263)"-->
      <!--                          d="M 287 5 L 292 5 Q 302 5 302 15 L 302 20"-->
      <!--                          stroke="#7ce7fd"-->
      <!--                  ></path>-->
      <!--                  <path-->
      <!--                          stroke-width="2"-->
      <!--                          fill="transparent"-->
      <!--                          stroke-linecap="round"-->
      <!--                          filter="url(#borderr-box-12-filterId-7d16738e-111e-4d5d-8dc6-4f14a472e263)"-->
      <!--                          d="-->
      <!--        M 287 240 L 292 240-->
      <!--        Q 302 240 302 230-->
      <!--        L 302 225-->
      <!--      "-->
      <!--                          stroke="#7ce7fd"-->
      <!--                  ></path>-->
      <!--                  <path-->
      <!--                          stroke-width="2"-->
      <!--                          fill="transparent"-->
      <!--                          stroke-linecap="round"-->
      <!--                          filter="url(#borderr-box-12-filterId-7d16738e-111e-4d5d-8dc6-4f14a472e263)"-->
      <!--                          d="-->
      <!--        M 20 240 L 15 240-->
      <!--        Q 5 240 5 230-->
      <!--        L 5 225-->
      <!--      "-->
      <!--                          stroke="#7ce7fd"-->
      <!--                  ></path>-->
      <!--                </svg>-->
      <!--                <div class="border-box-content"></div>-->
      <!--              </div>-->
      <!--              <resize-observer></resize-observer>-->
      <!--            </div>-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </li>-->


      <!--      <li id="ad2aeee7-b088-40df-a386-984be902e710chart-item" class="chart-item" style="zoom: 0.997396; will-change: auto; width: 339px; height: 245px; transform: translate3d(698px, 631px, 0px); z-index: 2; background-color: transparent;"><div class="chart-box animate__animated background selfAnimate_none" style="background: url(&quot;&quot;) no-repeat; transform: rotate(0deg);"><div id="ad2aeee7-b088-40df-a386-984be902e710" class="main animate__animated animate__slideInLeft"><div full="" id="ad2aeee7-b088-40df-a386-984be902e710"><div class="dv-border-box-12"><svg width="339" height="245" class="dv-border-svg-container"><defs><filter id="borderr-box-12-filterId-4c2148d1-a5e9-42f4-b85c-ddeadf8654b0" height="150%" width="150%" x="-25%" y="-25%"><feMorphology operator="dilate" radius="1" in="SourceAlpha" result="thicken"></feMorphology> <feGaussianBlur in="thicken" stdDeviation="2" result="blurred"></feGaussianBlur> <feFlood flood-color="rgba(124,231,253,0.7)" result="glowColor"><animate attributeName="flood-color" values="-->
      <!--              rgba(124,231,253,0.7);-->
      <!--              rgba(124,231,253,0.3);-->
      <!--              rgba(124,231,253,0.7);-->
      <!--            " dur="3s" begin="0s" repeatCount="indefinite"></animate></feFlood> <feComposite in="glowColor" in2="blurred" operator="in" result="softGlowColored"></feComposite> <feMerge><feMergeNode in="softGlowColored"></feMergeNode> <feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs> <path fill="transparent" stroke-width="2" stroke="#2e6099" d="-->
      <!--        M15 5 L 324 5 Q 334 5, 334 15-->
      <!--        L 334 230 Q 334 240, 324 240-->
      <!--        L 15, 240 Q 5 240 5 230 L 5 15-->
      <!--        Q 5 5 15 5-->
      <!--      "></path> <path stroke-width="2" fill="transparent" stroke-linecap="round" filter="url(#borderr-box-12-filterId-4c2148d1-a5e9-42f4-b85c-ddeadf8654b0)" d="M 20 5 L 15 5 Q 5 5 5 15 L 5 20" stroke="#7ce7fd"></path> <path stroke-width="2" fill="transparent" stroke-linecap="round" filter="url(#borderr-box-12-filterId-4c2148d1-a5e9-42f4-b85c-ddeadf8654b0)" d="M 319 5 L 324 5 Q 334 5 334 15 L 334 20" stroke="#7ce7fd"></path> <path stroke-width="2" fill="transparent" stroke-linecap="round" filter="url(#borderr-box-12-filterId-4c2148d1-a5e9-42f4-b85c-ddeadf8654b0)" d="-->
      <!--        M 319 240 L 324 240-->
      <!--        Q 334 240 334 230-->
      <!--        L 334 225-->
      <!--      " stroke="#7ce7fd"></path> <path stroke-width="2" fill="transparent" stroke-linecap="round" filter="url(#borderr-box-12-filterId-4c2148d1-a5e9-42f4-b85c-ddeadf8654b0)" d="-->
      <!--        M 20 240 L 15 240-->
      <!--        Q 5 240 5 230-->
      <!--        L 5 225-->
      <!--      " stroke="#7ce7fd"></path></svg> <div class="border-box-content"></div></div> <resize-observer></resize-observer></div></div></div></li>-->
      <li id="ad2aeee7-b088-40df-a386-984be902e710chart-item" class="chart-item" style="zoom: 0.997396; will-change: auto;
       width: 367px; height: 245px; transform: translate3d(549px, 597px, 0px); z-index: 2; background-color: transparent;"><div class="chart-box animate__animated background selfAnimate_none" style="background: url(&quot;&quot;) no-repeat; transform: rotate(0deg);"><div id="ad2aeee7-b088-40df-a386-984be902e710" class="main animate__animated animate__slideInLeft"><div full="" id="ad2aeee7-b088-40df-a386-984be902e710"><div class="dv-border-box-12"><svg width="367" height="245" class="dv-border-svg-container"><defs><filter id="borderr-box-12-filterId-397aa110-b94d-4d8c-acf9-d0980cf222e7" height="150%" width="150%" x="-25%" y="-25%"><feMorphology operator="dilate" radius="1" in="SourceAlpha" result="thicken"></feMorphology> <feGaussianBlur in="thicken" stdDeviation="2" result="blurred"></feGaussianBlur> <feFlood flood-color="rgba(124,231,253,0.7)" result="glowColor"><animate attributeName="flood-color" values="
              rgba(124,231,253,0.7);
              rgba(124,231,253,0.3);
              rgba(124,231,253,0.7);
            " dur="3s" begin="0s" repeatCount="indefinite"></animate></feFlood> <feComposite in="glowColor" in2="blurred" operator="in" result="softGlowColored"></feComposite> <feMerge><feMergeNode in="softGlowColored"></feMergeNode> <feMergeNode in="SourceGraphic"></feMergeNode></feMerge></filter></defs> <path fill="transparent" stroke-width="2" stroke="#2e6099" d="
        M15 5 L 352 5 Q 362 5, 362 15
        L 362 230 Q 362 240, 352 240
        L 15, 240 Q 5 240 5 230 L 5 15
        Q 5 5 15 5
      "></path> <path stroke-width="2" fill="transparent" stroke-linecap="round" filter="url(#borderr-box-12-filterId-397aa110-b94d-4d8c-acf9-d0980cf222e7)" d="M 20 5 L 15 5 Q 5 5 5 15 L 5 20" stroke="#7ce7fd"></path> <path stroke-width="2" fill="transparent" stroke-linecap="round" filter="url(#borderr-box-12-filterId-397aa110-b94d-4d8c-acf9-d0980cf222e7)" d="M 347 5 L 352 5 Q 362 5 362 15 L 362 20" stroke="#7ce7fd"></path> <path stroke-width="2" fill="transparent" stroke-linecap="round" filter="url(#borderr-box-12-filterId-397aa110-b94d-4d8c-acf9-d0980cf222e7)" d="
        M 347 240 L 352 240
        Q 362 240 362 230
        L 362 225
      " stroke="#7ce7fd"></path> <path stroke-width="2" fill="transparent" stroke-linecap="round" filter="url(#borderr-box-12-filterId-397aa110-b94d-4d8c-acf9-d0980cf222e7)" d="
        M 20 240 L 15 240
        Q 5 240 5 230
        L 5 225
      " stroke="#7ce7fd"></path></svg> <div class="border-box-content"></div></div> <resize-observer></resize-observer></div></div></div></li>


      <li
              id="d17cf223-06b8-4aef-b72a-7cd4757ea525chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 182px;
              height: 56px;
              transform: translate3d(1247px, 597px, 0px);
              z-index: 1015;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="d17cf223-06b8-4aef-b72a-7cd4757ea525"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="d17cf223-06b8-4aef-b72a-7cd4757ea525"
                    style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  "
            >
              <span>| 自移机尾</span>
            </p>
          </div>
        </div>
      </li>

      <li
              id="d17cf223-06b8-4aef-b72a-7cd4757ea525chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 182px;
              top: 32px;
              transform: translate3d(1247px, 597px, 0px);
              z-index: 1015;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="d17cf223-06b8-4aef-b72a-7cd4757ea525"
                  class="main animate__animated animate__slideInLeft"
          >
            <div class="data-panel" style="padding: 15px">
              <div v-for="item in actionConfig"
                   :key="item.code"
                   class="data-row"
                   style="display: flex; justify-content: space-between; color: #ffffff; font-size: 17px;"
                   :style="{ marginBottom: item.isLast ? '0' : '10px' }">
                <span>{{item.name}}:</span>
                <span style="color: #00ffff">
      {{getActionStatus(monitorDataZiYi[item.pe001],monitorDataZiYi[item.pe002])}}
    </span>
              </div>
            </div>
          </div>
        </div>
      </li>

      <li
              id="df5709ed-ee81-42e7-88d3-6813731c30c8chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 144px;
              height: 56px;
              transform: translate3d(983px, 597px, 0px);
              z-index: 1018;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="df5709ed-ee81-42e7-88d3-6813731c30c8"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="df5709ed-ee81-42e7-88d3-6813731c30c8"
                    style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  "
            >
              <span>| 变频张紧</span>
            </p>
          </div>
        </div>
      </li>

      <li
              id="df5709ed-ee81-42e7-88d3-6813731c30c8chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 144px;
              top: 32px;
              transform: translate3d(983px, 617px, 0px);
              z-index: 1018;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="df5709ed-ee81-42e7-88d3-6813731c30c8"
                  class="main animate__animated animate__slideInLeft"
          >
            <div class="data-panel" style="border-radius: 4px">
              <div v-for="item in frequencyConfig"
                   :key="item.code"
                   class="data-row"
                   style="display: flex; justify-content: space-between; color: #ffffff; font-size: 17px;"
                   :style="{ marginBottom: item.isLast ? '0' : '8px' }">
                <span>{{item.name}}:</span>
                <!--                <span style="color: #00ffff">{{monitorDataBianPin[item.key]}}{{item.unit}}</span>-->
                <span :style="{ color: getStatusColorAll(item, monitorDataBianPin[item.key]) }">
                        {{getStatusTextAll(item, monitorDataBianPin[item.key])}}{{item.unit}}
                      </span>
              </div>

              <div
                      class="data-row"
                      style="display: flex; justify-content: space-between; color: #ffffff; font-size: 17px;"
                      :style="{ marginBottom: true ? '0' : '10px' }">
                <span>调节模式:</span>
                <span style="color: #00ffff">{{tiaojieStatus}}</span>
              </div>
            </div>
          </div>
        </div>
      </li>
      <li
              id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 139px;
              height: 56px;
              transform: translate3d(280px, 597px, 0px);
              z-index: 1013;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2"
                    style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  "
            >
              <span>| 皮带运输</span>
            </p>
          </div>
        </div>
      </li>

      <li
              id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 175px;

              top: 12px;
              transform: translate3d(280px, 617px, 0px);
              z-index: 1013;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="6db27ff5-1b66-4dd1-8861-f7f1c31165e2"
                  class="main animate__animated animate__slideInLeft"
          >
            <div class="status-panel" style="padding: 15px">

              <div
                      class="status-item"
                      style="display: flex; justify-content: space-between; color: #ffffff; font-size: 17px;"
                      :style="{ marginBottom: true ? '10px' : '10px' }">
                <span>设备状态:</span>
                <span :style="{ color: getStatusColor('shebei',shebeiStatus) }">
                    {{getStatusText('shebei',shebeiStatus)}}
                  </span>
              </div>

              <div
                      class="status-item"
                      style="display: flex; justify-content: space-between; color: #ffffff; font-size: 17px;"
                      :style="{ marginBottom: true ? '10px' : '10px' }">
                <span>运行状态:</span>
                <span :style="{ color: getStatusColor('yunxing',yunxingStatus) }">
                    {{getStatusText('yunxing',yunxingStatus) }}
                  </span>
              </div>
              <div v-for="item in statusConfig"
                   :key="item.code"
                   class="status-item"
                   style="display: flex; justify-content: space-between; color: #ffffff; font-size: 17px;"
                   :style="{ marginBottom: item.isLast ? '0' : '10px' }">
                <span>{{item.name}}:</span>

                <span :style="{ color: getStatusColorAll(item, monitorData[item.key]) }">
                    {{getStatusTextAll(item, monitorDataPiDai[item.key])}}
                  </span>
              </div>
            </div>
          </div>
        </div>
      </li>

      <li
              id="5715f732-652a-44a2-ba81-62b87c621894chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 128px;
              height: 56px;
              transform: translate3d(32px, 597px, 0px);
              z-index: 1012;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="5715f732-652a-44a2-ba81-62b87c621894"
                  class="main animate__animated animate__slideInLeft"
          >
            <p
                    class="baseCss"
                    id="5715f732-652a-44a2-ba81-62b87c621894"
                    style="
                    justify-content: flex-start;
                    font-size: 25px;
                    color: rgb(255, 255, 255);
                    font-family: FZHZGBJW;
                    font-style: normal;
                    font-weight: 500;
                  "
            >
              <span>| 局扇 </span>
            </p>
          </div>
        </div>
      </li>

      <li
              id="5715f732-652a-44a2-ba81-62b87c621894chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 202px;
              margin-left: -23px;
              margin-top: 13px;

              transform: translate3d(32px, 612px, 0px);
              z-index: 1012;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="5715f732-652a-44a2-ba81-62b87c621894"
                  class="main animate__animated animate__slideInLeft"
          >
            <div class="data-panel" style="padding: 15px">
              <div
                      v-for="item in fanConfig"
                      :key="item.code"
                      class="data-item"
                      style="
                      display: flex;
                      justify-content: space-between;
                      margin-bottom: 10px;
                      color: #ffffff;
                      font-size: 17px;
                    "
                      :style="{ marginBottom: item.isLast ? '0' : '10px' }"
              >
                <span>{{item.name}}</span>
                <span style="color: #00ffff"
                >{{monitorDataJuShan[item.key] || 0}}{{item.unit}}</span
                >
              </div>
            </div>
          </div>
        </div>
      </li>
      <li
              id="c8e6ae87-b373-43b2-96d5-ceb54dbd4ba4chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 220px;
              height: 245px;
              transform: translate3d(1235px, 597px, 0px);
              z-index: 1;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="c8e6ae87-b373-43b2-96d5-ceb54dbd4ba4"
                  class="main animate__animated animate__slideInLeft"
          >
            <div full="" id="c8e6ae87-b373-43b2-96d5-ceb54dbd4ba4">
              <div class="dv-border-box-12">
                <svg
                        width="220"
                        height="245"
                        class="dv-border-svg-container"
                >
                  <defs>
                    <filter
                            id="borderr-box-12-filterId-8a392acd-b083-4272-b2a6-8ab0b8bbe7ef"
                            height="150%"
                            width="150%"
                            x="-25%"
                            y="-25%"
                    >
                      <feMorphology
                              operator="dilate"
                              radius="1"
                              in="SourceAlpha"
                              result="thicken"
                      ></feMorphology>
                      <feGaussianBlur
                              in="thicken"
                              stdDeviation="2"
                              result="blurred"
                      ></feGaussianBlur>
                      <feFlood
                              flood-color="rgba(124,231,253,0.7)"
                              result="glowColor"
                      >
                        <animate
                                attributeName="flood-color"
                                values="
              rgba(124,231,253,0.7);
              rgba(124,231,253,0.3);
              rgba(124,231,253,0.7);
            "
                                dur="3s"
                                begin="0s"
                                repeatCount="indefinite"
                        ></animate>
                      </feFlood>
                      <feComposite
                              in="glowColor"
                              in2="blurred"
                              operator="in"
                              result="softGlowColored"
                      ></feComposite>
                      <feMerge>
                        <feMergeNode in="softGlowColored"></feMergeNode>
                        <feMergeNode in="SourceGraphic"></feMergeNode>
                      </feMerge>
                    </filter>
                  </defs>
                  <path
                          fill="transparent"
                          stroke-width="2"
                          stroke="#2e6099"
                          d="
        M15 5 L 205 5 Q 215 5, 215 15
        L 215 230 Q 215 240, 205 240
        L 15, 240 Q 5 240 5 230 L 5 15
        Q 5 5 15 5
      "
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-8a392acd-b083-4272-b2a6-8ab0b8bbe7ef)"
                          d="M 20 5 L 15 5 Q 5 5 5 15 L 5 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-8a392acd-b083-4272-b2a6-8ab0b8bbe7ef)"
                          d="M 200 5 L 205 5 Q 215 5 215 15 L 215 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-8a392acd-b083-4272-b2a6-8ab0b8bbe7ef)"
                          d="
        M 200 240 L 205 240
        Q 215 240 215 230
        L 215 225
      "
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-8a392acd-b083-4272-b2a6-8ab0b8bbe7ef)"
                          d="
        M 20 240 L 15 240
        Q 5 240 5 230
        L 5 225
      "
                          stroke="#7ce7fd"
                  ></path>
                </svg>
                <div class="border-box-content"></div>
              </div>
              <resize-observer></resize-observer>
            </div>
          </div>
        </div>
      </li>
      <li
              id="502f1f73-1481-48c3-9019-1c19f85ca868chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 194px;
              height: 245px;
              transform: translate3d(965px, 597px, 0px);
              z-index: 1;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="502f1f73-1481-48c3-9019-1c19f85ca868"
                  class="main animate__animated animate__slideInLeft"
          >
            <div full="" id="502f1f73-1481-48c3-9019-1c19f85ca868">
              <div class="dv-border-box-12">
                <svg
                        width="194"
                        height="245"
                        class="dv-border-svg-container"
                >
                  <defs>
                    <filter
                            id="borderr-box-12-filterId-52e097c8-b8b3-49b1-8134-639180b06225"
                            height="150%"
                            width="150%"
                            x="-25%"
                            y="-25%"
                    >
                      <feMorphology
                              operator="dilate"
                              radius="1"
                              in="SourceAlpha"
                              result="thicken"
                      ></feMorphology>
                      <feGaussianBlur
                              in="thicken"
                              stdDeviation="2"
                              result="blurred"
                      ></feGaussianBlur>
                      <feFlood
                              flood-color="rgba(124,231,253,0.7)"
                              result="glowColor"
                      >
                        <animate
                                attributeName="flood-color"
                                values="
              rgba(124,231,253,0.7);
              rgba(124,231,253,0.3);
              rgba(124,231,253,0.7);
            "
                                dur="3s"
                                begin="0s"
                                repeatCount="indefinite"
                        ></animate>
                      </feFlood>
                      <feComposite
                              in="glowColor"
                              in2="blurred"
                              operator="in"
                              result="softGlowColored"
                      ></feComposite>
                      <feMerge>
                        <feMergeNode in="softGlowColored"></feMergeNode>
                        <feMergeNode in="SourceGraphic"></feMergeNode>
                      </feMerge>
                    </filter>
                  </defs>
                  <path
                          fill="transparent"
                          stroke-width="2"
                          stroke="#2e6099"
                          d="
        M15 5 L 179 5 Q 189 5, 189 15
        L 189 230 Q 189 240, 179 240
        L 15, 240 Q 5 240 5 230 L 5 15
        Q 5 5 15 5
      "
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-52e097c8-b8b3-49b1-8134-639180b06225)"
                          d="M 20 5 L 15 5 Q 5 5 5 15 L 5 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-52e097c8-b8b3-49b1-8134-639180b06225)"
                          d="M 174 5 L 179 5 Q 189 5 189 15 L 189 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-52e097c8-b8b3-49b1-8134-639180b06225)"
                          d="
        M 174 240 L 179 240
        Q 189 240 189 230
        L 189 225
      "
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-52e097c8-b8b3-49b1-8134-639180b06225)"
                          d="
        M 20 240 L 15 240
        Q 5 240 5 230
        L 5 225
      "
                          stroke="#7ce7fd"
                  ></path>
                </svg>
                <div class="border-box-content"></div>
              </div>
              <resize-observer></resize-observer>
            </div>
          </div>
        </div>
      </li>
      <li
              id="739aee13-1ab4-4254-ac4c-15361c7336f7chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 193px;
              height: 245px;
              transform: translate3d(270px, 597px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="739aee13-1ab4-4254-ac4c-15361c7336f7"
                  class="main animate__animated animate__slideInLeft"
          >
            <div full="" id="739aee13-1ab4-4254-ac4c-15361c7336f7">
              <div class="dv-border-box-12">
                <svg
                        width="193"
                        height="245"
                        class="dv-border-svg-container"
                >
                  <defs>
                    <filter
                            id="borderr-box-12-filterId-a4ab1ebf-2d9c-4564-86c1-195c762bea64"
                            height="150%"
                            width="150%"
                            x="-25%"
                            y="-25%"
                    >
                      <feMorphology
                              operator="dilate"
                              radius="1"
                              in="SourceAlpha"
                              result="thicken"
                      ></feMorphology>
                      <feGaussianBlur
                              in="thicken"
                              stdDeviation="2"
                              result="blurred"
                      ></feGaussianBlur>
                      <feFlood
                              flood-color="rgba(124,231,253,0.7)"
                              result="glowColor"
                      >
                        <animate
                                attributeName="flood-color"
                                values="
              rgba(124,231,253,0.7);
              rgba(124,231,253,0.3);
              rgba(124,231,253,0.7);
            "
                                dur="3s"
                                begin="0s"
                                repeatCount="indefinite"
                        ></animate>
                      </feFlood>
                      <feComposite
                              in="glowColor"
                              in2="blurred"
                              operator="in"
                              result="softGlowColored"
                      ></feComposite>
                      <feMerge>
                        <feMergeNode in="softGlowColored"></feMergeNode>
                        <feMergeNode in="SourceGraphic"></feMergeNode>
                      </feMerge>
                    </filter>
                  </defs>
                  <path
                          fill="transparent"
                          stroke-width="2"
                          stroke="#2e6099"
                          d="
        M15 5 L 178 5 Q 188 5, 188 15
        L 188 230 Q 188 240, 178 240
        L 15, 240 Q 5 240 5 230 L 5 15
        Q 5 5 15 5
      "
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-a4ab1ebf-2d9c-4564-86c1-195c762bea64)"
                          d="M 20 5 L 15 5 Q 5 5 5 15 L 5 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-a4ab1ebf-2d9c-4564-86c1-195c762bea64)"
                          d="M 173 5 L 178 5 Q 188 5 188 15 L 188 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-a4ab1ebf-2d9c-4564-86c1-195c762bea64)"
                          d="
        M 173 240 L 178 240
        Q 188 240 188 230
        L 188 225
      "
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-a4ab1ebf-2d9c-4564-86c1-195c762bea64)"
                          d="
        M 20 240 L 15 240
        Q 5 240 5 230
        L 5 225
      "
                          stroke="#7ce7fd"
                  ></path>
                </svg>
                <div class="border-box-content"></div>
              </div>
              <resize-observer></resize-observer>
            </div>
          </div>
        </div>
      </li>
      <li
              id="59ec4833-d95d-4fdb-8486-db9ff76e7e9bchart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 199px;
              height: 245px;
              transform: translate3d(11px, 597px, 0px);
              z-index: 1;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('') no-repeat; transform: rotate(0deg)"
        >
          <div
                  id="59ec4833-d95d-4fdb-8486-db9ff76e7e9b"
                  class="main animate__animated animate__slideInLeft"
          >
            <div full="" id="59ec4833-d95d-4fdb-8486-db9ff76e7e9b">
              <div class="dv-border-box-12">
                <svg
                        width="199"
                        height="245"
                        class="dv-border-svg-container"
                >
                  <defs>
                    <filter
                            id="borderr-box-12-filterId-d961ecf0-4a4e-40f9-99dc-0d5cdade98e6"
                            height="150%"
                            width="150%"
                            x="-25%"
                            y="-25%"
                    >
                      <feMorphology
                              operator="dilate"
                              radius="1"
                              in="SourceAlpha"
                              result="thicken"
                      ></feMorphology>
                      <feGaussianBlur
                              in="thicken"
                              stdDeviation="2"
                              result="blurred"
                      ></feGaussianBlur>
                      <feFlood
                              flood-color="rgba(124,231,253,0.7)"
                              result="glowColor"
                      >
                        <animate
                                attributeName="flood-color"
                                values="
              rgba(124,231,253,0.7);
              rgba(124,231,253,0.3);
              rgba(124,231,253,0.7);
            "
                                dur="3s"
                                begin="0s"
                                repeatCount="indefinite"
                        ></animate>
                      </feFlood>
                      <feComposite
                              in="glowColor"
                              in2="blurred"
                              operator="in"
                              result="softGlowColored"
                      ></feComposite>
                      <feMerge>
                        <feMergeNode in="softGlowColored"></feMergeNode>
                        <feMergeNode in="SourceGraphic"></feMergeNode>
                      </feMerge>
                    </filter>
                  </defs>
                  <path
                          fill="transparent"
                          stroke-width="2"
                          stroke="#2e6099"
                          d="
        M15 5 L 184 5 Q 194 5, 194 15
        L 194 230 Q 194 240, 184 240
        L 15, 240 Q 5 240 5 230 L 5 15
        Q 5 5 15 5
      "
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-d961ecf0-4a4e-40f9-99dc-0d5cdade98e6)"
                          d="M 20 5 L 15 5 Q 5 5 5 15 L 5 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-d961ecf0-4a4e-40f9-99dc-0d5cdade98e6)"
                          d="M 179 5 L 184 5 Q 194 5 194 15 L 194 20"
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-d961ecf0-4a4e-40f9-99dc-0d5cdade98e6)"
                          d="
        M 179 240 L 184 240
        Q 194 240 194 230
        L 194 225
      "
                          stroke="#7ce7fd"
                  ></path>
                  <path
                          stroke-width="2"
                          fill="transparent"
                          stroke-linecap="round"
                          filter="url(#borderr-box-12-filterId-d961ecf0-4a4e-40f9-99dc-0d5cdade98e6)"
                          d="
        M 20 240 L 15 240
        Q 5 240 5 230
        L 5 225
      "
                          stroke="#7ce7fd"
                  ></path>
                </svg>
                <div class="border-box-content"></div>
              </div>
              <resize-observer></resize-observer>
            </div>
          </div>
        </div>
      </li>

      <li
              id="473caadc-a0d8-4f66-b858-761dd03afef3chart-item"
              class="chart-item"
              style="
              zoom: 0.997396;
              will-change: auto;
              width: 1429px;
 margin-left: -10px;
 margin-top: -20px;
 height: 18px;
              transform: translate3d(34px, 875px, 0px);
              z-index: 2;
              background-color: transparent;
            "
      >
        <div
                class="chart-box animate__animated background selfAnimate_none"
                style="background: url('static/gif_02.png') no-repeat;height: 200px;"
        >
          <div
                  id="473caadc-a0d8-4f66-b858-761dd03afef3"
                  class="main animate__animated animate__slideInLeft"
          >
            <div
                    class="el-image full"
                    id="473caadc-a0d8-4f66-b858-761dd03afef3"
            >
              <div class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
              <!---->
            </div>
          </div>
        </div>
      </li>
    </ul>
    <div class="background" style="background: url('static/back.jpg');"></div>
  </div>
  <div class="el-dialog__wrapper resize" style="display: none">
    <div
            role="dialog"
            aria-modal="true"
            aria-label="dialog"
            class="el-dialog is-fullscreen"
    >
      <div class="el-dialog__header">
            <span class="el-dialog__title"></span
            ><button
              type="button"
              aria-label="Close"
              class="el-dialog__headerbtn"
      >
        <i class="el-dialog__close el-icon el-icon-close"></i>
      </button>
      </div>
      <!----><!---->
    </div>
  </div>
  <div
          class="el-loading-mask"
          style="background-color: rgba(0, 0, 0, 0.8); display: none"
  >
    <div class="el-loading-spinner">
      <svg viewBox="25 25 50 50" class="circular">
        <circle cx="50" cy="50" r="20" fill="none" class="path"></circle>
      </svg>
      <p class="el-loading-text">拼命加载中</p>
    </div>
  </div>

  <el-dialog
          title="历史查询"
          :visible.sync="historyDialogVisible"
          v-if="historyDialogVisible"
          width="75%"
          style="margin-top: -6vh; height: 900px;"
          :before-close="handleClose"
          custom-class="history-dialog"
  >
    <!-- 查询条件 -->
    <div class="query-form">
      <el-form :inline="true" :model="queryForm">
        <el-form-item label="查询类型">
          <el-select v-model="alarmType">
            <el-option label="报警记录" value="0"></el-option>
            <el-option label="运行记录" value="1"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
                  v-model="queryTime"
                  type="datetimerange"
                  format="yyyy-MM-dd HH:mm:ss"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  range-separator="至"
                  @change="timeChange"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getData">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <el-table :data="tableData" style="width: 100%">
      <el-table-column
              prop="alarmTime"
              label="时间"
              width="180"
      ></el-table-column>
      <el-table-column
              prop="alarmInfo"
              label="告警内容"
              align="center"
      ></el-table-column>

      <el-table-column
              prop="alarmType"
              label="状态"
              width="100"
      >
        <template v-slot="scope">
          <span v-if="scope.row.alarmType === 0">报警记录</span>
          <span v-if="scope.row.alarmType === 1">运行记录</span>

        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div
            class="pagination-container"
            style="margin-top: 20px; text-align: right"
    >
      <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="page"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
      >
      </el-pagination>
    </div>
  </el-dialog>

  <el-dialog
          title="智能分析"
          :visible.sync="analysisDialogVisible"
          width="80%"
          :close-on-click-modal="false"
          custom-class="analysis-dialog">
    <div style="
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
  ">
      <span style="color: #fff; font-size: 14px;">统计时间：</span>
      <el-date-picker
              v-model="analysisTimeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd"
              style="width: 380px;">
      </el-date-picker>
      <el-button
              type="primary"
              @click="initAnalysisChart"
              :loading="loading"
              style="
        margin-left: 10px;
        background-color: #188df0;
        border-color: #188df0;
      ">
        查询
      </el-button>
    </div>
    <div style="height: 500px">
      <div ref="analysisChart" style="width: 100%; height: 100%"></div>
    </div>
  </el-dialog>
</div>
<!-- Code injected by live-server -->
<script type="text/javascript">
  // <![CDATA[  <-- For SVG support
  if ("WebSocket" in window) {
    (function () {
      function refreshCSS() {
        var sheets = [].slice.call(document.getElementsByTagName("link"));
        var head = document.getElementsByTagName("head")[0];
        for (var i = 0; i < sheets.length; ++i) {
          var elem = sheets[i];
          head.removeChild(elem);
          var rel = elem.rel;
          if (
                  (elem.href && typeof rel != "string") ||
                  rel.length == 0 ||
                  rel.toLowerCase() == "stylesheet"
          ) {
            var url = elem.href.replace(/(&|\?)_cacheOverride=\d+/, "");
            elem.href =
                    url +
                    (url.indexOf("?") >= 0 ? "&" : "?") +
                    "_cacheOverride=" +
                    new Date().valueOf();
          }
          head.appendChild(elem);
        }
      }
      var protocol =
              window.location.protocol === "http:" ? "ws://" : "wss://";
      var address =
              protocol + window.location.host + window.location.pathname + "/ws";
      var socket = new WebSocket(address);
      socket.onmessage = function (msg) {
        if (msg.data == "reload") window.location.reload();
        else if (msg.data == "refreshcss") refreshCSS();
      };
      console.log("Live reload enabled.");
    })();
  }
  // ]]>
</script>

<script src="./static/js/vue.min.js"></script>
<script src="./static/js/jquery.min.js"></script>
<script src="./static/js/element-ui.js"></script>
<script src="./static/js/axios.min.js"></script>
<script src="./static/js/js/commonFun.js"></script>
<script src="./static/js/js/mixinsComponents.js"></script>
<script src="./static/js/js/mixinsMap.js"></script>
<script src="./static/js/js/mixinsThree.js"></script>
<script src="./static/js/js/mixinsDecoration.js"></script>
<script src="./static/js/js/mixinsEarth.js"></script>
<script src="./static/js/js/mixinsCesiumEarth.js"></script>
<script src="./static/js/js/video.min.js" async=""></script>
<script src="./static/js/js/vue-count-to.min.js" async=""></script>

<script>
  var baseurl1 = "http://**************:18001";
  var baseurl2 = "http://**************:18002";
  var baseurl3 = "http://**************:18003";
  var baseurl4 = "http://**************:18004";
  var baseurl5 = "http://**************:18005";
  var vm = new Vue({
    el: "#body",
    mixins: [mixinsComponents],
    data() {
      const end = new Date();
      const start = new Date();
      start.setDate(end.getDate() - 6);
      return {
        leftChart: null,
        rightChart: null,
        // 模拟数据,实际使用时替换为真实数据
        leftData: [],
        rightData: [],
        leftXAxisData: [],
        rightXAxisData: [],
        background: null,
        zoomWidth: 1,
        zoomHeight: 1,
        basicConfig: {
          description: "",
          width: 1920,
          height: 1080,
          window: [0],
          background: "#0D1D31",
          bluePrintConfig: {
            exportIdList: [],
            nodeData: { nodes: [], edges: [] },
            allNodeConfig: {
              judge: [],
              interActive: [],
              component: [],
              dataHandler: [],
            },
          },
          zoomMode: {
            default: "YOY",
            option: [
              { label: "同比缩放", value: "YOY" },
              { label: "自适应缩放", value: "adaptive" },
            ],
          },
          data: [],
        },
        loading: true,
        debounceTimer: null, // 防抖定时器
        isShowResize: false, // 放大图表的modal
        willResizeTemplate: [], // 将要放大的组件
        url: "static/js", // 加载js的地址
        EventSource: null, // 全局SSE source时间
        nowWindowIndex: 0, // 多桌面模式, 现在处于的桌面序号
        timer: null, // 定时器
        // bakAllTemplateList: null, // 备份组件 防止改变了大小改变了原始数据
        innerZoom: null, // 子元素缩放的倍数
        isLoadJsList: [], // 已经加载了的js列表
        cssMerge: "", // 通过id访问的css
        staticUrl: "./", // 静态资源地址
        zoom: 0.853,
        chart: null,
        chartData: {
          xData: [

          ],
          values: [

          ],
        },
        currentPersonnel: 0,
        totalPersonnel: 0,
        personnelList: [

          // 添加更多人员数据...
        ],
        webRtcServer: null,
        webRtcServer1: null,
        historyDialogVisible: false,
        queryForm: {
          type: "alarm",
          timeRange: [],
        },
        tableData: [],
        currentPage: 1,
        pageSize: 10,
        total: 0,
        temperature: 0,
        dust: 0,
        windSpeed: 0,
        t1Amount: 0,
        t2Amount: 0,
        t3Amount: 0,
        time: this.formatDate(new Date()),
        timer: null,
        isStatus:true,
        monitorData:[],
        monitorDataJuShan:[],
        monitorDataPiDai:[],
        monitorDataLeft:[],
        monitorDataRight:[],
        monitorDataBianPin:[],
        monitorDataZiYi:[],
        fanConfig: [
          {
            code: "PE_CODE_0053",
            name: "主风机前机电流",
            key: "mainFanFrontCurrent",
            unit: " A",
          },
          {
            code: "PE_CODE_0052",
            name: "主风机后机电流",
            key: "mainFanBackCurrent",
            unit: " A",
          },
          {
            code: "PE_CODE_0050",
            name: "辅风机前机电流",
            key: "auxFanFrontCurrent",
            unit: " A",
          },
          {
            code: "PE_CODE_0049",
            name: "辅风机后机电流",
            key: "auxFanBackCurrent",
            unit: " A",
          },
          {
            code: "PE_CODE_0051",
            name: "主风机电压",
            key: "mainFanVoltage",
            unit: " V",
          },
          {
            code: "PE_CODE_0048",
            name: "辅风机电压",
            key: "auxFanVoltage",
            unit: " V",
            isLast: true,
          },
        ],
        shebeiStatus:0,
        yunxingStatus:0,
        statusConfig: [
          {
            code: 'PE_CODE_0029',
            name: '电机1状态',
            key: 'motor1Status',
            statusMap: {
              0: { text: '停止', color: '#ff0000' },
              1: { text: '运行', color: '#00ff00' }
            }
          },
          {
            code: 'PE_CODE_0030',
            name: '电机2状态',
            key: 'motor2Status',
            statusMap: {
              0: { text: '停止', color: '#ff0000' },
              1: { text: '运行', color: '#00ff00' }
            }
          },
          {
            code: 'PE_CODE_0055',
            name: '速度',
            key: 'speed',
            unit: 'M/S',
            format: (value) => value.toFixed(1),
            color: '#00ffff'
          },
          {
            code: 'PE_CODE_0056',
            name: '电流',
            key: 'current',
            unit: 'A',
            format: (value) => value.toFixed(1),
            color: '#00ffff',
            isLast: true
          }
        ],
        leftColumnConfig: [
          {
            code: 'PE_CODE_0008',
            name: '油泵电流',
            key: 'oilPumpCurrent',
            unit: ' A'
          },
          {
            code: 'PE_CODE_0009',
            name: '截高电流',
            key: 'highLoadCurrent',
            unit: ' A'
          },
          {
            code: 'PE_CODE_0010',
            name: '截低电流',
            key: 'lowLoadCurrent',
            unit: ' A'
          },
          {
            code: 'PE_CODE_0011',
            name: '二运电流',
            key: 'secondTransportCurrent',
            unit: ' A'
          },
          {
            code: 'PE_CODE_0012',
            name: '风机电流',
            key: 'fanCurrent',
            unit: ' A'
          },
          {
            code: 'PE_CODE_0001',
            name: '系统电压',
            key: 'systemVoltage',
            unit: ' V',
            isLast: true
          }
        ],
        rightColumnConfig: [
          {
            code: 'PE_CODE_0004',
            name: '油箱温度',
            key: 'oilTankTemp',
            unit: '°C'
          },
          {
            code: 'PE_CODE_0002',
            name: '截低电机温度',
            key: 'lowLoadMotorTemp',
            unit: '°C'
          },
          {
            code: 'PE_CODE_0003',
            name: '截高电机温度',
            key: 'highLoadMotorTemp',
            unit: '°C'
          },
          {
            code: 'PE_CODE_0005',
            name: '控制状态',
            key: 'controlStatus',
            showColon: true,
            statusMap: {
              0: { text: '静止', color: '#00ff00' },
              1: { text: '本地', color: '#00ff00' },
              2: { text: '遥控', color: '#ff0000' },
              3: { text: '运控', color: '#00ff00' },
              4: { text: '地面', color: '#ff0000' },
            }
          },
          {
            code: 'PE_CODE_0006',
            name: '油泵状态',
            key: 'oilPumpStatus',
            showColon: true,
            statusMap: {
              0: { text: '静止', color: '#00ff00' },
              1: { text: '停止', color: '#ff0000' },
              2: { text: '预警', color: '#ee930c' },
              3: { text: '运行', color: '#00ff00' },
              4: { text: '准备启动', color: '#00ff00' }
            }
          },
          {
            code: 'PE_CODE_0007',
            name: '二运状态',
            key: 'secondTransportStatus',
            showColon: true,
            statusMap: {
              0: { text: '静止', color: '#00ff00' },
              1: { text: '本地', color: '#00ff00' },
              2: { text: '遥控', color: '#ff0000' },
              3: { text: '运控', color: '#00ff00' },
              4: { text: '地面', color: '#ff0000' },
            },
            isLast: true
          }
        ],
        frequencyConfig: [
          {
            code: 'PE_CODE_0026',
            name: '频率',
            key: 'frequency',
            defaultValue: '0.0',
            unit: 'HZ'
          },
          {
            code: 'PE_CODE_0027',
            name: '力矩',
            key: 'torque',
            defaultValue: '0.0',
            unit: '%'
          },
          {
            code: 'PE_CODE_0025',
            name: '电压',
            key: 'voltage',
            defaultValue: '0.0',
            unit: 'V'
          },
          {
            code: 'PE_CODE_0028',
            name: '实际拉力',
            key: 'tension',
            defaultValue: '0',
            unit: 'T'
          },
          // {
          //   code: 'PE_CODE_0031',
          //   name: '运行模式',
          //   key: 'runMode',
          //   defaultValue: '手动',
          //   unit: ''
          // },
          {
            code: 'PE_CODE_0031',
            name: '运行模式',
            key: 'runMode',
            statusMap: {
              0: { text: '停止', color: '#ff0000' },
              1: { text: '运行', color: '#00ff00' }
            },
          }

        ],
        actionConfig: [
          {
            name: '前机架侧移',
            pe001: 'PE_CODE_0015',  // 伸
            pe002: 'PE_CODE_0016',  // 收
            code: 'front_frame_shift',
            isLast: false
          },
          {
            name: '中机架侧移',
            pe001: 'PE_CODE_0017',
            pe002: 'PE_CODE_0018',
            code: 'middle_frame_shift',
            isLast: false
          },
          {
            name: '后机架侧移',
            pe001: 'PE_CODE_0019',
            pe002: 'PE_CODE_0020',
            code: 'back_frame_shift',
            isLast: false
          },
          {
            name: '推移缸',
            pe001: 'PE_CODE_0021',
            pe002: 'PE_CODE_0022',
            code: 'push_cylinder',
            isLast: false
          },
          {
            name: '左支撑',
            pe001: 'PE_CODE_0023',
            pe002: 'PE_CODE_0024',
            code: 'left_support',
            isLast: false
          },
          {
            name: '右支撑',
            pe001: 'PE_CODE_0013',
            pe002: 'PE_CODE_0014',
            code: 'right_support',
            isLast: true
          }
        ],
        tiaojieStatus:"手动",
        isShowLeft:false,
        isShowRight:false,
        page:1,
        alarmType:"0",
        queryTime:"",
        total:0,
        startTime:"",
        endTime:"",
        ulStyle: {
          marginLeft: '8%'
        },
        dayProgress:1,
        morningProgress:1,
        nightProgress:1,

        todayTotalProgress:1,
        totalProgress:1,
        analysisDialogVisible: false,
        analysisChart: null,
        chartData: {
          dates: [], // 日期数组
          progress: [], // 进尺数据数组
          rate: [] // 进尺率数组
        },
        analysisTimeRange: [
          start.toISOString().split('T')[0],
          end.toISOString().split('T')[0]
        ],
        loading: false, // 添加loading状态
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          },
          onPick: ({ maxDate, minDate }) => {
            if (minDate && !maxDate) {
              const thirtyDays = 30 * 24 * 60 * 60 * 1000;
              const maxLimit = new Date(minDate.getTime() + thirtyDays);
              this.pickerOptions.selectableRange = [
                minDate,
                maxLimit
              ];
            }
          }
        }
      };
    },
    watch:{
      alarmType(newVal, oldVal) {
        if(newVal!==oldVal){
          this.page = 1;
          this.total = 0
          this.getData()
        }
      }
    },

    mounted() {
      //这个time可以实时变动
      this.timer = setInterval(() => {
        this.time = this.formatDate(new Date());
      }, 1000);
      this.loadECharts();
      this.setZoom();
      window.addEventListener("resize", this.setZoom);

      // 模拟实时数据更新
      setInterval(this.updateData, 5000);
      const container = this.$el.querySelector(".scroll-content");
      container.innerHTML += container.innerHTML;
    },

    beforeDestroy() {
      if (this.timer) {
        clearInterval(this.timer);
      }
      if(this.timer22) {
        clearInterval(this.timer22);
      }
      window.removeEventListener("resize", this.handleResize);
      if (this.chart) {
        this.chart.dispose();
        this.chart = null;
      }
      // 移除监听
      clearTimeout(this.debounceTimer);
      clearInterval(this.timer);
      this.webRtcServer.disconnect();
      this.webRtcServer = null;
    },
    computed: {
      templateList() {
        //return this.allTemplateList[this.nowWindowIndex];
      },
    },
    async created() {
      this.loading = true;
      let searchKey = this.urlArgs();
      searchKey.id && (await this.initId(searchKey.id));
      if (!this.basicConfig.zoomMode) {
        this.basicConfig.zoomMode = {
          default: "YOY",
        };
      }
      await this.initJs(this.templateList);
      this.basicConfig.window &&
      this.basicConfig.window.length > 1 &&
      window.addEventListener("mousewheel", this.scrollFunc, {
        passive: false,
      });
      window.onresize = () => {
        this.onResize();
      };
      this.playVideo();
      //业务系统的数据请求
      this.updatePersonnel();
      this.updateTemperature();
      this.updateDust();
      this.updateWindSpeed();
      this.updateT1Amount();
      this.updateT2Amount();
      this.updateT3Amount();
      this.getkuangya()
      this.getkuangya2()
      this.piDaiApiResponse();
      this.juShanApiResponse();
      this.getJueMao();
      this.getBianPing();
      this.getziyi();
      this.isShowLeftStatus();
      this.isShowRightStatus();
      this.getStatus();
      this.getJinChi();

      this.timer22 = setInterval(() => {
        this.updatePersonnel();
        this.updateTemperature();
        this.updateDust();
        this.updateWindSpeed();
        this.updateT1Amount();
        this.updateT2Amount();
        this.updateT3Amount();
        this.getkuangya()
        this.getkuangya2()
        this.piDaiApiResponse();
        this.juShanApiResponse();
        this.getJueMao();
        this.getBianPing();
        this.getziyi();
        this.isShowLeftStatus();
        this.isShowRightStatus();
        this.getStatus()
        this.getJinChi();
      },60000)
    },
    methods: {
      getJinChi(){
        axios.get(
                "http://137.12.63.160/personweb/api/pdmc/getPDMCDailyData_PE"
        )
                .then((response) => {


                  this.morningProgress=response.data.morningShift;
                  this.dayProgress=response.data.afternoonShift;
                  this.nightProgress=response.data.eveningShift;

                  this.todayTotalProgress=response.data.dailyDis;
                  this.totalProgress=response.data.totalDis

                })
      },
      timeChange(value){

        this.startTime = value[0]
        this.endTime = value[1]
        this.getData()
      },
      getStatus(){
        axios.get(
                baseurl2 +
                "/interface-server-pe/indexReal/getCommunicationStatus"
        )
                .then((response) => {
                  if(response.data.data.value && response.data.data.value*1==0){
                    this.isStatus =true
                  }else{
                    this.isStatus =false
                  }
                })
      },
      isShowRightStatus(){
        // 模拟请求
        axios.get(
                baseurl2 +
                "/interface-server-pe/indexReal/getIndexReal?indexCodes=PE_CODE_0006"
        )
                .then((response) => {
                  response.data.data.forEach((item) => {
                    if (item.indexValue * 1 == 4) {
                      this.isShowRight = true
                    }
                  })
                })
      },
      isShowLeftStatus(){
        // 模拟请求
        axios.get(
                baseurl2 +
                "/interface-server-pe/indexReal/getIndexReal?indexCodes=PE_CODE_0036"
        )
                .then((response) => {
                  response.data.data.forEach((item) => {
                    if(item.indexValue*1==1){
                      this.isShowLeft = true
                    }

                  });
                })
      },
      getziyi(){
        const points = this.actionConfig
                .map(item => [item.pe001, item.pe002])
                .flat();
        // 模拟请求
        axios.get(
                baseurl2 +
                "/interface-server-pe/indexReal/getDataJwzy"
        )
                .then((response) => {
                  response.data.data.forEach((item) => {
                    const config = this.frequencyConfig.find(
                            (conf) => conf.code === item.baseIndexCode
                    );
                    if (config) {
                      // 可以在这里添加数值格式化
                      this.monitorDataZiYi[config.key] = item.indexValue
                    }

                  });
                })
      },
      getActionStatus(pe001, pe002) {
        if(!pe001 && !pe002) return '静止';  // 修改为静止
        if(pe001 && !pe002) return '伸';
        if(!pe001 && pe002) return '收';
        return '收';
      },
      getBianPing(){
        // 模拟请求
        axios.get(
                baseurl2 +
                "/interface-server-pe/indexReal/getDataBpzjzz"
        )
                .then((response) => {
                  response.data.data.forEach((item) => {
                    const config = this.frequencyConfig.find(
                            (conf) => conf.code === item.baseIndexCode
                    );
                    if (config) {
                      // 可以在这里添加数值格式化
                      this.monitorDataBianPin[config.key] = item.indexValue
                    }

                    if (item.baseIndexCode === 'PE_CODE_0032' && item.indexValue === 1) {
                      this.tiaojieStatus = "本地模式"
                    } else if (item.baseIndexCode === 'PE_CODE_0033' && item.indexValue === 1) {
                      this.tiaojieStatus = "远程模式"
                    } else if (item.baseIndexCode === 'PE_CODE_0034' && item.indexValue === 1) {
                      this.tiaojieStatus = "手动模式"
                    }
                  });
                })

      },
      getJueMao(){
        // 模拟请求
        axios.get(
                baseurl2 +
                "/interface-server-pe/indexReal/getDataJmj"
        )
                .then((response) => {
                  response.data.data.forEach((item) => {
                    const config = this.rightColumnConfig.find(
                            (conf) => conf.code === item.baseIndexCode
                    );
                    if (config) {
                      // 可以在这里添加数值格式化
                      this.monitorDataRight[config.key] = item.indexValue
                    }
                  });

                  response.data.data.forEach((item) => {
                    const config = this.leftColumnConfig.find(
                            (conf) => conf.code === item.baseIndexCode
                    );
                    if (config) {
                      // 可以在这里添加数值格式化
                      this.monitorDataLeft[config.key] = item.indexValue
                    }
                  });
                })
                .catch((error) => {
                  console.error("Error fetching personnel data:", error);
                });
      },
      // 处理接口返回数据
      piDaiApiResponse() {
        // 模拟请求
        axios
                .get(
                        baseurl2 +
                        "/interface-server-pe/indexReal/getDataPdys"
                )
                .then((response) => {

                  response.data.data.forEach((item) => {
                    const config = this.statusConfig.find(
                            (conf) => conf.code === item.baseIndexCode
                    );

                    if (item.baseIndexCode === 'PE_CODE_0039' && item.indexValue === 1) {
                      this.shebeiStatus = 0
                    } else if (item.baseIndexCode === 'PE_CODE_0040' && item.indexValue === 1) {
                      this.shebeiStatus = 1
                    } else if (item.baseIndexCode === 'PE_CODE_0041' && item.indexValue === 1) {
                      this.shebeiStatus = 2
                    }

                    if (item.baseIndexCode === 'PE_CODE_0035' && item.indexValue === 1) {
                      this.yunxingStatus = 0
                    } else if (item.baseIndexCode === 'PE_CODE_0036' && item.indexValue === 1) {
                      this.yunxingStatus = 1
                    } else if (item.baseIndexCode === 'PE_CODE_0037' && item.indexValue === 1) {
                      this.yunxingStatus = 2
                    }else if (item.baseIndexCode === 'PE_CODE_0038' && item.indexValue === 1) {
                      this.yunxingtatus = 3
                    }

                    if (config) {
                      // 可以在这里添加数值格式化
                      this.monitorDataPiDai[config.key] = this.formatValue(
                              item.indexValue,
                              config
                      );
                    }
                  });
                })
                .catch((error) => {
                  console.error("Error fetching personnel data:", error);
                });
      },
      formatValue(indexValue, config) {
        // 设备状态判断

        if (config.code === 'PE_CODE_0039' && indexValue === 1) {
          return '离线';
        } else if (config.code === 'PE_CODE_0040' && indexValue === 1) {
          return '近控';
        } else if (config.code === 'PE_CODE_0041' && indexValue === 1) {
          return '远控';
        }

        // 运行状态判断
        if (config.code === 'PE_CODE_0035' && indexValue === 1) {
          return '设备停止';
        } else if (config.code === 'PE_CODE_0036' && indexValue === 1) {
          return '正在运行';
        } else if (config.code === 'PE_CODE_0037' && indexValue === 1) {
          return '将要启车';
        } else if (config.code === 'PE_CODE_0038' && indexValue === 1) {
          return '将要停车';
        }

        // 如果不是特殊状态码，则返回原值
        return indexValue;
      },

      // 获取状态文本
      getStatusText(code,type) {
        if(code==='shebei'){
          if (type==0) {
            return '离线'
          }else if (type==1) {
            return  '远控'
          }else if (type==2) {
            return  '近控'
          }
        }else if(code ==='yunxing'){
          if (type==0) {
            return '设备停止'
          }else if (type==1) {
            return  '正在运行'
          }else if (type==2) {
            return  '将要启车'
          }else if (type===3){
            return  '将要停车'
          }
        }

      },

      // 获取状态颜色
      getStatusColor(code,type) {
        if(code ==='shebei'){
          if (type==0) {
            return '#f60606'
          }else if (type==1) {
            return  '#f6ea0e'
          }else if (type==2) {
            return  '#15f54a'
          }
        }else if(code ==='yunxing'){
          if (type==0) {
            return '#f60606'
          }else if (type==1) {
            return '#15f54a'
          }else if (type==2) {
            return   '#f6ea0e'
          }else if (type===3){
            return   '#f6ea0e'
          }
        }


      },

      // 获取状态文本
      getStatusTextAll(config, value) {
        if (config.statusMap) {
          return config.statusMap[value]?.text || '--'
        }
        if (config.format) {
          return `${config.format(value || 0)}${config.unit ? ' ' + config.unit : ''}`
        }

        return value || 0
      },

      // 获取状态颜色
      getStatusColorAll(config, value) {
        if (config.statusMap) {
          return config.statusMap[value]?.color || '#00ffff'
        }
        return config.color || '#00ffff'
      },
      // 处理接口返回数据
      juShanApiResponse() {
        // 模拟请求
        axios.get(
                baseurl2 +
                "/interface-server-pe/indexReal/getDataJs"
        )
                .then((response) => {
                  response.data.data.forEach((item) => {
                    const config = this.fanConfig.find(
                            (conf) => conf.code === item.baseIndexCode
                    );
                    if (config) {
                      // 可以在这里添加数值格式化
                      this.monitorDataJuShan[config.key] = item.indexValue
                    }
                  });
                })
                .catch((error) => {
                  console.error("Error fetching personnel data:", error);
                });
      },
      formatDate(date) {
        const year = date.getFullYear();
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        const seconds = String(date.getSeconds()).padStart(2, "0");

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      },
      //业务系统的数据请求
      updatePersonnel() {
        // 模拟请求
        axios
                .get(
                        baseurl3 +
                        "/interface-server-personlocation/VBJRTData/getByLocation?location=18425运顺掘进"
                )
                .then((response) => {

                  this.personnelList = response.data.data;
                  this.currentPersonnel = this.personnelList.length;
                  this.totalPersonnel = response.data.totalPersonnel;
                })
                .catch((error) => {
                  console.error("Error fetching personnel data:", error);
                });
      },
      getkuangya(){
        // 模拟请求
        this.leftData=[];
        this.leftXAxisData=[];
        axios
                .get(
                        baseurl5 +
                        "/interface-server-minepressure/ViewKYRTDARA/getViewKYRTDARA_MG"
                )
                .then((response) => {
                  console.log(33333,response.data.data)
                  if(response.data.data){
                    for (let i = 0; i < response.data.data.length; i++) {
                      this.leftXAxisData.push(response.data.data[i].monitorArea)
                      this.leftData.push(response.data.data[i].monitorValue)
                    }
                    this.setChartOption();

                  }
                  console.log(this.leftXAxisData,this.leftData)
                })
                .catch((error) => {
                  console.error("Error fetching temperature data:", error);
                });
      },
      getkuangya2(){
        // 模拟请求
        this.rightData=[];
        this.rightXAxisData=[];
        axios
                .get(
                        baseurl5 +
                        "/interface-server-minepressure/ViewKYRTDARA/getViewKYRTDARA_JD"
                )
                .then((response) => {

                  if(response.data.data){
                    for (let i = 0; i < response.data.data.length; i++) {
                      this.rightXAxisData.push(response.data.data[i].monitorArea)
                      this.rightData.push(response.data.data[i].monitorValue)
                    }
                    this.setChartOption();

                  }
                })
                .catch((error) => {
                  console.error("Error fetching temperature data:", error);
                });
      },
      updateTemperature() {
        // 模拟请求
        axios
                .get(
                        baseurl4 +
                        "/interface-server-safeymonitor/VBJRealtimeData/getBySsTransducerPoint?ssTransducerPoint=18425运顺掘进202队温度"
                )
                .then((response) => {
                  this.temperature = response.data.data[0].ssAnalogValue;
                })
                .catch((error) => {
                  console.error("Error fetching temperature data:", error);
                });
      },
      updateDust() {
        // 模拟请求
        axios
                .get(
                        baseurl4 +
                        "/interface-server-safeymonitor/VBJRealtimeData/getBySsTransducerPoint?ssTransducerPoint=18425运顺掘进202队粉尘"
                )
                .then((response) => {
                  this.dust = response.data.data[0].ssAnalogValue;
                })
                .catch((error) => {
                  console.error("Error fetching dust data:", error);
                });
      },
      updateWindSpeed() {
        // 模拟请求
        axios
                .get(
                        baseurl4 +
                        "/interface-server-safeymonitor/VBJRealtimeData/getBySsTransducerPoint?ssTransducerPoint=18425运顺掘进202队风速"
                )
                .then((response) => {
                  this.windSpeed = response.data.data[0].ssAnalogValue;
                })
                .catch((error) => {
                  console.error("Error fetching wind speed data:", error);
                });
      },
      updateT1Amount() {
        // 模拟请求
        axios
                .get(
                        baseurl4 +
                        "/interface-server-safeymonitor/VBJRealtimeData/getBySsTransducerPoint?ssTransducerPoint=18425运顺掘进202队T1"
                )
                .then((response) => {
                  this.t1Amount = response.data.data[0].ssAnalogValue;
                })
                .catch((error) => {
                  console.error("Error fetching t1 amount data:", error);
                });
      },
      updateT2Amount() {
        // 模拟请求
        axios
                .get(
                        baseurl4 +
                        "/interface-server-safeymonitor/VBJRealtimeData/getBySsTransducerPoint?ssTransducerPoint=18425运顺掘进202队T1"
                )
                .then((response) => {
                  this.t2Amount = response.data.data[0].ssAnalogValue;
                })
                .catch((error) => {
                  console.error("Error fetching t2 amount data:", error);
                });
      },
      updateT3Amount() {
        // 模拟请求
        axios
                .get(
                        baseurl4 +
                        "/interface-server-safeymonitor/VBJRealtimeData/getBySsTransducerPoint?ssTransducerPoint=18425运顺掘进202队T进4"
                )
                .then((response) => {
                  this.t3Amount = response.data.data[0].ssAnalogValue;
                })
                .catch((error) => {
                  console.error("Error fetching t3 amount data:", error);
                });
      },
      getData(){
        axios
                .get(
                        baseurl2 +
                        "/interface-server-pe/alarmIndexResult/page?alarmCycleType=0&alarmType="+this.alarmType+"&page="+this.page+"&size=10"+"&startTime="+this.startTime+"&endTime="+this.endTime
                )
                .then((response) => {
                  this.tableData =  response.data.data.content;
                  this.total =  response.data.data.totalPages
                })
                .catch((error) => {
                  console.error("Error fetching t3 amount data:", error);
                });
      },
      setZoom() {
        const designHeight = 1080;
        const clientHeight = document.documentElement.clientHeight;
        const zoomScale = clientHeight / designHeight;
        if(document.documentElement.clientHeight>1000){
          this.ulStyle={
            marginLeft: '0'
          }
        }else{
          this.ulStyle={
            marginLeft: '6%'
          }
        }
        // 使用Vue的方式获取DOM元素
        const liElements = document.querySelectorAll("li");
        liElements.forEach((li) => {
          li.style.zoom = zoomScale;
        });
        this.zoom = zoomScale;
        // 如果需要也可以设置图表容器的zoom
        if (this.$refs.chartBox) {
          this.$refs.chartBox.style.zoom = zoomScale;
        }

        // 重要：更新图表大小和缩放
        this.updateChartSize();
      },
      updateChartSize() {
        if (this.chart) {
          // 获取容器当前大小
          // const container = this.$refs.barChart;
          // const width = container.clientWidth;
          // const height = container.clientHeight;

          // // 应用缩放后的尺寸
          // this.chart.resize({
          //   width: width * this.zoomScale,
          //   height: height * this.zoomScale,
          // });

          // // 更新图表选项以适应新的尺寸
          // this.setChartOption();
          this.chart.resize();
        }
        this.leftChart && this.leftChart.resize()
        this.rightChart && this.rightChart.resize()
      },
      showHistoryDialog() {
        this.getData();
        this.historyDialogVisible = true;
      },
      handleClose() {
        this.page=1,
                this.alarmType="0",
                this.queryTime="",
                this.total=0,
                this.startTime="",
                this.endTime=""
        this.historyDialogVisible = false;
      },
      handleQuery() {
        // 实现查询逻辑
        // 根据 queryForm 中的条件调用后端接口获取数据
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.getData();
      },
      handleCurrentChange(val) {
        this.page = val;
        this.getData();
      },
      playVideo() {
        this.webRtcServer = new WebRtcStreamer(
                "video",
                "http://**************:8000" //本机ip+端口8000
        );

        this.webRtcServer.connect(
                "rtsp://**************:554/axis-media/media.amp" //这是填自己的rtsp流
        );
      },
      initChart() {
        // this.chart = echarts.init(this.$refs.barChart);
        // this.setChartOption();

        this.leftChart = echarts.init(this.$refs.leftBarChart)


        // 初始化右侧图表
        this.rightChart = echarts.init(this.$refs.rightBarChart)
        this.setChartOption();
      },
      setChartOption() {
        this.leftChart.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: this.leftXAxisData,
            axisLabel: {
              color: '#fff',
              // interval: 0,
              fontSize: 14
            },
            splitLine: {
              show: false
            },


          },
          yAxis: {
            type: 'value',
            name: 'KN',
            nameTextStyle: {
              color: '#fff'
            },
            axisLabel: {
              color: '#fff'
            },
            splitLine: {
              show: false
            },
          },
          series: [{
            data: this.leftData,
            type: 'bar',
            itemStyle: {
              color: '#7ce7fd'
            },
            label: {
              show: true,
              position: 'top',
              formatter: '{c} kN',
              fontSize: 14
            }
          }]
        })
        this.rightChart.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter: function (params) {
              // params 是一个数组，包含当前坐标轴下所有系列的数据
              let result = ''; // 初始化提示框内容
              params.forEach(function (item) {
                // item 是当前系列的数据
                result += `${item.name} --${item.value} mm\n`; // 添加系列名称、数值和单位
              });
              return result;
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: this.rightXAxisData,
            axisLabel: {
              color: '#fff',

              fontSize: 14
            },
            splitLine: {
              show: false
            },
          },
          yAxis: {
            type: 'value',
            name: 'mm',
            nameTextStyle: {
              color: '#fff'
            },
            axisLabel: {
              color: '#fff'
            },
            splitLine: {
              show: false
            },
          },
          series: [{
            data: this.rightData,
            type: 'bar',
            itemStyle: {
              color: '#7ce7fd'
            },
            label: {
              show: true,
              position: 'top',
              formatter: '{c} mm',
              fontSize: 14
            }
          }]
        })
        const option = {
          grid: {
            left: "3%",
            right: "4%",
            bottom: "8%",
            top: "8%",
            containLabel: true,
          },
          xAxis: {
            type: "category",
            data: this.chartData.xData,
            axisLabel: {
              color: "#fff",
              interval: 0,
              rotate: 0,
              fontSize: 10 * this.zoomScale, // 根据缩放调整字体大小
            },
            axisLine: {
              lineStyle: {
                color: "#0B3E7F",
              },
            },
          },
          yAxis: {
            type: "value",
            axisLabel: {
              color: "#fff",
              fontSize: 10 * this.zoomScale, // 根据缩放调整字体大小
            },
            splitLine: {
              lineStyle: {
                color: "rgba(11,62,127,0.3)",
              },
            },
            min: 0,
            max: 100,
            interval: 50, // 设置刻度间隔为500
            splitNumber: 3, // 将y轴分成3段
            splitLine: {
              lineStyle: {
                color: "rgba(11,62,127,0.3)",
              },
              // 只显示主刻度的分隔线
              show: true,
            },
            axisLine: {
              lineStyle: {
                color: "#0B3E7F",
              },
            },
          },
          series: [
            {
              data: this.chartData.values,
              type: "bar",
              barWidth: "50%",
              itemStyle: {
                normal: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: "#00FFFF",
                    },
                    {
                      offset: 1,
                      color: "#0066FF",
                    },
                  ]),
                },
              },
              animation: true,
              animationDuration: 2000,
              animationEasing: "cubicInOut",
            },
          ],
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
            formatter: function (params) {
              return (
                      params[0].name + "<br/>距离: " + params[0].value + "mm"
              );
            },
          },
        };
        // this.chart.setOption(option);
      },
      handleResize() {
        this.leftChart && this.leftChart.resize()
        this.rightChart && this.rightChart.resize()
      },
      // 更新数据的方法
      updateData() {
        // 模拟新数据
        // this.chartData.values = this.chartData.values.map((value) => {
        //   return value + Math.random() * 100 - 50;
        // });
        // 更新图表
        this.setChartOption();
      },
      // 外部调用的方法,用于更新实际数据
      updateChartData(newData) {
        this.chartData.values = newData;
        this.setChartOption();
      },
      urlArgs() {
        var args = {};
        var query = location.search.substring(1);
        var pairs = query.split("&");
        for (var i = 0; i < pairs.length; i++) {
          var pos = pairs[i].indexOf("=");
          if (pos == -1) {
            continue;
          }
          var name = pairs[i].substring(0, pos);
          var value = pairs[i].substring(pos + 1);
          args[name] = value;
        }
        return args;
      },
      async onResize() {
        // 根据当前屏幕尺寸进行缩放达到和编辑器显示的效果一致
        // for (let item of this.templateList) {
        //   if (this.basicConfig.zoomMode.default === "YOY") {
        //     this.initElementZoom(item.property);
        //   }
        //   this.initSize(item.property);
        //   item.isLoading = this.guid();
        //   this.$forceUpdate();
        //   await this.initData(item);
        // }
        this.loading = false;
        let zoomHeight = 1;
        if (this.basicConfig.zoomMode.default === "YOY") {
          // if (document.documentElement.clientWidth > this.basicConfig.width) {
          // 实际宽比预设宽大 则按照页面实际高除去 预设放大的高 进行缩放
          zoomHeight =
                  document.documentElement.clientHeight /
                  (this.innerZoom * this.basicConfig.height);
          if (zoomHeight > 1) {
            zoomHeight = 1;
          }
          // }
          this.$refs.body.style.zoom = zoomHeight;
        }
      },
      initElementZoom(item) {
        // 刷新组件的缩放
        let elementWidth = this.basicConfig.width / item.width;
        let element = this.$refs[item.elementId + "Template"];
        this.innerZoom =
                ((elementWidth / this.basicConfig.width) *
                        document.documentElement.clientWidth) /
                elementWidth;
        element && (element[0].style.zoom = this.innerZoom);
      },
      scrollFunc(e) {
        if (e.wheelDelta > 0) {
          this.$refs.carousel.setActiveItem(this.nowWindowIndex + 1);
        } else {
          this.$refs.carousel.setActiveItem(this.nowWindowIndex - 1);
        }
      },
      windowChange(e) {
        // 改变窗口
        this.nowWindowIndex = e;
        this.$nextTick(() => {
          this.onResize();
        });
      },
      async initId(id) {
        this.loading = true;
        this.templateList = [];
        let apiUrlOrigin = this.url.substr(0, this.url.length - 12);
        if (apiUrlOrigin === "..") {
          apiUrlOrigin = "https://moctest.sobeylingyun.com";
        }
        const apiUrl = `${apiUrlOrigin}/screen/api/v1/screen/${id}`;
        const res = await axios(apiUrl, {
          withCredentials: true, // 带上cookie
        });

        const { name, background, componentList, extend } = res.data.data;
        const templateList = {
          templateList: componentList,
          ...extend,
        };
        const tempData = [];
        this.allTemplateList = [];
        templateList.templateList.map((item) => {
          const upIndex = item.extend.upIndex || 0; // 获取保存时的upIndex 即所处的桌面序号
          if (!this.allTemplateList[upIndex]) {
            this.allTemplateList[upIndex] = [];
          }
          this.allTemplateList[upIndex].push(item.extend);
        });
        this.nowWindowIndex = 0;
        delete templateList.templateList;
        this.basicConfig = {
          ...templateList,
        };
        if (!this.basicConfig.window) {
          this.basicConfig.window = [0];
        }
        const cssList = [];
        this.cssMerge = ""; // 每次都清空
        this.allTemplateList = this.allTemplate2NotGroupAllTemplate(
                this.allTemplateList
        );
        this.allTemplateList.map((item) => {
          this.loadCss(item, cssList);
        });
        var style = document.createElement("style");
        style.type = "text/css";
        style.innerHTML = this.cssMerge.replaceAll("../../../", "../");
        document.getElementsByTagName("head")[0].appendChild(style);
      },
      loadCss(templateList, cssList) {
        //动态加载css使用到的css文件 并合并
        const find = (node) => {
          // 匿名函数防止this指向错误
          node.map((item) => {
            let { type, componentsType } = item.property;
            if (type == "group") {
              find(item.property.children);
            } else {
              if (
                      !cssList.includes(item.type) &&
                      componentsType !== "charts"
              ) {
                if (componentsType === "private") {
                  // 页面组件 在进行细分
                  this.loadCss(item.property.option.templateList, cssList);
                } else {
                  // 防止重复加载
                  cssList.push(type);
                  if (type.slice(0, 3) === "tab") {
                    type = "tab"; // tab1 tab2 用的都是tab.css文件
                  }
                  $.ajax({
                    async: false,
                    url: `./lib/css/${type}.css`,
                    success: (data) => {
                      this.cssMerge += data;
                    },
                  });
                }
              }
            }
          });
        };
        find(templateList);
      },
      async initJs(templateList) {
        // 动态加载js文件
        if (!templateList) {
          this.loading = false;
          return;
        }
        for (let item of templateList) {
          const { type, componentsType, option, secondType } =
                  item.property;
          if (secondType === "city" && !this.isLoadJsList.includes("L7")) {
            await this.loadJS(`${this.url}/js/L7-min.js`);
            await this.initMap();
            this.isLoadJsList.push("L7");
          } else if (
                  ["charts", "pyramid", "earth", "cesiumEarth", "L7"].includes(
                          componentsType
                  )
          ) {
            // pyramid 用到了 echarts
            if (!this.isLoadJsList.includes("echarts")) {
              await this.loadJS("static/js/echarts.min.js");
              this.isLoadJsList.push("echarts");
            }
            if (
                    ["area", "areaPoint"].includes(componentsType) &&
                    !this.isLoadJsList.includes("china")
            ) {
              await this.loadJS(`${this.url}/js/china.js`);
              this.isLoadJsList.push("china");
            } else if (
                    secondType === "earth" &&
                    !this.isLoadJsList.includes("echartsGl")
            ) {
              await this.loadJS(`${this.url}/js/echarts-gl.min.js`);
              this.isLoadJsList.push("echartsGl");
              await this.initEarth();
            } else if (
                    secondType === "cesiumEarth" &&
                    !this.isLoadJsList.includes("cesiumEarth")
            ) {
              await this.loadJS(`${this.url}/Cesium/Cesium.js`);
              this.isLoadJsList.push("cesiumEarth");
              await this.initCesiumEarth();
            } else if (
                    ["textCloud1", "textCloud"].includes(secondType) &&
                    !this.isLoadJsList.includes("echarts-wordcloud")
            ) {
              await this.loadJS(`${this.url}/js/echarts-wordcloud.min.js`);
              this.isLoadJsList.push("echarts-wordcloud");
            }
          } else if (
                  ["border", "decoration"].includes(componentsType) &&
                  !this.isLoadJsList.includes("dataV")
          ) {
            await this.loadJS(`${this.url}/js/dataV.js`);
            await this.initDecoration();
            this.isLoadJsList.push("dataV");
          } else if (
                  ((componentsType === "L7" && type === "queue") ||
                          type === "spaceTime") &&
                  !this.isLoadJsList.includes("three")
          ) {
            await this.loadJS(`${this.url}/js/three.min.js`);
            await this.loadJS(
                    "https://cdn.bootcdn.net/ajax/libs/tween.js/0.11.0/Tween.min.js"
            );
            await this.loadJS(`${this.url}/js/TrackballControls.js`);
            await this.loadJS(`${this.url}/js/CSS3DRenderer.js`);
            await this.initThree(); // 初始化three 组件
            this.isLoadJsList.push("three");
          } else if (
                  type === "smartCity" &&
                  !this.isLoadJsList.includes("smartCity")
          ) {
            await this.loadJS(
                    "http://earthsdk.com/v/last/XbsjEarth/XbsjEarth.js"
            );
          } else if (componentsType === "private") {
            // 存在页面组件 则加载重新遍历
            await this.initJs(option.templateList);
            return;
          }
        }

        this.basicConfig.waterMask &&
        this.addWaterMask(
                "productPreview",
                this.basicConfig.waterMask,
                document.body
        );
        await this.initTemplateList();
      },
      async loadJS(url) {
        return new Promise(function (resolve, reject) {
          var script = document.createElement("script");
          script.type = "text/javascript";
          script.async = false;
          if (script.readyState) {
            //IE
            script.onreadystatechange = function () {
              if (
                      script.readyState == "loaded" ||
                      script.readyState == "complete"
              ) {
                script.onreadystatechange = null;
                resolve("success: " + url);
              }
            };
          } else {
            //Others
            script.onload = function () {
              resolve("success: " + url);
            };
          }
          script.onerror = function () {
            reject(Error(url + "load error!"));
          };
          script.src = url;
          document.body.appendChild(script);
        });
      },
      closeModal() {
        this.isShowResize = false;
      },
      resizeTemplate(item) {
        if (item.property.componentsType !== "L7") {
          this.isShowResize = true;
          this.willResizeTemplate = item;
        }
      },
      async initTemplateList() {
        // 初始化获取templateList
        await this.initComponent();
        this.initPage();
      },
      async initData(template) {
        const item = template.property;
        switch (item.dataBindType.default) {
          case "api":
          {
            if (this.timer) {
              clearInterval(this.timer);
            }
            this.timer = setInterval(async () => {
              // 自动更新数据
              await this.initApi(template);
            }, (item.refresh || 10) * 1000 * 60);
            await this.initApi(template);
          }
            break;

          case "SSE":
          {
            await this.SSEBind();
          }
            break;
        }
      },
      initSize(item) {

        Object.keys(item).map((key) => {
          switch (key) {
            case "rotate":
            {
              let elementRef = this.$refs["chartBox" + item.elementId]; // 用ref代替
              elementRef[0].style.transform = `rotate(${item[key]}deg)`;
            }
              break;
            case "width":
            case "height":
            case "left":
            case "top":
            {
              if (this.basicConfig.zoomMode.default === "YOY") {
                this.setValue(item, key, item[key]);
              } else {
                let basicConfigKey = key;
                if (key === "left") basicConfigKey = "width";
                if (key === "top") basicConfigKey = "height";
                this.setValue(
                        item,
                        key,
                        `${
                                (item[key] / this.basicConfig[basicConfigKey]) * 100
                        }%`
                );
              }
            }
              break;
            case "zIndex":
            case "backgroundColor":
            {
              this.setValue(item, key, item[key]);
            }
              break;

              break;
          }
        });
      },
      async initPage() {
        // 渲染数据
        this.templateList.sort((a, b) => {
          return a.property.shareApiId ? 1 : -1; // 排序 shareApiId 往后排
        });
        this.onResize();
        // 初始化背景图片的属性
        if (this.basicConfig.blur) {
          this.$refs.background.style.filter = `blur(${this.basicConfig.blur}px)`;
        }
      },
      async loadECharts() {
        if (typeof echarts === "undefined") {
          await this.loadJS("static/js/echarts.min.js");
        }
        this.initChart();
      },
      showAnalysisDialog() {
        this.analysisDialogVisible = true;
        this.$nextTick(() => {
          this.initAnalysisChart();
        });
      },

      handleAnalysisTimeChange(val) {
        if (val) {
          const [startDate, endDate] = val;
          const start = new Date(startDate);
          const end = new Date(endDate);
          const diffDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));

          if (diffDays > 30) {
            this.$message.warning('时间区间不能超过30天');
            this.analysisTimeRange = [];
            return;
          }

          // 获取选定时间范围的数据
          this.getAnalysisData(startDate, endDate);
        }
      },

      async initAnalysisChart() {
        if(!this.analysisChart) {
          this.analysisChart = echarts.init(this.$refs.analysisChart);
        }
        if (!this.analysisTimeRange || this.analysisTimeRange.length !== 2) {
          this.$message.warning('请选择时间范围');
          return;
        }

        const [startDate, endDate] = this.analysisTimeRange;
        const start = new Date(startDate);
        const end = new Date(endDate);
        const diffDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));

        if (diffDays > 30) {
          this.$message.warning('时间区间不能超过30天');
          return;
        }

        // 设置loading状态
        this.loading = true;
        try {
          await this.getAnalysisData(startDate, endDate);
        } finally {
          this.loading = false;
        }
        // await this.getAnalysisData(startDate, endDate);

        const option = {
          backgroundColor: 'transparent',
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross'
            },
            formatter: function(params) {
              let html = `${params[0].axisValue}<br/>`;
              params.forEach(item => {
                let value = item.value;
                let unit = item.seriesName === '进尺率' ? ' %' : ' cm';
                let marker = item.marker;
                html += `${marker}${item.seriesName}: ${value}${unit}<br/>`;
              });
              return html;
            }
          },
          legend: {
            data: ['计划进度', '实际进度', '进尺率'],
            textStyle: {
              color: '#fff'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [{
            type: 'category',
            data: this.chartData.dates,
            axisLabel: {
              color: '#fff'
            },
            axisLine: {
              lineStyle: {
                color: '#7ce7fd'
              }
            }
          }],
          yAxis: [
            {
              type: 'value',
              name: '进度(米)',
              nameTextStyle: {
                color: '#fff'
              },
              axisLabel: {
                color: '#fff'
              },
              axisLine: {
                lineStyle: {
                  color: '#7ce7fd'
                }
              },
              splitLine: {
                lineStyle: {
                  color: 'rgba(124,231,253,0.1)'
                }
              }
            },
            {
              type: 'value',
              name: '进尺率(%)',
              nameTextStyle: {
                color: '#fff'
              },
              axisLabel: {
                color: '#fff'
              },
              axisLine: {
                lineStyle: {
                  color: '#7ce7fd'
                }
              },
              splitLine: {
                show: false
              }
            }
          ],
          series: [
            {
              name: '计划进度',
              type: 'bar',
              data: this.chartData.plannedProgress,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#91cc75' },
                  { offset: 1, color: '#5eb95e' }
                ])
              },
              barGap: '0%' // 调整柱间距
            },
            {
              name: '实际进度',
              type: 'bar',
              data: this.chartData.actualProgress,
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#83bff6' },
                  { offset: 1, color: '#188df0' }
                ])
              }
            },
            {
              name: '进尺率',
              type: 'line',
              yAxisIndex: 1,
              data: this.chartData.rate,
              symbol: 'circle',
              symbolSize: 8,
              lineStyle: {
                color: '#ffeb3b',
                width: 2
              },
              itemStyle: {
                color: '#ffeb3b'
              }
            }
          ]
        };

        this.analysisChart.setOption(option);
      },



      async getAnalysisData(startDate, endDate) {
        try {
          if (this.analysisChart) {
            this.analysisChart.showLoading({
              text: '加载中...',
              color: '#7ce7fd',
              textColor: '#fff',
              maskColor: 'rgba(0, 19, 52, 0.8)'
            });
          }

          // 这里替换为实际的API调用
          const response = await axios.get(baseurl2+"/interface-server-pe/footage/result/getFootageResult?page=0&size=100000&cycle=0&startTime="+startDate+"&endTime="+endDate);




          // 模拟数据
          const days = [];
          const plannedProgress = [];
          const actualProgress = [];
          const rate = [];

          if(response.data.data.content && response.data.data.content.length>0){
            for (let i = 0; i < response.data.data.content.length; i++) {
              days.push(response.data.data.content[i].dateTime);
              plannedProgress.push(response.data.data.content[i].dailyPlan);
              actualProgress.push(response.data.data.content[i].dailyDis);
              rate.push(response.data.data.content[i].dailyDisRatio);
            }
          }

          this.chartData = {
            dates: days,
            plannedProgress,
            actualProgress,
            rate
          };


        } catch (error) {
          console.error('获取分析数据失败:', error);
          this.$message.error('获取数据失败');
        } finally {
          if (this.analysisChart) {
            this.analysisChart.hideLoading();
          }
        }
      }
    },
  });
</script>
<script type="text/javascript" src="static/js/js/dataV.js"></script>
</body>
</html>