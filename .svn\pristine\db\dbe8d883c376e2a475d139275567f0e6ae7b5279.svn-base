.smartCity-arrow {
  width: 100%;
  position: absolute;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  top: 10%;
  left: 0;
  z-index: 99;
}
.smartCity-arrow > a {
  background: #1e5fc1;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.smartCity-arrow > a > i {
  color: 40px;
  color: #fff;
}
.smartCity-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 30%;
  height: 100%;
  position: absolute;
  top: 0;
  transform: translate3d(-100%, 0, 0);
  transition: 0.3s;
  overflow: auto;
}
.smartCity-list > li {
  width: 100%;
  min-height: 200px;
  max-height: 200px;
  background-color: cadetblue;
  cursor: pointer;
  color: #fff;
  font-size: 30px;
}
