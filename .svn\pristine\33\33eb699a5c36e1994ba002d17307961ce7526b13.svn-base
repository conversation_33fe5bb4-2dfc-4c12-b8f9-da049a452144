var VueResize=function(e){"use strict";var t;function i(){i.init||(i.init=!0,t=-1!==function(){var e=window.navigator.userAgent,t=e.indexOf("MSIE ");if(t>0)return parseInt(e.substring(t+5,e.indexOf(".",t)),10);if(e.indexOf("Trident/")>0){var i=e.indexOf("rv:");return parseInt(e.substring(i+3,e.indexOf(".",i)),10)}var n=e.indexOf("Edge/");return n>0?parseInt(e.substring(n+5,e.indexOf(".",n)),10):-1}())}const n={name:"ResizeObserver",mounted:function(){var e=this;i(),this.$nextTick((function(){e._w=e.$el.offsetWidth,e._h=e.$el.offsetHeight}));var n=document.createElement("object");this._resizeObject=n,n.setAttribute("aria-hidden","true"),n.setAttribute("tabindex",-1),n.onload=this.addResizeHandlers,n.type="text/html",t&&this.$el.appendChild(n),n.data="about:blank",t||this.$el.appendChild(n)},beforeDestroy:function(){this.removeResizeHandlers()},methods:{compareAndNotify:function(){this._w===this.$el.offsetWidth&&this._h===this.$el.offsetHeight||(this._w=this.$el.offsetWidth,this._h=this.$el.offsetHeight,this.$emit("notify",{width:this._w,height:this._h}))},addResizeHandlers:function(){this._resizeObject.contentDocument.defaultView.addEventListener("resize",this.compareAndNotify),this.compareAndNotify()},removeResizeHandlers:function(){this._resizeObject&&this._resizeObject.onload&&(!t&&this._resizeObject.contentDocument&&this._resizeObject.contentDocument.defaultView.removeEventListener("resize",this.compareAndNotify),this.$el.removeChild(this._resizeObject),this._resizeObject.onload=null,this._resizeObject=null)}}};var s=function(){var e=this.$createElement;return(this._self._c||e)("div",{staticClass:"resize-observer",attrs:{tabindex:"-1"}})};s._withStripped=!0;const r=function(e,t,i,n,s,r,o,d,a,c){"boolean"!=typeof o&&(a=d,d=o,o=!1);const f="function"==typeof i?i.options:i;let h;if(e&&e.render&&(f.render=e.render,f.staticRenderFns=e.staticRenderFns,f._compiled=!0,s&&(f.functional=!0)),n&&(f._scopeId=n),r?(h=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),t&&t.call(this,a(e)),e&&e._registeredComponents&&e._registeredComponents.add(r)},f._ssrRegister=h):t&&(h=o?function(e){t.call(this,c(e,this.$root.$options.shadowRoot))}:function(e){t.call(this,d(e))}),h)if(f.functional){const e=f.render;f.render=function(t,i){return h.call(i),e(t,i)}}else{const e=f.beforeCreate;f.beforeCreate=e?[].concat(e,h):[h]}return i}({render:s,staticRenderFns:[]},void 0,n,"data-v-2b830392",!1,void 0,!1,void 0,void 0,void 0);function o(e){e.component("resize-observer",r),e.component("ResizeObserver",r)}var d={version:"0.5.0",install:o},a=null;return"undefined"!=typeof window?a=window.Vue:"undefined"!=typeof global&&(a=global.Vue),a&&a.use(d),e.ResizeObserver=r,e.default=d,e.install=o,e}({});
//# sourceMappingURL=vue-resize.min.js.map
