.cardList {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  height: 100%;
}
.cardList li {
  display: flex;
  flex: 40%;
  flex-direction: column;
  justify-content: space-between;
  max-width: 40%;
  height: 15%;
  margin-bottom: 1%;
  padding: 3%;
  background: url(../../../iframe/image/cardList/details.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.cardList li:nth-of-type(2n-1) {
  margin-right: 2%;
}
.cardList li > p {
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.cardList li > div {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.cardList li > div > p {
  margin-right: 20px;
}
.cardList li > div > p > i {
  margin-right: 10px;
}
