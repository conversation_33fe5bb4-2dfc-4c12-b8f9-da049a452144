.capsuleList{
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.capsuleList li{
  height: 10%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-between;
  padding:10px 0;
}

.capsuleList li div:nth-of-type(1){
  /* flex:7; */
  border-radius:10px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.capsuleList li div:nth-of-type(1)  .el-avatar{
  /* width: 54px; */
  /* height: 54px; */
  /* min-width: 45px; */
  /* min-height: 45px; */
  /* position: relative; */
  /* left: 27px; */
}
.capsuleList li div:nth-of-type(1) div{
  width:100%;
  /* height: 50%; */
  max-height: 54px;
}

.capsuleList li div:nth-of-type(2){
  /* flex:1; */
  width: 10%;
  text-align: center;
}

