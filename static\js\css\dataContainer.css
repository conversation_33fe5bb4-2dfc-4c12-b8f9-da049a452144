.dataContainer {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  height: 100%;
}
.dataContainer li {
  position: relative;
  flex: 15%;
  height: 29%;
  padding: 1%;
}
.dataContainer li img {
  width: 100%;
  height: 100%;
}
.dataContainer li div {
  position: absolute;
  bottom: 20;
  display: flex;
  flex-direction: column;
  /* height: 20%; */
  width: calc(100% - 60px);
  padding-left: 20px;
  background: rgba(8, 12, 22, 0.63);
}
.dataContainer li div p {
  display: -webkit-box;
  margin-top: 10px;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.dataContainer .video,
.dataContainer .voice {
  position: absolute;
  top: 50%;
  left: 48%;
  width: 60px;
  height: 60px;
  transform: translate(-50%, -50%);
  cursor: pointer;
}
#dataContainerVideo {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80%;
  transform: translate(-50%, -50%);
}
