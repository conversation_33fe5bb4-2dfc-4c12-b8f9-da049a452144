<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>跳转中...</title>
    <script src="./static/js/axios.min.js"></script>
</head>
<body>
    <script>
        // 从URL获取orgId
        const urlParams = new URLSearchParams(window.location.search);
        const orgId = urlParams.get('orgId') || 1;

        // 请求场景配置
        axios.get(`http://************:18002/interface-server-automated/adminConfig/getSceneByOrgId?orgId=${orgId}`)
            .then(response => {
                if(response.data.code === 200 && response.data.data && response.data.data.length > 0) {
                    // 获取第一个活跃的场景
                    const activeScene = response.data.data.find(scene => scene.isActivity) || response.data.data[0];
                    if(activeScene && activeScene.url) {
                        // 重定向到场景URL
                        var url = `${activeScene.url}?sceneId=${activeScene.id}`;
                        history.replaceState({},'',url)
                        window.location = url;

                        window.addEventListener('DOMContentLoaded',function(){
                            const cleanUrl = activeScene.url;
                            history.replaceState({},'',cleanUrl)
                        })
                    } else {
                        console.error('No valid scene URL found');
                    }
                } else {
                    console.error('No scenes found');
                }
            })
            .catch(error => {
                console.error('Error fetching scene config:', error);
            });
    </script>
</body>
</html>