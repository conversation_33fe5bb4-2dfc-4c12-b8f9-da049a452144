!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.mpegts=t():e.mpegts=t()}(window,(function(){return function(e){var t={};function i(n){if(t[n])return t[n].exports;var a=t[n]={i:n,l:!1,exports:{}};return e[n].call(a.exports,a,a.exports,i),a.l=!0,a.exports}return i.m=e,i.c=t,i.d=function(e,t,n){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)i.d(n,a,function(t){return e[t]}.bind(null,a));return n},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=20)}([function(e,t,i){"use strict";var n=i(9),a=i.n(n),r=function(){function e(){}return e.e=function(t,i){t&&!e.FORCE_GLOBAL_TAG||(t=e.GLOBAL_TAG);var n="[".concat(t,"] > ").concat(i);e.ENABLE_CALLBACK&&e.emitter.emit("log","error",n),e.ENABLE_ERROR&&(console.error?console.error(n):console.warn?console.warn(n):console.log(n))},e.i=function(t,i){t&&!e.FORCE_GLOBAL_TAG||(t=e.GLOBAL_TAG);var n="[".concat(t,"] > ").concat(i);e.ENABLE_CALLBACK&&e.emitter.emit("log","info",n),e.ENABLE_INFO&&(console.info?console.info(n):console.log(n))},e.w=function(t,i){t&&!e.FORCE_GLOBAL_TAG||(t=e.GLOBAL_TAG);var n="[".concat(t,"] > ").concat(i);e.ENABLE_CALLBACK&&e.emitter.emit("log","warn",n),e.ENABLE_WARN&&(console.warn?console.warn(n):console.log(n))},e.d=function(t,i){t&&!e.FORCE_GLOBAL_TAG||(t=e.GLOBAL_TAG);var n="[".concat(t,"] > ").concat(i);e.ENABLE_CALLBACK&&e.emitter.emit("log","debug",n),e.ENABLE_DEBUG&&(console.debug?console.debug(n):console.log(n))},e.v=function(t,i){t&&!e.FORCE_GLOBAL_TAG||(t=e.GLOBAL_TAG);var n="[".concat(t,"] > ").concat(i);e.ENABLE_CALLBACK&&e.emitter.emit("log","verbose",n),e.ENABLE_VERBOSE&&console.log(n)},e}();r.GLOBAL_TAG="mpegts.js",r.FORCE_GLOBAL_TAG=!1,r.ENABLE_ERROR=!0,r.ENABLE_INFO=!0,r.ENABLE_WARN=!0,r.ENABLE_DEBUG=!0,r.ENABLE_VERBOSE=!0,r.ENABLE_CALLBACK=!1,r.emitter=new a.a,t.a=r},function(e,t,i){"use strict";var n;!function(e){e.IO_ERROR="io_error",e.DEMUX_ERROR="demux_error",e.INIT_SEGMENT="init_segment",e.MEDIA_SEGMENT="media_segment",e.LOADING_COMPLETE="loading_complete",e.RECOVERED_EARLY_EOF="recovered_early_eof",e.MEDIA_INFO="media_info",e.METADATA_ARRIVED="metadata_arrived",e.SCRIPTDATA_ARRIVED="scriptdata_arrived",e.TIMED_ID3_METADATA_ARRIVED="timed_id3_metadata_arrived",e.SYNCHRONOUS_KLV_METADATA_ARRIVED="synchronous_klv_metadata_arrived",e.ASYNCHRONOUS_KLV_METADATA_ARRIVED="asynchronous_klv_metadata_arrived",e.SMPTE2038_METADATA_ARRIVED="smpte2038_metadata_arrived",e.SCTE35_METADATA_ARRIVED="scte35_metadata_arrived",e.PES_PRIVATE_DATA_DESCRIPTOR="pes_private_data_descriptor",e.PES_PRIVATE_DATA_ARRIVED="pes_private_data_arrived",e.STATISTICS_INFO="statistics_info",e.RECOMMEND_SEEKPOINT="recommend_seekpoint"}(n||(n={})),t.a=n},function(e,t,i){"use strict";i.d(t,"c",(function(){return a})),i.d(t,"b",(function(){return r})),i.d(t,"a",(function(){return o}));var n=i(3),a={kIdle:0,kConnecting:1,kBuffering:2,kError:3,kComplete:4},r={OK:"OK",EXCEPTION:"Exception",HTTP_STATUS_CODE_INVALID:"HttpStatusCodeInvalid",CONNECTING_TIMEOUT:"ConnectingTimeout",EARLY_EOF:"EarlyEof",UNRECOVERABLE_EARLY_EOF:"UnrecoverableEarlyEof"},o=function(){function e(e){this._type=e||"undefined",this._status=a.kIdle,this._needStash=!1,this._onContentLengthKnown=null,this._onURLRedirect=null,this._onDataArrival=null,this._onError=null,this._onComplete=null}return e.prototype.destroy=function(){this._status=a.kIdle,this._onContentLengthKnown=null,this._onURLRedirect=null,this._onDataArrival=null,this._onError=null,this._onComplete=null},e.prototype.isWorking=function(){return this._status===a.kConnecting||this._status===a.kBuffering},Object.defineProperty(e.prototype,"type",{get:function(){return this._type},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"status",{get:function(){return this._status},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"needStashBuffer",{get:function(){return this._needStash},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onContentLengthKnown",{get:function(){return this._onContentLengthKnown},set:function(e){this._onContentLengthKnown=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onURLRedirect",{get:function(){return this._onURLRedirect},set:function(e){this._onURLRedirect=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onDataArrival",{get:function(){return this._onDataArrival},set:function(e){this._onDataArrival=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onError",{get:function(){return this._onError},set:function(e){this._onError=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onComplete",{get:function(){return this._onComplete},set:function(e){this._onComplete=e},enumerable:!1,configurable:!0}),e.prototype.open=function(e,t){throw new n.c("Unimplemented abstract function!")},e.prototype.abort=function(){throw new n.c("Unimplemented abstract function!")},e}()},function(e,t,i){"use strict";i.d(t,"d",(function(){return r})),i.d(t,"a",(function(){return o})),i.d(t,"b",(function(){return s})),i.d(t,"c",(function(){return d}));var n,a=(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),r=function(){function e(e){this._message=e}return Object.defineProperty(e.prototype,"name",{get:function(){return"RuntimeException"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"message",{get:function(){return this._message},enumerable:!1,configurable:!0}),e.prototype.toString=function(){return this.name+": "+this.message},e}(),o=function(e){function t(t){return e.call(this,t)||this}return a(t,e),Object.defineProperty(t.prototype,"name",{get:function(){return"IllegalStateException"},enumerable:!1,configurable:!0}),t}(r),s=function(e){function t(t){return e.call(this,t)||this}return a(t,e),Object.defineProperty(t.prototype,"name",{get:function(){return"InvalidArgumentException"},enumerable:!1,configurable:!0}),t}(r),d=function(e){function t(t){return e.call(this,t)||this}return a(t,e),Object.defineProperty(t.prototype,"name",{get:function(){return"NotImplementedException"},enumerable:!1,configurable:!0}),t}(r)},function(e,t,i){"use strict";var n;!function(e){e.ERROR="error",e.LOADING_COMPLETE="loading_complete",e.RECOVERED_EARLY_EOF="recovered_early_eof",e.MEDIA_INFO="media_info",e.METADATA_ARRIVED="metadata_arrived",e.SCRIPTDATA_ARRIVED="scriptdata_arrived",e.TIMED_ID3_METADATA_ARRIVED="timed_id3_metadata_arrived",e.SYNCHRONOUS_KLV_METADATA_ARRIVED="synchronous_klv_metadata_arrived",e.ASYNCHRONOUS_KLV_METADATA_ARRIVED="asynchronous_klv_metadata_arrived",e.SMPTE2038_METADATA_ARRIVED="smpte2038_metadata_arrived",e.SCTE35_METADATA_ARRIVED="scte35_metadata_arrived",e.PES_PRIVATE_DATA_DESCRIPTOR="pes_private_data_descriptor",e.PES_PRIVATE_DATA_ARRIVED="pes_private_data_arrived",e.STATISTICS_INFO="statistics_info",e.DESTROYING="destroying"}(n||(n={})),t.a=n},function(e,t,i){"use strict";var n={};!function(){var e=self.navigator.userAgent.toLowerCase(),t=/(edge)\/([\w.]+)/.exec(e)||/(opr)[\/]([\w.]+)/.exec(e)||/(chrome)[ \/]([\w.]+)/.exec(e)||/(iemobile)[\/]([\w.]+)/.exec(e)||/(version)(applewebkit)[ \/]([\w.]+).*(safari)[ \/]([\w.]+)/.exec(e)||/(webkit)[ \/]([\w.]+).*(version)[ \/]([\w.]+).*(safari)[ \/]([\w.]+)/.exec(e)||/(webkit)[ \/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||e.indexOf("trident")>=0&&/(rv)(?::| )([\w.]+)/.exec(e)||e.indexOf("compatible")<0&&/(firefox)[ \/]([\w.]+)/.exec(e)||[],i=/(ipad)/.exec(e)||/(ipod)/.exec(e)||/(windows phone)/.exec(e)||/(iphone)/.exec(e)||/(kindle)/.exec(e)||/(android)/.exec(e)||/(windows)/.exec(e)||/(mac)/.exec(e)||/(linux)/.exec(e)||/(cros)/.exec(e)||[],a={browser:t[5]||t[3]||t[1]||"",version:t[2]||t[4]||"0",majorVersion:t[4]||t[2]||"0",platform:i[0]||""},r={};if(a.browser){r[a.browser]=!0;var o=a.majorVersion.split(".");r.version={major:parseInt(a.majorVersion,10),string:a.version},o.length>1&&(r.version.minor=parseInt(o[1],10)),o.length>2&&(r.version.build=parseInt(o[2],10))}if(a.platform&&(r[a.platform]=!0),(r.chrome||r.opr||r.safari)&&(r.webkit=!0),r.rv||r.iemobile){r.rv&&delete r.rv;a.browser="msie",r.msie=!0}if(r.edge){delete r.edge;a.browser="msedge",r.msedge=!0}if(r.opr){a.browser="opera",r.opera=!0}if(r.safari&&r.android){a.browser="android",r.android=!0}for(var s in r.name=a.browser,r.platform=a.platform,n)n.hasOwnProperty(s)&&delete n[s];Object.assign(n,r)}(),t.a=n},function(e,t,i){"use strict";t.a={OK:"OK",FORMAT_ERROR:"FormatError",FORMAT_UNSUPPORTED:"FormatUnsupported",CODEC_UNSUPPORTED:"CodecUnsupported"}},function(e,t,i){"use strict";var n;!function(e){e.ERROR="error",e.SOURCE_OPEN="source_open",e.UPDATE_END="update_end",e.BUFFER_FULL="buffer_full",e.START_STREAMING="start_streaming",e.END_STREAMING="end_streaming"}(n||(n={})),t.a=n},function(e,t,i){"use strict";var n=i(9),a=i.n(n),r=i(0),o=function(){function e(){}return Object.defineProperty(e,"forceGlobalTag",{get:function(){return r.a.FORCE_GLOBAL_TAG},set:function(t){r.a.FORCE_GLOBAL_TAG=t,e._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(e,"globalTag",{get:function(){return r.a.GLOBAL_TAG},set:function(t){r.a.GLOBAL_TAG=t,e._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(e,"enableAll",{get:function(){return r.a.ENABLE_VERBOSE&&r.a.ENABLE_DEBUG&&r.a.ENABLE_INFO&&r.a.ENABLE_WARN&&r.a.ENABLE_ERROR},set:function(t){r.a.ENABLE_VERBOSE=t,r.a.ENABLE_DEBUG=t,r.a.ENABLE_INFO=t,r.a.ENABLE_WARN=t,r.a.ENABLE_ERROR=t,e._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(e,"enableDebug",{get:function(){return r.a.ENABLE_DEBUG},set:function(t){r.a.ENABLE_DEBUG=t,e._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(e,"enableVerbose",{get:function(){return r.a.ENABLE_VERBOSE},set:function(t){r.a.ENABLE_VERBOSE=t,e._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(e,"enableInfo",{get:function(){return r.a.ENABLE_INFO},set:function(t){r.a.ENABLE_INFO=t,e._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(e,"enableWarn",{get:function(){return r.a.ENABLE_WARN},set:function(t){r.a.ENABLE_WARN=t,e._notifyChange()},enumerable:!1,configurable:!0}),Object.defineProperty(e,"enableError",{get:function(){return r.a.ENABLE_ERROR},set:function(t){r.a.ENABLE_ERROR=t,e._notifyChange()},enumerable:!1,configurable:!0}),e.getConfig=function(){return{globalTag:r.a.GLOBAL_TAG,forceGlobalTag:r.a.FORCE_GLOBAL_TAG,enableVerbose:r.a.ENABLE_VERBOSE,enableDebug:r.a.ENABLE_DEBUG,enableInfo:r.a.ENABLE_INFO,enableWarn:r.a.ENABLE_WARN,enableError:r.a.ENABLE_ERROR,enableCallback:r.a.ENABLE_CALLBACK}},e.applyConfig=function(e){r.a.GLOBAL_TAG=e.globalTag,r.a.FORCE_GLOBAL_TAG=e.forceGlobalTag,r.a.ENABLE_VERBOSE=e.enableVerbose,r.a.ENABLE_DEBUG=e.enableDebug,r.a.ENABLE_INFO=e.enableInfo,r.a.ENABLE_WARN=e.enableWarn,r.a.ENABLE_ERROR=e.enableError,r.a.ENABLE_CALLBACK=e.enableCallback},e._notifyChange=function(){var t=e.emitter;if(t.listenerCount("change")>0){var i=e.getConfig();t.emit("change",i)}},e.registerListener=function(t){e.emitter.addListener("change",t)},e.removeListener=function(t){e.emitter.removeListener("change",t)},e.addLogListener=function(t){r.a.emitter.addListener("log",t),r.a.emitter.listenerCount("log")>0&&(r.a.ENABLE_CALLBACK=!0,e._notifyChange())},e.removeLogListener=function(t){r.a.emitter.removeListener("log",t),0===r.a.emitter.listenerCount("log")&&(r.a.ENABLE_CALLBACK=!1,e._notifyChange())},e}();o.emitter=new a.a,t.a=o},function(e,t,i){"use strict";var n,a="object"==typeof Reflect?Reflect:null,r=a&&"function"==typeof a.apply?a.apply:function(e,t,i){return Function.prototype.apply.call(e,t,i)};n=a&&"function"==typeof a.ownKeys?a.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var o=Number.isNaN||function(e){return e!=e};function s(){s.init.call(this)}e.exports=s,e.exports.once=function(e,t){return new Promise((function(i,n){function a(i){e.removeListener(t,r),n(i)}function r(){"function"==typeof e.removeListener&&e.removeListener("error",a),i([].slice.call(arguments))}g(e,t,r,{once:!0}),"error"!==t&&function(e,t,i){"function"==typeof e.on&&g(e,"error",t,i)}(e,a,{once:!0})}))},s.EventEmitter=s,s.prototype._events=void 0,s.prototype._eventsCount=0,s.prototype._maxListeners=void 0;var d=10;function _(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function c(e){return void 0===e._maxListeners?s.defaultMaxListeners:e._maxListeners}function h(e,t,i,n){var a,r,o,s;if(_(i),void 0===(r=e._events)?(r=e._events=Object.create(null),e._eventsCount=0):(void 0!==r.newListener&&(e.emit("newListener",t,i.listener?i.listener:i),r=e._events),o=r[t]),void 0===o)o=r[t]=i,++e._eventsCount;else if("function"==typeof o?o=r[t]=n?[i,o]:[o,i]:n?o.unshift(i):o.push(i),(a=c(e))>0&&o.length>a&&!o.warned){o.warned=!0;var d=new Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");d.name="MaxListenersExceededWarning",d.emitter=e,d.type=t,d.count=o.length,s=d,console&&console.warn&&console.warn(s)}return e}function l(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function u(e,t,i){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:i},a=l.bind(n);return a.listener=i,n.wrapFn=a,a}function f(e,t,i){var n=e._events;if(void 0===n)return[];var a=n[t];return void 0===a?[]:"function"==typeof a?i?[a.listener||a]:[a]:i?function(e){for(var t=new Array(e.length),i=0;i<t.length;++i)t[i]=e[i].listener||e[i];return t}(a):m(a,a.length)}function p(e){var t=this._events;if(void 0!==t){var i=t[e];if("function"==typeof i)return 1;if(void 0!==i)return i.length}return 0}function m(e,t){for(var i=new Array(t),n=0;n<t;++n)i[n]=e[n];return i}function g(e,t,i,n){if("function"==typeof e.on)n.once?e.once(t,i):e.on(t,i);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function a(r){n.once&&e.removeEventListener(t,a),i(r)}))}}Object.defineProperty(s,"defaultMaxListeners",{enumerable:!0,get:function(){return d},set:function(e){if("number"!=typeof e||e<0||o(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");d=e}}),s.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},s.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||o(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},s.prototype.getMaxListeners=function(){return c(this)},s.prototype.emit=function(e){for(var t=[],i=1;i<arguments.length;i++)t.push(arguments[i]);var n="error"===e,a=this._events;if(void 0!==a)n=n&&void 0===a.error;else if(!n)return!1;if(n){var o;if(t.length>0&&(o=t[0]),o instanceof Error)throw o;var s=new Error("Unhandled error."+(o?" ("+o.message+")":""));throw s.context=o,s}var d=a[e];if(void 0===d)return!1;if("function"==typeof d)r(d,this,t);else{var _=d.length,c=m(d,_);for(i=0;i<_;++i)r(c[i],this,t)}return!0},s.prototype.addListener=function(e,t){return h(this,e,t,!1)},s.prototype.on=s.prototype.addListener,s.prototype.prependListener=function(e,t){return h(this,e,t,!0)},s.prototype.once=function(e,t){return _(t),this.on(e,u(this,e,t)),this},s.prototype.prependOnceListener=function(e,t){return _(t),this.prependListener(e,u(this,e,t)),this},s.prototype.removeListener=function(e,t){var i,n,a,r,o;if(_(t),void 0===(n=this._events))return this;if(void 0===(i=n[e]))return this;if(i===t||i.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,i.listener||t));else if("function"!=typeof i){for(a=-1,r=i.length-1;r>=0;r--)if(i[r]===t||i[r].listener===t){o=i[r].listener,a=r;break}if(a<0)return this;0===a?i.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(i,a),1===i.length&&(n[e]=i[0]),void 0!==n.removeListener&&this.emit("removeListener",e,o||t)}return this},s.prototype.off=s.prototype.removeListener,s.prototype.removeAllListeners=function(e){var t,i,n;if(void 0===(i=this._events))return this;if(void 0===i.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==i[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete i[e]),this;if(0===arguments.length){var a,r=Object.keys(i);for(n=0;n<r.length;++n)"removeListener"!==(a=r[n])&&this.removeAllListeners(a);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=i[e]))this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},s.prototype.listeners=function(e){return f(this,e,!0)},s.prototype.rawListeners=function(e){return f(this,e,!1)},s.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):p.call(e,t)},s.prototype.listenerCount=p,s.prototype.eventNames=function(){return this._eventsCount>0?n(this._events):[]}},function(e,t,i){"use strict";i.d(t,"b",(function(){return r})),i.d(t,"a",(function(){return o}));var n=i(2),a=i(6),r={NETWORK_ERROR:"NetworkError",MEDIA_ERROR:"MediaError",OTHER_ERROR:"OtherError"},o={NETWORK_EXCEPTION:n.b.EXCEPTION,NETWORK_STATUS_CODE_INVALID:n.b.HTTP_STATUS_CODE_INVALID,NETWORK_TIMEOUT:n.b.CONNECTING_TIMEOUT,NETWORK_UNRECOVERABLE_EARLY_EOF:n.b.UNRECOVERABLE_EARLY_EOF,MEDIA_MSE_ERROR:"MediaMSEError",MEDIA_FORMAT_ERROR:a.a.FORMAT_ERROR,MEDIA_FORMAT_UNSUPPORTED:a.a.FORMAT_UNSUPPORTED,MEDIA_CODEC_UNSUPPORTED:a.a.CODEC_UNSUPPORTED}},function(e,t,i){"use strict";i.d(t,"d",(function(){return n})),i.d(t,"b",(function(){return a})),i.d(t,"a",(function(){return r})),i.d(t,"c",(function(){return o}));var n=function(e,t,i,n,a){this.dts=e,this.pts=t,this.duration=i,this.originalDts=n,this.isSyncPoint=a,this.fileposition=null},a=function(){function e(){this.beginDts=0,this.endDts=0,this.beginPts=0,this.endPts=0,this.originalBeginDts=0,this.originalEndDts=0,this.syncPoints=[],this.firstSample=null,this.lastSample=null}return e.prototype.appendSyncPoint=function(e){e.isSyncPoint=!0,this.syncPoints.push(e)},e}(),r=function(){function e(){this._list=[]}return e.prototype.clear=function(){this._list=[]},e.prototype.appendArray=function(e){var t=this._list;0!==e.length&&(t.length>0&&e[0].originalDts<t[t.length-1].originalDts&&this.clear(),Array.prototype.push.apply(t,e))},e.prototype.getLastSyncPointBeforeDts=function(e){if(0==this._list.length)return null;var t=this._list,i=0,n=t.length-1,a=0,r=0,o=n;for(e<t[0].dts&&(i=0,r=o+1);r<=o;){if((a=r+Math.floor((o-r)/2))===n||e>=t[a].dts&&e<t[a+1].dts){i=a;break}t[a].dts<e?r=a+1:o=a-1}return this._list[i]},e}(),o=function(){function e(e){this._type=e,this._list=[],this._lastAppendLocation=-1}return Object.defineProperty(e.prototype,"type",{get:function(){return this._type},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"length",{get:function(){return this._list.length},enumerable:!1,configurable:!0}),e.prototype.isEmpty=function(){return 0===this._list.length},e.prototype.clear=function(){this._list=[],this._lastAppendLocation=-1},e.prototype._searchNearestSegmentBefore=function(e){var t=this._list;if(0===t.length)return-2;var i=t.length-1,n=0,a=0,r=i,o=0;if(e<t[0].originalBeginDts)return o=-1;for(;a<=r;){if((n=a+Math.floor((r-a)/2))===i||e>t[n].lastSample.originalDts&&e<t[n+1].originalBeginDts){o=n;break}t[n].originalBeginDts<e?a=n+1:r=n-1}return o},e.prototype._searchNearestSegmentAfter=function(e){return this._searchNearestSegmentBefore(e)+1},e.prototype.append=function(e){var t=this._list,i=e,n=this._lastAppendLocation,a=0;-1!==n&&n<t.length&&i.originalBeginDts>=t[n].lastSample.originalDts&&(n===t.length-1||n<t.length-1&&i.originalBeginDts<t[n+1].originalBeginDts)?a=n+1:t.length>0&&(a=this._searchNearestSegmentBefore(i.originalBeginDts)+1),this._lastAppendLocation=a,this._list.splice(a,0,i)},e.prototype.getLastSegmentBefore=function(e){var t=this._searchNearestSegmentBefore(e);return t>=0?this._list[t]:null},e.prototype.getLastSampleBefore=function(e){var t=this.getLastSegmentBefore(e);return null!=t?t.lastSample:null},e.prototype.getLastSyncPointBefore=function(e){for(var t=this._searchNearestSegmentBefore(e),i=this._list[t].syncPoints;0===i.length&&t>0;)t--,i=this._list[t].syncPoints;return i.length>0?i[i.length-1]:null},e}()},function(e,t,i){"use strict";var n=function(){function e(){this.mimeType=null,this.duration=null,this.hasAudio=null,this.hasVideo=null,this.audioCodec=null,this.videoCodec=null,this.audioDataRate=null,this.videoDataRate=null,this.audioSampleRate=null,this.audioChannelCount=null,this.width=null,this.height=null,this.fps=null,this.profile=null,this.level=null,this.refFrames=null,this.chromaFormat=null,this.sarNum=null,this.sarDen=null,this.metadata=null,this.segments=null,this.segmentCount=null,this.hasKeyframesIndex=null,this.keyframesIndex=null}return e.prototype.isComplete=function(){var e=!1===this.hasAudio||!0===this.hasAudio&&null!=this.audioCodec&&null!=this.audioSampleRate&&null!=this.audioChannelCount,t=!1===this.hasVideo||!0===this.hasVideo&&null!=this.videoCodec&&null!=this.width&&null!=this.height&&null!=this.fps&&null!=this.profile&&null!=this.level&&null!=this.refFrames&&null!=this.chromaFormat&&null!=this.sarNum&&null!=this.sarDen;return null!=this.mimeType&&e&&t},e.prototype.isSeekable=function(){return!0===this.hasKeyframesIndex},e.prototype.getNearestKeyframe=function(e){if(null==this.keyframesIndex)return null;var t=this.keyframesIndex,i=this._search(t.times,e);return{index:i,milliseconds:t.times[i],fileposition:t.filepositions[i]}},e.prototype._search=function(e,t){var i=0,n=e.length-1,a=0,r=0,o=n;for(t<e[0]&&(i=0,r=o+1);r<=o;){if((a=r+Math.floor((o-r)/2))===n||t>=e[a]&&t<e[a+1]){i=a;break}e[a]<t?r=a+1:o=a-1}return i},e}();t.a=n},function(e,t,i){"use strict";var n=i(9),a=i.n(n),r=i(0),o=i(5),s=i(12);function d(e,t,i){var n=e;if(t+i<n.length){for(;i--;)if(128!=(192&n[++t]))return!1;return!0}return!1}var _,c=function(e){for(var t=[],i=e,n=0,a=e.length;n<a;)if(i[n]<128)t.push(String.fromCharCode(i[n])),++n;else{if(i[n]<192);else if(i[n]<224){if(d(i,n,1))if((r=(31&i[n])<<6|63&i[n+1])>=128){t.push(String.fromCharCode(65535&r)),n+=2;continue}}else if(i[n]<240){if(d(i,n,2))if((r=(15&i[n])<<12|(63&i[n+1])<<6|63&i[n+2])>=2048&&55296!=(63488&r)){t.push(String.fromCharCode(65535&r)),n+=3;continue}}else if(i[n]<248){var r;if(d(i,n,3))if((r=(7&i[n])<<18|(63&i[n+1])<<12|(63&i[n+2])<<6|63&i[n+3])>65536&&r<1114112){r-=65536,t.push(String.fromCharCode(r>>>10|55296)),t.push(String.fromCharCode(1023&r|56320)),n+=4;continue}}t.push(String.fromCharCode(65533)),++n}return t.join("")},h=i(3),l=(_=new ArrayBuffer(2),new DataView(_).setInt16(0,256,!0),256===new Int16Array(_)[0]),u=function(){function e(){}return e.parseScriptData=function(t,i,n){var a={};try{var o=e.parseValue(t,i,n),s=e.parseValue(t,i+o.size,n-o.size);a[o.data]=s.data}catch(e){r.a.e("AMF",e.toString())}return a},e.parseObject=function(t,i,n){if(n<3)throw new h.a("Data not enough when parse ScriptDataObject");var a=e.parseString(t,i,n),r=e.parseValue(t,i+a.size,n-a.size),o=r.objectEnd;return{data:{name:a.data,value:r.data},size:a.size+r.size,objectEnd:o}},e.parseVariable=function(t,i,n){return e.parseObject(t,i,n)},e.parseString=function(e,t,i){if(i<2)throw new h.a("Data not enough when parse String");var n=new DataView(e,t,i).getUint16(0,!l);return{data:n>0?c(new Uint8Array(e,t+2,n)):"",size:2+n}},e.parseLongString=function(e,t,i){if(i<4)throw new h.a("Data not enough when parse LongString");var n=new DataView(e,t,i).getUint32(0,!l);return{data:n>0?c(new Uint8Array(e,t+4,n)):"",size:4+n}},e.parseDate=function(e,t,i){if(i<10)throw new h.a("Data size invalid when parse Date");var n=new DataView(e,t,i),a=n.getFloat64(0,!l),r=n.getInt16(8,!l);return{data:new Date(a+=60*r*1e3),size:10}},e.parseValue=function(t,i,n){if(n<1)throw new h.a("Data not enough when parse Value");var a,o=new DataView(t,i,n),s=1,d=o.getUint8(0),_=!1;try{switch(d){case 0:a=o.getFloat64(1,!l),s+=8;break;case 1:a=!!o.getUint8(1),s+=1;break;case 2:var c=e.parseString(t,i+1,n-1);a=c.data,s+=c.size;break;case 3:a={};var u=0;for(9==(16777215&o.getUint32(n-4,!l))&&(u=3);s<n-4;){var f=e.parseObject(t,i+s,n-s-u);if(f.objectEnd)break;a[f.data.name]=f.data.value,s+=f.size}if(s<=n-3)9===(16777215&o.getUint32(s-1,!l))&&(s+=3);break;case 8:a={},s+=4;u=0;for(9==(16777215&o.getUint32(n-4,!l))&&(u=3);s<n-8;){var p=e.parseVariable(t,i+s,n-s-u);if(p.objectEnd)break;a[p.data.name]=p.data.value,s+=p.size}if(s<=n-3)9===(16777215&o.getUint32(s-1,!l))&&(s+=3);break;case 9:a=void 0,s=1,_=!0;break;case 10:a=[];var m=o.getUint32(1,!l);s+=4;for(var g=0;g<m;g++){var y=e.parseValue(t,i+s,n-s);a.push(y.data),s+=y.size}break;case 11:var v=e.parseDate(t,i+1,n-1);a=v.data,s+=v.size;break;case 12:var S=e.parseString(t,i+1,n-1);a=S.data,s+=S.size;break;default:s=n,r.a.w("AMF","Unsupported AMF value type "+d)}}catch(e){r.a.e("AMF",e.toString())}return{data:a,size:s,objectEnd:_}},e}(),f=function(){function e(e){this.TAG="ExpGolomb",this._buffer=e,this._buffer_index=0,this._total_bytes=e.byteLength,this._total_bits=8*e.byteLength,this._current_word=0,this._current_word_bits_left=0}return e.prototype.destroy=function(){this._buffer=null},e.prototype._fillCurrentWord=function(){var e=this._total_bytes-this._buffer_index;if(e<=0)throw new h.a("ExpGolomb: _fillCurrentWord() but no bytes available");var t=Math.min(4,e),i=new Uint8Array(4);i.set(this._buffer.subarray(this._buffer_index,this._buffer_index+t)),this._current_word=new DataView(i.buffer).getUint32(0,!1),this._buffer_index+=t,this._current_word_bits_left=8*t},e.prototype.readBits=function(e){if(e>32)throw new h.b("ExpGolomb: readBits() bits exceeded max 32bits!");if(e<=this._current_word_bits_left){var t=this._current_word>>>32-e;return this._current_word<<=e,this._current_word_bits_left-=e,t}var i=this._current_word_bits_left?this._current_word:0;i>>>=32-this._current_word_bits_left;var n=e-this._current_word_bits_left;this._fillCurrentWord();var a=Math.min(n,this._current_word_bits_left),r=this._current_word>>>32-a;return this._current_word<<=a,this._current_word_bits_left-=a,i=i<<a|r},e.prototype.readBool=function(){return 1===this.readBits(1)},e.prototype.readByte=function(){return this.readBits(8)},e.prototype._skipLeadingZero=function(){var e;for(e=0;e<this._current_word_bits_left;e++)if(0!=(this._current_word&2147483648>>>e))return this._current_word<<=e,this._current_word_bits_left-=e,e;return this._fillCurrentWord(),e+this._skipLeadingZero()},e.prototype.readUEG=function(){var e=this._skipLeadingZero();return this.readBits(e+1)-1},e.prototype.readSEG=function(){var e=this.readUEG();return 1&e?e+1>>>1:-1*(e>>>1)},e}(),p=function(){function e(){}return e._ebsp2rbsp=function(e){for(var t=e,i=t.byteLength,n=new Uint8Array(i),a=0,r=0;r<i;r++)r>=2&&3===t[r]&&0===t[r-1]&&0===t[r-2]||(n[a]=t[r],a++);return new Uint8Array(n.buffer,0,a)},e.parseSPS=function(t){for(var i=t.subarray(1,4),n="avc1.",a=0;a<3;a++){var r=i[a].toString(16);r.length<2&&(r="0"+r),n+=r}var o=e._ebsp2rbsp(t),s=new f(o);s.readByte();var d=s.readByte();s.readByte();var _=s.readByte();s.readUEG();var c=e.getProfileString(d),h=e.getLevelString(_),l=1,u=420,p=8,m=8;if((100===d||110===d||122===d||244===d||44===d||83===d||86===d||118===d||128===d||138===d||144===d)&&(3===(l=s.readUEG())&&s.readBits(1),l<=3&&(u=[0,420,422,444][l]),p=s.readUEG()+8,m=s.readUEG()+8,s.readBits(1),s.readBool()))for(var g=3!==l?8:12,y=0;y<g;y++)s.readBool()&&(y<6?e._skipScalingList(s,16):e._skipScalingList(s,64));s.readUEG();var v=s.readUEG();if(0===v)s.readUEG();else if(1===v){s.readBits(1),s.readSEG(),s.readSEG();var S=s.readUEG();for(y=0;y<S;y++)s.readSEG()}var b=s.readUEG();s.readBits(1);var E=s.readUEG(),A=s.readUEG(),R=s.readBits(1);0===R&&s.readBits(1),s.readBits(1);var T=0,L=0,k=0,w=0;s.readBool()&&(T=s.readUEG(),L=s.readUEG(),k=s.readUEG(),w=s.readUEG());var D=1,M=1,C=0,O=!0,B=0,I=0;if(s.readBool()){if(s.readBool()){var P=s.readByte();P>0&&P<16?(D=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][P-1],M=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][P-1]):255===P&&(D=s.readByte()<<8|s.readByte(),M=s.readByte()<<8|s.readByte())}if(s.readBool()&&s.readBool(),s.readBool()&&(s.readBits(4),s.readBool()&&s.readBits(24)),s.readBool()&&(s.readUEG(),s.readUEG()),s.readBool()){var x=s.readBits(32),U=s.readBits(32);O=s.readBool(),C=(B=U)/(I=2*x)}}var N=1;1===D&&1===M||(N=D/M);var V=0,G=0;0===l?(V=1,G=2-R):(V=3===l?1:2,G=(1===l?2:1)*(2-R));var F=16*(E+1),j=16*(A+1)*(2-R);F-=(T+L)*V,j-=(k+w)*G;var z=Math.ceil(F*N);return s.destroy(),s=null,{codec_mimetype:n,profile_idc:d,level_idc:_,profile_string:c,level_string:h,chroma_format_idc:l,bit_depth:p,bit_depth_luma:p,bit_depth_chroma:m,ref_frames:b,chroma_format:u,chroma_format_string:e.getChromaFormatString(u),frame_rate:{fixed:O,fps:C,fps_den:I,fps_num:B},sar_ratio:{width:D,height:M},codec_size:{width:F,height:j},present_size:{width:z,height:j}}},e._skipScalingList=function(e,t){for(var i=8,n=8,a=0;a<t;a++)0!==n&&(n=(i+e.readSEG()+256)%256),i=0===n?i:n},e.getProfileString=function(e){switch(e){case 66:return"Baseline";case 77:return"Main";case 88:return"Extended";case 100:return"High";case 110:return"High10";case 122:return"High422";case 244:return"High444";default:return"Unknown"}},e.getLevelString=function(e){return(e/10).toFixed(1)},e.getChromaFormatString=function(e){switch(e){case 420:return"4:2:0";case 422:return"4:2:2";case 444:return"4:4:4";default:return"Unknown"}},e}(),m=i(6),g=function(){function e(){}return e._ebsp2rbsp=function(e){for(var t=e,i=t.byteLength,n=new Uint8Array(i),a=0,r=0;r<i;r++)r>=2&&3===t[r]&&0===t[r-1]&&0===t[r-2]||(n[a]=t[r],a++);return new Uint8Array(n.buffer,0,a)},e.parseVPS=function(t){var i=e._ebsp2rbsp(t),n=new f(i);n.readByte(),n.readByte();n.readBits(4);n.readBits(2);n.readBits(6);return{num_temporal_layers:n.readBits(3)+1,temporal_id_nested:n.readBool()}},e.parseSPS=function(t){var i=e._ebsp2rbsp(t),n=new f(i);n.readByte(),n.readByte();for(var a=0,r=0,o=0,s=0,d=(n.readBits(4),n.readBits(3)),_=(n.readBool(),n.readBits(2)),c=n.readBool(),h=n.readBits(5),l=n.readByte(),u=n.readByte(),p=n.readByte(),m=n.readByte(),g=n.readByte(),y=n.readByte(),v=n.readByte(),S=n.readByte(),b=n.readByte(),E=n.readByte(),A=n.readByte(),R=[],T=[],L=0;L<d;L++)R.push(n.readBool()),T.push(n.readBool());if(d>0)for(L=d;L<8;L++)n.readBits(2);for(L=0;L<d;L++)R[L]&&(n.readByte(),n.readByte(),n.readByte(),n.readByte(),n.readByte(),n.readByte(),n.readByte(),n.readByte(),n.readByte(),n.readByte(),n.readByte()),T[L]&&n.readByte();n.readUEG();var k=n.readUEG();3==k&&n.readBits(1);var w=n.readUEG(),D=n.readUEG();n.readBool()&&(a+=n.readUEG(),r+=n.readUEG(),o+=n.readUEG(),s+=n.readUEG());var M=n.readUEG(),C=n.readUEG(),O=n.readUEG();for(L=n.readBool()?0:d;L<=d;L++)n.readUEG(),n.readUEG(),n.readUEG();n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG();if(n.readBool()&&n.readBool())for(var B=0;B<4;B++)for(var I=0;I<(3===B?2:6);I++){if(n.readBool()){var P=Math.min(64,1<<4+(B<<1));B>1&&n.readSEG();for(L=0;L<P;L++)n.readSEG()}else n.readUEG()}n.readBool(),n.readBool();n.readBool()&&(n.readByte(),n.readUEG(),n.readUEG(),n.readBool());var x=n.readUEG(),U=0;for(L=0;L<x;L++){var N=!1;if(0!==L&&(N=n.readBool()),N){L===x&&n.readUEG(),n.readBool(),n.readUEG();for(var V=0,G=0;G<=U;G++){var F=n.readBool(),j=!1;F||(j=n.readBool()),(F||j)&&V++}U=V}else{var z=n.readUEG(),H=n.readUEG();U=z+H;for(G=0;G<z;G++)n.readUEG(),n.readBool();for(G=0;G<H;G++)n.readUEG(),n.readBool()}}if(n.readBool()){var q=n.readUEG();for(L=0;L<q;L++){for(G=0;G<O+4;G++)n.readBits(1);n.readBits(1)}}var K=0,W=1,Y=1,X=!1,Q=1,J=1;n.readBool(),n.readBool();if(n.readBool()){if(n.readBool()){var Z=n.readByte();Z>0&&Z<=16?(W=[1,12,10,16,40,24,20,32,80,18,15,64,160,4,3,2][Z-1],Y=[1,11,11,11,33,11,11,11,33,11,11,33,99,3,2,1][Z-1]):255===Z&&(W=n.readBits(16),Y=n.readBits(16))}if(n.readBool()&&n.readBool(),n.readBool())n.readBits(3),n.readBool(),n.readBool()&&(n.readByte(),n.readByte(),n.readByte());n.readBool()&&(n.readUEG(),n.readUEG());n.readBool(),n.readBool(),n.readBool();if(n.readBool()&&(n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG()),n.readBool())if(Q=n.readBits(32),J=n.readBits(32),n.readBool()&&n.readUEG(),n.readBool()){var $=!1,ee=!1,te=!1;if($=n.readBool(),ee=n.readBool(),$||ee){(te=n.readBool())&&(n.readByte(),n.readBits(5),n.readBool(),n.readBits(5));n.readBits(4),n.readBits(4);te&&n.readBits(4),n.readBits(5),n.readBits(5),n.readBits(5)}for(L=0;L<=d;L++){var ie=n.readBool();X=ie;var ne=!0,ae=1;ie||(ne=n.readBool());var re=!1;if(ne?n.readUEG():re=n.readBool(),re||(ae=n.readUEG()+1),$){for(G=0;G<ae;G++)n.readUEG(),n.readUEG(),te&&(n.readUEG(),n.readUEG());n.readBool()}if(ee){for(G=0;G<ae;G++)n.readUEG(),n.readUEG(),te&&(n.readUEG(),n.readUEG());n.readBool()}}}if(n.readBool()){n.readBool(),n.readBool(),n.readBool();K=n.readUEG();n.readUEG(),n.readUEG(),n.readUEG(),n.readUEG()}}n.readBool();var oe="hvc1.".concat(h,".1.L").concat(A,".B0"),se=w-(a+r)*(1===k||2===k?2:1),de=D-(o+s)*(1===k?2:1),_e=1;return 1!==W&&1!==Y&&(_e=W/Y),n.destroy(),n=null,{codec_mimetype:oe,profile_string:e.getProfileString(h),level_string:e.getLevelString(A),profile_idc:h,bit_depth:M+8,ref_frames:1,chroma_format:k,chroma_format_string:e.getChromaFormatString(k),general_level_idc:A,general_profile_space:_,general_tier_flag:c,general_profile_idc:h,general_profile_compatibility_flags_1:l,general_profile_compatibility_flags_2:u,general_profile_compatibility_flags_3:p,general_profile_compatibility_flags_4:m,general_constraint_indicator_flags_1:g,general_constraint_indicator_flags_2:y,general_constraint_indicator_flags_3:v,general_constraint_indicator_flags_4:S,general_constraint_indicator_flags_5:b,general_constraint_indicator_flags_6:E,min_spatial_segmentation_idc:K,constant_frame_rate:0,chroma_format_idc:k,bit_depth_luma_minus8:M,bit_depth_chroma_minus8:C,frame_rate:{fixed:X,fps:J/Q,fps_den:Q,fps_num:J},sar_ratio:{width:W,height:Y},codec_size:{width:se,height:de},present_size:{width:se*_e,height:de}}},e.parsePPS=function(t){var i=e._ebsp2rbsp(t),n=new f(i);n.readByte(),n.readByte();n.readUEG(),n.readUEG(),n.readBool(),n.readBool(),n.readBits(3),n.readBool(),n.readBool(),n.readUEG(),n.readUEG(),n.readSEG(),n.readBool(),n.readBool();if(n.readBool())n.readUEG();n.readSEG(),n.readSEG(),n.readBool(),n.readBool(),n.readBool(),n.readBool();var a=n.readBool(),r=n.readBool(),o=1;return r&&a?o=0:r?o=3:a&&(o=2),{parallelismType:o}},e.getChromaFormatString=function(e){switch(e){case 0:return"4:0:0";case 1:return"4:2:0";case 2:return"4:2:2";case 3:return"4:4:4";default:return"Unknown"}},e.getProfileString=function(e){switch(e){case 1:return"Main";case 2:return"Main10";case 3:return"MainSP";case 4:return"Rext";case 9:return"SCC";default:return"Unknown"}},e.getLevelString=function(e){return(e/30).toFixed(1)},e}();function y(e){return e.byteOffset%2==0&&e.byteLength%2==0}function v(e){return e.byteOffset%4==0&&e.byteLength%4==0}function S(e,t){for(var i=0;i<e.length;i++)if(e[i]!==t[i])return!1;return!0}var b=function(e,t){return e.byteLength===t.byteLength&&(v(e)&&v(t)?function(e,t){return S(new Uint32Array(e.buffer,e.byteOffset,e.byteLength/4),new Uint32Array(t.buffer,t.byteOffset,t.byteLength/4))}(e,t):y(e)&&y(t)?function(e,t){return S(new Uint16Array(e.buffer,e.byteOffset,e.byteLength/2),new Uint16Array(t.buffer,t.byteOffset,t.byteLength/2))}(e,t):function(e,t){return S(e,t)}(e,t))},E=function(){function e(){}return e.parseOBUs=function(t){for(var i=null,n=0;n<t.byteLength;){t[n];var a=(120&t[n])>>3,r=0!=(4&t[n]),o=0!=(2&t[n]);t[n];n+=1,r&&(n+=1);var s=Number.POSITIVE_INFINITY;if(o){s=0;for(var d=0;s|=(127&t[n])<<7*d,0!=(128&t[n+=1]);d++);}1===a&&(i=e.parseSeuqneceHeader(t.subarray(n,n+s))),n+=s}return i},e.parseSeuqneceHeader=function(t){var i=new f(t),n=i.readBits(3),a=(i.readBool(),i.readBool()),r=!0,o=0,s=0,d=[];if(a)d.push({operating_point_idc:0,level:i.readBits(5),tier:0});else{if(i.readBool()){var _=i.readBits(32),c=i.readBits(32),h=i.readBool();if(h){for(var l=0;;){if(0!==i.readBits(1))break;l+=1}l>=32?4294967295:(1<<l)-1+i.readBits(l)}if((o=c)/(s=_),r=h,i.readBool()){i.readBits(5);i.readBits(32),i.readBits(5),i.readBits(5)}}for(var u=i.readBool(),p=i.readBits(5),m=0;m<=p;m++){var g=i.readBits(12),y=i.readBits(5),v=y>7?i.readBits(1):0;if(d.push({operating_point_idc:g,level:y,tier:v}),u)if(i.readBool())i.readBits(4)}}var S=d[0],b=S.level,E=S.tier,A=i.readBits(4),R=i.readBits(4),T=i.readBits(A+1)+1,L=i.readBits(R+1)+1,k=!1;if(a||(k=i.readBool()),k)i.readBits(4),i.readBits(4);i.readBool(),i.readBool(),i.readBool();var w=!1;if(!a){if(i.readBool(),i.readBool(),i.readBool(),i.readBool(),w=i.readBool())i.readBool(),i.readBool();if(i.readBool()?2:i.readBits(1))i.readBool()?2:i.readBits(1);else 2;if(w)i.readBits(3)+1;else 0}i.readBool(),i.readBool(),i.readBool();var D=i.readBool(),M=8;2===n&&D?M=i.readBool()?12:10:M=D?10:8;var C=!1;1!==n&&(C=i.readBool());if(i.readBool())i.readBits(8),i.readBits(8),i.readBits(8);var O=1,B=1;if(C){i.readBits(1),O=1,B=1}else{if(i.readBits(1),0==n)O=1,B=1;else if(1==n)O=0,B=0;else{if(12==M)if(i.readBits(1))i.readBits(1);else;else O=1,B=0}if(O&&B)i.readBits(2);i.readBits(1)}i.readBool();i.destroy(),i=null;return{codec_mimetype:"av01.".concat(n,".").concat(e.getLevelString(b,E),".").concat(M.toString(10).padStart(2,"0")),level:b,tier:E,level_string:e.getLevelString(b,E),profile_idc:n,profile_string:"".concat(n),bit_depth:M,ref_frames:1,chroma_format:e.getChromaFormat(C,O,B),chroma_format_string:e.getChromaFormatString(C,O,B),frame_rate:{fixed:r,fps:o/s,fps_den:s,fps_num:o},sar_ratio:{width:1,height:1},codec_size:{width:T,height:L},present_size:{width:1*T,height:L}}},e.getLevelString=function(e,t){return"".concat(e.toString(10).padStart(2,"0")).concat(0===t?"M":"H")},e.getChromaFormat=function(e,t,i){return e?0:0===t&&0===i?3:1===t&&0===i?2:1===t&&1===i?1:Number.NaN},e.getChromaFormatString=function(e,t,i){return e?"4:0:0":0===t&&0===i?"4:4:4":1===t&&0===i?"4:2:2":1===t&&1===i?"4:2:0":"Unknown"},e}();var A,R=function(){function e(e,t){this.TAG="FLVDemuxer",this._config=t,this._onError=null,this._onMediaInfo=null,this._onMetaDataArrived=null,this._onScriptDataArrived=null,this._onTrackMetadata=null,this._onDataAvailable=null,this._dataOffset=e.dataOffset,this._firstParse=!0,this._dispatch=!1,this._hasAudio=e.hasAudioTrack,this._hasVideo=e.hasVideoTrack,this._hasAudioFlagOverrided=!1,this._hasVideoFlagOverrided=!1,this._audioInitialMetadataDispatched=!1,this._videoInitialMetadataDispatched=!1,this._mediaInfo=new s.a,this._mediaInfo.hasAudio=this._hasAudio,this._mediaInfo.hasVideo=this._hasVideo,this._metadata=null,this._audioMetadata=null,this._videoMetadata=null,this._naluLengthSize=4,this._timestampBase=0,this._timescale=1e3,this._duration=0,this._durationOverrided=!1,this._referenceFrameRate={fixed:!0,fps:23.976,fps_num:23976,fps_den:1e3},this._flvSoundRateTable=[5500,11025,22050,44100,48e3],this._mpegSamplingRates=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],this._mpegAudioV10SampleRateTable=[44100,48e3,32e3,0],this._mpegAudioV20SampleRateTable=[22050,24e3,16e3,0],this._mpegAudioV25SampleRateTable=[11025,12e3,8e3,0],this._mpegAudioL1BitRateTable=[0,32,64,96,128,160,192,224,256,288,320,352,384,416,448,-1],this._mpegAudioL2BitRateTable=[0,32,48,56,64,80,96,112,128,160,192,224,256,320,384,-1],this._mpegAudioL3BitRateTable=[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1],this._videoTrack={type:"video",id:1,sequenceNumber:0,samples:[],length:0},this._audioTrack={type:"audio",id:2,sequenceNumber:0,samples:[],length:0},this._littleEndian=function(){var e=new ArrayBuffer(2);return new DataView(e).setInt16(0,256,!0),256===new Int16Array(e)[0]}()}return e.prototype.destroy=function(){this._mediaInfo=null,this._metadata=null,this._audioMetadata=null,this._videoMetadata=null,this._videoTrack=null,this._audioTrack=null,this._onError=null,this._onMediaInfo=null,this._onMetaDataArrived=null,this._onScriptDataArrived=null,this._onTrackMetadata=null,this._onDataAvailable=null},e.probe=function(e){var t=new Uint8Array(e);if(t.byteLength<9)return{needMoreData:!0};var i={match:!1};if(70!==t[0]||76!==t[1]||86!==t[2]||1!==t[3])return i;var n,a,r=(4&t[4])>>>2!=0,o=0!=(1&t[4]),s=(n=t)[a=5]<<24|n[a+1]<<16|n[a+2]<<8|n[a+3];return s<9?i:{match:!0,consumed:s,dataOffset:s,hasAudioTrack:r,hasVideoTrack:o}},e.prototype.bindDataSource=function(e){return e.onDataArrival=this.parseChunks.bind(this),this},Object.defineProperty(e.prototype,"onTrackMetadata",{get:function(){return this._onTrackMetadata},set:function(e){this._onTrackMetadata=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onMediaInfo",{get:function(){return this._onMediaInfo},set:function(e){this._onMediaInfo=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onMetaDataArrived",{get:function(){return this._onMetaDataArrived},set:function(e){this._onMetaDataArrived=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onScriptDataArrived",{get:function(){return this._onScriptDataArrived},set:function(e){this._onScriptDataArrived=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onError",{get:function(){return this._onError},set:function(e){this._onError=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onDataAvailable",{get:function(){return this._onDataAvailable},set:function(e){this._onDataAvailable=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"timestampBase",{get:function(){return this._timestampBase},set:function(e){this._timestampBase=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"overridedDuration",{get:function(){return this._duration},set:function(e){this._durationOverrided=!0,this._duration=e,this._mediaInfo.duration=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"overridedHasAudio",{set:function(e){this._hasAudioFlagOverrided=!0,this._hasAudio=e,this._mediaInfo.hasAudio=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"overridedHasVideo",{set:function(e){this._hasVideoFlagOverrided=!0,this._hasVideo=e,this._mediaInfo.hasVideo=e},enumerable:!1,configurable:!0}),e.prototype.resetMediaInfo=function(){this._mediaInfo=new s.a},e.prototype._isInitialMetadataDispatched=function(){return this._hasAudio&&this._hasVideo?this._audioInitialMetadataDispatched&&this._videoInitialMetadataDispatched:this._hasAudio&&!this._hasVideo?this._audioInitialMetadataDispatched:!(this._hasAudio||!this._hasVideo)&&this._videoInitialMetadataDispatched},e.prototype.parseChunks=function(t,i){if(!(this._onError&&this._onMediaInfo&&this._onTrackMetadata&&this._onDataAvailable))throw new h.a("Flv: onError & onMediaInfo & onTrackMetadata & onDataAvailable callback must be specified");var n=0,a=this._littleEndian;if(0===i){if(!(t.byteLength>13))return 0;n=e.probe(t).dataOffset}this._firstParse&&(this._firstParse=!1,i+n!==this._dataOffset&&r.a.w(this.TAG,"First time parsing but chunk byteStart invalid!"),0!==(o=new DataView(t,n)).getUint32(0,!a)&&r.a.w(this.TAG,"PrevTagSize0 !== 0 !!!"),n+=4);for(;n<t.byteLength;){this._dispatch=!0;var o=new DataView(t,n);if(n+11+4>t.byteLength)break;var s=o.getUint8(0),d=16777215&o.getUint32(0,!a);if(n+11+d+4>t.byteLength)break;if(8===s||9===s||18===s){var _=o.getUint8(4),c=o.getUint8(5),l=o.getUint8(6)|c<<8|_<<16|o.getUint8(7)<<24;0!==(16777215&o.getUint32(7,!a))&&r.a.w(this.TAG,"Meet tag which has StreamID != 0!");var u=n+11;switch(s){case 8:this._parseAudioData(t,u,d,l);break;case 9:this._parseVideoData(t,u,d,l,i+n);break;case 18:this._parseScriptData(t,u,d)}var f=o.getUint32(11+d,!a);f!==11+d&&r.a.w(this.TAG,"Invalid PrevTagSize ".concat(f)),n+=11+d+4}else r.a.w(this.TAG,"Unsupported tag type ".concat(s,", skipped")),n+=11+d+4}return this._isInitialMetadataDispatched()&&this._dispatch&&(this._audioTrack.length||this._videoTrack.length)&&this._onDataAvailable(this._audioTrack,this._videoTrack),n},e.prototype._parseScriptData=function(e,t,i){var n=u.parseScriptData(e,t,i);if(n.hasOwnProperty("onMetaData")){if(null==n.onMetaData||"object"!=typeof n.onMetaData)return void r.a.w(this.TAG,"Invalid onMetaData structure!");this._metadata&&r.a.w(this.TAG,"Found another onMetaData tag!"),this._metadata=n;var a=this._metadata.onMetaData;if(this._onMetaDataArrived&&this._onMetaDataArrived(Object.assign({},a)),"boolean"==typeof a.hasAudio&&!1===this._hasAudioFlagOverrided&&(this._hasAudio=a.hasAudio,this._mediaInfo.hasAudio=this._hasAudio),"boolean"==typeof a.hasVideo&&!1===this._hasVideoFlagOverrided&&(this._hasVideo=a.hasVideo,this._mediaInfo.hasVideo=this._hasVideo),"number"==typeof a.audiodatarate&&(this._mediaInfo.audioDataRate=a.audiodatarate),"number"==typeof a.videodatarate&&(this._mediaInfo.videoDataRate=a.videodatarate),"number"==typeof a.width&&(this._mediaInfo.width=a.width),"number"==typeof a.height&&(this._mediaInfo.height=a.height),"number"==typeof a.duration){if(!this._durationOverrided){var o=Math.floor(a.duration*this._timescale);this._duration=o,this._mediaInfo.duration=o}}else this._mediaInfo.duration=0;if("number"==typeof a.framerate){var s=Math.floor(1e3*a.framerate);if(s>0){var d=s/1e3;this._referenceFrameRate.fixed=!0,this._referenceFrameRate.fps=d,this._referenceFrameRate.fps_num=s,this._referenceFrameRate.fps_den=1e3,this._mediaInfo.fps=d}}if("object"==typeof a.keyframes){this._mediaInfo.hasKeyframesIndex=!0;var _=a.keyframes;this._mediaInfo.keyframesIndex=this._parseKeyframesIndex(_),a.keyframes=null}else this._mediaInfo.hasKeyframesIndex=!1;this._dispatch=!1,this._mediaInfo.metadata=a,r.a.v(this.TAG,"Parsed onMetaData"),this._mediaInfo.isComplete()&&this._onMediaInfo(this._mediaInfo)}Object.keys(n).length>0&&this._onScriptDataArrived&&this._onScriptDataArrived(Object.assign({},n))},e.prototype._parseKeyframesIndex=function(e){for(var t=[],i=[],n=1;n<e.times.length;n++){var a=this._timestampBase+Math.floor(1e3*e.times[n]);t.push(a),i.push(e.filepositions[n])}return{times:t,filepositions:i}},e.prototype._parseAudioData=function(e,t,i,n){if(i<=1)r.a.w(this.TAG,"Flv: Invalid audio packet, missing SoundData payload!");else if(!0!==this._hasAudioFlagOverrided||!1!==this._hasAudio){this._littleEndian;var a=new DataView(e,t,i).getUint8(0),o=a>>>4;if(2===o||10===o){var s=0,d=(12&a)>>>2;if(d>=0&&d<=4){s=this._flvSoundRateTable[d];var _=1&a,c=this._audioMetadata,h=this._audioTrack;if(c||(!1===this._hasAudio&&!1===this._hasAudioFlagOverrided&&(this._hasAudio=!0,this._mediaInfo.hasAudio=!0),(c=this._audioMetadata={}).type="audio",c.id=h.id,c.timescale=this._timescale,c.duration=this._duration,c.audioSampleRate=s,c.channelCount=0===_?1:2),10===o){var l=this._parseAACAudioData(e,t+1,i-1);if(null==l)return;if(0===l.packetType){if(c.config){if(b(l.data.config,c.config))return;r.a.w(this.TAG,"AudioSpecificConfig has been changed, re-generate initialization segment")}var u=l.data;c.audioSampleRate=u.samplingRate,c.channelCount=u.channelCount,c.codec=u.codec,c.originalCodec=u.originalCodec,c.config=u.config,c.refSampleDuration=1024/c.audioSampleRate*c.timescale,r.a.v(this.TAG,"Parsed AudioSpecificConfig"),this._isInitialMetadataDispatched()?this._dispatch&&(this._audioTrack.length||this._videoTrack.length)&&this._onDataAvailable(this._audioTrack,this._videoTrack):this._audioInitialMetadataDispatched=!0,this._dispatch=!1,this._onTrackMetadata("audio",c),(g=this._mediaInfo).audioCodec=c.originalCodec,g.audioSampleRate=c.audioSampleRate,g.audioChannelCount=c.channelCount,g.hasVideo?null!=g.videoCodec&&(g.mimeType='video/x-flv; codecs="'+g.videoCodec+","+g.audioCodec+'"'):g.mimeType='video/x-flv; codecs="'+g.audioCodec+'"',g.isComplete()&&this._onMediaInfo(g)}else if(1===l.packetType){var f=this._timestampBase+n,p={unit:l.data,length:l.data.byteLength,dts:f,pts:f};h.samples.push(p),h.length+=l.data.length}else r.a.e(this.TAG,"Flv: Unsupported AAC data type ".concat(l.packetType))}else if(2===o){if(!c.codec){var g;if(null==(u=this._parseMP3AudioData(e,t+1,i-1,!0)))return;c.audioSampleRate=u.samplingRate,c.channelCount=u.channelCount,c.codec=u.codec,c.originalCodec=u.originalCodec,c.refSampleDuration=1152/c.audioSampleRate*c.timescale,r.a.v(this.TAG,"Parsed MPEG Audio Frame Header"),this._audioInitialMetadataDispatched=!0,this._onTrackMetadata("audio",c),(g=this._mediaInfo).audioCodec=c.codec,g.audioSampleRate=c.audioSampleRate,g.audioChannelCount=c.channelCount,g.audioDataRate=u.bitRate,g.hasVideo?null!=g.videoCodec&&(g.mimeType='video/x-flv; codecs="'+g.videoCodec+","+g.audioCodec+'"'):g.mimeType='video/x-flv; codecs="'+g.audioCodec+'"',g.isComplete()&&this._onMediaInfo(g)}var y=this._parseMP3AudioData(e,t+1,i-1,!1);if(null==y)return;f=this._timestampBase+n;var v={unit:y,length:y.byteLength,dts:f,pts:f};h.samples.push(v),h.length+=y.length}}else this._onError(m.a.FORMAT_ERROR,"Flv: Invalid audio sample rate idx: "+d)}else this._onError(m.a.CODEC_UNSUPPORTED,"Flv: Unsupported audio codec idx: "+o)}},e.prototype._parseAACAudioData=function(e,t,i){if(!(i<=1)){var n={},a=new Uint8Array(e,t,i);return n.packetType=a[0],0===a[0]?n.data=this._parseAACAudioSpecificConfig(e,t+1,i-1):n.data=a.subarray(1),n}r.a.w(this.TAG,"Flv: Invalid AAC packet, missing AACPacketType or/and Data!")},e.prototype._parseAACAudioSpecificConfig=function(e,t,i){var n,a,r=new Uint8Array(e,t,i),o=null,s=0,d=null;if(s=n=r[0]>>>3,(a=(7&r[0])<<1|r[1]>>>7)<0||a>=this._mpegSamplingRates.length)this._onError(m.a.FORMAT_ERROR,"Flv: AAC invalid sampling frequency index!");else{var _=this._mpegSamplingRates[a],c=(120&r[1])>>>3;if(!(c<0||c>=8)){5===s&&(d=(7&r[1])<<1|r[2]>>>7,(124&r[2])>>>2);var h=self.navigator.userAgent.toLowerCase();return-1!==h.indexOf("firefox")?a>=6?(s=5,o=new Array(4),d=a-3):(s=2,o=new Array(2),d=a):-1!==h.indexOf("android")?(s=2,o=new Array(2),d=a):(s=5,d=a,o=new Array(4),a>=6?d=a-3:1===c&&(s=2,o=new Array(2),d=a)),o[0]=s<<3,o[0]|=(15&a)>>>1,o[1]=(15&a)<<7,o[1]|=(15&c)<<3,5===s&&(o[1]|=(15&d)>>>1,o[2]=(1&d)<<7,o[2]|=8,o[3]=0),{config:o,samplingRate:_,channelCount:c,codec:"mp4a.40."+s,originalCodec:"mp4a.40."+n}}this._onError(m.a.FORMAT_ERROR,"Flv: AAC invalid channel configuration")}},e.prototype._parseMP3AudioData=function(e,t,i,n){if(!(i<4)){this._littleEndian;var a=new Uint8Array(e,t,i),o=null;if(n){if(255!==a[0])return;var s=a[1]>>>3&3,d=(6&a[1])>>1,_=(240&a[2])>>>4,c=(12&a[2])>>>2,h=3!==(a[3]>>>6&3)?2:1,l=0,u=0;switch(s){case 0:l=this._mpegAudioV25SampleRateTable[c];break;case 2:l=this._mpegAudioV20SampleRateTable[c];break;case 3:l=this._mpegAudioV10SampleRateTable[c]}switch(d){case 1:34,_<this._mpegAudioL3BitRateTable.length&&(u=this._mpegAudioL3BitRateTable[_]);break;case 2:33,_<this._mpegAudioL2BitRateTable.length&&(u=this._mpegAudioL2BitRateTable[_]);break;case 3:32,_<this._mpegAudioL1BitRateTable.length&&(u=this._mpegAudioL1BitRateTable[_])}o={bitRate:u,samplingRate:l,channelCount:h,codec:"mp3",originalCodec:"mp3"}}else o=a;return o}r.a.w(this.TAG,"Flv: Invalid MP3 packet, header missing!")},e.prototype._parseVideoData=function(e,t,i,n,a){if(i<=1)r.a.w(this.TAG,"Flv: Invalid video packet, missing VideoData payload!");else if(!0!==this._hasVideoFlagOverrided||!1!==this._hasVideo){var o=new Uint8Array(e,t,i)[0],s=(112&o)>>>4;if(0!=(128&o)){var d=15&o,_=String.fromCharCode.apply(String,new Uint8Array(e,t,i).slice(1,5));if("hvc1"===_)this._parseEnhancedHEVCVideoPacket(e,t+5,i-5,n,a,s,d);else{if("av01"!==_)return void this._onError(m.a.CODEC_UNSUPPORTED,"Flv: Unsupported codec in video frame: ".concat(_));this._parseEnhancedAV1VideoPacket(e,t+5,i-5,n,a,s,d)}}else{var c=15&o;if(7===c)this._parseAVCVideoPacket(e,t+1,i-1,n,a,s);else{if(12!==c)return void this._onError(m.a.CODEC_UNSUPPORTED,"Flv: Unsupported codec in video frame: ".concat(c));this._parseHEVCVideoPacket(e,t+1,i-1,n,a,s)}}}},e.prototype._parseAVCVideoPacket=function(e,t,i,n,a,o){if(i<4)r.a.w(this.TAG,"Flv: Invalid AVC packet, missing AVCPacketType or/and CompositionTime");else{var s=this._littleEndian,d=new DataView(e,t,i),_=d.getUint8(0),c=(16777215&d.getUint32(0,!s))<<8>>8;if(0===_)this._parseAVCDecoderConfigurationRecord(e,t+4,i-4);else if(1===_)this._parseAVCVideoData(e,t+4,i-4,n,a,o,c);else if(2!==_)return void this._onError(m.a.FORMAT_ERROR,"Flv: Invalid video packet type ".concat(_))}},e.prototype._parseHEVCVideoPacket=function(e,t,i,n,a,o){if(i<4)r.a.w(this.TAG,"Flv: Invalid HEVC packet, missing HEVCPacketType or/and CompositionTime");else{var s=this._littleEndian,d=new DataView(e,t,i),_=d.getUint8(0),c=(16777215&d.getUint32(0,!s))<<8>>8;if(0===_)this._parseHEVCDecoderConfigurationRecord(e,t+4,i-4);else if(1===_)this._parseHEVCVideoData(e,t+4,i-4,n,a,o,c);else if(2!==_)return void this._onError(m.a.FORMAT_ERROR,"Flv: Invalid video packet type ".concat(_))}},e.prototype._parseEnhancedHEVCVideoPacket=function(e,t,i,n,a,r,o){var s=this._littleEndian,d=new DataView(e,t,i);if(0===o)this._parseHEVCDecoderConfigurationRecord(e,t,i);else if(1===o){var _=(4294967040&d.getUint32(0,!s))>>8;this._parseHEVCVideoData(e,t+3,i-3,n,a,r,_)}else if(3===o)this._parseHEVCVideoData(e,t,i,n,a,r,0);else if(2!==o)return void this._onError(m.a.FORMAT_ERROR,"Flv: Invalid video packet type ".concat(o))},e.prototype._parseEnhancedAV1VideoPacket=function(e,t,i,n,a,r,o){this._littleEndian,new DataView(e,t,i);if(0===o)this._parseAV1CodecConfigurationRecord(e,t,i);else if(1===o)this._parseAV1VideoData(e,t,i,n,a,r,0);else{if(5===o)return void this._onError(m.a.FORMAT_ERROR,"Flv: Not Supported MP2T AV1 video packet type ".concat(o));if(2!==o)return void this._onError(m.a.FORMAT_ERROR,"Flv: Invalid video packet type ".concat(o))}},e.prototype._parseAVCDecoderConfigurationRecord=function(e,t,i){if(i<7)r.a.w(this.TAG,"Flv: Invalid AVCDecoderConfigurationRecord, lack of data!");else{var n=this._videoMetadata,a=this._videoTrack,o=this._littleEndian,s=new DataView(e,t,i);if(n){if(void 0!==n.avcc){var d=new Uint8Array(e,t,i);if(b(d,n.avcc))return;r.a.w(this.TAG,"AVCDecoderConfigurationRecord has been changed, re-generate initialization segment")}}else!1===this._hasVideo&&!1===this._hasVideoFlagOverrided&&(this._hasVideo=!0,this._mediaInfo.hasVideo=!0),(n=this._videoMetadata={}).type="video",n.id=a.id,n.timescale=this._timescale,n.duration=this._duration;var _=s.getUint8(0),c=s.getUint8(1);s.getUint8(2),s.getUint8(3);if(1===_&&0!==c)if(this._naluLengthSize=1+(3&s.getUint8(4)),3===this._naluLengthSize||4===this._naluLengthSize){var h=31&s.getUint8(5);if(0!==h){h>1&&r.a.w(this.TAG,"Flv: Strange AVCDecoderConfigurationRecord: SPS Count = ".concat(h));for(var l=6,u=0;u<h;u++){var f=s.getUint16(l,!o);if(l+=2,0!==f){var g=new Uint8Array(e,t+l,f);l+=f;var y=p.parseSPS(g);if(0===u){n.codecWidth=y.codec_size.width,n.codecHeight=y.codec_size.height,n.presentWidth=y.present_size.width,n.presentHeight=y.present_size.height,n.profile=y.profile_string,n.level=y.level_string,n.bitDepth=y.bit_depth,n.chromaFormat=y.chroma_format,n.sarRatio=y.sar_ratio,n.frameRate=y.frame_rate,!1!==y.frame_rate.fixed&&0!==y.frame_rate.fps_num&&0!==y.frame_rate.fps_den||(n.frameRate=this._referenceFrameRate);var v=n.frameRate.fps_den,S=n.frameRate.fps_num;n.refSampleDuration=n.timescale*(v/S);for(var E=g.subarray(1,4),A="avc1.",R=0;R<3;R++){var T=E[R].toString(16);T.length<2&&(T="0"+T),A+=T}n.codec=A;var L=this._mediaInfo;L.width=n.codecWidth,L.height=n.codecHeight,L.fps=n.frameRate.fps,L.profile=n.profile,L.level=n.level,L.refFrames=y.ref_frames,L.chromaFormat=y.chroma_format_string,L.sarNum=n.sarRatio.width,L.sarDen=n.sarRatio.height,L.videoCodec=A,L.hasAudio?null!=L.audioCodec&&(L.mimeType='video/x-flv; codecs="'+L.videoCodec+","+L.audioCodec+'"'):L.mimeType='video/x-flv; codecs="'+L.videoCodec+'"',L.isComplete()&&this._onMediaInfo(L)}}}var k=s.getUint8(l);if(0!==k){k>1&&r.a.w(this.TAG,"Flv: Strange AVCDecoderConfigurationRecord: PPS Count = ".concat(k)),l++;for(u=0;u<k;u++){f=s.getUint16(l,!o);l+=2,0!==f&&(l+=f)}n.avcc=new Uint8Array(i),n.avcc.set(new Uint8Array(e,t,i),0),r.a.v(this.TAG,"Parsed AVCDecoderConfigurationRecord"),this._isInitialMetadataDispatched()?this._dispatch&&(this._audioTrack.length||this._videoTrack.length)&&this._onDataAvailable(this._audioTrack,this._videoTrack):this._videoInitialMetadataDispatched=!0,this._dispatch=!1,this._onTrackMetadata("video",n)}else this._onError(m.a.FORMAT_ERROR,"Flv: Invalid AVCDecoderConfigurationRecord: No PPS")}else this._onError(m.a.FORMAT_ERROR,"Flv: Invalid AVCDecoderConfigurationRecord: No SPS")}else this._onError(m.a.FORMAT_ERROR,"Flv: Strange NaluLengthSizeMinusOne: ".concat(this._naluLengthSize-1));else this._onError(m.a.FORMAT_ERROR,"Flv: Invalid AVCDecoderConfigurationRecord")}},e.prototype._parseHEVCDecoderConfigurationRecord=function(e,t,i){if(i<22)r.a.w(this.TAG,"Flv: Invalid HEVCDecoderConfigurationRecord, lack of data!");else{var n=this._videoMetadata,a=this._videoTrack,o=this._littleEndian,s=new DataView(e,t,i);if(n){if(void 0!==n.hvcc){var d=new Uint8Array(e,t,i);if(b(d,n.hvcc))return;r.a.w(this.TAG,"HEVCDecoderConfigurationRecord has been changed, re-generate initialization segment")}}else!1===this._hasVideo&&!1===this._hasVideoFlagOverrided&&(this._hasVideo=!0,this._mediaInfo.hasVideo=!0),(n=this._videoMetadata={}).type="video",n.id=a.id,n.timescale=this._timescale,n.duration=this._duration;var _=s.getUint8(0),c=31&s.getUint8(1);if(1===_&&0!==c)if(this._naluLengthSize=1+(3&s.getUint8(21)),3===this._naluLengthSize||4===this._naluLengthSize){for(var h=s.getUint8(22),l=0,u=23;l<h;l++){var f=63&s.getUint8(u+0),p=s.getUint16(u+1,!o);u+=3;for(var y=0;y<p;y++){var v=s.getUint16(u+0,!o);if(0===y)if(33===f){u+=2;var S=new Uint8Array(e,t+u,v),E=g.parseSPS(S);n.codecWidth=E.codec_size.width,n.codecHeight=E.codec_size.height,n.presentWidth=E.present_size.width,n.presentHeight=E.present_size.height,n.profile=E.profile_string,n.level=E.level_string,n.bitDepth=E.bit_depth,n.chromaFormat=E.chroma_format,n.sarRatio=E.sar_ratio,n.frameRate=E.frame_rate,!1!==E.frame_rate.fixed&&0!==E.frame_rate.fps_num&&0!==E.frame_rate.fps_den||(n.frameRate=this._referenceFrameRate);var A=n.frameRate.fps_den,R=n.frameRate.fps_num;n.refSampleDuration=n.timescale*(A/R),n.codec=E.codec_mimetype;var T=this._mediaInfo;T.width=n.codecWidth,T.height=n.codecHeight,T.fps=n.frameRate.fps,T.profile=n.profile,T.level=n.level,T.refFrames=E.ref_frames,T.chromaFormat=E.chroma_format_string,T.sarNum=n.sarRatio.width,T.sarDen=n.sarRatio.height,T.videoCodec=E.codec_mimetype,T.hasAudio?null!=T.audioCodec&&(T.mimeType='video/x-flv; codecs="'+T.videoCodec+","+T.audioCodec+'"'):T.mimeType='video/x-flv; codecs="'+T.videoCodec+'"',T.isComplete()&&this._onMediaInfo(T),u+=v}else u+=2+v;else u+=2+v}}n.hvcc=new Uint8Array(i),n.hvcc.set(new Uint8Array(e,t,i),0),r.a.v(this.TAG,"Parsed HEVCDecoderConfigurationRecord"),this._isInitialMetadataDispatched()?this._dispatch&&(this._audioTrack.length||this._videoTrack.length)&&this._onDataAvailable(this._audioTrack,this._videoTrack):this._videoInitialMetadataDispatched=!0,this._dispatch=!1,this._onTrackMetadata("video",n)}else this._onError(m.a.FORMAT_ERROR,"Flv: Strange NaluLengthSizeMinusOne: ".concat(this._naluLengthSize-1));else this._onError(m.a.FORMAT_ERROR,"Flv: Invalid HEVCDecoderConfigurationRecord")}},e.prototype._parseAV1CodecConfigurationRecord=function(e,t,i){if(i<4)r.a.w(this.TAG,"Flv: Invalid AV1CodecConfigurationRecord, lack of data!");else{var n=this._videoMetadata,a=this._videoTrack,o=(this._littleEndian,new DataView(e,t,i));n?void 0!==n.av1c&&r.a.w(this.TAG,"Found another AV1CodecConfigurationRecord!"):(!1===this._hasVideo&&!1===this._hasVideoFlagOverrided&&(this._hasVideo=!0,this._mediaInfo.hasVideo=!0),(n=this._videoMetadata={}).type="video",n.id=a.id,n.timescale=this._timescale,n.duration=this._duration);var s=127&o.getUint8(0);o.getUint8(1),o.getUint8(1),o.getUint8(2);if(1===s){var d=E.parseOBUs(new Uint8Array(e,t+4,i-4));if(null!=d){n.codecWidth=d.codec_size.width,n.codecHeight=d.codec_size.height,n.presentWidth=d.present_size.width,n.presentHeight=d.present_size.height,n.profile=d.profile_string,n.level=d.level_string,n.bitDepth=d.bit_depth,n.chromaFormat=d.chroma_format,n.sarRatio=d.sar_ratio,n.frameRate=d.frame_rate,!1!==d.frame_rate.fixed&&0!==d.frame_rate.fps_num&&0!==d.frame_rate.fps_den||(n.frameRate=this._referenceFrameRate);var _=n.frameRate.fps_den,c=n.frameRate.fps_num;n.refSampleDuration=n.timescale*(_/c),n.codec=d.codec_mimetype;var h=this._mediaInfo;h.width=n.codecWidth,h.height=n.codecHeight,h.fps=n.frameRate.fps,h.profile=n.profile,h.level=n.level,h.refFrames=d.ref_frames,h.chromaFormat=d.chroma_format_string,h.sarNum=n.sarRatio.width,h.sarDen=n.sarRatio.height,h.videoCodec=d.codec_mimetype,h.hasAudio?null!=h.audioCodec&&(h.mimeType='video/x-flv; codecs="'+h.videoCodec+","+h.audioCodec+'"'):h.mimeType='video/x-flv; codecs="'+h.videoCodec+'"',h.isComplete()&&this._onMediaInfo(h),n.av1c=new Uint8Array(i),n.av1c.set(new Uint8Array(e,t,i),0),r.a.v(this.TAG,"Parsed AV1CodecConfigurationRecord"),this._isInitialMetadataDispatched()?this._dispatch&&(this._audioTrack.length||this._videoTrack.length)&&this._onDataAvailable(this._audioTrack,this._videoTrack):this._videoInitialMetadataDispatched=!0,this._dispatch=!1,this._onTrackMetadata("video",n)}else this._onError(m.a.FORMAT_ERROR,"Flv: Invalid AV1CodecConfigurationRecord")}else this._onError(m.a.FORMAT_ERROR,"Flv: Invalid AV1CodecConfigurationRecord")}},e.prototype._parseAVCVideoData=function(e,t,i,n,a,o,s){for(var d=this._littleEndian,_=new DataView(e,t,i),c=[],h=0,l=0,u=this._naluLengthSize,f=this._timestampBase+n,p=1===o;l<i;){if(l+4>=i){r.a.w(this.TAG,"Malformed Nalu near timestamp ".concat(f,", offset = ").concat(l,", dataSize = ").concat(i));break}var m=_.getUint32(l,!d);if(3===u&&(m>>>=8),m>i-u)return void r.a.w(this.TAG,"Malformed Nalus near timestamp ".concat(f,", NaluSize > DataSize!"));var g=31&_.getUint8(l+u);5===g&&(p=!0);var y=new Uint8Array(e,t+l,u+m),v={type:g,data:y};c.push(v),h+=y.byteLength,l+=u+m}if(c.length){var S=this._videoTrack,b={units:c,length:h,isKeyframe:p,dts:f,cts:s,pts:f+s};p&&(b.fileposition=a),S.samples.push(b),S.length+=h}},e.prototype._parseHEVCVideoData=function(e,t,i,n,a,o,s){for(var d=this._littleEndian,_=new DataView(e,t,i),c=[],h=0,l=0,u=this._naluLengthSize,f=this._timestampBase+n,p=1===o;l<i;){if(l+4>=i){r.a.w(this.TAG,"Malformed Nalu near timestamp ".concat(f,", offset = ").concat(l,", dataSize = ").concat(i));break}var m=_.getUint32(l,!d);if(3===u&&(m>>>=8),m>i-u)return void r.a.w(this.TAG,"Malformed Nalus near timestamp ".concat(f,", NaluSize > DataSize!"));var g=31&_.getUint8(l+u);19!==g&&20!==g||(p=!0);var y=new Uint8Array(e,t+l,u+m),v={type:g,data:y};c.push(v),h+=y.byteLength,l+=u+m}if(c.length){var S=this._videoTrack,b={units:c,length:h,isKeyframe:p,dts:f,cts:s,pts:f+s};p&&(b.fileposition=a),S.samples.push(b),S.length+=h}},e.prototype._parseAV1VideoData=function(e,t,i,n,a,r,o){this._littleEndian,new DataView(e,t,i);var s,d=[],_=this._timestampBase+n,c=1===r;if(s=i,d.push({unitType:0,data:new Uint8Array(e,t+0,i)}),d.length){var h=this._videoTrack,l={units:d,length:s,isKeyframe:c,dts:_,cts:o,pts:_+o};c&&(l.fileposition=a),h.samples.push(l),h.length+=s}},e}(),T=function(){function e(){}return e.prototype.destroy=function(){this.onError=null,this.onMediaInfo=null,this.onMetaDataArrived=null,this.onTrackMetadata=null,this.onDataAvailable=null,this.onTimedID3Metadata=null,this.onSynchronousKLVMetadata=null,this.onAsynchronousKLVMetadata=null,this.onSMPTE2038Metadata=null,this.onSCTE35Metadata=null,this.onPESPrivateData=null,this.onPESPrivateDataDescriptor=null},e}(),L=function(){this.program_pmt_pid={}};!function(e){e[e.kMPEG1Audio=3]="kMPEG1Audio",e[e.kMPEG2Audio=4]="kMPEG2Audio",e[e.kPESPrivateData=6]="kPESPrivateData",e[e.kADTSAAC=15]="kADTSAAC",e[e.kLOASAAC=17]="kLOASAAC",e[e.kAC3=129]="kAC3",e[e.kEAC3=135]="kEAC3",e[e.kMetadata=21]="kMetadata",e[e.kSCTE35=134]="kSCTE35",e[e.kH264=27]="kH264",e[e.kH265=36]="kH265"}(A||(A={}));var k,w=function(){this.pid_stream_type={},this.common_pids={h264:void 0,h265:void 0,adts_aac:void 0,loas_aac:void 0,opus:void 0,ac3:void 0,eac3:void 0,mp3:void 0},this.pes_private_data_pids={},this.timed_id3_pids={},this.synchronous_klv_pids={},this.asynchronous_klv_pids={},this.scte_35_pids={},this.smpte2038_pids={}},D=function(){},M=function(){},C=function(){this.slices=[],this.total_length=0,this.expected_length=0,this.file_position=0};!function(e){e[e.kUnspecified=0]="kUnspecified",e[e.kSliceNonIDR=1]="kSliceNonIDR",e[e.kSliceDPA=2]="kSliceDPA",e[e.kSliceDPB=3]="kSliceDPB",e[e.kSliceDPC=4]="kSliceDPC",e[e.kSliceIDR=5]="kSliceIDR",e[e.kSliceSEI=6]="kSliceSEI",e[e.kSliceSPS=7]="kSliceSPS",e[e.kSlicePPS=8]="kSlicePPS",e[e.kSliceAUD=9]="kSliceAUD",e[e.kEndOfSequence=10]="kEndOfSequence",e[e.kEndOfStream=11]="kEndOfStream",e[e.kFiller=12]="kFiller",e[e.kSPSExt=13]="kSPSExt",e[e.kReserved0=14]="kReserved0"}(k||(k={}));var O,B,I=function(){},P=function(e){var t=e.data.byteLength;this.type=e.type,this.data=new Uint8Array(4+t),new DataView(this.data.buffer).setUint32(0,t),this.data.set(e.data,4)},x=function(){function e(e){this.TAG="H264AnnexBParser",this.current_startcode_offset_=0,this.eof_flag_=!1,this.data_=e,this.current_startcode_offset_=this.findNextStartCodeOffset(0),this.eof_flag_&&r.a.e(this.TAG,"Could not find H264 startcode until payload end!")}return e.prototype.findNextStartCodeOffset=function(e){for(var t=e,i=this.data_;;){if(t+3>=i.byteLength)return this.eof_flag_=!0,i.byteLength;var n=i[t+0]<<24|i[t+1]<<16|i[t+2]<<8|i[t+3],a=i[t+0]<<16|i[t+1]<<8|i[t+2];if(1===n||1===a)return t;t++}},e.prototype.readNextNaluPayload=function(){for(var e=this.data_,t=null;null==t&&!this.eof_flag_;){var i=this.current_startcode_offset_,n=31&e[i+=1===(e[i]<<24|e[i+1]<<16|e[i+2]<<8|e[i+3])?4:3],a=(128&e[i])>>>7,r=this.findNextStartCodeOffset(i);if(this.current_startcode_offset_=r,!(n>=k.kReserved0)&&0===a){var o=e.subarray(i,r);(t=new I).type=n,t.data=o}}return t},e}(),U=function(){function e(e,t,i){var n=8+e.byteLength+1+2+t.byteLength,a=!1;66!==e[3]&&77!==e[3]&&88!==e[3]&&(a=!0,n+=4);var r=this.data=new Uint8Array(n);r[0]=1,r[1]=e[1],r[2]=e[2],r[3]=e[3],r[4]=255,r[5]=225;var o=e.byteLength;r[6]=o>>>8,r[7]=255&o;var s=8;r.set(e,8),r[s+=o]=1;var d=t.byteLength;r[s+1]=d>>>8,r[s+2]=255&d,r.set(t,s+3),s+=3+d,a&&(r[s]=252|i.chroma_format_idc,r[s+1]=248|i.bit_depth_luma-8,r[s+2]=248|i.bit_depth_chroma-8,r[s+3]=0,s+=4)}return e.prototype.getData=function(){return this.data},e}();!function(e){e[e.kNull=0]="kNull",e[e.kAACMain=1]="kAACMain",e[e.kAAC_LC=2]="kAAC_LC",e[e.kAAC_SSR=3]="kAAC_SSR",e[e.kAAC_LTP=4]="kAAC_LTP",e[e.kAAC_SBR=5]="kAAC_SBR",e[e.kAAC_Scalable=6]="kAAC_Scalable",e[e.kLayer1=32]="kLayer1",e[e.kLayer2=33]="kLayer2",e[e.kLayer3=34]="kLayer3"}(O||(O={})),function(e){e[e.k96000Hz=0]="k96000Hz",e[e.k88200Hz=1]="k88200Hz",e[e.k64000Hz=2]="k64000Hz",e[e.k48000Hz=3]="k48000Hz",e[e.k44100Hz=4]="k44100Hz",e[e.k32000Hz=5]="k32000Hz",e[e.k24000Hz=6]="k24000Hz",e[e.k22050Hz=7]="k22050Hz",e[e.k16000Hz=8]="k16000Hz",e[e.k12000Hz=9]="k12000Hz",e[e.k11025Hz=10]="k11025Hz",e[e.k8000Hz=11]="k8000Hz",e[e.k7350Hz=12]="k7350Hz"}(B||(B={}));var N,V,G=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],F=(N=function(e,t){return(N=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}N(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),j=function(){},z=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return F(t,e),t}(j),H=function(){function e(e){this.TAG="AACADTSParser",this.data_=e,this.current_syncword_offset_=this.findNextSyncwordOffset(0),this.eof_flag_&&r.a.e(this.TAG,"Could not found ADTS syncword until payload end")}return e.prototype.findNextSyncwordOffset=function(e){for(var t=e,i=this.data_;;){if(t+7>=i.byteLength)return this.eof_flag_=!0,i.byteLength;if(4095===(i[t+0]<<8|i[t+1])>>>4)return t;t++}},e.prototype.readNextAACFrame=function(){for(var e=this.data_,t=null;null==t&&!this.eof_flag_;){var i=this.current_syncword_offset_,n=(8&e[i+1])>>>3,a=(6&e[i+1])>>>1,r=1&e[i+1],o=(192&e[i+2])>>>6,s=(60&e[i+2])>>>2,d=(1&e[i+2])<<2|(192&e[i+3])>>>6,_=(3&e[i+3])<<11|e[i+4]<<3|(224&e[i+5])>>>5;e[i+6];if(i+_>this.data_.byteLength){this.eof_flag_=!0,this.has_last_incomplete_data=!0;break}var c=1===r?7:9,h=_-c;i+=c;var l=this.findNextSyncwordOffset(i+h);if(this.current_syncword_offset_=l,(0===n||1===n)&&0===a){var u=e.subarray(i,i+h);(t=new j).audio_object_type=o+1,t.sampling_freq_index=s,t.sampling_frequency=G[s],t.channel_config=d,t.data=u}}return t},e.prototype.hasIncompleteData=function(){return this.has_last_incomplete_data},e.prototype.getIncompleteData=function(){return this.has_last_incomplete_data?this.data_.subarray(this.current_syncword_offset_):null},e}(),q=function(){function e(e){this.TAG="AACLOASParser",this.data_=e,this.current_syncword_offset_=this.findNextSyncwordOffset(0),this.eof_flag_&&r.a.e(this.TAG,"Could not found LOAS syncword until payload end")}return e.prototype.findNextSyncwordOffset=function(e){for(var t=e,i=this.data_;;){if(t+1>=i.byteLength)return this.eof_flag_=!0,i.byteLength;if(695===(i[t+0]<<3|i[t+1]>>>5))return t;t++}},e.prototype.getLATMValue=function(e){for(var t=e.readBits(2),i=0,n=0;n<=t;n++)i<<=8,i|=e.readByte();return i},e.prototype.readNextAACFrame=function(e){for(var t=this.data_,i=null;null==i&&!this.eof_flag_;){var n=this.current_syncword_offset_,a=(31&t[n+1])<<8|t[n+2];if(n+3+a>=this.data_.byteLength){this.eof_flag_=!0,this.has_last_incomplete_data=!0;break}var o=new f(t.subarray(n+3,n+3+a)),s=null;if(o.readBool()){if(null==e){r.a.w(this.TAG,"StreamMuxConfig Missing"),this.current_syncword_offset_=this.findNextSyncwordOffset(n+3+a),o.destroy();continue}s=e}else{var d=o.readBool();if(d&&o.readBool()){r.a.e(this.TAG,"audioMuxVersionA is Not Supported"),o.destroy();break}if(d&&this.getLATMValue(o),!o.readBool()){r.a.e(this.TAG,"allStreamsSameTimeFraming zero is Not Supported"),o.destroy();break}if(0!==o.readBits(6)){r.a.e(this.TAG,"more than 2 numSubFrames Not Supported"),o.destroy();break}if(0!==o.readBits(4)){r.a.e(this.TAG,"more than 2 numProgram Not Supported"),o.destroy();break}if(0!==o.readBits(3)){r.a.e(this.TAG,"more than 2 numLayer Not Supported"),o.destroy();break}var _=d?this.getLATMValue(o):0,c=o.readBits(5);_-=5;var h=o.readBits(4);_-=4;var l=o.readBits(4);_-=4,o.readBits(3),(_-=3)>0&&o.readBits(_);var u=o.readBits(3);if(0!==u){r.a.e(this.TAG,"frameLengthType = ".concat(u,". Only frameLengthType = 0 Supported")),o.destroy();break}o.readByte();var p=o.readBool();if(p)if(d)this.getLATMValue(o);else{for(var m=0;;){m<<=8;var g=o.readBool();if(m+=o.readByte(),!g)break}console.log(m)}o.readBool()&&o.readByte(),(s=new z).audio_object_type=c,s.sampling_freq_index=h,s.sampling_frequency=G[s.sampling_freq_index],s.channel_config=l,s.other_data_present=p}for(var y=0;;){var v=o.readByte();if(y+=v,255!==v)break}for(var S=new Uint8Array(y),b=0;b<y;b++)S[b]=o.readByte();(i=new z).audio_object_type=s.audio_object_type,i.sampling_freq_index=s.sampling_freq_index,i.sampling_frequency=G[s.sampling_freq_index],i.channel_config=s.channel_config,i.other_data_present=s.other_data_present,i.data=S,this.current_syncword_offset_=this.findNextSyncwordOffset(n+3+a)}return i},e.prototype.hasIncompleteData=function(){return this.has_last_incomplete_data},e.prototype.getIncompleteData=function(){return this.has_last_incomplete_data?this.data_.subarray(this.current_syncword_offset_):null},e}(),K=function(e){var t=null,i=e.audio_object_type,n=e.audio_object_type,a=e.sampling_freq_index,r=e.channel_config,o=0,s=navigator.userAgent.toLowerCase();-1!==s.indexOf("firefox")?a>=6?(n=5,t=new Array(4),o=a-3):(n=2,t=new Array(2),o=a):-1!==s.indexOf("android")?(n=2,t=new Array(2),o=a):(n=5,o=a,t=new Array(4),a>=6?o=a-3:1===r&&(n=2,t=new Array(2),o=a)),t[0]=n<<3,t[0]|=(15&a)>>>1,t[1]=(15&a)<<7,t[1]|=(15&r)<<3,5===n&&(t[1]|=(15&o)>>>1,t[2]=(1&o)<<7,t[2]|=8,t[3]=0),this.config=t,this.sampling_rate=G[a],this.channel_count=r,this.codec_mimetype="mp4a.40."+n,this.original_codec_mimetype="mp4a.40."+i},W=function(){},Y=function(){};!function(e){e[e.kSpliceNull=0]="kSpliceNull",e[e.kSpliceSchedule=4]="kSpliceSchedule",e[e.kSpliceInsert=5]="kSpliceInsert",e[e.kTimeSignal=6]="kTimeSignal",e[e.kBandwidthReservation=7]="kBandwidthReservation",e[e.kPrivateCommand=255]="kPrivateCommand"}(V||(V={}));var X,Q=function(e){var t=e.readBool();return t?(e.readBits(6),{time_specified_flag:t,pts_time:4*e.readBits(31)+e.readBits(2)}):(e.readBits(7),{time_specified_flag:t})},J=function(e){var t=e.readBool();return e.readBits(6),{auto_return:t,duration:4*e.readBits(31)+e.readBits(2)}},Z=function(e,t){var i=t.readBits(8);return e?{component_tag:i}:{component_tag:i,splice_time:Q(t)}},$=function(e){return{component_tag:e.readBits(8),utc_splice_time:e.readBits(32)}},ee=function(e){var t=e.readBits(32),i=e.readBool();e.readBits(7);var n={splice_event_id:t,splice_event_cancel_indicator:i};if(i)return n;if(n.out_of_network_indicator=e.readBool(),n.program_splice_flag=e.readBool(),n.duration_flag=e.readBool(),e.readBits(5),n.program_splice_flag)n.utc_splice_time=e.readBits(32);else{n.component_count=e.readBits(8),n.components=[];for(var a=0;a<n.component_count;a++)n.components.push($(e))}return n.duration_flag&&(n.break_duration=J(e)),n.unique_program_id=e.readBits(16),n.avail_num=e.readBits(8),n.avails_expected=e.readBits(8),n},te=function(e,t,i,n){return{descriptor_tag:e,descriptor_length:t,identifier:i,provider_avail_id:n.readBits(32)}},ie=function(e,t,i,n){var a=n.readBits(8),r=n.readBits(3);n.readBits(5);for(var o="",s=0;s<r;s++)o+=String.fromCharCode(n.readBits(8));return{descriptor_tag:e,descriptor_length:t,identifier:i,preroll:a,dtmf_count:r,DTMF_char:o}},ne=function(e){var t=e.readBits(8);return e.readBits(7),{component_tag:t,pts_offset:4*e.readBits(31)+e.readBits(2)}},ae=function(e,t,i,n){var a=n.readBits(32),r=n.readBool();n.readBits(7);var o={descriptor_tag:e,descriptor_length:t,identifier:i,segmentation_event_id:a,segmentation_event_cancel_indicator:r};if(r)return o;if(o.program_segmentation_flag=n.readBool(),o.segmentation_duration_flag=n.readBool(),o.delivery_not_restricted_flag=n.readBool(),o.delivery_not_restricted_flag?n.readBits(5):(o.web_delivery_allowed_flag=n.readBool(),o.no_regional_blackout_flag=n.readBool(),o.archive_allowed_flag=n.readBool(),o.device_restrictions=n.readBits(2)),!o.program_segmentation_flag){o.component_count=n.readBits(8),o.components=[];for(var s=0;s<o.component_count;s++)o.components.push(ne(n))}o.segmentation_duration_flag&&(o.segmentation_duration=n.readBits(40)),o.segmentation_upid_type=n.readBits(8),o.segmentation_upid_length=n.readBits(8);var d=new Uint8Array(o.segmentation_upid_length);for(s=0;s<o.segmentation_upid_length;s++)d[s]=n.readBits(8);return o.segmentation_upid=d.buffer,o.segmentation_type_id=n.readBits(8),o.segment_num=n.readBits(8),o.segments_expected=n.readBits(8),52!==o.segmentation_type_id&&54!==o.segmentation_type_id&&56!==o.segmentation_type_id&&58!==o.segmentation_type_id||(o.sub_segment_num=n.readBits(8),o.sub_segments_expected=n.readBits(8)),o},re=function(e,t,i,n){return{descriptor_tag:e,descriptor_length:t,identifier:i,TAI_seconds:n.readBits(48),TAI_ns:n.readBits(32),UTC_offset:n.readBits(16)}},oe=function(e){return{component_tag:e.readBits(8),ISO_code:String.fromCharCode(e.readBits(8),e.readBits(8),e.readBits(8)),Bit_Stream_Mode:e.readBits(3),Num_Channels:e.readBits(4),Full_Srvc_Audio:e.readBool()}},se=function(e,t,i,n){for(var a=n.readBits(4),r=[],o=0;o<a;o++)r.push(oe(n));return{descriptor_tag:e,descriptor_length:t,identifier:i,audio_count:a,components:r}},de=function(e){var t=new f(e),i=t.readBits(8),n=t.readBool(),a=t.readBool();t.readBits(2);var r=t.readBits(12),o=t.readBits(8),s=t.readBool(),d=t.readBits(6),_=4*t.readBits(31)+t.readBits(2),c=t.readBits(8),h=t.readBits(12),l=t.readBits(12),u=t.readBits(8),p=null;u===V.kSpliceNull?p={}:u===V.kSpliceSchedule?p=function(e){for(var t=e.readBits(8),i=[],n=0;n<t;n++)i.push(ee(e));return{splice_count:t,events:i}}(t):u===V.kSpliceInsert?p=function(e){var t=e.readBits(32),i=e.readBool();e.readBits(7);var n={splice_event_id:t,splice_event_cancel_indicator:i};if(i)return n;if(n.out_of_network_indicator=e.readBool(),n.program_splice_flag=e.readBool(),n.duration_flag=e.readBool(),n.splice_immediate_flag=e.readBool(),e.readBits(4),n.program_splice_flag&&!n.splice_immediate_flag&&(n.splice_time=Q(e)),!n.program_splice_flag){n.component_count=e.readBits(8),n.components=[];for(var a=0;a<n.component_count;a++)n.components.push(Z(n.splice_immediate_flag,e))}return n.duration_flag&&(n.break_duration=J(e)),n.unique_program_id=e.readBits(16),n.avail_num=e.readBits(8),n.avails_expected=e.readBits(8),n}(t):u===V.kTimeSignal?p=function(e){return{splice_time:Q(e)}}(t):u===V.kBandwidthReservation?p={}:u===V.kPrivateCommand?p=function(e,t){for(var i=String.fromCharCode(t.readBits(8),t.readBits(8),t.readBits(8),t.readBits(8)),n=new Uint8Array(e-4),a=0;a<e-4;a++)n[a]=t.readBits(8);return{identifier:i,private_data:n.buffer}}(l,t):t.readBits(8*l);for(var m=[],g=t.readBits(16),y=0;y<g;){var v=t.readBits(8),S=t.readBits(8),b=String.fromCharCode(t.readBits(8),t.readBits(8),t.readBits(8),t.readBits(8));0===v?m.push(te(v,S,b,t)):1===v?m.push(ie(v,S,b,t)):2===v?m.push(ae(v,S,b,t)):3===v?m.push(re(v,S,b,t)):4===v?m.push(se(v,S,b,t)):t.readBits(8*(S-4)),y+=2+S}var E={table_id:i,section_syntax_indicator:n,private_indicator:a,section_length:r,protocol_version:o,encrypted_packet:s,encryption_algorithm:d,pts_adjustment:_,cw_index:c,tier:h,splice_command_length:l,splice_command_type:u,splice_command:p,descriptor_loop_length:g,splice_descriptors:m,E_CRC32:s?t.readBits(32):void 0,CRC32:t.readBits(32)};if(u===V.kSpliceInsert){var A=p;if(A.splice_event_cancel_indicator)return{splice_command_type:u,detail:E,data:e};if(A.program_splice_flag&&!A.splice_immediate_flag){var R=A.duration_flag?A.break_duration.auto_return:void 0,T=A.duration_flag?A.break_duration.duration/90:void 0;return A.splice_time.time_specified_flag?{splice_command_type:u,pts:(_+A.splice_time.pts_time)%Math.pow(2,33),auto_return:R,duraiton:T,detail:E,data:e}:{splice_command_type:u,auto_return:R,duraiton:T,detail:E,data:e}}return{splice_command_type:u,auto_return:R=A.duration_flag?A.break_duration.auto_return:void 0,duraiton:T=A.duration_flag?A.break_duration.duration/90:void 0,detail:E,data:e}}if(u===V.kTimeSignal){var L=p;return L.splice_time.time_specified_flag?{splice_command_type:u,pts:(_+L.splice_time.pts_time)%Math.pow(2,33),detail:E,data:e}:{splice_command_type:u,detail:E,data:e}}return{splice_command_type:u,detail:E,data:e}};!function(e){e[e.kSliceIDR_W_RADL=19]="kSliceIDR_W_RADL",e[e.kSliceIDR_N_LP=20]="kSliceIDR_N_LP",e[e.kSliceCRA_NUT=21]="kSliceCRA_NUT",e[e.kSliceVPS=32]="kSliceVPS",e[e.kSliceSPS=33]="kSliceSPS",e[e.kSlicePPS=34]="kSlicePPS",e[e.kSliceAUD=35]="kSliceAUD"}(X||(X={}));var _e=function(){},ce=function(e){var t=e.data.byteLength;this.type=e.type,this.data=new Uint8Array(4+t),new DataView(this.data.buffer).setUint32(0,t),this.data.set(e.data,4)},he=function(){function e(e){this.TAG="H265AnnexBParser",this.current_startcode_offset_=0,this.eof_flag_=!1,this.data_=e,this.current_startcode_offset_=this.findNextStartCodeOffset(0),this.eof_flag_&&r.a.e(this.TAG,"Could not find H265 startcode until payload end!")}return e.prototype.findNextStartCodeOffset=function(e){for(var t=e,i=this.data_;;){if(t+3>=i.byteLength)return this.eof_flag_=!0,i.byteLength;var n=i[t+0]<<24|i[t+1]<<16|i[t+2]<<8|i[t+3],a=i[t+0]<<16|i[t+1]<<8|i[t+2];if(1===n||1===a)return t;t++}},e.prototype.readNextNaluPayload=function(){for(var e=this.data_,t=null;null==t&&!this.eof_flag_;){var i=this.current_startcode_offset_,n=e[i+=1===(e[i]<<24|e[i+1]<<16|e[i+2]<<8|e[i+3])?4:3]>>1&63,a=(128&e[i])>>>7,r=this.findNextStartCodeOffset(i);if(this.current_startcode_offset_=r,0===a){var o=e.subarray(i,r);(t=new _e).type=n,t.data=o}}return t},e}(),le=function(){function e(e,t,i,n){var a=23+(5+e.byteLength)+(5+t.byteLength)+(5+i.byteLength),r=this.data=new Uint8Array(a);r[0]=1,r[1]=(3&n.general_profile_space)<<6|(n.general_tier_flag?1:0)<<5|31&n.general_profile_idc,r[2]=n.general_profile_compatibility_flags_1,r[3]=n.general_profile_compatibility_flags_2,r[4]=n.general_profile_compatibility_flags_3,r[5]=n.general_profile_compatibility_flags_4,r[6]=n.general_constraint_indicator_flags_1,r[7]=n.general_constraint_indicator_flags_2,r[8]=n.general_constraint_indicator_flags_3,r[9]=n.general_constraint_indicator_flags_4,r[10]=n.general_constraint_indicator_flags_5,r[11]=n.general_constraint_indicator_flags_6,r[12]=n.general_level_idc,r[13]=240|(3840&n.min_spatial_segmentation_idc)>>8,r[14]=255&n.min_spatial_segmentation_idc,r[15]=252|3&n.parallelismType,r[16]=252|3&n.chroma_format_idc,r[17]=248|7&n.bit_depth_luma_minus8,r[18]=248|7&n.bit_depth_chroma_minus8,r[19]=0,r[20]=0,r[21]=(3&n.constant_frame_rate)<<6|(7&n.num_temporal_layers)<<3|(n.temporal_id_nested?1:0)<<2|3,r[22]=3,r[23]=128|X.kSliceVPS,r[24]=0,r[25]=1,r[26]=(65280&e.byteLength)>>8,r[27]=(255&e.byteLength)>>0,r.set(e,28),r[23+(5+e.byteLength)+0]=128|X.kSliceSPS,r[23+(5+e.byteLength)+1]=0,r[23+(5+e.byteLength)+2]=1,r[23+(5+e.byteLength)+3]=(65280&t.byteLength)>>8,r[23+(5+e.byteLength)+4]=(255&t.byteLength)>>0,r.set(t,23+(5+e.byteLength)+5),r[23+(5+e.byteLength+5+t.byteLength)+0]=128|X.kSlicePPS,r[23+(5+e.byteLength+5+t.byteLength)+1]=0,r[23+(5+e.byteLength+5+t.byteLength)+2]=1,r[23+(5+e.byteLength+5+t.byteLength)+3]=(65280&i.byteLength)>>8,r[23+(5+e.byteLength+5+t.byteLength)+4]=(255&i.byteLength)>>0,r.set(i,23+(5+e.byteLength+5+t.byteLength)+5)}return e.prototype.getData=function(){return this.data},e}(),ue=function(){},fe=function(){},pe=function(){},me=[[64,64,80,80,96,96,112,112,128,128,160,160,192,192,224,224,256,256,320,320,384,384,448,448,512,512,640,640,768,768,896,896,1024,1024,1152,1152,1280,1280],[69,70,87,88,104,105,121,122,139,140,174,175,208,209,243,244,278,279,348,349,417,418,487,488,557,558,696,697,835,836,975,976,1114,1115,1253,1254,1393,1394],[96,96,120,120,144,144,168,168,192,192,240,240,288,288,336,336,384,384,480,480,576,576,672,672,768,768,960,960,1152,1152,1344,1344,1536,1536,1728,1728,1920,1920]],ge=function(){function e(e){this.TAG="AC3Parser",this.data_=e,this.current_syncword_offset_=this.findNextSyncwordOffset(0),this.eof_flag_&&r.a.e(this.TAG,"Could not found AC3 syncword until payload end")}return e.prototype.findNextSyncwordOffset=function(e){for(var t=e,i=this.data_;;){if(t+7>=i.byteLength)return this.eof_flag_=!0,i.byteLength;if(2935===(i[t+0]<<8|i[t+1]<<0))return t;t++}},e.prototype.readNextAC3Frame=function(){for(var e=this.data_,t=null;null==t&&!this.eof_flag_;){var i=this.current_syncword_offset_,n=e[i+4]>>6,a=[48e3,44200,33e3][n],r=63&e[i+4],o=2*me[n][r];if(i+o>this.data_.byteLength){this.eof_flag_=!0,this.has_last_incomplete_data=!0;break}var s=this.findNextSyncwordOffset(i+o);this.current_syncword_offset_=s;var d=e[i+5]>>3,_=7&e[i+5],c=e[i+6]>>5,h=0;0!=(1&c)&&1!==c&&(h+=2),0!=(4&c)&&(h+=2),2===c&&(h+=2);var l=(e[i+6]<<8|e[i+7]<<0)>>12-h&1,u=[2,1,2,3,3,4,4,5][c]+l;(t=new pe).sampling_frequency=a,t.channel_count=u,t.channel_mode=c,t.bit_stream_identification=d,t.low_frequency_effects_channel_on=l,t.bit_stream_mode=_,t.frame_size_code=r,t.data=e.subarray(i,i+o)}return t},e.prototype.hasIncompleteData=function(){return this.has_last_incomplete_data},e.prototype.getIncompleteData=function(){return this.has_last_incomplete_data?this.data_.subarray(this.current_syncword_offset_):null},e}(),ye=function(e){var t;t=[e.sampling_rate_code<<6|e.bit_stream_identification<<1|e.bit_stream_mode>>2,(3&e.bit_stream_mode)<<6|e.channel_mode<<3|e.low_frequency_effects_channel_on<<2|e.frame_size_code>>4,e.frame_size_code<<4&224],this.config=t,this.sampling_rate=e.sampling_frequency,this.bit_stream_identification=e.bit_stream_identification,this.bit_stream_mode=e.bit_stream_mode,this.low_frequency_effects_channel_on=e.low_frequency_effects_channel_on,this.channel_count=e.channel_count,this.channel_mode=e.channel_mode,this.codec_mimetype="ac-3",this.original_codec_mimetype="ac-3"},ve=function(){},Se=function(){function e(e){this.TAG="EAC3Parser",this.data_=e,this.current_syncword_offset_=this.findNextSyncwordOffset(0),this.eof_flag_&&r.a.e(this.TAG,"Could not found AC3 syncword until payload end")}return e.prototype.findNextSyncwordOffset=function(e){for(var t=e,i=this.data_;;){if(t+7>=i.byteLength)return this.eof_flag_=!0,i.byteLength;if(2935===(i[t+0]<<8|i[t+1]<<0))return t;t++}},e.prototype.readNextEAC3Frame=function(){for(var e=this.data_,t=null;null==t&&!this.eof_flag_;){var i=this.current_syncword_offset_,n=new f(e.subarray(i+2)),a=(n.readBits(2),n.readBits(3),n.readBits(11)+1<<1),r=n.readBits(2),o=null,s=null;3===r?(o=[24e3,22060,16e3][r=n.readBits(2)],s=3):(o=[48e3,44100,32e3][r],s=n.readBits(2));var d=n.readBits(3),_=n.readBits(1),c=n.readBits(5);if(i+a>this.data_.byteLength){this.eof_flag_=!0,this.has_last_incomplete_data=!0;break}var h=this.findNextSyncwordOffset(i+a);this.current_syncword_offset_=h;var l=[2,1,2,3,3,4,4,5][d]+_;n.destroy(),(t=new ve).sampling_frequency=o,t.channel_count=l,t.channel_mode=d,t.bit_stream_identification=c,t.low_frequency_effects_channel_on=_,t.frame_size=a,t.num_blks=[1,2,3,6][s],t.data=e.subarray(i,i+a)}return t},e.prototype.hasIncompleteData=function(){return this.has_last_incomplete_data},e.prototype.getIncompleteData=function(){return this.has_last_incomplete_data?this.data_.subarray(this.current_syncword_offset_):null},e}(),be=function(e){var t,i=Math.floor(e.frame_size*e.sampling_frequency/(16*e.num_blks));t=[255&i,248&i,e.sampling_rate_code<<6|e.bit_stream_identification<<1|0,0|e.channel_mode<<1|e.low_frequency_effects_channel_on<<0,0],this.config=t,this.sampling_rate=e.sampling_frequency,this.bit_stream_identification=e.bit_stream_identification,this.num_blks=e.num_blks,this.low_frequency_effects_channel_on=e.low_frequency_effects_channel_on,this.channel_count=e.channel_count,this.channel_mode=e.channel_mode,this.codec_mimetype="ec-3",this.original_codec_mimetype="ec-3"},Ee=function(){},Ae=function(){var e=function(t,i){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(t,i)};return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),Re=function(){return(Re=Object.assign||function(e){for(var t,i=1,n=arguments.length;i<n;i++)for(var a in t=arguments[i])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e}).apply(this,arguments)},Te=function(e){function t(t,i){var n=e.call(this)||this;return n.TAG="TSDemuxer",n.first_parse_=!0,n.media_info_=new s.a,n.timescale_=90,n.duration_=0,n.current_pmt_pid_=-1,n.program_pmt_map_={},n.pes_slice_queues_={},n.section_slice_queues_={},n.video_metadata_={vps:void 0,sps:void 0,pps:void 0,details:void 0},n.audio_metadata_={codec:void 0,audio_object_type:void 0,sampling_freq_index:void 0,sampling_frequency:void 0,channel_config:void 0},n.aac_last_sample_pts_=void 0,n.aac_last_incomplete_data_=null,n.has_video_=!1,n.has_audio_=!1,n.video_init_segment_dispatched_=!1,n.audio_init_segment_dispatched_=!1,n.video_metadata_changed_=!1,n.audio_metadata_changed_=!1,n.loas_previous_frame=null,n.video_track_={type:"video",id:1,sequenceNumber:0,samples:[],length:0},n.audio_track_={type:"audio",id:2,sequenceNumber:0,samples:[],length:0},n.ts_packet_size_=t.ts_packet_size,n.sync_offset_=t.sync_offset,n.config_=i,n}return Ae(t,e),t.prototype.destroy=function(){this.media_info_=null,this.pes_slice_queues_=null,this.section_slice_queues_=null,this.video_metadata_=null,this.audio_metadata_=null,this.aac_last_incomplete_data_=null,this.video_track_=null,this.audio_track_=null,e.prototype.destroy.call(this)},t.probe=function(e){var t=new Uint8Array(e),i=-1,n=188;if(t.byteLength<=3*n)return{needMoreData:!0};for(;-1===i;){for(var a=Math.min(1e3,t.byteLength-3*n),o=0;o<a;){if(71===t[o]&&71===t[o+n]&&71===t[o+2*n]){i=o;break}o++}if(-1===i)if(188===n)n=192;else{if(192!==n)break;n=204}}return-1===i?{match:!1}:(192===n&&i>=4?(r.a.v("TSDemuxer","ts_packet_size = 192, m2ts mode"),i-=4):204===n&&r.a.v("TSDemuxer","ts_packet_size = 204, RS encoded MPEG2-TS stream"),{match:!0,consumed:0,ts_packet_size:n,sync_offset:i})},t.prototype.bindDataSource=function(e){return e.onDataArrival=this.parseChunks.bind(this),this},t.prototype.resetMediaInfo=function(){this.media_info_=new s.a},t.prototype.parseChunks=function(e,t){if(!(this.onError&&this.onMediaInfo&&this.onTrackMetadata&&this.onDataAvailable))throw new h.a("onError & onMediaInfo & onTrackMetadata & onDataAvailable callback must be specified");var i=0;for(this.first_parse_&&(this.first_parse_=!1,i=this.sync_offset_);i+this.ts_packet_size_<=e.byteLength;){var n=t+i;192===this.ts_packet_size_&&(i+=4);var a=new Uint8Array(e,i,188),o=a[0];if(71!==o){r.a.e(this.TAG,"sync_byte = ".concat(o,", not 0x47"));break}var s=(64&a[1])>>>6,d=(a[1],(31&a[1])<<8|a[2]),_=(48&a[3])>>>4,c=15&a[3],l={},u=4;if(2==_||3==_){var f=a[4];if(5+f===188){i+=188,204===this.ts_packet_size_&&(i+=16);continue}f>0&&(l=this.parseAdaptationField(e,i+4,1+f)),u=5+f}if(1==_||3==_)if(0===d||d===this.current_pmt_pid_||null!=this.pmt_&&this.pmt_.pid_stream_type[d]===A.kSCTE35){var p=188-u;this.handleSectionSlice(e,i+u,p,{pid:d,file_position:n,payload_unit_start_indicator:s,continuity_conunter:c,random_access_indicator:l.random_access_indicator})}else if(null!=this.pmt_&&null!=this.pmt_.pid_stream_type[d]){p=188-u;var m=this.pmt_.pid_stream_type[d];d!==this.pmt_.common_pids.h264&&d!==this.pmt_.common_pids.h265&&d!==this.pmt_.common_pids.adts_aac&&d!==this.pmt_.common_pids.loas_aac&&d!==this.pmt_.common_pids.ac3&&d!==this.pmt_.common_pids.eac3&&d!==this.pmt_.common_pids.opus&&d!==this.pmt_.common_pids.mp3&&!0!==this.pmt_.pes_private_data_pids[d]&&!0!==this.pmt_.timed_id3_pids[d]&&!0!==this.pmt_.synchronous_klv_pids[d]&&!0!==this.pmt_.asynchronous_klv_pids[d]||this.handlePESSlice(e,i+u,p,{pid:d,stream_type:m,file_position:n,payload_unit_start_indicator:s,continuity_conunter:c,random_access_indicator:l.random_access_indicator})}i+=188,204===this.ts_packet_size_&&(i+=16)}return this.dispatchAudioVideoMediaSegment(),i},t.prototype.parseAdaptationField=function(e,t,i){var n=new Uint8Array(e,t,i),a=n[0];return a>0?a>183?(r.a.w(this.TAG,"Illegal adaptation_field_length: ".concat(a)),{}):{discontinuity_indicator:(128&n[1])>>>7,random_access_indicator:(64&n[1])>>>6,elementary_stream_priority_indicator:(32&n[1])>>>5}:{}},t.prototype.handleSectionSlice=function(e,t,i,n){var a=new Uint8Array(e,t,i),r=this.section_slice_queues_[n.pid];if(n.payload_unit_start_indicator){var o=a[0];if(null!=r&&0!==r.total_length){var s=new Uint8Array(e,t+1,Math.min(i,o));r.slices.push(s),r.total_length+=s.byteLength,r.total_length===r.expected_length?this.emitSectionSlices(r,n):this.clearSlices(r,n)}for(var d=1+o;d<a.byteLength;){if(255===a[d+0])break;var _=(15&a[d+1])<<8|a[d+2];this.section_slice_queues_[n.pid]=new C,(r=this.section_slice_queues_[n.pid]).expected_length=_+3,r.file_position=n.file_position,r.random_access_indicator=n.random_access_indicator;s=new Uint8Array(e,t+d,Math.min(i-d,r.expected_length-r.total_length));r.slices.push(s),r.total_length+=s.byteLength,r.total_length===r.expected_length?this.emitSectionSlices(r,n):r.total_length>=r.expected_length&&this.clearSlices(r,n),d+=s.byteLength}}else if(null!=r&&0!==r.total_length){s=new Uint8Array(e,t,Math.min(i,r.expected_length-r.total_length));r.slices.push(s),r.total_length+=s.byteLength,r.total_length===r.expected_length?this.emitSectionSlices(r,n):r.total_length>=r.expected_length&&this.clearSlices(r,n)}},t.prototype.handlePESSlice=function(e,t,i,n){var a=new Uint8Array(e,t,i),o=a[0]<<16|a[1]<<8|a[2],s=(a[3],a[4]<<8|a[5]);if(n.payload_unit_start_indicator){if(1!==o)return void r.a.e(this.TAG,"handlePESSlice: packet_start_code_prefix should be 1 but with value ".concat(o));var d=this.pes_slice_queues_[n.pid];d&&(0===d.expected_length||d.expected_length===d.total_length?this.emitPESSlices(d,n):this.clearSlices(d,n)),this.pes_slice_queues_[n.pid]=new C,this.pes_slice_queues_[n.pid].file_position=n.file_position,this.pes_slice_queues_[n.pid].random_access_indicator=n.random_access_indicator}if(null!=this.pes_slice_queues_[n.pid]){var _=this.pes_slice_queues_[n.pid];_.slices.push(a),n.payload_unit_start_indicator&&(_.expected_length=0===s?0:s+6),_.total_length+=a.byteLength,_.expected_length>0&&_.expected_length===_.total_length?this.emitPESSlices(_,n):_.expected_length>0&&_.expected_length<_.total_length&&this.clearSlices(_,n)}},t.prototype.emitSectionSlices=function(e,t){for(var i=new Uint8Array(e.total_length),n=0,a=0;n<e.slices.length;n++){var r=e.slices[n];i.set(r,a),a+=r.byteLength}e.slices=[],e.expected_length=-1,e.total_length=0;var o=new M;o.pid=t.pid,o.data=i,o.file_position=e.file_position,o.random_access_indicator=e.random_access_indicator,this.parseSection(o)},t.prototype.emitPESSlices=function(e,t){for(var i=new Uint8Array(e.total_length),n=0,a=0;n<e.slices.length;n++){var r=e.slices[n];i.set(r,a),a+=r.byteLength}e.slices=[],e.expected_length=-1,e.total_length=0;var o=new D;o.pid=t.pid,o.data=i,o.stream_type=t.stream_type,o.file_position=e.file_position,o.random_access_indicator=e.random_access_indicator,this.parsePES(o)},t.prototype.clearSlices=function(e,t){e.slices=[],e.expected_length=-1,e.total_length=0},t.prototype.parseSection=function(e){var t=e.data,i=e.pid;0===i?this.parsePAT(t):i===this.current_pmt_pid_?this.parsePMT(t):null!=this.pmt_&&this.pmt_.scte_35_pids[i]&&this.parseSCTE35(t)},t.prototype.parsePES=function(e){var t=e.data,i=t[0]<<16|t[1]<<8|t[2],n=t[3],a=t[4]<<8|t[5];if(1===i){if(188!==n&&190!==n&&191!==n&&240!==n&&241!==n&&255!==n&&242!==n&&248!==n){t[6];var o=(192&t[7])>>>6,s=t[8],d=void 0,_=void 0;2!==o&&3!==o||(d=536870912*(14&t[9])+4194304*(255&t[10])+16384*(254&t[11])+128*(255&t[12])+(254&t[13])/2,_=3===o?536870912*(14&t[14])+4194304*(255&t[15])+16384*(254&t[16])+128*(255&t[17])+(254&t[18])/2:d);var c=9+s,h=void 0;if(0!==a){if(a<3+s)return void r.a.v(this.TAG,"Malformed PES: PES_packet_length < 3 + PES_header_data_length");h=a-3-s}else h=t.byteLength-c;var l=t.subarray(c,c+h);switch(e.stream_type){case A.kMPEG1Audio:case A.kMPEG2Audio:this.parseMP3Payload(l,d);break;case A.kPESPrivateData:this.pmt_.common_pids.opus===e.pid?this.parseOpusPayload(l,d):this.pmt_.common_pids.ac3===e.pid?this.parseAC3Payload(l,d):this.pmt_.common_pids.eac3===e.pid?this.parseEAC3Payload(l,d):this.pmt_.asynchronous_klv_pids[e.pid]?this.parseAsynchronousKLVMetadataPayload(l,e.pid,n):this.pmt_.smpte2038_pids[e.pid]?this.parseSMPTE2038MetadataPayload(l,d,_,e.pid,n):this.parsePESPrivateDataPayload(l,d,_,e.pid,n);break;case A.kADTSAAC:this.parseADTSAACPayload(l,d);break;case A.kLOASAAC:this.parseLOASAACPayload(l,d);break;case A.kAC3:this.parseAC3Payload(l,d);break;case A.kEAC3:this.parseEAC3Payload(l,d);break;case A.kMetadata:this.pmt_.timed_id3_pids[e.pid]?this.parseTimedID3MetadataPayload(l,d,_,e.pid,n):this.pmt_.synchronous_klv_pids[e.pid]&&this.parseSynchronousKLVMetadataPayload(l,d,_,e.pid,n);break;case A.kH264:this.parseH264Payload(l,d,_,e.file_position,e.random_access_indicator);break;case A.kH265:this.parseH265Payload(l,d,_,e.file_position,e.random_access_indicator)}}else if((188===n||191===n||240===n||241===n||255===n||242===n||248===n)&&e.stream_type===A.kPESPrivateData){c=6,h=void 0;h=0!==a?a:t.byteLength-c;l=t.subarray(c,c+h);this.parsePESPrivateDataPayload(l,void 0,void 0,e.pid,n)}}else r.a.e(this.TAG,"parsePES: packet_start_code_prefix should be 1 but with value ".concat(i))},t.prototype.parsePAT=function(e){var t=e[0];if(0===t){var i=(15&e[1])<<8|e[2],n=(e[3],e[4],(62&e[5])>>>1),a=1&e[5],o=e[6],s=(e[7],null);if(1===a&&0===o)(s=new L).version_number=n;else if(null==(s=this.pat_))return;for(var d=i-5-4,_=-1,c=-1,h=8;h<8+d;h+=4){var l=e[h]<<8|e[h+1],u=(31&e[h+2])<<8|e[h+3];0===l?s.network_pid=u:(s.program_pmt_pid[l]=u,-1===_&&(_=l),-1===c&&(c=u))}1===a&&0===o&&(null==this.pat_&&r.a.v(this.TAG,"Parsed first PAT: ".concat(JSON.stringify(s))),this.pat_=s,this.current_program_=_,this.current_pmt_pid_=c)}else r.a.e(this.TAG,"parsePAT: table_id ".concat(t," is not corresponded to PAT!"))},t.prototype.parsePMT=function(e){var t=e[0];if(2===t){var i=(15&e[1])<<8|e[2],n=e[3]<<8|e[4],a=(62&e[5])>>>1,o=1&e[5],s=e[6],d=(e[7],null);if(1===o&&0===s)(d=new w).program_number=n,d.version_number=a,this.program_pmt_map_[n]=d;else if(null==(d=this.program_pmt_map_[n]))return;e[8],e[9];for(var _=(15&e[10])<<8|e[11],c=12+_,h=i-9-_-4,l=c;l<c+h;){var u=e[l],f=(31&e[l+1])<<8|e[l+2],p=(15&e[l+3])<<8|e[l+4];d.pid_stream_type[f]=u;var m=d.common_pids.h264||d.common_pids.h265,g=d.common_pids.adts_aac||d.common_pids.loas_aac||d.common_pids.ac3||d.common_pids.eac3||d.common_pids.opus||d.common_pids.mp3;if(u!==A.kH264||m)if(u!==A.kH265||m)if(u!==A.kADTSAAC||g)if(u!==A.kLOASAAC||g)if(u!==A.kAC3||g)if(u!==A.kEAC3||g)if(u!==A.kMPEG1Audio&&u!==A.kMPEG2Audio||g)if(u===A.kPESPrivateData){if(d.pes_private_data_pids[f]=!0,p>0){for(var y=l+5;y<l+5+p;){var v=e[y+0],S=e[y+1];if(5===v){var b=String.fromCharCode.apply(String,Array.from(e.subarray(y+2,y+2+S)));"VANC"===b?d.smpte2038_pids[f]=!0:"Opus"===b?d.common_pids.opus=f:"KLVA"===b&&(d.asynchronous_klv_pids[f]=!0)}else if(127===v&&f===d.common_pids.opus){var E=null;if(128===e[y+2]&&(E=e[y+3]),null==E){r.a.e(this.TAG,"Not Supported Opus channel count.");continue}var R={codec:"opus",channel_count:0==(15&E)?2:15&E,channel_config_code:E,sample_rate:48e3},T={codec:"opus",meta:R};0==this.audio_init_segment_dispatched_?(this.audio_metadata_=R,this.dispatchAudioInitSegment(T)):this.detectAudioMetadataChange(T)&&(this.dispatchAudioMediaSegment(),this.dispatchAudioInitSegment(T))}y+=2+S}var L=e.subarray(l+5,l+5+p);this.dispatchPESPrivateDataDescriptor(f,u,L)}}else if(u===A.kMetadata){if(p>0)for(y=l+5;y<l+5+p;){v=e[y+0];var k=e[y+1];if(38===v){var D=e[y+2]<<8|e[y+3]<<0,M=null;65535===D&&(M=String.fromCharCode.apply(String,Array.from(e.subarray(y+4,y+4+4))));var C=null;if(255===e[y+4+(65535===D?4:0)]){var O=4+(65535===D?4:0)+1;C=String.fromCharCode.apply(String,Array.from(e.subarray(y+O,y+O+4)))}"ID3 "===M&&"ID3 "===C?d.timed_id3_pids[f]=!0:"KLVA"===C&&(d.synchronous_klv_pids[f]=!0)}y+=2+k}}else u===A.kSCTE35&&(d.scte_35_pids[f]=!0);else d.common_pids.mp3=f;else d.common_pids.eac3=f;else d.common_pids.ac3=f;else d.common_pids.loas_aac=f;else d.common_pids.adts_aac=f;else d.common_pids.h265=f;else d.common_pids.h264=f;l+=5+p}n===this.current_program_&&(null==this.pmt_&&r.a.v(this.TAG,"Parsed first PMT: ".concat(JSON.stringify(d))),this.pmt_=d,(d.common_pids.h264||d.common_pids.h265)&&(this.has_video_=!0),(d.common_pids.adts_aac||d.common_pids.loas_aac||d.common_pids.ac3||d.common_pids.opus||d.common_pids.mp3)&&(this.has_audio_=!0))}else r.a.e(this.TAG,"parsePMT: table_id ".concat(t," is not corresponded to PMT!"))},t.prototype.parseSCTE35=function(e){var t=de(e);if(null!=t.pts){var i=Math.floor(t.pts/this.timescale_);t.pts=i}else t.nearest_pts=this.aac_last_sample_pts_;this.onSCTE35Metadata&&this.onSCTE35Metadata(t)},t.prototype.parseH264Payload=function(e,t,i,n,a){for(var o=new x(e),s=null,d=[],_=0,c=!1;null!=(s=o.readNextNaluPayload());){var h=new P(s);if(h.type===k.kSliceSPS){var l=p.parseSPS(s.data);this.video_init_segment_dispatched_?!0===this.detectVideoMetadataChange(h,l)&&(r.a.v(this.TAG,"H264: Critical h264 metadata has been changed, attempt to re-generate InitSegment"),this.video_metadata_changed_=!0,this.video_metadata_={vps:void 0,sps:h,pps:void 0,details:l}):(this.video_metadata_.sps=h,this.video_metadata_.details=l)}else h.type===k.kSlicePPS?this.video_init_segment_dispatched_&&!this.video_metadata_changed_||(this.video_metadata_.pps=h,this.video_metadata_.sps&&this.video_metadata_.pps&&(this.video_metadata_changed_&&this.dispatchVideoMediaSegment(),this.dispatchVideoInitSegment())):(h.type===k.kSliceIDR||h.type===k.kSliceNonIDR&&1===a)&&(c=!0);this.video_init_segment_dispatched_&&(d.push(h),_+=h.data.byteLength)}var u=Math.floor(t/this.timescale_),f=Math.floor(i/this.timescale_);if(d.length){var m=this.video_track_,g={units:d,length:_,isKeyframe:c,dts:f,pts:u,cts:u-f,file_position:n};m.samples.push(g),m.length+=_}},t.prototype.parseH265Payload=function(e,t,i,n,a){for(var o=new he(e),s=null,d=[],_=0,c=!1;null!=(s=o.readNextNaluPayload());){var h=new ce(s);if(h.type===X.kSliceVPS){if(!this.video_init_segment_dispatched_){var l=g.parseVPS(s.data);this.video_metadata_.vps=h,this.video_metadata_.details=Re(Re({},this.video_metadata_.details),l)}}else if(h.type===X.kSliceSPS){l=g.parseSPS(s.data);this.video_init_segment_dispatched_?!0===this.detectVideoMetadataChange(h,l)&&(r.a.v(this.TAG,"H265: Critical h265 metadata has been changed, attempt to re-generate InitSegment"),this.video_metadata_changed_=!0,this.video_metadata_={vps:void 0,sps:h,pps:void 0,details:l}):(this.video_metadata_.sps=h,this.video_metadata_.details=Re(Re({},this.video_metadata_.details),l))}else if(h.type===X.kSlicePPS){if(!this.video_init_segment_dispatched_||this.video_metadata_changed_){l=g.parsePPS(s.data);this.video_metadata_.pps=h,this.video_metadata_.details=Re(Re({},this.video_metadata_.details),l),this.video_metadata_.vps&&this.video_metadata_.sps&&this.video_metadata_.pps&&(this.video_metadata_changed_&&this.dispatchVideoMediaSegment(),this.dispatchVideoInitSegment())}}else h.type!==X.kSliceIDR_W_RADL&&h.type!==X.kSliceIDR_N_LP&&h.type!==X.kSliceCRA_NUT||(c=!0);this.video_init_segment_dispatched_&&(d.push(h),_+=h.data.byteLength)}var u=Math.floor(t/this.timescale_),f=Math.floor(i/this.timescale_);if(d.length){var p=this.video_track_,m={units:d,length:_,isKeyframe:c,dts:f,pts:u,cts:u-f,file_position:n};p.samples.push(m),p.length+=_}},t.prototype.detectVideoMetadataChange=function(e,t){if(t.codec_mimetype!==this.video_metadata_.details.codec_mimetype)return r.a.v(this.TAG,"Video: Codec mimeType changed from "+"".concat(this.video_metadata_.details.codec_mimetype," to ").concat(t.codec_mimetype)),!0;if(t.codec_size.width!==this.video_metadata_.details.codec_size.width||t.codec_size.height!==this.video_metadata_.details.codec_size.height){var i=this.video_metadata_.details.codec_size,n=t.codec_size;return r.a.v(this.TAG,"Video: Coded Resolution changed from "+"".concat(i.width,"x").concat(i.height," to ").concat(n.width,"x").concat(n.height)),!0}return t.present_size.width!==this.video_metadata_.details.present_size.width&&(r.a.v(this.TAG,"Video: Present resolution width changed from "+"".concat(this.video_metadata_.details.present_size.width," to ").concat(t.present_size.width)),!0)},t.prototype.isInitSegmentDispatched=function(){return this.has_video_&&this.has_audio_?this.video_init_segment_dispatched_&&this.audio_init_segment_dispatched_:this.has_video_&&!this.has_audio_?this.video_init_segment_dispatched_:!(this.has_video_||!this.has_audio_)&&this.audio_init_segment_dispatched_},t.prototype.dispatchVideoInitSegment=function(){var e=this.video_metadata_.details,t={type:"video"};t.id=this.video_track_.id,t.timescale=1e3,t.duration=this.duration_,t.codecWidth=e.codec_size.width,t.codecHeight=e.codec_size.height,t.presentWidth=e.present_size.width,t.presentHeight=e.present_size.height,t.profile=e.profile_string,t.level=e.level_string,t.bitDepth=e.bit_depth,t.chromaFormat=e.chroma_format,t.sarRatio=e.sar_ratio,t.frameRate=e.frame_rate;var i=t.frameRate.fps_den,n=t.frameRate.fps_num;if(t.refSampleDuration=i/n*1e3,t.codec=e.codec_mimetype,this.video_metadata_.vps){var a=this.video_metadata_.vps.data.subarray(4),o=this.video_metadata_.sps.data.subarray(4),s=this.video_metadata_.pps.data.subarray(4),d=new le(a,o,s,e);t.hvcc=d.getData(),0==this.video_init_segment_dispatched_&&r.a.v(this.TAG,"Generated first HEVCDecoderConfigurationRecord for mimeType: ".concat(t.codec))}else{o=this.video_metadata_.sps.data.subarray(4),s=this.video_metadata_.pps.data.subarray(4);var _=new U(o,s,e);t.avcc=_.getData(),0==this.video_init_segment_dispatched_&&r.a.v(this.TAG,"Generated first AVCDecoderConfigurationRecord for mimeType: ".concat(t.codec))}this.onTrackMetadata("video",t),this.video_init_segment_dispatched_=!0,this.video_metadata_changed_=!1;var c=this.media_info_;c.hasVideo=!0,c.width=t.codecWidth,c.height=t.codecHeight,c.fps=t.frameRate.fps,c.profile=t.profile,c.level=t.level,c.refFrames=e.ref_frames,c.chromaFormat=e.chroma_format_string,c.sarNum=t.sarRatio.width,c.sarDen=t.sarRatio.height,c.videoCodec=t.codec,c.hasAudio&&c.audioCodec?c.mimeType='video/mp2t; codecs="'.concat(c.videoCodec,",").concat(c.audioCodec,'"'):c.mimeType='video/mp2t; codecs="'.concat(c.videoCodec,'"'),c.isComplete()&&this.onMediaInfo(c)},t.prototype.dispatchVideoMediaSegment=function(){this.isInitSegmentDispatched()&&this.video_track_.length&&this.onDataAvailable(null,this.video_track_)},t.prototype.dispatchAudioMediaSegment=function(){this.isInitSegmentDispatched()&&this.audio_track_.length&&this.onDataAvailable(this.audio_track_,null)},t.prototype.dispatchAudioVideoMediaSegment=function(){this.isInitSegmentDispatched()&&(this.audio_track_.length||this.video_track_.length)&&this.onDataAvailable(this.audio_track_,this.video_track_)},t.prototype.parseADTSAACPayload=function(e,t){if(!this.has_video_||this.video_init_segment_dispatched_){if(this.aac_last_incomplete_data_){var i=new Uint8Array(e.byteLength+this.aac_last_incomplete_data_.byteLength);i.set(this.aac_last_incomplete_data_,0),i.set(e,this.aac_last_incomplete_data_.byteLength),e=i}var n,a;if(null!=t&&(a=t/this.timescale_),"aac"===this.audio_metadata_.codec){if(null==t&&null!=this.aac_last_sample_pts_)n=1024/this.audio_metadata_.sampling_frequency*1e3,a=this.aac_last_sample_pts_+n;else if(null==t)return void r.a.w(this.TAG,"AAC: Unknown pts");if(this.aac_last_incomplete_data_&&this.aac_last_sample_pts_){n=1024/this.audio_metadata_.sampling_frequency*1e3;var o=this.aac_last_sample_pts_+n;Math.abs(o-a)>1&&(r.a.w(this.TAG,"AAC: Detected pts overlapped, "+"expected: ".concat(o,"ms, PES pts: ").concat(a,"ms")),a=o)}}for(var s,d=new H(e),_=null,c=a;null!=(_=d.readNextAACFrame());){n=1024/_.sampling_frequency*1e3;var h={codec:"aac",data:_};0==this.audio_init_segment_dispatched_?(this.audio_metadata_={codec:"aac",audio_object_type:_.audio_object_type,sampling_freq_index:_.sampling_freq_index,sampling_frequency:_.sampling_frequency,channel_config:_.channel_config},this.dispatchAudioInitSegment(h)):this.detectAudioMetadataChange(h)&&(this.dispatchAudioMediaSegment(),this.dispatchAudioInitSegment(h)),s=c;var l=Math.floor(c),u={unit:_.data,length:_.data.byteLength,pts:l,dts:l};this.audio_track_.samples.push(u),this.audio_track_.length+=_.data.byteLength,c+=n}d.hasIncompleteData()&&(this.aac_last_incomplete_data_=d.getIncompleteData()),s&&(this.aac_last_sample_pts_=s)}},t.prototype.parseLOASAACPayload=function(e,t){var i;if(!this.has_video_||this.video_init_segment_dispatched_){if(this.aac_last_incomplete_data_){var n=new Uint8Array(e.byteLength+this.aac_last_incomplete_data_.byteLength);n.set(this.aac_last_incomplete_data_,0),n.set(e,this.aac_last_incomplete_data_.byteLength),e=n}var a,o;if(null!=t&&(o=t/this.timescale_),"aac"===this.audio_metadata_.codec){if(null==t&&null!=this.aac_last_sample_pts_)a=1024/this.audio_metadata_.sampling_frequency*1e3,o=this.aac_last_sample_pts_+a;else if(null==t)return void r.a.w(this.TAG,"AAC: Unknown pts");if(this.aac_last_incomplete_data_&&this.aac_last_sample_pts_){a=1024/this.audio_metadata_.sampling_frequency*1e3;var s=this.aac_last_sample_pts_+a;Math.abs(s-o)>1&&(r.a.w(this.TAG,"AAC: Detected pts overlapped, "+"expected: ".concat(s,"ms, PES pts: ").concat(o,"ms")),o=s)}}for(var d,_=new q(e),c=null,h=o;null!=(c=_.readNextAACFrame(null!==(i=this.loas_previous_frame)&&void 0!==i?i:void 0));){this.loas_previous_frame=c,a=1024/c.sampling_frequency*1e3;var l={codec:"aac",data:c};0==this.audio_init_segment_dispatched_?(this.audio_metadata_={codec:"aac",audio_object_type:c.audio_object_type,sampling_freq_index:c.sampling_freq_index,sampling_frequency:c.sampling_frequency,channel_config:c.channel_config},this.dispatchAudioInitSegment(l)):this.detectAudioMetadataChange(l)&&(this.dispatchAudioMediaSegment(),this.dispatchAudioInitSegment(l)),d=h;var u=Math.floor(h),f={unit:c.data,length:c.data.byteLength,pts:u,dts:u};this.audio_track_.samples.push(f),this.audio_track_.length+=c.data.byteLength,h+=a}_.hasIncompleteData()&&(this.aac_last_incomplete_data_=_.getIncompleteData()),d&&(this.aac_last_sample_pts_=d)}},t.prototype.parseAC3Payload=function(e,t){if(!this.has_video_||this.video_init_segment_dispatched_){var i,n;if(null!=t&&(n=t/this.timescale_),"ac-3"===this.audio_metadata_.codec)if(null==t&&null!=this.aac_last_sample_pts_)i=1536/this.audio_metadata_.sampling_frequency*1e3,n=this.aac_last_sample_pts_+i;else if(null==t)return void r.a.w(this.TAG,"AC3: Unknown pts");for(var a,o=new ge(e),s=null,d=n;null!=(s=o.readNextAC3Frame());){i=1536/s.sampling_frequency*1e3;var _={codec:"ac-3",data:s};0==this.audio_init_segment_dispatched_?(this.audio_metadata_={codec:"ac-3",sampling_frequency:s.sampling_frequency,bit_stream_identification:s.bit_stream_identification,bit_stream_mode:s.bit_stream_mode,low_frequency_effects_channel_on:s.low_frequency_effects_channel_on,channel_mode:s.channel_mode},this.dispatchAudioInitSegment(_)):this.detectAudioMetadataChange(_)&&(this.dispatchAudioMediaSegment(),this.dispatchAudioInitSegment(_)),a=d;var c=Math.floor(d),h={unit:s.data,length:s.data.byteLength,pts:c,dts:c};this.audio_track_.samples.push(h),this.audio_track_.length+=s.data.byteLength,d+=i}a&&(this.aac_last_sample_pts_=a)}},t.prototype.parseEAC3Payload=function(e,t){if(!this.has_video_||this.video_init_segment_dispatched_){var i,n;if(null!=t&&(n=t/this.timescale_),"ec-3"===this.audio_metadata_.codec)if(null==t&&null!=this.aac_last_sample_pts_)i=256*this.audio_metadata_.num_blks/this.audio_metadata_.sampling_frequency*1e3,n=this.aac_last_sample_pts_+i;else if(null==t)return void r.a.w(this.TAG,"EAC3: Unknown pts");for(var a,o=new Se(e),s=null,d=n;null!=(s=o.readNextEAC3Frame());){i=1536/s.sampling_frequency*1e3;var _={codec:"ec-3",data:s};0==this.audio_init_segment_dispatched_?(this.audio_metadata_={codec:"ec-3",sampling_frequency:s.sampling_frequency,bit_stream_identification:s.bit_stream_identification,low_frequency_effects_channel_on:s.low_frequency_effects_channel_on,num_blks:s.num_blks,channel_mode:s.channel_mode},this.dispatchAudioInitSegment(_)):this.detectAudioMetadataChange(_)&&(this.dispatchAudioMediaSegment(),this.dispatchAudioInitSegment(_)),a=d;var c=Math.floor(d),h={unit:s.data,length:s.data.byteLength,pts:c,dts:c};this.audio_track_.samples.push(h),this.audio_track_.length+=s.data.byteLength,d+=i}a&&(this.aac_last_sample_pts_=a)}},t.prototype.parseOpusPayload=function(e,t){if(!this.has_video_||this.video_init_segment_dispatched_){var i,n;if(null!=t&&(n=t/this.timescale_),"opus"===this.audio_metadata_.codec)if(null==t&&null!=this.aac_last_sample_pts_)i=20,n=this.aac_last_sample_pts_+i;else if(null==t)return void r.a.w(this.TAG,"Opus: Unknown pts");for(var a,o=n,s=0;s<e.length;){i=20;for(var d=0!=(16&e[s+1]),_=0!=(8&e[s+1]),c=s+2,h=0;255===e[c];)h+=255,c+=1;h+=e[c],c+=1,c+=d?2:0,c+=_?2:0,a=o;var l=Math.floor(o),u=e.slice(c,c+h),f={unit:u,length:u.byteLength,pts:l,dts:l};this.audio_track_.samples.push(f),this.audio_track_.length+=u.byteLength,o+=i,s=c+h}a&&(this.aac_last_sample_pts_=a)}},t.prototype.parseMP3Payload=function(e,t){if(!this.has_video_||this.video_init_segment_dispatched_){var i=[0,32,64,96,128,160,192,224,256,288,320,352,384,416,448,-1],n=[0,32,48,56,64,80,96,112,128,160,192,224,256,320,384,-1],a=[0,32,40,48,56,64,80,96,112,128,160,192,224,256,320,-1],r=e[1]>>>3&3,o=(6&e[1])>>1,s=(240&e[2])>>>4,d=(12&e[2])>>>2,_=3!==(e[3]>>>6&3)?2:1,c=0,h=34;switch(r){case 0:c=[11025,12e3,8e3,0][d];break;case 2:c=[22050,24e3,16e3,0][d];break;case 3:c=[44100,48e3,32e3,0][d]}switch(o){case 1:h=34,s<a.length&&a[s];break;case 2:h=33,s<n.length&&n[s];break;case 3:h=32,s<i.length&&i[s]}var l=new fe;l.object_type=h,l.sample_rate=c,l.channel_count=_,l.data=e;var u={codec:"mp3",data:l};0==this.audio_init_segment_dispatched_?(this.audio_metadata_={codec:"mp3",object_type:h,sample_rate:c,channel_count:_},this.dispatchAudioInitSegment(u)):this.detectAudioMetadataChange(u)&&(this.dispatchAudioMediaSegment(),this.dispatchAudioInitSegment(u));var f={unit:e,length:e.byteLength,pts:t/this.timescale_,dts:t/this.timescale_};this.audio_track_.samples.push(f),this.audio_track_.length+=e.byteLength}},t.prototype.detectAudioMetadataChange=function(e){if(e.codec!==this.audio_metadata_.codec)return r.a.v(this.TAG,"Audio: Audio Codecs changed from "+"".concat(this.audio_metadata_.codec," to ").concat(e.codec)),!0;if("aac"===e.codec&&"aac"===this.audio_metadata_.codec){if((t=e.data).audio_object_type!==this.audio_metadata_.audio_object_type)return r.a.v(this.TAG,"AAC: AudioObjectType changed from "+"".concat(this.audio_metadata_.audio_object_type," to ").concat(t.audio_object_type)),!0;if(t.sampling_freq_index!==this.audio_metadata_.sampling_freq_index)return r.a.v(this.TAG,"AAC: SamplingFrequencyIndex changed from "+"".concat(this.audio_metadata_.sampling_freq_index," to ").concat(t.sampling_freq_index)),!0;if(t.channel_config!==this.audio_metadata_.channel_config)return r.a.v(this.TAG,"AAC: Channel configuration changed from "+"".concat(this.audio_metadata_.channel_config," to ").concat(t.channel_config)),!0}else if("ac-3"===e.codec&&"ac-3"===this.audio_metadata_.codec){var t;if((t=e.data).sampling_frequency!==this.audio_metadata_.sampling_frequency)return r.a.v(this.TAG,"AC3: Sampling Frequency changed from "+"".concat(this.audio_metadata_.sampling_frequency," to ").concat(t.sampling_frequency)),!0;if(t.bit_stream_identification!==this.audio_metadata_.bit_stream_identification)return r.a.v(this.TAG,"AC3: Bit Stream Identification changed from "+"".concat(this.audio_metadata_.bit_stream_identification," to ").concat(t.bit_stream_identification)),!0;if(t.bit_stream_mode!==this.audio_metadata_.bit_stream_mode)return r.a.v(this.TAG,"AC3: BitStream Mode changed from "+"".concat(this.audio_metadata_.bit_stream_mode," to ").concat(t.bit_stream_mode)),!0;if(t.channel_mode!==this.audio_metadata_.channel_mode)return r.a.v(this.TAG,"AC3: Channel Mode changed from "+"".concat(this.audio_metadata_.channel_mode," to ").concat(t.channel_mode)),!0;if(t.low_frequency_effects_channel_on!==this.audio_metadata_.low_frequency_effects_channel_on)return r.a.v(this.TAG,"AC3: Low Frequency Effects Channel On changed from "+"".concat(this.audio_metadata_.low_frequency_effects_channel_on," to ").concat(t.low_frequency_effects_channel_on)),!0}else if("opus"===e.codec&&"opus"===this.audio_metadata_.codec){if((i=e.meta).sample_rate!==this.audio_metadata_.sample_rate)return r.a.v(this.TAG,"Opus: SamplingFrequencyIndex changed from "+"".concat(this.audio_metadata_.sample_rate," to ").concat(i.sample_rate)),!0;if(i.channel_count!==this.audio_metadata_.channel_count)return r.a.v(this.TAG,"Opus: Channel count changed from "+"".concat(this.audio_metadata_.channel_count," to ").concat(i.channel_count)),!0}else if("mp3"===e.codec&&"mp3"===this.audio_metadata_.codec){var i;if((i=e.data).object_type!==this.audio_metadata_.object_type)return r.a.v(this.TAG,"MP3: AudioObjectType changed from "+"".concat(this.audio_metadata_.object_type," to ").concat(i.object_type)),!0;if(i.sample_rate!==this.audio_metadata_.sample_rate)return r.a.v(this.TAG,"MP3: SamplingFrequencyIndex changed from "+"".concat(this.audio_metadata_.sample_rate," to ").concat(i.sample_rate)),!0;if(i.channel_count!==this.audio_metadata_.channel_count)return r.a.v(this.TAG,"MP3: Channel count changed from "+"".concat(this.audio_metadata_.channel_count," to ").concat(i.channel_count)),!0}return!1},t.prototype.dispatchAudioInitSegment=function(e){var t={type:"audio"};if(t.id=this.audio_track_.id,t.timescale=1e3,t.duration=this.duration_,"aac"===this.audio_metadata_.codec){var i="aac"===e.codec?e.data:null,n=new K(i);t.audioSampleRate=n.sampling_rate,t.channelCount=n.channel_count,t.codec=n.codec_mimetype,t.originalCodec=n.original_codec_mimetype,t.config=n.config,t.refSampleDuration=1024/t.audioSampleRate*t.timescale}else if("ac-3"===this.audio_metadata_.codec){var a="ac-3"===e.codec?e.data:null,o=new ye(a);t.audioSampleRate=o.sampling_rate,t.channelCount=o.channel_count,t.codec=o.codec_mimetype,t.originalCodec=o.original_codec_mimetype,t.config=o.config,t.refSampleDuration=1536/t.audioSampleRate*t.timescale}else if("ec-3"===this.audio_metadata_.codec){var s="ec-3"===e.codec?e.data:null,d=new be(s);t.audioSampleRate=d.sampling_rate,t.channelCount=d.channel_count,t.codec=d.codec_mimetype,t.originalCodec=d.original_codec_mimetype,t.config=d.config,t.refSampleDuration=256*d.num_blks/t.audioSampleRate*t.timescale}else"opus"===this.audio_metadata_.codec?(t.audioSampleRate=this.audio_metadata_.sample_rate,t.channelCount=this.audio_metadata_.channel_count,t.channelConfigCode=this.audio_metadata_.channel_config_code,t.codec="opus",t.originalCodec="opus",t.config=void 0,t.refSampleDuration=20):"mp3"===this.audio_metadata_.codec&&(t.audioSampleRate=this.audio_metadata_.sample_rate,t.channelCount=this.audio_metadata_.channel_count,t.codec="mp3",t.originalCodec="mp3",t.config=void 0);0==this.audio_init_segment_dispatched_&&r.a.v(this.TAG,"Generated first AudioSpecificConfig for mimeType: ".concat(t.codec)),this.onTrackMetadata("audio",t),this.audio_init_segment_dispatched_=!0,this.video_metadata_changed_=!1;var _=this.media_info_;_.hasAudio=!0,_.audioCodec=t.originalCodec,_.audioSampleRate=t.audioSampleRate,_.audioChannelCount=t.channelCount,_.hasVideo&&_.videoCodec?_.mimeType='video/mp2t; codecs="'.concat(_.videoCodec,",").concat(_.audioCodec,'"'):_.mimeType='video/mp2t; codecs="'.concat(_.audioCodec,'"'),_.isComplete()&&this.onMediaInfo(_)},t.prototype.dispatchPESPrivateDataDescriptor=function(e,t,i){var n=new Y;n.pid=e,n.stream_type=t,n.descriptor=i,this.onPESPrivateDataDescriptor&&this.onPESPrivateDataDescriptor(n)},t.prototype.parsePESPrivateDataPayload=function(e,t,i,n,a){var r=new W;if(r.pid=n,r.stream_id=a,r.len=e.byteLength,r.data=e,null!=t){var o=Math.floor(t/this.timescale_);r.pts=o}else r.nearest_pts=this.aac_last_sample_pts_;if(null!=i){var s=Math.floor(i/this.timescale_);r.dts=s}this.onPESPrivateData&&this.onPESPrivateData(r)},t.prototype.parseTimedID3MetadataPayload=function(e,t,i,n,a){var r=new W;if(r.pid=n,r.stream_id=a,r.len=e.byteLength,r.data=e,null!=t){var o=Math.floor(t/this.timescale_);r.pts=o}if(null!=i){var s=Math.floor(i/this.timescale_);r.dts=s}this.onTimedID3Metadata&&this.onTimedID3Metadata(r)},t.prototype.parseSynchronousKLVMetadataPayload=function(e,t,i,n,a){var r=new Ee;if(r.pid=n,r.stream_id=a,r.len=e.byteLength,r.data=e,null!=t){var o=Math.floor(t/this.timescale_);r.pts=o}if(null!=i){var s=Math.floor(i/this.timescale_);r.dts=s}r.access_units=function(e){for(var t=[],i=0;i+5<e.byteLength;){var n=e[i+0],a=e[i+1],r=e[i+2],o=e[i+3]<<8|e[i+4]<<0,s=e.slice(i+5,i+5+o);t.push({service_id:n,sequence_number:a,flags:r,data:s}),i+=5+o}return t}(e),this.onSynchronousKLVMetadata&&this.onSynchronousKLVMetadata(r)},t.prototype.parseAsynchronousKLVMetadataPayload=function(e,t,i){var n=new W;n.pid=t,n.stream_id=i,n.len=e.byteLength,n.data=e,this.onAsynchronousKLVMetadata&&this.onAsynchronousKLVMetadata(n)},t.prototype.parseSMPTE2038MetadataPayload=function(e,t,i,n,a){var r=new ue;if(r.pid=n,r.stream_id=a,r.len=e.byteLength,r.data=e,null!=t){var o=Math.floor(t/this.timescale_);r.pts=o}if(r.nearest_pts=this.aac_last_sample_pts_,null!=i){var s=Math.floor(i/this.timescale_);r.dts=s}r.ancillaries=function(e){for(var t=new f(e),i=0,n=[];;){if(i+=6,0!==t.readBits(6))break;var a=t.readBool();i+=1;var r=t.readBits(11);i+=11;var o=t.readBits(12);i+=12;var s=255&t.readBits(10);i+=10;var d=255&t.readBits(10);i+=10;var _=255&t.readBits(10);i+=10;for(var c=new Uint8Array(_),h=0;h<_;h++){var l=255&t.readBits(10);i+=10,c[h]=l}t.readBits(10);i+=10;var u="User Defined";65===s?7===d&&(u="SCTE-104"):95===s?220===d?u="ARIB STD-B37 (1SEG)":221===d?u="ARIB STD-B37 (ANALOG)":222===d?u="ARIB STD-B37 (SD)":223===d&&(u="ARIB STD-B37 (HD)"):97===s&&(1===d?u="EIA-708":2===d&&(u="EIA-608")),n.push({yc_indicator:a,line_number:r,horizontal_offset:o,did:s,sdid:d,user_data:c,description:u,information:{}}),t.readBits(8-(i-Math.floor(i/8))%8),i+=(8-(i-Math.floor(i/8)))%8}return t.destroy(),t=null,n}(e),this.onSMPTE2038Metadata&&this.onSMPTE2038Metadata(r)},t}(T),Le=function(e,t,i){if(i||2===arguments.length)for(var n,a=0,r=t.length;a<r;a++)!n&&a in t||(n||(n=Array.prototype.slice.call(t,0,a)),n[a]=t[a]);return e.concat(n||Array.prototype.slice.call(t))},ke=function(){function e(){}return e.init=function(){for(var t in e.types={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],hvc1:[],hvcC:[],av01:[],av1C:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[],".mp3":[],Opus:[],dOps:[],"ac-3":[],dac3:[],"ec-3":[],dec3:[]},e.types)e.types.hasOwnProperty(t)&&(e.types[t]=[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3)]);var i=e.constants={};i.FTYP=new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]),i.STSD_PREFIX=new Uint8Array([0,0,0,0,0,0,0,1]),i.STTS=new Uint8Array([0,0,0,0,0,0,0,0]),i.STSC=i.STCO=i.STTS,i.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),i.HDLR_VIDEO=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),i.HDLR_AUDIO=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),i.DREF=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),i.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),i.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])},e.box=function(e){for(var t=8,i=null,n=Array.prototype.slice.call(arguments,1),a=n.length,r=0;r<a;r++)t+=n[r].byteLength;(i=new Uint8Array(t))[0]=t>>>24&255,i[1]=t>>>16&255,i[2]=t>>>8&255,i[3]=255&t,i.set(e,4);var o=8;for(r=0;r<a;r++)i.set(n[r],o),o+=n[r].byteLength;return i},e.generateInitSegment=function(t){var i=e.box(e.types.ftyp,e.constants.FTYP),n=e.moov(t),a=new Uint8Array(i.byteLength+n.byteLength);return a.set(i,0),a.set(n,i.byteLength),a},e.moov=function(t){var i=e.mvhd(t.timescale,t.duration),n=e.trak(t),a=e.mvex(t);return e.box(e.types.moov,i,n,a)},e.mvhd=function(t,i){return e.box(e.types.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t,i>>>24&255,i>>>16&255,i>>>8&255,255&i,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]))},e.trak=function(t){return e.box(e.types.trak,e.tkhd(t),e.mdia(t))},e.tkhd=function(t){var i=t.id,n=t.duration,a=t.presentWidth,r=t.presentHeight;return e.box(e.types.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,i>>>24&255,i>>>16&255,i>>>8&255,255&i,0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,a>>>8&255,255&a,0,0,r>>>8&255,255&r,0,0]))},e.mdia=function(t){return e.box(e.types.mdia,e.mdhd(t),e.hdlr(t),e.minf(t))},e.mdhd=function(t){var i=t.timescale,n=t.duration;return e.box(e.types.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,i>>>24&255,i>>>16&255,i>>>8&255,255&i,n>>>24&255,n>>>16&255,n>>>8&255,255&n,85,196,0,0]))},e.hdlr=function(t){var i=null;return i="audio"===t.type?e.constants.HDLR_AUDIO:e.constants.HDLR_VIDEO,e.box(e.types.hdlr,i)},e.minf=function(t){var i=null;return i="audio"===t.type?e.box(e.types.smhd,e.constants.SMHD):e.box(e.types.vmhd,e.constants.VMHD),e.box(e.types.minf,i,e.dinf(),e.stbl(t))},e.dinf=function(){return e.box(e.types.dinf,e.box(e.types.dref,e.constants.DREF))},e.stbl=function(t){return e.box(e.types.stbl,e.stsd(t),e.box(e.types.stts,e.constants.STTS),e.box(e.types.stsc,e.constants.STSC),e.box(e.types.stsz,e.constants.STSZ),e.box(e.types.stco,e.constants.STCO))},e.stsd=function(t){return"audio"===t.type?"mp3"===t.codec?e.box(e.types.stsd,e.constants.STSD_PREFIX,e.mp3(t)):"ac-3"===t.codec?e.box(e.types.stsd,e.constants.STSD_PREFIX,e.ac3(t)):"ec-3"===t.codec?e.box(e.types.stsd,e.constants.STSD_PREFIX,e.ec3(t)):"opus"===t.codec?e.box(e.types.stsd,e.constants.STSD_PREFIX,e.Opus(t)):e.box(e.types.stsd,e.constants.STSD_PREFIX,e.mp4a(t)):"video"===t.type&&t.codec.startsWith("hvc1")?e.box(e.types.stsd,e.constants.STSD_PREFIX,e.hvc1(t)):"video"===t.type&&t.codec.startsWith("av01")?e.box(e.types.stsd,e.constants.STSD_PREFIX,e.av01(t)):e.box(e.types.stsd,e.constants.STSD_PREFIX,e.avc1(t))},e.mp3=function(t){var i=t.channelCount,n=t.audioSampleRate,a=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,i,0,16,0,0,0,0,n>>>8&255,255&n,0,0]);return e.box(e.types[".mp3"],a)},e.mp4a=function(t){var i=t.channelCount,n=t.audioSampleRate,a=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,i,0,16,0,0,0,0,n>>>8&255,255&n,0,0]);return e.box(e.types.mp4a,a,e.esds(t))},e.ac3=function(t){var i=t.channelCount,n=t.audioSampleRate,a=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,i,0,16,0,0,0,0,n>>>8&255,255&n,0,0]);return e.box(e.types["ac-3"],a,e.box(e.types.dac3,new Uint8Array(t.config)))},e.ec3=function(t){var i=t.channelCount,n=t.audioSampleRate,a=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,i,0,16,0,0,0,0,n>>>8&255,255&n,0,0]);return e.box(e.types["ec-3"],a,e.box(e.types.dec3,new Uint8Array(t.config)))},e.esds=function(t){var i=t.config||[],n=i.length,a=new Uint8Array([0,0,0,0,3,23+n,0,1,0,4,15+n,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([n]).concat(i).concat([6,1,2]));return e.box(e.types.esds,a)},e.Opus=function(t){var i=t.channelCount,n=t.audioSampleRate,a=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,i,0,16,0,0,0,0,n>>>8&255,255&n,0,0]);return e.box(e.types.Opus,a,e.dOps(t))},e.dOps=function(t){var i=t.channelCount,n=t.channelConfigCode,a=t.audioSampleRate;if(t.config)return e.box(e.types.dOps,o);var r=[];switch(n){case 1:case 2:r=[0];break;case 0:r=[255,1,1,0,1];break;case 128:r=[255,2,0,0,1];break;case 3:r=[1,2,1,0,2,1];break;case 4:r=[1,2,2,0,1,2,3];break;case 5:r=[1,3,2,0,4,1,2,3];break;case 6:r=[1,4,2,0,4,1,2,3,5];break;case 7:r=[1,4,2,0,4,1,2,3,5,6];break;case 8:r=[1,5,3,0,6,1,2,3,4,5,7];break;case 130:r=[1,1,2,0,1];break;case 131:r=[1,1,3,0,1,2];break;case 132:r=[1,1,4,0,1,2,3];break;case 133:r=[1,1,5,0,1,2,3,4];break;case 134:r=[1,1,6,0,1,2,3,4,5];break;case 135:r=[1,1,7,0,1,2,3,4,5,6];break;case 136:r=[1,1,8,0,1,2,3,4,5,6,7]}var o=new Uint8Array(Le([0,i,0,0,a>>>24&255,a>>>17&255,a>>>8&255,a>>>0&255,0,0],r,!0));return e.box(e.types.dOps,o)},e.avc1=function(t){var i=t.avcc,n=t.codecWidth,a=t.codecHeight,r=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,n>>>8&255,255&n,a>>>8&255,255&a,0,72,0,0,0,72,0,0,0,0,0,0,0,1,10,120,113,113,47,102,108,118,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return e.box(e.types.avc1,r,e.box(e.types.avcC,i))},e.hvc1=function(t){var i=t.hvcc,n=t.codecWidth,a=t.codecHeight,r=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,n>>>8&255,255&n,a>>>8&255,255&a,0,72,0,0,0,72,0,0,0,0,0,0,0,1,10,120,113,113,47,102,108,118,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return e.box(e.types.hvc1,r,e.box(e.types.hvcC,i))},e.av01=function(t){var i=t.av1c,n=t.codecWidth||192,a=t.codecHeight||108,r=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,n>>>8&255,255&n,a>>>8&255,255&a,0,72,0,0,0,72,0,0,0,0,0,0,0,1,10,120,113,113,47,102,108,118,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,255,255]);return e.box(e.types.av01,r,e.box(e.types.av1C,i))},e.mvex=function(t){return e.box(e.types.mvex,e.trex(t))},e.trex=function(t){var i=t.id,n=new Uint8Array([0,0,0,0,i>>>24&255,i>>>16&255,i>>>8&255,255&i,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return e.box(e.types.trex,n)},e.moof=function(t,i){return e.box(e.types.moof,e.mfhd(t.sequenceNumber),e.traf(t,i))},e.mfhd=function(t){var i=new Uint8Array([0,0,0,0,t>>>24&255,t>>>16&255,t>>>8&255,255&t]);return e.box(e.types.mfhd,i)},e.traf=function(t,i){var n=t.id,a=e.box(e.types.tfhd,new Uint8Array([0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n])),r=e.box(e.types.tfdt,new Uint8Array([0,0,0,0,i>>>24&255,i>>>16&255,i>>>8&255,255&i])),o=e.sdtp(t),s=e.trun(t,o.byteLength+16+16+8+16+8+8);return e.box(e.types.traf,a,r,s,o)},e.sdtp=function(t){for(var i=t.samples||[],n=i.length,a=new Uint8Array(4+n),r=0;r<n;r++){var o=i[r].flags;a[r+4]=o.isLeading<<6|o.dependsOn<<4|o.isDependedOn<<2|o.hasRedundancy}return e.box(e.types.sdtp,a)},e.trun=function(t,i){var n=t.samples||[],a=n.length,r=12+16*a,o=new Uint8Array(r);i+=8+r,o.set([0,0,15,1,a>>>24&255,a>>>16&255,a>>>8&255,255&a,i>>>24&255,i>>>16&255,i>>>8&255,255&i],0);for(var s=0;s<a;s++){var d=n[s].duration,_=n[s].size,c=n[s].flags,h=n[s].cts;o.set([d>>>24&255,d>>>16&255,d>>>8&255,255&d,_>>>24&255,_>>>16&255,_>>>8&255,255&_,c.isLeading<<2|c.dependsOn,c.isDependedOn<<6|c.hasRedundancy<<4|c.isNonSync,0,0,h>>>24&255,h>>>16&255,h>>>8&255,255&h],12+16*s)}return e.box(e.types.trun,o)},e.mdat=function(t){return e.box(e.types.mdat,t)},e}();ke.init();var we=ke,De=function(){function e(){}return e.getSilentFrame=function(e,t){if("mp4a.40.2"===e){if(1===t)return new Uint8Array([0,200,0,128,35,128]);if(2===t)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(3===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(4===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(5===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(6===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224])}else{if(1===t)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(2===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(3===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}return null},e}(),Me=i(11),Ce=function(){function e(e){this.TAG="MP4Remuxer",this._config=e,this._isLive=!0===e.isLive,this._dtsBase=-1,this._dtsBaseInited=!1,this._audioDtsBase=1/0,this._videoDtsBase=1/0,this._audioNextDts=void 0,this._videoNextDts=void 0,this._audioStashedLastSample=null,this._videoStashedLastSample=null,this._audioMeta=null,this._videoMeta=null,this._audioSegmentInfoList=new Me.c("audio"),this._videoSegmentInfoList=new Me.c("video"),this._onInitSegment=null,this._onMediaSegment=null,this._forceFirstIDR=!(!o.a.chrome||!(o.a.version.major<50||50===o.a.version.major&&o.a.version.build<2661)),this._fillSilentAfterSeek=o.a.msedge||o.a.msie,this._mp3UseMpegAudio=!o.a.firefox,this._fillAudioTimestampGap=this._config.fixAudioTimestampGap}return e.prototype.destroy=function(){this._dtsBase=-1,this._dtsBaseInited=!1,this._audioMeta=null,this._videoMeta=null,this._audioSegmentInfoList.clear(),this._audioSegmentInfoList=null,this._videoSegmentInfoList.clear(),this._videoSegmentInfoList=null,this._onInitSegment=null,this._onMediaSegment=null},e.prototype.bindDataSource=function(e){return e.onDataAvailable=this.remux.bind(this),e.onTrackMetadata=this._onTrackMetadataReceived.bind(this),this},Object.defineProperty(e.prototype,"onInitSegment",{get:function(){return this._onInitSegment},set:function(e){this._onInitSegment=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onMediaSegment",{get:function(){return this._onMediaSegment},set:function(e){this._onMediaSegment=e},enumerable:!1,configurable:!0}),e.prototype.insertDiscontinuity=function(){this._audioNextDts=this._videoNextDts=void 0},e.prototype.seek=function(e){this._audioStashedLastSample=null,this._videoStashedLastSample=null,this._videoSegmentInfoList.clear(),this._audioSegmentInfoList.clear()},e.prototype.remux=function(e,t){if(!this._onMediaSegment)throw new h.a("MP4Remuxer: onMediaSegment callback must be specificed!");this._dtsBaseInited||this._calculateDtsBase(e,t),t&&this._remuxVideo(t),e&&this._remuxAudio(e)},e.prototype._onTrackMetadataReceived=function(e,t){var i=null,n="mp4",a=t.codec;if("audio"===e)this._audioMeta=t,"mp3"===t.codec&&this._mp3UseMpegAudio?(n="mpeg",a="",i=new Uint8Array):i=we.generateInitSegment(t);else{if("video"!==e)return;this._videoMeta=t,i=we.generateInitSegment(t)}if(!this._onInitSegment)throw new h.a("MP4Remuxer: onInitSegment callback must be specified!");this._onInitSegment(e,{type:e,data:i.buffer,codec:a,container:"".concat(e,"/").concat(n),mediaDuration:t.duration})},e.prototype._calculateDtsBase=function(e,t){this._dtsBaseInited||(e&&e.samples&&e.samples.length&&(this._audioDtsBase=e.samples[0].dts),t&&t.samples&&t.samples.length&&(this._videoDtsBase=t.samples[0].dts),this._dtsBase=Math.min(this._audioDtsBase,this._videoDtsBase),this._dtsBaseInited=!0)},e.prototype.getTimestampBase=function(){if(this._dtsBaseInited)return this._dtsBase},e.prototype.flushStashedSamples=function(){var e=this._videoStashedLastSample,t=this._audioStashedLastSample,i={type:"video",id:1,sequenceNumber:0,samples:[],length:0};null!=e&&(i.samples.push(e),i.length=e.length);var n={type:"audio",id:2,sequenceNumber:0,samples:[],length:0};null!=t&&(n.samples.push(t),n.length=t.length),this._videoStashedLastSample=null,this._audioStashedLastSample=null,this._remuxVideo(i,!0),this._remuxAudio(n,!0)},e.prototype._remuxAudio=function(e,t){if(null!=this._audioMeta){var i,n=e,a=n.samples,s=void 0,d=-1,_=this._audioMeta.refSampleDuration,c="mp3"===this._audioMeta.codec&&this._mp3UseMpegAudio,h=this._dtsBaseInited&&void 0===this._audioNextDts,l=!1;if(a&&0!==a.length&&(1!==a.length||t)){var u=0,f=null,p=0;c?(u=0,p=n.length):(u=8,p=8+n.length);var m=null;if(a.length>1&&(p-=(m=a.pop()).length),null!=this._audioStashedLastSample){var g=this._audioStashedLastSample;this._audioStashedLastSample=null,a.unshift(g),p+=g.length}null!=m&&(this._audioStashedLastSample=m);var y=a[0].dts-this._dtsBase;if(this._audioNextDts)s=y-this._audioNextDts;else if(this._audioSegmentInfoList.isEmpty())s=0,this._fillSilentAfterSeek&&!this._videoSegmentInfoList.isEmpty()&&"mp3"!==this._audioMeta.originalCodec&&(l=!0);else{var v=this._audioSegmentInfoList.getLastSampleBefore(y);if(null!=v){var S=y-(v.originalDts+v.duration);S<=3&&(S=0),s=y-(v.dts+v.duration+S)}else s=0}if(l){var b=y-s,E=this._videoSegmentInfoList.getLastSegmentBefore(y);if(null!=E&&E.beginDts<b){if(B=De.getSilentFrame(this._audioMeta.originalCodec,this._audioMeta.channelCount)){var A=E.beginDts,R=b-E.beginDts;r.a.v(this.TAG,"InsertPrefixSilentAudio: dts: ".concat(A,", duration: ").concat(R)),a.unshift({unit:B,dts:A,pts:A}),p+=B.byteLength}}else l=!1}for(var T=[],L=0;L<a.length;L++){var k=(g=a[L]).unit,w=g.dts-this._dtsBase,D=(A=w,!1),M=null,C=0;if(!(w<-.001)){if("mp3"!==this._audioMeta.codec){var O=w;if(this._audioNextDts&&(O=this._audioNextDts),(s=w-O)<=-3*_){r.a.w(this.TAG,"Dropping 1 audio frame (originalDts: ".concat(w," ms ,curRefDts: ").concat(O," ms)  due to dtsCorrection: ").concat(s," ms overlap."));continue}if(s>=3*_&&this._fillAudioTimestampGap&&!o.a.safari){D=!0;var B,I=Math.floor(s/_);r.a.w(this.TAG,"Large audio timestamp gap detected, may cause AV sync to drift. Silent frames will be generated to avoid unsync.\n"+"originalDts: ".concat(w," ms, curRefDts: ").concat(O," ms, ")+"dtsCorrection: ".concat(Math.round(s)," ms, generate: ").concat(I," frames")),A=Math.floor(O),C=Math.floor(O+_)-A,null==(B=De.getSilentFrame(this._audioMeta.originalCodec,this._audioMeta.channelCount))&&(r.a.w(this.TAG,"Unable to generate silent frame for "+"".concat(this._audioMeta.originalCodec," with ").concat(this._audioMeta.channelCount," channels, repeat last frame")),B=k),M=[];for(var P=0;P<I;P++){O+=_;var x=Math.floor(O),U=Math.floor(O+_)-x,N={dts:x,pts:x,cts:0,unit:B,size:B.byteLength,duration:U,originalDts:w,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0}};M.push(N),p+=N.size}this._audioNextDts=O+_}else A=Math.floor(O),C=Math.floor(O+_)-A,this._audioNextDts=O+_}else{if(A=w-s,L!==a.length-1)C=a[L+1].dts-this._dtsBase-s-A;else if(null!=m)C=m.dts-this._dtsBase-s-A;else C=T.length>=1?T[T.length-1].duration:Math.floor(_);this._audioNextDts=A+C}-1===d&&(d=A),T.push({dts:A,pts:A,cts:0,unit:g.unit,size:g.unit.byteLength,duration:C,originalDts:w,flags:{isLeading:0,dependsOn:1,isDependedOn:0,hasRedundancy:0}}),D&&T.push.apply(T,M)}}if(0===T.length)return n.samples=[],void(n.length=0);c?f=new Uint8Array(p):((f=new Uint8Array(p))[0]=p>>>24&255,f[1]=p>>>16&255,f[2]=p>>>8&255,f[3]=255&p,f.set(we.types.mdat,4));for(L=0;L<T.length;L++){k=T[L].unit;f.set(k,u),u+=k.byteLength}var V=T[T.length-1];i=V.dts+V.duration;var G=new Me.b;G.beginDts=d,G.endDts=i,G.beginPts=d,G.endPts=i,G.originalBeginDts=T[0].originalDts,G.originalEndDts=V.originalDts+V.duration,G.firstSample=new Me.d(T[0].dts,T[0].pts,T[0].duration,T[0].originalDts,!1),G.lastSample=new Me.d(V.dts,V.pts,V.duration,V.originalDts,!1),this._isLive||this._audioSegmentInfoList.append(G),n.samples=T,n.sequenceNumber++;var F=null;F=c?new Uint8Array:we.moof(n,d),n.samples=[],n.length=0;var j={type:"audio",data:this._mergeBoxes(F,f).buffer,sampleCount:T.length,info:G};c&&h&&(j.timestampOffset=d),this._onMediaSegment("audio",j)}}},e.prototype._remuxVideo=function(e,t){if(null!=this._videoMeta){var i,n,a=e,r=a.samples,o=void 0,s=-1,d=-1;if(r&&0!==r.length&&(1!==r.length||t)){var _=8,c=null,h=8+e.length,l=null;if(r.length>1&&(h-=(l=r.pop()).length),null!=this._videoStashedLastSample){var u=this._videoStashedLastSample;this._videoStashedLastSample=null,r.unshift(u),h+=u.length}null!=l&&(this._videoStashedLastSample=l);var f=r[0].dts-this._dtsBase;if(this._videoNextDts)o=f-this._videoNextDts;else if(this._videoSegmentInfoList.isEmpty())o=0;else{var p=this._videoSegmentInfoList.getLastSampleBefore(f);if(null!=p){var m=f-(p.originalDts+p.duration);m<=3&&(m=0),o=f-(p.dts+p.duration+m)}else o=0}for(var g=new Me.b,y=[],v=0;v<r.length;v++){var S=(u=r[v]).dts-this._dtsBase,b=u.isKeyframe,E=S-o,A=u.cts,R=E+A;-1===s&&(s=E,d=R);var T=0;if(v!==r.length-1)T=r[v+1].dts-this._dtsBase-o-E;else if(null!=l)T=l.dts-this._dtsBase-o-E;else T=y.length>=1?y[y.length-1].duration:Math.floor(this._videoMeta.refSampleDuration);if(b){var L=new Me.d(E,R,T,u.dts,!0);L.fileposition=u.fileposition,g.appendSyncPoint(L)}y.push({dts:E,pts:R,cts:A,units:u.units,size:u.length,isKeyframe:b,duration:T,originalDts:S,flags:{isLeading:0,dependsOn:b?2:1,isDependedOn:b?1:0,hasRedundancy:0,isNonSync:b?0:1}})}(c=new Uint8Array(h))[0]=h>>>24&255,c[1]=h>>>16&255,c[2]=h>>>8&255,c[3]=255&h,c.set(we.types.mdat,4);for(v=0;v<y.length;v++)for(var k=y[v].units;k.length;){var w=k.shift().data;c.set(w,_),_+=w.byteLength}var D=y[y.length-1];if(i=D.dts+D.duration,n=D.pts+D.duration,this._videoNextDts=i,g.beginDts=s,g.endDts=i,g.beginPts=d,g.endPts=n,g.originalBeginDts=y[0].originalDts,g.originalEndDts=D.originalDts+D.duration,g.firstSample=new Me.d(y[0].dts,y[0].pts,y[0].duration,y[0].originalDts,y[0].isKeyframe),g.lastSample=new Me.d(D.dts,D.pts,D.duration,D.originalDts,D.isKeyframe),this._isLive||this._videoSegmentInfoList.append(g),a.samples=y,a.sequenceNumber++,this._forceFirstIDR){var M=y[0].flags;M.dependsOn=2,M.isNonSync=0}var C=we.moof(a,s);a.samples=[],a.length=0,this._onMediaSegment("video",{type:"video",data:this._mergeBoxes(C,c).buffer,sampleCount:y.length,info:g})}}},e.prototype._mergeBoxes=function(e,t){var i=new Uint8Array(e.byteLength+t.byteLength);return i.set(e,0),i.set(t,e.byteLength),i},e}(),Oe=i(14),Be=i(1),Ie=(i(2),function(){function e(e,t){this.TAG="TransmuxingController",this._emitter=new a.a,this._config=t,e.segments||(e.segments=[{duration:e.duration,filesize:e.filesize,url:e.url}]),"boolean"!=typeof e.cors&&(e.cors=!0),"boolean"!=typeof e.withCredentials&&(e.withCredentials=!1),this._mediaDataSource=e,this._currentSegmentIndex=0;var i=0;this._mediaDataSource.segments.forEach((function(n){n.timestampBase=i,i+=n.duration,n.cors=e.cors,n.withCredentials=e.withCredentials,t.referrerPolicy&&(n.referrerPolicy=t.referrerPolicy)})),isNaN(i)||this._mediaDataSource.duration===i||(this._mediaDataSource.duration=i),this._mediaInfo=null,this._demuxer=null,this._remuxer=null,this._ioctl=null,this._pendingSeekTime=null,this._pendingResolveSeekPoint=null,this._statisticsReporter=null}return e.prototype.destroy=function(){this._mediaInfo=null,this._mediaDataSource=null,this._statisticsReporter&&this._disableStatisticsReporter(),this._ioctl&&(this._ioctl.destroy(),this._ioctl=null),this._demuxer&&(this._demuxer.destroy(),this._demuxer=null),this._remuxer&&(this._remuxer.destroy(),this._remuxer=null),this._emitter.removeAllListeners(),this._emitter=null},e.prototype.on=function(e,t){this._emitter.addListener(e,t)},e.prototype.off=function(e,t){this._emitter.removeListener(e,t)},e.prototype.start=function(){this._loadSegment(0),this._enableStatisticsReporter()},e.prototype._loadSegment=function(e,t){this._currentSegmentIndex=e;var i=this._mediaDataSource.segments[e],n=this._ioctl=new Oe.a(i,this._config,e);n.onError=this._onIOException.bind(this),n.onSeeked=this._onIOSeeked.bind(this),n.onComplete=this._onIOComplete.bind(this),n.onRedirect=this._onIORedirect.bind(this),n.onRecoveredEarlyEof=this._onIORecoveredEarlyEof.bind(this),t?this._demuxer.bindDataSource(this._ioctl):n.onDataArrival=this._onInitChunkArrival.bind(this),n.open(t)},e.prototype.stop=function(){this._internalAbort(),this._disableStatisticsReporter()},e.prototype._internalAbort=function(){this._ioctl&&(this._ioctl.destroy(),this._ioctl=null)},e.prototype.pause=function(){this._ioctl&&this._ioctl.isWorking()&&(this._ioctl.pause(),this._disableStatisticsReporter())},e.prototype.resume=function(){this._ioctl&&this._ioctl.isPaused()&&(this._ioctl.resume(),this._enableStatisticsReporter())},e.prototype.seek=function(e){if(null!=this._mediaInfo&&this._mediaInfo.isSeekable()){var t=this._searchSegmentIndexContains(e);if(t===this._currentSegmentIndex){var i=this._mediaInfo.segments[t];if(null==i)this._pendingSeekTime=e;else{var n=i.getNearestKeyframe(e);this._remuxer.seek(n.milliseconds),this._ioctl.seek(n.fileposition),this._pendingResolveSeekPoint=n.milliseconds}}else{var a=this._mediaInfo.segments[t];if(null==a)this._pendingSeekTime=e,this._internalAbort(),this._remuxer.seek(),this._remuxer.insertDiscontinuity(),this._loadSegment(t);else{n=a.getNearestKeyframe(e);this._internalAbort(),this._remuxer.seek(e),this._remuxer.insertDiscontinuity(),this._demuxer.resetMediaInfo(),this._demuxer.timestampBase=this._mediaDataSource.segments[t].timestampBase,this._loadSegment(t,n.fileposition),this._pendingResolveSeekPoint=n.milliseconds,this._reportSegmentMediaInfo(t)}}this._enableStatisticsReporter()}},e.prototype._searchSegmentIndexContains=function(e){for(var t=this._mediaDataSource.segments,i=t.length-1,n=0;n<t.length;n++)if(e<t[n].timestampBase){i=n-1;break}return i},e.prototype._onInitChunkArrival=function(e,t){var i=this,n=0;if(t>0)this._demuxer.bindDataSource(this._ioctl),this._demuxer.timestampBase=this._mediaDataSource.segments[this._currentSegmentIndex].timestampBase,n=this._demuxer.parseChunks(e,t);else{var a=null;(a=R.probe(e)).match&&(this._setupFLVDemuxerRemuxer(a),n=this._demuxer.parseChunks(e,t)),a.match||a.needMoreData||(a=Te.probe(e)).match&&(this._setupTSDemuxerRemuxer(a),n=this._demuxer.parseChunks(e,t)),a.match||a.needMoreData||(a=null,r.a.e(this.TAG,"Non MPEG-TS/FLV, Unsupported media type!"),Promise.resolve().then((function(){i._internalAbort()})),this._emitter.emit(Be.a.DEMUX_ERROR,m.a.FORMAT_UNSUPPORTED,"Non MPEG-TS/FLV, Unsupported media type!"))}return n},e.prototype._setupFLVDemuxerRemuxer=function(e){this._demuxer=new R(e,this._config),this._remuxer||(this._remuxer=new Ce(this._config));var t=this._mediaDataSource;null==t.duration||isNaN(t.duration)||(this._demuxer.overridedDuration=t.duration),"boolean"==typeof t.hasAudio&&(this._demuxer.overridedHasAudio=t.hasAudio),"boolean"==typeof t.hasVideo&&(this._demuxer.overridedHasVideo=t.hasVideo),this._demuxer.timestampBase=t.segments[this._currentSegmentIndex].timestampBase,this._demuxer.onError=this._onDemuxException.bind(this),this._demuxer.onMediaInfo=this._onMediaInfo.bind(this),this._demuxer.onMetaDataArrived=this._onMetaDataArrived.bind(this),this._demuxer.onScriptDataArrived=this._onScriptDataArrived.bind(this),this._remuxer.bindDataSource(this._demuxer.bindDataSource(this._ioctl)),this._remuxer.onInitSegment=this._onRemuxerInitSegmentArrival.bind(this),this._remuxer.onMediaSegment=this._onRemuxerMediaSegmentArrival.bind(this)},e.prototype._setupTSDemuxerRemuxer=function(e){var t=this._demuxer=new Te(e,this._config);this._remuxer||(this._remuxer=new Ce(this._config)),t.onError=this._onDemuxException.bind(this),t.onMediaInfo=this._onMediaInfo.bind(this),t.onMetaDataArrived=this._onMetaDataArrived.bind(this),t.onTimedID3Metadata=this._onTimedID3Metadata.bind(this),t.onSynchronousKLVMetadata=this._onSynchronousKLVMetadata.bind(this),t.onAsynchronousKLVMetadata=this._onAsynchronousKLVMetadata.bind(this),t.onSMPTE2038Metadata=this._onSMPTE2038Metadata.bind(this),t.onSCTE35Metadata=this._onSCTE35Metadata.bind(this),t.onPESPrivateDataDescriptor=this._onPESPrivateDataDescriptor.bind(this),t.onPESPrivateData=this._onPESPrivateData.bind(this),this._remuxer.bindDataSource(this._demuxer),this._demuxer.bindDataSource(this._ioctl),this._remuxer.onInitSegment=this._onRemuxerInitSegmentArrival.bind(this),this._remuxer.onMediaSegment=this._onRemuxerMediaSegmentArrival.bind(this)},e.prototype._onMediaInfo=function(e){var t=this;null==this._mediaInfo&&(this._mediaInfo=Object.assign({},e),this._mediaInfo.keyframesIndex=null,this._mediaInfo.segments=[],this._mediaInfo.segmentCount=this._mediaDataSource.segments.length,Object.setPrototypeOf(this._mediaInfo,s.a.prototype));var i=Object.assign({},e);Object.setPrototypeOf(i,s.a.prototype),this._mediaInfo.segments[this._currentSegmentIndex]=i,this._reportSegmentMediaInfo(this._currentSegmentIndex),null!=this._pendingSeekTime&&Promise.resolve().then((function(){var e=t._pendingSeekTime;t._pendingSeekTime=null,t.seek(e)}))},e.prototype._onMetaDataArrived=function(e){this._emitter.emit(Be.a.METADATA_ARRIVED,e)},e.prototype._onScriptDataArrived=function(e){this._emitter.emit(Be.a.SCRIPTDATA_ARRIVED,e)},e.prototype._onTimedID3Metadata=function(e){var t=this._remuxer.getTimestampBase();null!=t&&(null!=e.pts&&(e.pts-=t),null!=e.dts&&(e.dts-=t),this._emitter.emit(Be.a.TIMED_ID3_METADATA_ARRIVED,e))},e.prototype._onSynchronousKLVMetadata=function(e){var t=this._remuxer.getTimestampBase();null!=t&&(null!=e.pts&&(e.pts-=t),null!=e.dts&&(e.dts-=t),this._emitter.emit(Be.a.SYNCHRONOUS_KLV_METADATA_ARRIVED,e))},e.prototype._onAsynchronousKLVMetadata=function(e){this._emitter.emit(Be.a.ASYNCHRONOUS_KLV_METADATA_ARRIVED,e)},e.prototype._onSMPTE2038Metadata=function(e){var t=this._remuxer.getTimestampBase();null!=t&&(null!=e.pts&&(e.pts-=t),null!=e.dts&&(e.dts-=t),null!=e.nearest_pts&&(e.nearest_pts-=t),this._emitter.emit(Be.a.SMPTE2038_METADATA_ARRIVED,e))},e.prototype._onSCTE35Metadata=function(e){var t=this._remuxer.getTimestampBase();null!=t&&(null!=e.pts&&(e.pts-=t),null!=e.nearest_pts&&(e.nearest_pts-=t),this._emitter.emit(Be.a.SCTE35_METADATA_ARRIVED,e))},e.prototype._onPESPrivateDataDescriptor=function(e){this._emitter.emit(Be.a.PES_PRIVATE_DATA_DESCRIPTOR,e)},e.prototype._onPESPrivateData=function(e){var t=this._remuxer.getTimestampBase();null!=t&&(null!=e.pts&&(e.pts-=t),null!=e.nearest_pts&&(e.nearest_pts-=t),null!=e.dts&&(e.dts-=t),this._emitter.emit(Be.a.PES_PRIVATE_DATA_ARRIVED,e))},e.prototype._onIOSeeked=function(){this._remuxer.insertDiscontinuity()},e.prototype._onIOComplete=function(e){var t=e+1;t<this._mediaDataSource.segments.length?(this._internalAbort(),this._remuxer&&this._remuxer.flushStashedSamples(),this._loadSegment(t)):(this._remuxer&&this._remuxer.flushStashedSamples(),this._emitter.emit(Be.a.LOADING_COMPLETE),this._disableStatisticsReporter())},e.prototype._onIORedirect=function(e){var t=this._ioctl.extraData;this._mediaDataSource.segments[t].redirectedURL=e},e.prototype._onIORecoveredEarlyEof=function(){this._emitter.emit(Be.a.RECOVERED_EARLY_EOF)},e.prototype._onIOException=function(e,t){r.a.e(this.TAG,"IOException: type = ".concat(e,", code = ").concat(t.code,", msg = ").concat(t.msg)),this._emitter.emit(Be.a.IO_ERROR,e,t),this._disableStatisticsReporter()},e.prototype._onDemuxException=function(e,t){r.a.e(this.TAG,"DemuxException: type = ".concat(e,", info = ").concat(t)),this._emitter.emit(Be.a.DEMUX_ERROR,e,t)},e.prototype._onRemuxerInitSegmentArrival=function(e,t){this._emitter.emit(Be.a.INIT_SEGMENT,e,t)},e.prototype._onRemuxerMediaSegmentArrival=function(e,t){if(null==this._pendingSeekTime&&(this._emitter.emit(Be.a.MEDIA_SEGMENT,e,t),null!=this._pendingResolveSeekPoint&&"video"===e)){var i=t.info.syncPoints,n=this._pendingResolveSeekPoint;this._pendingResolveSeekPoint=null,o.a.safari&&i.length>0&&i[0].originalDts===n&&(n=i[0].pts),this._emitter.emit(Be.a.RECOMMEND_SEEKPOINT,n)}},e.prototype._enableStatisticsReporter=function(){null==this._statisticsReporter&&(this._statisticsReporter=self.setInterval(this._reportStatisticsInfo.bind(this),this._config.statisticsInfoReportInterval))},e.prototype._disableStatisticsReporter=function(){this._statisticsReporter&&(self.clearInterval(this._statisticsReporter),this._statisticsReporter=null)},e.prototype._reportSegmentMediaInfo=function(e){var t=this._mediaInfo.segments[e],i=Object.assign({},t);i.duration=this._mediaInfo.duration,i.segmentCount=this._mediaInfo.segmentCount,delete i.segments,delete i.keyframesIndex,this._emitter.emit(Be.a.MEDIA_INFO,i)},e.prototype._reportStatisticsInfo=function(){var e={};e.url=this._ioctl.currentURL,e.hasRedirect=this._ioctl.hasRedirect,e.hasRedirect&&(e.redirectedURL=this._ioctl.currentRedirectedURL),e.speed=this._ioctl.currentSpeed,e.loaderType=this._ioctl.loaderType,e.currentSegmentIndex=this._currentSegmentIndex,e.totalSegmentCount=this._mediaDataSource.segments.length,this._emitter.emit(Be.a.STATISTICS_INFO,e)},e}());t.a=Ie},function(e,t,i){"use strict";var n,a=i(0),r=function(){function e(){this._firstCheckpoint=0,this._lastCheckpoint=0,this._intervalBytes=0,this._totalBytes=0,this._lastSecondBytes=0,self.performance&&self.performance.now?this._now=self.performance.now.bind(self.performance):this._now=Date.now}return e.prototype.reset=function(){this._firstCheckpoint=this._lastCheckpoint=0,this._totalBytes=this._intervalBytes=0,this._lastSecondBytes=0},e.prototype.addBytes=function(e){0===this._firstCheckpoint?(this._firstCheckpoint=this._now(),this._lastCheckpoint=this._firstCheckpoint,this._intervalBytes+=e,this._totalBytes+=e):this._now()-this._lastCheckpoint<1e3?(this._intervalBytes+=e,this._totalBytes+=e):(this._lastSecondBytes=this._intervalBytes,this._intervalBytes=e,this._totalBytes+=e,this._lastCheckpoint=this._now())},Object.defineProperty(e.prototype,"currentKBps",{get:function(){this.addBytes(0);var e=(this._now()-this._lastCheckpoint)/1e3;return 0==e&&(e=1),this._intervalBytes/e/1024},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"lastSecondKBps",{get:function(){return this.addBytes(0),0!==this._lastSecondBytes?this._lastSecondBytes/1024:this._now()-this._lastCheckpoint>=500?this.currentKBps:0},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"averageKBps",{get:function(){var e=(this._now()-this._firstCheckpoint)/1e3;return this._totalBytes/e/1024},enumerable:!1,configurable:!0}),e}(),o=i(2),s=i(5),d=i(3),_=(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),c=function(e){function t(t,i){var n=e.call(this,"fetch-stream-loader")||this;return n.TAG="FetchStreamLoader",n._seekHandler=t,n._config=i,n._needStash=!0,n._requestAbort=!1,n._abortController=null,n._contentLength=null,n._receivedLength=0,n}return _(t,e),t.isSupported=function(){try{var e=s.a.msedge&&s.a.version.minor>=15048,t=!s.a.msedge||e;return self.fetch&&self.ReadableStream&&t}catch(e){return!1}},t.prototype.destroy=function(){this.isWorking()&&this.abort(),e.prototype.destroy.call(this)},t.prototype.open=function(e,t){var i=this;this._dataSource=e,this._range=t;var n=e.url;this._config.reuseRedirectedURL&&null!=e.redirectedURL&&(n=e.redirectedURL);var a=this._seekHandler.getConfig(n,t),r=new self.Headers;if("object"==typeof a.headers){var s=a.headers;for(var _ in s)s.hasOwnProperty(_)&&r.append(_,s[_])}var c={method:"GET",headers:r,mode:"cors",cache:"default",referrerPolicy:"no-referrer-when-downgrade"};if("object"==typeof this._config.headers)for(var _ in this._config.headers)r.append(_,this._config.headers[_]);!1===e.cors&&(c.mode="same-origin"),e.withCredentials&&(c.credentials="include"),e.referrerPolicy&&(c.referrerPolicy=e.referrerPolicy),self.AbortController&&(this._abortController=new self.AbortController,c.signal=this._abortController.signal),this._status=o.c.kConnecting,self.fetch(a.url,c).then((function(e){if(i._requestAbort)return i._status=o.c.kIdle,void e.body.cancel();if(e.ok&&e.status>=200&&e.status<=299){if(e.url!==a.url&&i._onURLRedirect){var t=i._seekHandler.removeURLParameters(e.url);i._onURLRedirect(t)}var n=e.headers.get("Content-Length");return null!=n&&(i._contentLength=parseInt(n),0!==i._contentLength&&i._onContentLengthKnown&&i._onContentLengthKnown(i._contentLength)),i._pump.call(i,e.body.getReader())}if(i._status=o.c.kError,!i._onError)throw new d.d("FetchStreamLoader: Http code invalid, "+e.status+" "+e.statusText);i._onError(o.b.HTTP_STATUS_CODE_INVALID,{code:e.status,msg:e.statusText})})).catch((function(e){if(!i._abortController||!i._abortController.signal.aborted){if(i._status=o.c.kError,!i._onError)throw e;i._onError(o.b.EXCEPTION,{code:-1,msg:e.message})}}))},t.prototype.abort=function(){if(this._requestAbort=!0,(this._status!==o.c.kBuffering||!s.a.chrome)&&this._abortController)try{this._abortController.abort()}catch(e){}},t.prototype._pump=function(e){var t=this;return e.read().then((function(i){if(i.done)if(null!==t._contentLength&&t._receivedLength<t._contentLength){t._status=o.c.kError;var n=o.b.EARLY_EOF,a={code:-1,msg:"Fetch stream meet Early-EOF"};if(!t._onError)throw new d.d(a.msg);t._onError(n,a)}else t._status=o.c.kComplete,t._onComplete&&t._onComplete(t._range.from,t._range.from+t._receivedLength-1);else{if(t._abortController&&t._abortController.signal.aborted)return void(t._status=o.c.kComplete);if(!0===t._requestAbort)return t._status=o.c.kComplete,e.cancel();t._status=o.c.kBuffering;var r=i.value.buffer,s=t._range.from+t._receivedLength;t._receivedLength+=r.byteLength,t._onDataArrival&&t._onDataArrival(r,s,t._receivedLength),t._pump(e)}})).catch((function(e){if(t._abortController&&t._abortController.signal.aborted)t._status=o.c.kComplete;else if(11!==e.code||!s.a.msedge){t._status=o.c.kError;var i=0,n=null;if(19!==e.code&&"network error"!==e.message||!(null===t._contentLength||null!==t._contentLength&&t._receivedLength<t._contentLength)?(i=o.b.EXCEPTION,n={code:e.code,msg:e.message}):(i=o.b.EARLY_EOF,n={code:e.code,msg:"Fetch stream meet Early-EOF"}),!t._onError)throw new d.d(n.msg);t._onError(i,n)}}))},t}(o.a),h=function(){var e=function(t,i){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(t,i)};return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),l=function(e){function t(t,i){var n=e.call(this,"xhr-moz-chunked-loader")||this;return n.TAG="MozChunkedLoader",n._seekHandler=t,n._config=i,n._needStash=!0,n._xhr=null,n._requestAbort=!1,n._contentLength=null,n._receivedLength=0,n}return h(t,e),t.isSupported=function(){try{var e=new XMLHttpRequest;return e.open("GET","https://example.com",!0),e.responseType="moz-chunked-arraybuffer","moz-chunked-arraybuffer"===e.responseType}catch(e){return a.a.w("MozChunkedLoader",e.message),!1}},t.prototype.destroy=function(){this.isWorking()&&this.abort(),this._xhr&&(this._xhr.onreadystatechange=null,this._xhr.onprogress=null,this._xhr.onloadend=null,this._xhr.onerror=null,this._xhr=null),e.prototype.destroy.call(this)},t.prototype.open=function(e,t){this._dataSource=e,this._range=t;var i=e.url;this._config.reuseRedirectedURL&&null!=e.redirectedURL&&(i=e.redirectedURL);var n=this._seekHandler.getConfig(i,t);this._requestURL=n.url;var a=this._xhr=new XMLHttpRequest;if(a.open("GET",n.url,!0),a.responseType="moz-chunked-arraybuffer",a.onreadystatechange=this._onReadyStateChange.bind(this),a.onprogress=this._onProgress.bind(this),a.onloadend=this._onLoadEnd.bind(this),a.onerror=this._onXhrError.bind(this),e.withCredentials&&(a.withCredentials=!0),"object"==typeof n.headers){var r=n.headers;for(var s in r)r.hasOwnProperty(s)&&a.setRequestHeader(s,r[s])}if("object"==typeof this._config.headers){r=this._config.headers;for(var s in r)r.hasOwnProperty(s)&&a.setRequestHeader(s,r[s])}this._status=o.c.kConnecting,a.send()},t.prototype.abort=function(){this._requestAbort=!0,this._xhr&&this._xhr.abort(),this._status=o.c.kComplete},t.prototype._onReadyStateChange=function(e){var t=e.target;if(2===t.readyState){if(null!=t.responseURL&&t.responseURL!==this._requestURL&&this._onURLRedirect){var i=this._seekHandler.removeURLParameters(t.responseURL);this._onURLRedirect(i)}if(0!==t.status&&(t.status<200||t.status>299)){if(this._status=o.c.kError,!this._onError)throw new d.d("MozChunkedLoader: Http code invalid, "+t.status+" "+t.statusText);this._onError(o.b.HTTP_STATUS_CODE_INVALID,{code:t.status,msg:t.statusText})}else this._status=o.c.kBuffering}},t.prototype._onProgress=function(e){if(this._status!==o.c.kError){null===this._contentLength&&null!==e.total&&0!==e.total&&(this._contentLength=e.total,this._onContentLengthKnown&&this._onContentLengthKnown(this._contentLength));var t=e.target.response,i=this._range.from+this._receivedLength;this._receivedLength+=t.byteLength,this._onDataArrival&&this._onDataArrival(t,i,this._receivedLength)}},t.prototype._onLoadEnd=function(e){!0!==this._requestAbort?this._status!==o.c.kError&&(this._status=o.c.kComplete,this._onComplete&&this._onComplete(this._range.from,this._range.from+this._receivedLength-1)):this._requestAbort=!1},t.prototype._onXhrError=function(e){this._status=o.c.kError;var t=0,i=null;if(this._contentLength&&e.loaded<this._contentLength?(t=o.b.EARLY_EOF,i={code:-1,msg:"Moz-Chunked stream meet Early-Eof"}):(t=o.b.EXCEPTION,i={code:-1,msg:e.constructor.name+" "+e.type}),!this._onError)throw new d.d(i.msg);this._onError(t,i)},t}(o.a),u=function(){var e=function(t,i){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(t,i)};return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),f=(function(e){function t(t,i){var n=e.call(this,"xhr-msstream-loader")||this;return n.TAG="MSStreamLoader",n._seekHandler=t,n._config=i,n._needStash=!0,n._xhr=null,n._reader=null,n._totalRange=null,n._currentRange=null,n._currentRequestURL=null,n._currentRedirectedURL=null,n._contentLength=null,n._receivedLength=0,n._bufferLimit=16777216,n._lastTimeBufferSize=0,n._isReconnecting=!1,n}u(t,e),t.isSupported=function(){try{if(void 0===self.MSStream||void 0===self.MSStreamReader)return!1;var e=new XMLHttpRequest;return e.open("GET","https://example.com",!0),e.responseType="ms-stream","ms-stream"===e.responseType}catch(e){return a.a.w("MSStreamLoader",e.message),!1}},t.prototype.destroy=function(){this.isWorking()&&this.abort(),this._reader&&(this._reader.onprogress=null,this._reader.onload=null,this._reader.onerror=null,this._reader=null),this._xhr&&(this._xhr.onreadystatechange=null,this._xhr=null),e.prototype.destroy.call(this)},t.prototype.open=function(e,t){this._internalOpen(e,t,!1)},t.prototype._internalOpen=function(e,t,i){this._dataSource=e,i?this._currentRange=t:this._totalRange=t;var n=e.url;this._config.reuseRedirectedURL&&(null!=this._currentRedirectedURL?n=this._currentRedirectedURL:null!=e.redirectedURL&&(n=e.redirectedURL));var a=this._seekHandler.getConfig(n,t);this._currentRequestURL=a.url;var r=this._reader=new self.MSStreamReader;r.onprogress=this._msrOnProgress.bind(this),r.onload=this._msrOnLoad.bind(this),r.onerror=this._msrOnError.bind(this);var s=this._xhr=new XMLHttpRequest;if(s.open("GET",a.url,!0),s.responseType="ms-stream",s.onreadystatechange=this._xhrOnReadyStateChange.bind(this),s.onerror=this._xhrOnError.bind(this),e.withCredentials&&(s.withCredentials=!0),"object"==typeof a.headers){var d=a.headers;for(var _ in d)d.hasOwnProperty(_)&&s.setRequestHeader(_,d[_])}if("object"==typeof this._config.headers){d=this._config.headers;for(var _ in d)d.hasOwnProperty(_)&&s.setRequestHeader(_,d[_])}this._isReconnecting?this._isReconnecting=!1:this._status=o.c.kConnecting,s.send()},t.prototype.abort=function(){this._internalAbort(),this._status=o.c.kComplete},t.prototype._internalAbort=function(){this._reader&&(1===this._reader.readyState&&this._reader.abort(),this._reader.onprogress=null,this._reader.onload=null,this._reader.onerror=null,this._reader=null),this._xhr&&(this._xhr.abort(),this._xhr.onreadystatechange=null,this._xhr=null)},t.prototype._xhrOnReadyStateChange=function(e){var t=e.target;if(2===t.readyState)if(t.status>=200&&t.status<=299){if(this._status=o.c.kBuffering,null!=t.responseURL){var i=this._seekHandler.removeURLParameters(t.responseURL);t.responseURL!==this._currentRequestURL&&i!==this._currentRedirectedURL&&(this._currentRedirectedURL=i,this._onURLRedirect&&this._onURLRedirect(i))}var n=t.getResponseHeader("Content-Length");if(null!=n&&null==this._contentLength){var a=parseInt(n);a>0&&(this._contentLength=a,this._onContentLengthKnown&&this._onContentLengthKnown(this._contentLength))}}else{if(this._status=o.c.kError,!this._onError)throw new d.d("MSStreamLoader: Http code invalid, "+t.status+" "+t.statusText);this._onError(o.b.HTTP_STATUS_CODE_INVALID,{code:t.status,msg:t.statusText})}else if(3===t.readyState&&t.status>=200&&t.status<=299){this._status=o.c.kBuffering;var r=t.response;this._reader.readAsArrayBuffer(r)}},t.prototype._xhrOnError=function(e){this._status=o.c.kError;var t=o.b.EXCEPTION,i={code:-1,msg:e.constructor.name+" "+e.type};if(!this._onError)throw new d.d(i.msg);this._onError(t,i)},t.prototype._msrOnProgress=function(e){var t=e.target.result;if(null!=t){var i=t.slice(this._lastTimeBufferSize);this._lastTimeBufferSize=t.byteLength;var n=this._totalRange.from+this._receivedLength;this._receivedLength+=i.byteLength,this._onDataArrival&&this._onDataArrival(i,n,this._receivedLength),t.byteLength>=this._bufferLimit&&(a.a.v(this.TAG,"MSStream buffer exceeded max size near ".concat(n+i.byteLength,", reconnecting...")),this._doReconnectIfNeeded())}else this._doReconnectIfNeeded()},t.prototype._doReconnectIfNeeded=function(){if(null==this._contentLength||this._receivedLength<this._contentLength){this._isReconnecting=!0,this._lastTimeBufferSize=0,this._internalAbort();var e={from:this._totalRange.from+this._receivedLength,to:-1};this._internalOpen(this._dataSource,e,!0)}},t.prototype._msrOnLoad=function(e){this._status=o.c.kComplete,this._onComplete&&this._onComplete(this._totalRange.from,this._totalRange.from+this._receivedLength-1)},t.prototype._msrOnError=function(e){this._status=o.c.kError;var t=0,i=null;if(this._contentLength&&this._receivedLength<this._contentLength?(t=o.b.EARLY_EOF,i={code:-1,msg:"MSStream meet Early-Eof"}):(t=o.b.EARLY_EOF,i={code:-1,msg:e.constructor.name+" "+e.type}),!this._onError)throw new d.d(i.msg);this._onError(t,i)}}(o.a),function(){var e=function(t,i){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(t,i)};return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}()),p=function(e){function t(t,i){var n=e.call(this,"xhr-range-loader")||this;return n.TAG="RangeLoader",n._seekHandler=t,n._config=i,n._needStash=!1,n._chunkSizeKBList=[128,256,384,512,768,1024,1536,2048,3072,4096,5120,6144,7168,8192],n._currentChunkSizeKB=384,n._currentSpeedNormalized=0,n._zeroSpeedChunkCount=0,n._xhr=null,n._speedSampler=new r,n._requestAbort=!1,n._waitForTotalLength=!1,n._totalLengthReceived=!1,n._currentRequestURL=null,n._currentRedirectedURL=null,n._currentRequestRange=null,n._totalLength=null,n._contentLength=null,n._receivedLength=0,n._lastTimeLoaded=0,n}return f(t,e),t.isSupported=function(){try{var e=new XMLHttpRequest;return e.open("GET","https://example.com",!0),e.responseType="arraybuffer","arraybuffer"===e.responseType}catch(e){return a.a.w("RangeLoader",e.message),!1}},t.prototype.destroy=function(){this.isWorking()&&this.abort(),this._xhr&&(this._xhr.onreadystatechange=null,this._xhr.onprogress=null,this._xhr.onload=null,this._xhr.onerror=null,this._xhr=null),e.prototype.destroy.call(this)},Object.defineProperty(t.prototype,"currentSpeed",{get:function(){return this._speedSampler.lastSecondKBps},enumerable:!1,configurable:!0}),t.prototype.open=function(e,t){this._dataSource=e,this._range=t,this._status=o.c.kConnecting;var i=!1;null!=this._dataSource.filesize&&0!==this._dataSource.filesize&&(i=!0,this._totalLength=this._dataSource.filesize),this._totalLengthReceived||i?this._openSubRange():(this._waitForTotalLength=!0,this._internalOpen(this._dataSource,{from:0,to:-1}))},t.prototype._openSubRange=function(){var e=1024*this._currentChunkSizeKB,t=this._range.from+this._receivedLength,i=t+e;null!=this._contentLength&&i-this._range.from>=this._contentLength&&(i=this._range.from+this._contentLength-1),this._currentRequestRange={from:t,to:i},this._internalOpen(this._dataSource,this._currentRequestRange)},t.prototype._internalOpen=function(e,t){this._lastTimeLoaded=0;var i=e.url;this._config.reuseRedirectedURL&&(null!=this._currentRedirectedURL?i=this._currentRedirectedURL:null!=e.redirectedURL&&(i=e.redirectedURL));var n=this._seekHandler.getConfig(i,t);this._currentRequestURL=n.url;var a=this._xhr=new XMLHttpRequest;if(a.open("GET",n.url,!0),a.responseType="arraybuffer",a.onreadystatechange=this._onReadyStateChange.bind(this),a.onprogress=this._onProgress.bind(this),a.onload=this._onLoad.bind(this),a.onerror=this._onXhrError.bind(this),e.withCredentials&&(a.withCredentials=!0),"object"==typeof n.headers){var r=n.headers;for(var o in r)r.hasOwnProperty(o)&&a.setRequestHeader(o,r[o])}if("object"==typeof this._config.headers){r=this._config.headers;for(var o in r)r.hasOwnProperty(o)&&a.setRequestHeader(o,r[o])}a.send()},t.prototype.abort=function(){this._requestAbort=!0,this._internalAbort(),this._status=o.c.kComplete},t.prototype._internalAbort=function(){this._xhr&&(this._xhr.onreadystatechange=null,this._xhr.onprogress=null,this._xhr.onload=null,this._xhr.onerror=null,this._xhr.abort(),this._xhr=null)},t.prototype._onReadyStateChange=function(e){var t=e.target;if(2===t.readyState){if(null!=t.responseURL){var i=this._seekHandler.removeURLParameters(t.responseURL);t.responseURL!==this._currentRequestURL&&i!==this._currentRedirectedURL&&(this._currentRedirectedURL=i,this._onURLRedirect&&this._onURLRedirect(i))}if(t.status>=200&&t.status<=299){if(this._waitForTotalLength)return;this._status=o.c.kBuffering}else{if(this._status=o.c.kError,!this._onError)throw new d.d("RangeLoader: Http code invalid, "+t.status+" "+t.statusText);this._onError(o.b.HTTP_STATUS_CODE_INVALID,{code:t.status,msg:t.statusText})}}},t.prototype._onProgress=function(e){if(this._status!==o.c.kError){if(null===this._contentLength){var t=!1;if(this._waitForTotalLength){this._waitForTotalLength=!1,this._totalLengthReceived=!0,t=!0;var i=e.total;this._internalAbort(),null!=i&0!==i&&(this._totalLength=i)}if(-1===this._range.to?this._contentLength=this._totalLength-this._range.from:this._contentLength=this._range.to-this._range.from+1,t)return void this._openSubRange();this._onContentLengthKnown&&this._onContentLengthKnown(this._contentLength)}var n=e.loaded-this._lastTimeLoaded;this._lastTimeLoaded=e.loaded,this._speedSampler.addBytes(n)}},t.prototype._normalizeSpeed=function(e){var t=this._chunkSizeKBList,i=t.length-1,n=0,a=0,r=i;if(e<t[0])return t[0];for(;a<=r;){if((n=a+Math.floor((r-a)/2))===i||e>=t[n]&&e<t[n+1])return t[n];t[n]<e?a=n+1:r=n-1}},t.prototype._onLoad=function(e){if(this._status!==o.c.kError)if(this._waitForTotalLength)this._waitForTotalLength=!1;else{this._lastTimeLoaded=0;var t=this._speedSampler.lastSecondKBps;if(0===t&&(this._zeroSpeedChunkCount++,this._zeroSpeedChunkCount>=3&&(t=this._speedSampler.currentKBps)),0!==t){var i=this._normalizeSpeed(t);this._currentSpeedNormalized!==i&&(this._currentSpeedNormalized=i,this._currentChunkSizeKB=i)}var n=e.target.response,a=this._range.from+this._receivedLength;this._receivedLength+=n.byteLength;var r=!1;null!=this._contentLength&&this._receivedLength<this._contentLength?this._openSubRange():r=!0,this._onDataArrival&&this._onDataArrival(n,a,this._receivedLength),r&&(this._status=o.c.kComplete,this._onComplete&&this._onComplete(this._range.from,this._range.from+this._receivedLength-1))}},t.prototype._onXhrError=function(e){this._status=o.c.kError;var t=0,i=null;if(this._contentLength&&this._receivedLength>0&&this._receivedLength<this._contentLength?(t=o.b.EARLY_EOF,i={code:-1,msg:"RangeLoader meet Early-Eof"}):(t=o.b.EXCEPTION,i={code:-1,msg:e.constructor.name+" "+e.type}),!this._onError)throw new d.d(i.msg);this._onError(t,i)},t}(o.a),m=function(){var e=function(t,i){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(t,i)};return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)}}(),g=function(e){function t(){var t=e.call(this,"websocket-loader")||this;return t.TAG="WebSocketLoader",t._needStash=!0,t._ws=null,t._requestAbort=!1,t._receivedLength=0,t}return m(t,e),t.isSupported=function(){try{return void 0!==self.WebSocket}catch(e){return!1}},t.prototype.destroy=function(){this._ws&&this.abort(),e.prototype.destroy.call(this)},t.prototype.open=function(e){try{var t=this._ws=new self.WebSocket(e.url);t.binaryType="arraybuffer",t.onopen=this._onWebSocketOpen.bind(this),t.onclose=this._onWebSocketClose.bind(this),t.onmessage=this._onWebSocketMessage.bind(this),t.onerror=this._onWebSocketError.bind(this),this._status=o.c.kConnecting}catch(e){this._status=o.c.kError;var i={code:e.code,msg:e.message};if(!this._onError)throw new d.d(i.msg);this._onError(o.b.EXCEPTION,i)}},t.prototype.abort=function(){var e=this._ws;!e||0!==e.readyState&&1!==e.readyState||(this._requestAbort=!0,e.close()),this._ws=null,this._status=o.c.kComplete},t.prototype._onWebSocketOpen=function(e){this._status=o.c.kBuffering},t.prototype._onWebSocketClose=function(e){!0!==this._requestAbort?(this._status=o.c.kComplete,this._onComplete&&this._onComplete(0,this._receivedLength-1)):this._requestAbort=!1},t.prototype._onWebSocketMessage=function(e){var t=this;if(e.data instanceof ArrayBuffer)this._dispatchArrayBuffer(e.data);else if(e.data instanceof Blob){var i=new FileReader;i.onload=function(){t._dispatchArrayBuffer(i.result)},i.readAsArrayBuffer(e.data)}else{this._status=o.c.kError;var n={code:-1,msg:"Unsupported WebSocket message type: "+e.data.constructor.name};if(!this._onError)throw new d.d(n.msg);this._onError(o.b.EXCEPTION,n)}},t.prototype._dispatchArrayBuffer=function(e){var t=e,i=this._receivedLength;this._receivedLength+=t.byteLength,this._onDataArrival&&this._onDataArrival(t,i,this._receivedLength)},t.prototype._onWebSocketError=function(e){this._status=o.c.kError;var t={code:e.code,msg:e.message};if(!this._onError)throw new d.d(t.msg);this._onError(o.b.EXCEPTION,t)},t}(o.a),y=function(){function e(e){this._zeroStart=e||!1}return e.prototype.getConfig=function(e,t){var i={};if(0!==t.from||-1!==t.to){var n=void 0;n=-1!==t.to?"bytes=".concat(t.from.toString(),"-").concat(t.to.toString()):"bytes=".concat(t.from.toString(),"-"),i.Range=n}else this._zeroStart&&(i.Range="bytes=0-");return{url:e,headers:i}},e.prototype.removeURLParameters=function(e){return e},e}(),v=function(){function e(e,t){this._startName=e,this._endName=t}return e.prototype.getConfig=function(e,t){var i=e;if(0!==t.from||-1!==t.to){var n=!0;-1===i.indexOf("?")&&(i+="?",n=!1),n&&(i+="&"),i+="".concat(this._startName,"=").concat(t.from.toString()),-1!==t.to&&(i+="&".concat(this._endName,"=").concat(t.to.toString()))}return{url:i,headers:{}}},e.prototype.removeURLParameters=function(e){var t=e.split("?")[0],i=void 0,n=e.indexOf("?");-1!==n&&(i=e.substring(n+1));var a="";if(null!=i&&i.length>0)for(var r=i.split("&"),o=0;o<r.length;o++){var s=r[o].split("="),d=o>0;s[0]!==this._startName&&s[0]!==this._endName&&(d&&(a+="&"),a+=r[o])}return 0===a.length?t:t+"?"+a},e}(),S=function(){function e(e,t,i){this.TAG="IOController",this._config=t,this._extraData=i,this._stashInitialSize=65536,null!=t.stashInitialSize&&t.stashInitialSize>0&&(this._stashInitialSize=t.stashInitialSize),this._stashUsed=0,this._stashSize=this._stashInitialSize,this._bufferSize=3145728,this._stashBuffer=new ArrayBuffer(this._bufferSize),this._stashByteStart=0,this._enableStash=!0,!1===t.enableStashBuffer&&(this._enableStash=!1),this._loader=null,this._loaderClass=null,this._seekHandler=null,this._dataSource=e,this._isWebSocketURL=/wss?:\/\/(.+?)/.test(e.url),this._refTotalLength=e.filesize?e.filesize:null,this._totalLength=this._refTotalLength,this._fullRequestFlag=!1,this._currentRange=null,this._redirectedURL=null,this._speedNormalized=0,this._speedSampler=new r,this._speedNormalizeList=[32,64,96,128,192,256,384,512,768,1024,1536,2048,3072,4096],this._isEarlyEofReconnecting=!1,this._paused=!1,this._resumeFrom=0,this._onDataArrival=null,this._onSeeked=null,this._onError=null,this._onComplete=null,this._onRedirect=null,this._onRecoveredEarlyEof=null,this._selectSeekHandler(),this._selectLoader(),this._createLoader()}return e.prototype.destroy=function(){this._loader.isWorking()&&this._loader.abort(),this._loader.destroy(),this._loader=null,this._loaderClass=null,this._dataSource=null,this._stashBuffer=null,this._stashUsed=this._stashSize=this._bufferSize=this._stashByteStart=0,this._currentRange=null,this._speedSampler=null,this._isEarlyEofReconnecting=!1,this._onDataArrival=null,this._onSeeked=null,this._onError=null,this._onComplete=null,this._onRedirect=null,this._onRecoveredEarlyEof=null,this._extraData=null},e.prototype.isWorking=function(){return this._loader&&this._loader.isWorking()&&!this._paused},e.prototype.isPaused=function(){return this._paused},Object.defineProperty(e.prototype,"status",{get:function(){return this._loader.status},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"extraData",{get:function(){return this._extraData},set:function(e){this._extraData=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onDataArrival",{get:function(){return this._onDataArrival},set:function(e){this._onDataArrival=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onSeeked",{get:function(){return this._onSeeked},set:function(e){this._onSeeked=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onError",{get:function(){return this._onError},set:function(e){this._onError=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onComplete",{get:function(){return this._onComplete},set:function(e){this._onComplete=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onRedirect",{get:function(){return this._onRedirect},set:function(e){this._onRedirect=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onRecoveredEarlyEof",{get:function(){return this._onRecoveredEarlyEof},set:function(e){this._onRecoveredEarlyEof=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentURL",{get:function(){return this._dataSource.url},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"hasRedirect",{get:function(){return null!=this._redirectedURL||null!=this._dataSource.redirectedURL},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentRedirectedURL",{get:function(){return this._redirectedURL||this._dataSource.redirectedURL},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentSpeed",{get:function(){return this._loaderClass===p?this._loader.currentSpeed:this._speedSampler.lastSecondKBps},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"loaderType",{get:function(){return this._loader.type},enumerable:!1,configurable:!0}),e.prototype._selectSeekHandler=function(){var e=this._config;if("range"===e.seekType)this._seekHandler=new y(this._config.rangeLoadZeroStart);else if("param"===e.seekType){var t=e.seekParamStart||"bstart",i=e.seekParamEnd||"bend";this._seekHandler=new v(t,i)}else{if("custom"!==e.seekType)throw new d.b("Invalid seekType in config: ".concat(e.seekType));if("function"!=typeof e.customSeekHandler)throw new d.b("Custom seekType specified in config but invalid customSeekHandler!");this._seekHandler=new e.customSeekHandler}},e.prototype._selectLoader=function(){if(null!=this._config.customLoader)this._loaderClass=this._config.customLoader;else if(this._isWebSocketURL)this._loaderClass=g;else if(c.isSupported())this._loaderClass=c;else if(l.isSupported())this._loaderClass=l;else{if(!p.isSupported())throw new d.d("Your browser doesn't support xhr with arraybuffer responseType!");this._loaderClass=p}},e.prototype._createLoader=function(){this._loader=new this._loaderClass(this._seekHandler,this._config),!1===this._loader.needStashBuffer&&(this._enableStash=!1),this._loader.onContentLengthKnown=this._onContentLengthKnown.bind(this),this._loader.onURLRedirect=this._onURLRedirect.bind(this),this._loader.onDataArrival=this._onLoaderChunkArrival.bind(this),this._loader.onComplete=this._onLoaderComplete.bind(this),this._loader.onError=this._onLoaderError.bind(this)},e.prototype.open=function(e){this._currentRange={from:0,to:-1},e&&(this._currentRange.from=e),this._speedSampler.reset(),e||(this._fullRequestFlag=!0),this._loader.open(this._dataSource,Object.assign({},this._currentRange))},e.prototype.abort=function(){this._loader.abort(),this._paused&&(this._paused=!1,this._resumeFrom=0)},e.prototype.pause=function(){this.isWorking()&&(this._loader.abort(),0!==this._stashUsed?(this._resumeFrom=this._stashByteStart,this._currentRange.to=this._stashByteStart-1):this._resumeFrom=this._currentRange.to+1,this._stashUsed=0,this._stashByteStart=0,this._paused=!0)},e.prototype.resume=function(){if(this._paused){this._paused=!1;var e=this._resumeFrom;this._resumeFrom=0,this._internalSeek(e,!0)}},e.prototype.seek=function(e){this._paused=!1,this._stashUsed=0,this._stashByteStart=0,this._internalSeek(e,!0)},e.prototype._internalSeek=function(e,t){this._loader.isWorking()&&this._loader.abort(),this._flushStashBuffer(t),this._loader.destroy(),this._loader=null;var i={from:e,to:-1};this._currentRange={from:i.from,to:-1},this._speedSampler.reset(),this._stashSize=this._stashInitialSize,this._createLoader(),this._loader.open(this._dataSource,i),this._onSeeked&&this._onSeeked()},e.prototype.updateUrl=function(e){if(!e||"string"!=typeof e||0===e.length)throw new d.b("Url must be a non-empty string!");this._dataSource.url=e},e.prototype._expandBuffer=function(e){for(var t=this._stashSize;t+1048576<e;)t*=2;if((t+=1048576)!==this._bufferSize){var i=new ArrayBuffer(t);if(this._stashUsed>0){var n=new Uint8Array(this._stashBuffer,0,this._stashUsed);new Uint8Array(i,0,t).set(n,0)}this._stashBuffer=i,this._bufferSize=t}},e.prototype._normalizeSpeed=function(e){var t=this._speedNormalizeList,i=t.length-1,n=0,a=0,r=i;if(e<t[0])return t[0];for(;a<=r;){if((n=a+Math.floor((r-a)/2))===i||e>=t[n]&&e<t[n+1])return t[n];t[n]<e?a=n+1:r=n-1}},e.prototype._adjustStashSize=function(e){var t=0;(t=this._config.isLive?e/8:e<512?e:e>=512&&e<=1024?Math.floor(1.5*e):2*e)>8192&&(t=8192);var i=1024*t+1048576;this._bufferSize<i&&this._expandBuffer(i),this._stashSize=1024*t},e.prototype._dispatchChunks=function(e,t){return this._currentRange.to=t+e.byteLength-1,this._onDataArrival(e,t)},e.prototype._onURLRedirect=function(e){this._redirectedURL=e,this._onRedirect&&this._onRedirect(e)},e.prototype._onContentLengthKnown=function(e){e&&this._fullRequestFlag&&(this._totalLength=e,this._fullRequestFlag=!1)},e.prototype._onLoaderChunkArrival=function(e,t,i){if(!this._onDataArrival)throw new d.a("IOController: No existing consumer (onDataArrival) callback!");if(!this._paused){this._isEarlyEofReconnecting&&(this._isEarlyEofReconnecting=!1,this._onRecoveredEarlyEof&&this._onRecoveredEarlyEof()),this._speedSampler.addBytes(e.byteLength);var n=this._speedSampler.lastSecondKBps;if(0!==n){var a=this._normalizeSpeed(n);this._speedNormalized!==a&&(this._speedNormalized=a,this._adjustStashSize(a))}if(this._enableStash)if(0===this._stashUsed&&0===this._stashByteStart&&(this._stashByteStart=t),this._stashUsed+e.byteLength<=this._stashSize){(s=new Uint8Array(this._stashBuffer,0,this._stashSize)).set(new Uint8Array(e),this._stashUsed),this._stashUsed+=e.byteLength}else{s=new Uint8Array(this._stashBuffer,0,this._bufferSize);if(this._stashUsed>0){var r=this._stashBuffer.slice(0,this._stashUsed);if((_=this._dispatchChunks(r,this._stashByteStart))<r.byteLength){if(_>0){c=new Uint8Array(r,_);s.set(c,0),this._stashUsed=c.byteLength,this._stashByteStart+=_}}else this._stashUsed=0,this._stashByteStart+=_;this._stashUsed+e.byteLength>this._bufferSize&&(this._expandBuffer(this._stashUsed+e.byteLength),s=new Uint8Array(this._stashBuffer,0,this._bufferSize)),s.set(new Uint8Array(e),this._stashUsed),this._stashUsed+=e.byteLength}else{if((_=this._dispatchChunks(e,t))<e.byteLength)(o=e.byteLength-_)>this._bufferSize&&(this._expandBuffer(o),s=new Uint8Array(this._stashBuffer,0,this._bufferSize)),s.set(new Uint8Array(e,_),0),this._stashUsed+=o,this._stashByteStart=t+_}}else if(0===this._stashUsed){var o;if((_=this._dispatchChunks(e,t))<e.byteLength)(o=e.byteLength-_)>this._bufferSize&&this._expandBuffer(o),(s=new Uint8Array(this._stashBuffer,0,this._bufferSize)).set(new Uint8Array(e,_),0),this._stashUsed+=o,this._stashByteStart=t+_}else{var s,_;if(this._stashUsed+e.byteLength>this._bufferSize&&this._expandBuffer(this._stashUsed+e.byteLength),(s=new Uint8Array(this._stashBuffer,0,this._bufferSize)).set(new Uint8Array(e),this._stashUsed),this._stashUsed+=e.byteLength,(_=this._dispatchChunks(this._stashBuffer.slice(0,this._stashUsed),this._stashByteStart))<this._stashUsed&&_>0){var c=new Uint8Array(this._stashBuffer,_);s.set(c,0)}this._stashUsed-=_,this._stashByteStart+=_}}},e.prototype._flushStashBuffer=function(e){if(this._stashUsed>0){var t=this._stashBuffer.slice(0,this._stashUsed),i=this._dispatchChunks(t,this._stashByteStart),n=t.byteLength-i;if(i<t.byteLength){if(!e){if(i>0){var r=new Uint8Array(this._stashBuffer,0,this._bufferSize),o=new Uint8Array(t,i);r.set(o,0),this._stashUsed=o.byteLength,this._stashByteStart+=i}return 0}a.a.w(this.TAG,"".concat(n," bytes unconsumed data remain when flush buffer, dropped"))}return this._stashUsed=0,this._stashByteStart=0,n}return 0},e.prototype._onLoaderComplete=function(e,t){this._flushStashBuffer(!0),this._onComplete&&this._onComplete(this._extraData)},e.prototype._onLoaderError=function(e,t){switch(a.a.e(this.TAG,"Loader error, code = ".concat(t.code,", msg = ").concat(t.msg)),this._flushStashBuffer(!1),this._isEarlyEofReconnecting&&(this._isEarlyEofReconnecting=!1,e=o.b.UNRECOVERABLE_EARLY_EOF),e){case o.b.EARLY_EOF:if(!this._config.isLive&&this._totalLength){var i=this._currentRange.to+1;return void(i<this._totalLength&&(a.a.w(this.TAG,"Connection lost, trying reconnect..."),this._isEarlyEofReconnecting=!0,this._internalSeek(i,!1)))}e=o.b.UNRECOVERABLE_EARLY_EOF;break;case o.b.UNRECOVERABLE_EARLY_EOF:case o.b.CONNECTING_TIMEOUT:case o.b.HTTP_STATUS_CODE_INVALID:case o.b.EXCEPTION:}if(!this._onError)throw new d.d("IOException: "+t.msg);this._onError(e,t)},e}();t.a=S},function(e,t,i){"use strict";var n=function(){function e(){}return e.install=function(){Object.setPrototypeOf=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},Object.assign=Object.assign||function(e){if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var t=Object(e),i=1;i<arguments.length;i++){var n=arguments[i];if(null!=n)for(var a in n)n.hasOwnProperty(a)&&(t[a]=n[a])}return t},String.prototype.startsWith||Object.defineProperty(String.prototype,"startsWith",{value:function(e,t){var i=t>0?0|t:0;return this.substring(i,i+e.length)===e}}),"function"!=typeof self.Promise&&i(21).polyfill()},e}();n.install(),t.a=n},function(e,t,i){"use strict";var n=i(9),a=i.n(n),r=i(0),o=i(5),s=i(7),d=i(3),_=function(){function e(e){this.TAG="MSEController",this._config=e,this._emitter=new a.a,this._config.isLive&&null==this._config.autoCleanupSourceBuffer&&(this._config.autoCleanupSourceBuffer=!0),this.e={onSourceOpen:this._onSourceOpen.bind(this),onSourceEnded:this._onSourceEnded.bind(this),onSourceClose:this._onSourceClose.bind(this),onStartStreaming:this._onStartStreaming.bind(this),onEndStreaming:this._onEndStreaming.bind(this),onQualityChange:this._onQualityChange.bind(this),onSourceBufferError:this._onSourceBufferError.bind(this),onSourceBufferUpdateEnd:this._onSourceBufferUpdateEnd.bind(this)},this._useManagedMediaSource="ManagedMediaSource"in self&&!("MediaSource"in self),this._mediaSource=null,this._mediaSourceObjectURL=null,this._mediaElementProxy=null,this._isBufferFull=!1,this._hasPendingEos=!1,this._requireSetMediaDuration=!1,this._pendingMediaDuration=0,this._pendingSourceBufferInit=[],this._mimeTypes={video:null,audio:null},this._sourceBuffers={video:null,audio:null},this._lastInitSegments={video:null,audio:null},this._pendingSegments={video:[],audio:[]},this._pendingRemoveRanges={video:[],audio:[]}}return e.prototype.destroy=function(){this._mediaSource&&this.shutdown(),this._mediaSourceObjectURL&&revokeObjectURL(),this.e=null,this._emitter.removeAllListeners(),this._emitter=null},e.prototype.on=function(e,t){this._emitter.addListener(e,t)},e.prototype.off=function(e,t){this._emitter.removeListener(e,t)},e.prototype.initialize=function(e){if(this._mediaSource)throw new d.a("MediaSource has been attached to an HTMLMediaElement!");this._useManagedMediaSource&&r.a.v(this.TAG,"Using ManagedMediaSource");var t=this._mediaSource=this._useManagedMediaSource?new self.ManagedMediaSource:new self.MediaSource;t.addEventListener("sourceopen",this.e.onSourceOpen),t.addEventListener("sourceended",this.e.onSourceEnded),t.addEventListener("sourceclose",this.e.onSourceClose),this._useManagedMediaSource&&(t.addEventListener("startstreaming",this.e.onStartStreaming),t.addEventListener("endstreaming",this.e.onEndStreaming),t.addEventListener("qualitychange",this.e.onQualityChange)),this._mediaElementProxy=e},e.prototype.shutdown=function(){if(this._mediaSource){var e=this._mediaSource;for(var t in this._sourceBuffers){var i=this._pendingSegments[t];i.splice(0,i.length),this._pendingSegments[t]=null,this._pendingRemoveRanges[t]=null,this._lastInitSegments[t]=null;var n=this._sourceBuffers[t];if(n){if("closed"!==e.readyState){try{e.removeSourceBuffer(n)}catch(e){r.a.e(this.TAG,e.message)}n.removeEventListener("error",this.e.onSourceBufferError),n.removeEventListener("updateend",this.e.onSourceBufferUpdateEnd)}this._mimeTypes[t]=null,this._sourceBuffers[t]=null}}if("open"===e.readyState)try{e.endOfStream()}catch(e){r.a.e(this.TAG,e.message)}this._mediaElementProxy=null,e.removeEventListener("sourceopen",this.e.onSourceOpen),e.removeEventListener("sourceended",this.e.onSourceEnded),e.removeEventListener("sourceclose",this.e.onSourceClose),this._useManagedMediaSource&&(e.removeEventListener("startstraming",this.e.onStartStreaming),e.removeEventListener("endstreaming",this.e.onEndStreaming),e.removeEventListener("qualitychange",this.e.onQualityChange)),this._pendingSourceBufferInit=[],this._isBufferFull=!1,this._mediaSource=null}},e.prototype.isManagedMediaSource=function(){return this._useManagedMediaSource},e.prototype.getObject=function(){if(!this._mediaSource)throw new d.a("MediaSource has not been initialized yet!");return this._mediaSource},e.prototype.getHandle=function(){if(!this._mediaSource)throw new d.a("MediaSource has not been initialized yet!");return this._mediaSource.handle},e.prototype.getObjectURL=function(){if(!this._mediaSource)throw new d.a("MediaSource has not been initialized yet!");return null==this._mediaSourceObjectURL&&(this._mediaSourceObjectURL=URL.createObjectURL(this._mediaSource)),this._mediaSourceObjectURL},e.prototype.revokeObjectURL=function(){this._mediaSourceObjectURL&&(URL.revokeObjectURL(this._mediaSourceObjectURL),this._mediaSourceObjectURL=null)},e.prototype.appendInitSegment=function(e,t){if(void 0===t&&(t=void 0),!this._mediaSource||"open"!==this._mediaSource.readyState||!1===this._mediaSource.streaming)return this._pendingSourceBufferInit.push(e),void this._pendingSegments[e.type].push(e);var i=e,n="".concat(i.container);i.codec&&i.codec.length>0&&(n+=";codecs=".concat(i.codec));var a=!1;if(r.a.v(this.TAG,"Received Initialization Segment, mimeType: "+n),this._lastInitSegments[i.type]=i,n!==this._mimeTypes[i.type]){if(this._mimeTypes[i.type])r.a.v(this.TAG,"Notice: ".concat(i.type," mimeType changed, origin: ").concat(this._mimeTypes[i.type],", target: ").concat(n));else{a=!0;try{var d=this._sourceBuffers[i.type]=this._mediaSource.addSourceBuffer(n);d.addEventListener("error",this.e.onSourceBufferError),d.addEventListener("updateend",this.e.onSourceBufferUpdateEnd)}catch(e){return r.a.e(this.TAG,e.message),void this._emitter.emit(s.a.ERROR,{code:e.code,msg:e.message})}}this._mimeTypes[i.type]=n}t||this._pendingSegments[i.type].push(i),a||this._sourceBuffers[i.type]&&!this._sourceBuffers[i.type].updating&&this._doAppendSegments(),o.a.safari&&"audio/mpeg"===i.container&&i.mediaDuration>0&&(this._requireSetMediaDuration=!0,this._pendingMediaDuration=i.mediaDuration/1e3,this._updateMediaSourceDuration())},e.prototype.appendMediaSegment=function(e){var t=e;this._pendingSegments[t.type].push(t),this._config.autoCleanupSourceBuffer&&this._needCleanupSourceBuffer()&&this._doCleanupSourceBuffer();var i=this._sourceBuffers[t.type];!i||i.updating||this._hasPendingRemoveRanges()||this._doAppendSegments()},e.prototype.flush=function(){for(var e in this._sourceBuffers)if(this._sourceBuffers[e]){var t=this._sourceBuffers[e];if("open"===this._mediaSource.readyState)try{t.abort()}catch(e){r.a.e(this.TAG,e.message)}var i=this._pendingSegments[e];if(i.splice(0,i.length),"closed"!==this._mediaSource.readyState){for(var n=0;n<t.buffered.length;n++){var a=t.buffered.start(n),s=t.buffered.end(n);this._pendingRemoveRanges[e].push({start:a,end:s})}if(t.updating||this._doRemoveRanges(),o.a.safari){var d=this._lastInitSegments[e];d&&(this._pendingSegments[e].push(d),t.updating||this._doAppendSegments())}}}},e.prototype.endOfStream=function(){var e=this._mediaSource,t=this._sourceBuffers;e&&"open"===e.readyState?t.video&&t.video.updating||t.audio&&t.audio.updating?this._hasPendingEos=!0:(this._hasPendingEos=!1,e.endOfStream()):e&&"closed"===e.readyState&&this._hasPendingSegments()&&(this._hasPendingEos=!0)},e.prototype._needCleanupSourceBuffer=function(){if(!this._config.autoCleanupSourceBuffer)return!1;var e=this._mediaElementProxy.getCurrentTime();for(var t in this._sourceBuffers){var i=this._sourceBuffers[t];if(i){var n=i.buffered;if(n.length>=1&&e-n.start(0)>=this._config.autoCleanupMaxBackwardDuration)return!0}}return!1},e.prototype._doCleanupSourceBuffer=function(){var e=this._mediaElementProxy.getCurrentTime();for(var t in this._sourceBuffers){var i=this._sourceBuffers[t];if(i){for(var n=i.buffered,a=!1,r=0;r<n.length;r++){var o=n.start(r),s=n.end(r);if(o<=e&&e<s+3){if(e-o>=this._config.autoCleanupMaxBackwardDuration){a=!0;var d=e-this._config.autoCleanupMinBackwardDuration;this._pendingRemoveRanges[t].push({start:o,end:d})}}else s<e&&(a=!0,this._pendingRemoveRanges[t].push({start:o,end:s}))}a&&!i.updating&&this._doRemoveRanges()}}},e.prototype._updateMediaSourceDuration=function(){var e=this._sourceBuffers;if(0!==this._mediaElementProxy.getReadyState()&&"open"===this._mediaSource.readyState&&!(e.video&&e.video.updating||e.audio&&e.audio.updating)){var t=this._mediaSource.duration,i=this._pendingMediaDuration;i>0&&(isNaN(t)||i>t)&&(r.a.v(this.TAG,"Update MediaSource duration from ".concat(t," to ").concat(i)),this._mediaSource.duration=i),this._requireSetMediaDuration=!1,this._pendingMediaDuration=0}},e.prototype._doRemoveRanges=function(){for(var e in this._pendingRemoveRanges)if(this._sourceBuffers[e]&&!this._sourceBuffers[e].updating)for(var t=this._sourceBuffers[e],i=this._pendingRemoveRanges[e];i.length&&!t.updating;){var n=i.shift();t.remove(n.start,n.end)}},e.prototype._doAppendSegments=function(){var e=this._pendingSegments;for(var t in e)if(this._sourceBuffers[t]&&!this._sourceBuffers[t].updating&&!1!==this._mediaSource.streaming&&e[t].length>0){var i=e[t].shift();if(i.timestampOffset){var n=this._sourceBuffers[t].timestampOffset,a=i.timestampOffset/1e3;Math.abs(n-a)>.1&&(r.a.v(this.TAG,"Update MPEG audio timestampOffset from ".concat(n," to ").concat(a)),this._sourceBuffers[t].timestampOffset=a),delete i.timestampOffset}if(!i.data||0===i.data.byteLength)continue;try{this._sourceBuffers[t].appendBuffer(i.data),this._isBufferFull=!1}catch(e){this._pendingSegments[t].unshift(i),22===e.code?(this._isBufferFull||this._emitter.emit(s.a.BUFFER_FULL),this._isBufferFull=!0):(r.a.e(this.TAG,e.message),this._emitter.emit(s.a.ERROR,{code:e.code,msg:e.message}))}}},e.prototype._onSourceOpen=function(){if(r.a.v(this.TAG,"MediaSource onSourceOpen"),this._mediaSource.removeEventListener("sourceopen",this.e.onSourceOpen),this._pendingSourceBufferInit.length>0)for(var e=this._pendingSourceBufferInit;e.length;){var t=e.shift();this.appendInitSegment(t,!0)}this._hasPendingSegments()&&this._doAppendSegments(),this._emitter.emit(s.a.SOURCE_OPEN)},e.prototype._onStartStreaming=function(){r.a.v(this.TAG,"ManagedMediaSource onStartStreaming"),this._emitter.emit(s.a.START_STREAMING)},e.prototype._onEndStreaming=function(){r.a.v(this.TAG,"ManagedMediaSource onEndStreaming"),this._emitter.emit(s.a.END_STREAMING)},e.prototype._onQualityChange=function(){r.a.v(this.TAG,"ManagedMediaSource onQualityChange")},e.prototype._onSourceEnded=function(){r.a.v(this.TAG,"MediaSource onSourceEnded")},e.prototype._onSourceClose=function(){r.a.v(this.TAG,"MediaSource onSourceClose"),this._mediaSource&&null!=this.e&&(this._mediaSource.removeEventListener("sourceopen",this.e.onSourceOpen),this._mediaSource.removeEventListener("sourceended",this.e.onSourceEnded),this._mediaSource.removeEventListener("sourceclose",this.e.onSourceClose),this._useManagedMediaSource&&(this._mediaSource.removeEventListener("startstraming",this.e.onStartStreaming),this._mediaSource.removeEventListener("endstreaming",this.e.onEndStreaming),this._mediaSource.removeEventListener("qualitychange",this.e.onQualityChange)))},e.prototype._hasPendingSegments=function(){var e=this._pendingSegments;return e.video.length>0||e.audio.length>0},e.prototype._hasPendingRemoveRanges=function(){var e=this._pendingRemoveRanges;return e.video.length>0||e.audio.length>0},e.prototype._onSourceBufferUpdateEnd=function(){this._requireSetMediaDuration?this._updateMediaSourceDuration():this._hasPendingRemoveRanges()?this._doRemoveRanges():this._hasPendingSegments()?this._doAppendSegments():this._hasPendingEos&&this.endOfStream(),this._emitter.emit(s.a.UPDATE_END)},e.prototype._onSourceBufferError=function(e){r.a.e(this.TAG,"SourceBuffer Error: ".concat(e))},e}();t.a=_},function(e,t,i){"use strict";var n=i(9),a=i.n(n),r=i(18),o=i.n(r),s=i(0),d=i(8),_=i(13),c=i(1),h=(i(19),i(12)),l=function(){function e(e,t){if(this.TAG="Transmuxer",this._emitter=new a.a,t.enableWorker&&"undefined"!=typeof Worker)try{this._worker=o()(19),this._workerDestroying=!1,this._worker.addEventListener("message",this._onWorkerMessage.bind(this)),this._worker.postMessage({cmd:"init",param:[e,t]}),this.e={onLoggingConfigChanged:this._onLoggingConfigChanged.bind(this)},d.a.registerListener(this.e.onLoggingConfigChanged),this._worker.postMessage({cmd:"logging_config",param:d.a.getConfig()})}catch(i){s.a.e(this.TAG,"Error while initialize transmuxing worker, fallback to inline transmuxing"),this._worker=null,this._controller=new _.a(e,t)}else this._controller=new _.a(e,t);if(this._controller){var i=this._controller;i.on(c.a.IO_ERROR,this._onIOError.bind(this)),i.on(c.a.DEMUX_ERROR,this._onDemuxError.bind(this)),i.on(c.a.INIT_SEGMENT,this._onInitSegment.bind(this)),i.on(c.a.MEDIA_SEGMENT,this._onMediaSegment.bind(this)),i.on(c.a.LOADING_COMPLETE,this._onLoadingComplete.bind(this)),i.on(c.a.RECOVERED_EARLY_EOF,this._onRecoveredEarlyEof.bind(this)),i.on(c.a.MEDIA_INFO,this._onMediaInfo.bind(this)),i.on(c.a.METADATA_ARRIVED,this._onMetaDataArrived.bind(this)),i.on(c.a.SCRIPTDATA_ARRIVED,this._onScriptDataArrived.bind(this)),i.on(c.a.TIMED_ID3_METADATA_ARRIVED,this._onTimedID3MetadataArrived.bind(this)),i.on(c.a.SYNCHRONOUS_KLV_METADATA_ARRIVED,this._onSynchronousKLVMetadataArrived.bind(this)),i.on(c.a.ASYNCHRONOUS_KLV_METADATA_ARRIVED,this._onAsynchronousKLVMetadataArrived.bind(this)),i.on(c.a.SMPTE2038_METADATA_ARRIVED,this._onSMPTE2038MetadataArrived.bind(this)),i.on(c.a.SCTE35_METADATA_ARRIVED,this._onSCTE35MetadataArrived.bind(this)),i.on(c.a.PES_PRIVATE_DATA_DESCRIPTOR,this._onPESPrivateDataDescriptor.bind(this)),i.on(c.a.PES_PRIVATE_DATA_ARRIVED,this._onPESPrivateDataArrived.bind(this)),i.on(c.a.STATISTICS_INFO,this._onStatisticsInfo.bind(this)),i.on(c.a.RECOMMEND_SEEKPOINT,this._onRecommendSeekpoint.bind(this))}}return e.prototype.destroy=function(){this._worker?this._workerDestroying||(this._workerDestroying=!0,this._worker.postMessage({cmd:"destroy"}),d.a.removeListener(this.e.onLoggingConfigChanged),this.e=null):(this._controller.destroy(),this._controller=null),this._emitter.removeAllListeners(),this._emitter=null},e.prototype.on=function(e,t){this._emitter.addListener(e,t)},e.prototype.off=function(e,t){this._emitter.removeListener(e,t)},e.prototype.hasWorker=function(){return null!=this._worker},e.prototype.open=function(){this._worker?this._worker.postMessage({cmd:"start"}):this._controller.start()},e.prototype.close=function(){this._worker?this._worker.postMessage({cmd:"stop"}):this._controller.stop()},e.prototype.seek=function(e){this._worker?this._worker.postMessage({cmd:"seek",param:e}):this._controller.seek(e)},e.prototype.pause=function(){this._worker?this._worker.postMessage({cmd:"pause"}):this._controller.pause()},e.prototype.resume=function(){this._worker?this._worker.postMessage({cmd:"resume"}):this._controller.resume()},e.prototype._onInitSegment=function(e,t){var i=this;Promise.resolve().then((function(){i._emitter.emit(c.a.INIT_SEGMENT,e,t)}))},e.prototype._onMediaSegment=function(e,t){var i=this;Promise.resolve().then((function(){i._emitter.emit(c.a.MEDIA_SEGMENT,e,t)}))},e.prototype._onLoadingComplete=function(){var e=this;Promise.resolve().then((function(){e._emitter.emit(c.a.LOADING_COMPLETE)}))},e.prototype._onRecoveredEarlyEof=function(){var e=this;Promise.resolve().then((function(){e._emitter.emit(c.a.RECOVERED_EARLY_EOF)}))},e.prototype._onMediaInfo=function(e){var t=this;Promise.resolve().then((function(){t._emitter.emit(c.a.MEDIA_INFO,e)}))},e.prototype._onMetaDataArrived=function(e){var t=this;Promise.resolve().then((function(){t._emitter.emit(c.a.METADATA_ARRIVED,e)}))},e.prototype._onScriptDataArrived=function(e){var t=this;Promise.resolve().then((function(){t._emitter.emit(c.a.SCRIPTDATA_ARRIVED,e)}))},e.prototype._onTimedID3MetadataArrived=function(e){var t=this;Promise.resolve().then((function(){t._emitter.emit(c.a.TIMED_ID3_METADATA_ARRIVED,e)}))},e.prototype._onSynchronousKLVMetadataArrived=function(e){var t=this;Promise.resolve().then((function(){t._emitter.emit(c.a.SYNCHRONOUS_KLV_METADATA_ARRIVED,e)}))},e.prototype._onAsynchronousKLVMetadataArrived=function(e){var t=this;Promise.resolve().then((function(){t._emitter.emit(c.a.ASYNCHRONOUS_KLV_METADATA_ARRIVED,e)}))},e.prototype._onSMPTE2038MetadataArrived=function(e){var t=this;Promise.resolve().then((function(){t._emitter.emit(c.a.SMPTE2038_METADATA_ARRIVED,e)}))},e.prototype._onSCTE35MetadataArrived=function(e){var t=this;Promise.resolve().then((function(){t._emitter.emit(c.a.SCTE35_METADATA_ARRIVED,e)}))},e.prototype._onPESPrivateDataDescriptor=function(e){var t=this;Promise.resolve().then((function(){t._emitter.emit(c.a.PES_PRIVATE_DATA_DESCRIPTOR,e)}))},e.prototype._onPESPrivateDataArrived=function(e){var t=this;Promise.resolve().then((function(){t._emitter.emit(c.a.PES_PRIVATE_DATA_ARRIVED,e)}))},e.prototype._onStatisticsInfo=function(e){var t=this;Promise.resolve().then((function(){t._emitter.emit(c.a.STATISTICS_INFO,e)}))},e.prototype._onIOError=function(e,t){var i=this;Promise.resolve().then((function(){i._emitter.emit(c.a.IO_ERROR,e,t)}))},e.prototype._onDemuxError=function(e,t){var i=this;Promise.resolve().then((function(){i._emitter.emit(c.a.DEMUX_ERROR,e,t)}))},e.prototype._onRecommendSeekpoint=function(e){var t=this;Promise.resolve().then((function(){t._emitter.emit(c.a.RECOMMEND_SEEKPOINT,e)}))},e.prototype._onLoggingConfigChanged=function(e){this._worker&&this._worker.postMessage({cmd:"logging_config",param:e})},e.prototype._onWorkerMessage=function(e){var t=e.data,i=t.data;if("destroyed"===t.msg||this._workerDestroying)return this._workerDestroying=!1,this._worker.terminate(),void(this._worker=null);switch(t.msg){case c.a.INIT_SEGMENT:case c.a.MEDIA_SEGMENT:this._emitter.emit(t.msg,i.type,i.data);break;case c.a.LOADING_COMPLETE:case c.a.RECOVERED_EARLY_EOF:this._emitter.emit(t.msg);break;case c.a.MEDIA_INFO:Object.setPrototypeOf(i,h.a.prototype),this._emitter.emit(t.msg,i);break;case c.a.METADATA_ARRIVED:case c.a.SCRIPTDATA_ARRIVED:case c.a.TIMED_ID3_METADATA_ARRIVED:case c.a.SYNCHRONOUS_KLV_METADATA_ARRIVED:case c.a.ASYNCHRONOUS_KLV_METADATA_ARRIVED:case c.a.SMPTE2038_METADATA_ARRIVED:case c.a.SCTE35_METADATA_ARRIVED:case c.a.PES_PRIVATE_DATA_DESCRIPTOR:case c.a.PES_PRIVATE_DATA_ARRIVED:case c.a.STATISTICS_INFO:this._emitter.emit(t.msg,i);break;case c.a.IO_ERROR:case c.a.DEMUX_ERROR:this._emitter.emit(t.msg,i.type,i.info);break;case c.a.RECOMMEND_SEEKPOINT:this._emitter.emit(t.msg,i);break;case"logcat_callback":s.a.emitter.emit("log",i.type,i.logcat)}},e}();t.a=l},function(e,t,i){function n(e){var t={};function i(n){if(t[n])return t[n].exports;var a=t[n]={i:n,l:!1,exports:{}};return e[n].call(a.exports,a,a.exports,i),a.l=!0,a.exports}i.m=e,i.c=t,i.i=function(e){return e},i.d=function(e,t,n){i.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:n})},i.r=function(e){Object.defineProperty(e,"__esModule",{value:!0})},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="/",i.oe=function(e){throw console.error(e),e};var n=i(i.s=ENTRY_MODULE);return n.default||n}function a(e){return(e+"").replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}function r(e,t,n){var r={};r[n]=[];var o=t.toString(),s=o.match(/^function\s?\w*\(\w+,\s*\w+,\s*(\w+)\)/);if(!s)return r;for(var d,_=s[1],c=new RegExp("(\\\\n|\\W)"+a(_)+"\\(\\s*(/\\*.*?\\*/)?\\s*.*?([\\.|\\-|\\+|\\w|/|@]+).*?\\)","g");d=c.exec(o);)"dll-reference"!==d[3]&&r[n].push(d[3]);for(c=new RegExp("\\("+a(_)+'\\("(dll-reference\\s([\\.|\\-|\\+|\\w|/|@]+))"\\)\\)\\(\\s*(/\\*.*?\\*/)?\\s*.*?([\\.|\\-|\\+|\\w|/|@]+).*?\\)',"g");d=c.exec(o);)e[d[2]]||(r[n].push(d[1]),e[d[2]]=i(d[1]).m),r[d[2]]=r[d[2]]||[],r[d[2]].push(d[4]);for(var h,l=Object.keys(r),u=0;u<l.length;u++)for(var f=0;f<r[l[u]].length;f++)h=r[l[u]][f],isNaN(1*h)||(r[l[u]][f]=1*r[l[u]][f]);return r}function o(e){return Object.keys(e).reduce((function(t,i){return t||e[i].length>0}),!1)}e.exports=function(e,t){t=t||{};var a={main:i.m},s=t.all?{main:Object.keys(a.main)}:function(e,t){for(var i={main:[t]},n={main:[]},a={main:{}};o(i);)for(var s=Object.keys(i),d=0;d<s.length;d++){var _=s[d],c=i[_].pop();if(a[_]=a[_]||{},!a[_][c]&&e[_][c]){a[_][c]=!0,n[_]=n[_]||[],n[_].push(c);for(var h=r(e,e[_][c],_),l=Object.keys(h),u=0;u<l.length;u++)i[l[u]]=i[l[u]]||[],i[l[u]]=i[l[u]].concat(h[l[u]])}}return n}(a,e),d="";Object.keys(s).filter((function(e){return"main"!==e})).forEach((function(e){for(var t=0;s[e][t];)t++;s[e].push(t),a[e][t]="(function(module, exports, __webpack_require__) { module.exports = __webpack_require__; })",d=d+"var "+e+" = ("+n.toString().replace("ENTRY_MODULE",JSON.stringify(t))+")({"+s[e].map((function(t){return JSON.stringify(t)+": "+a[e][t].toString()})).join(",")+"});\n"})),d=d+"new (("+n.toString().replace("ENTRY_MODULE",JSON.stringify(e))+")({"+s.main.map((function(e){return JSON.stringify(e)+": "+a.main[e].toString()})).join(",")+"}))(self);";var _=new self.Blob([d],{type:"text/javascript"});if(t.bare)return _;var c=(self.URL||self.webkitURL||self.mozURL||self.msURL).createObjectURL(_),h=new self.Worker(c);return h.objectURL=c,h}},function(e,t,i){"use strict";i.r(t);i(0);var n=i(8),a=i(15),r=i(13),o=i(1);t.default=function(e){var t=null,i=function(t,i){e.postMessage({msg:"logcat_callback",data:{type:t,logcat:i}})}.bind(this);function s(t,i){var n={msg:o.a.INIT_SEGMENT,data:{type:t,data:i}};e.postMessage(n,[i.data])}function d(t,i){var n={msg:o.a.MEDIA_SEGMENT,data:{type:t,data:i}};e.postMessage(n,[i.data])}function _(){var t={msg:o.a.LOADING_COMPLETE};e.postMessage(t)}function c(){var t={msg:o.a.RECOVERED_EARLY_EOF};e.postMessage(t)}function h(t){var i={msg:o.a.MEDIA_INFO,data:t};e.postMessage(i)}function l(t){var i={msg:o.a.METADATA_ARRIVED,data:t};e.postMessage(i)}function u(t){var i={msg:o.a.SCRIPTDATA_ARRIVED,data:t};e.postMessage(i)}function f(t){var i={msg:o.a.TIMED_ID3_METADATA_ARRIVED,data:t};e.postMessage(i)}function p(t){var i={msg:o.a.SYNCHRONOUS_KLV_METADATA_ARRIVED,data:t};e.postMessage(i)}function m(t){var i={msg:o.a.ASYNCHRONOUS_KLV_METADATA_ARRIVED,data:t};e.postMessage(i)}function g(t){var i={msg:o.a.SMPTE2038_METADATA_ARRIVED,data:t};e.postMessage(i)}function y(t){var i={msg:o.a.SCTE35_METADATA_ARRIVED,data:t};e.postMessage(i)}function v(t){var i={msg:o.a.PES_PRIVATE_DATA_DESCRIPTOR,data:t};e.postMessage(i)}function S(t){var i={msg:o.a.PES_PRIVATE_DATA_ARRIVED,data:t};e.postMessage(i)}function b(t){var i={msg:o.a.STATISTICS_INFO,data:t};e.postMessage(i)}function E(t,i){e.postMessage({msg:o.a.IO_ERROR,data:{type:t,info:i}})}function A(t,i){e.postMessage({msg:o.a.DEMUX_ERROR,data:{type:t,info:i}})}function R(t){e.postMessage({msg:o.a.RECOMMEND_SEEKPOINT,data:t})}a.a.install(),e.addEventListener("message",(function(a){switch(a.data.cmd){case"init":(t=new r.a(a.data.param[0],a.data.param[1])).on(o.a.IO_ERROR,E.bind(this)),t.on(o.a.DEMUX_ERROR,A.bind(this)),t.on(o.a.INIT_SEGMENT,s.bind(this)),t.on(o.a.MEDIA_SEGMENT,d.bind(this)),t.on(o.a.LOADING_COMPLETE,_.bind(this)),t.on(o.a.RECOVERED_EARLY_EOF,c.bind(this)),t.on(o.a.MEDIA_INFO,h.bind(this)),t.on(o.a.METADATA_ARRIVED,l.bind(this)),t.on(o.a.SCRIPTDATA_ARRIVED,u.bind(this)),t.on(o.a.TIMED_ID3_METADATA_ARRIVED,f.bind(this)),t.on(o.a.SYNCHRONOUS_KLV_METADATA_ARRIVED,p.bind(this)),t.on(o.a.ASYNCHRONOUS_KLV_METADATA_ARRIVED,m.bind(this)),t.on(o.a.SMPTE2038_METADATA_ARRIVED,g.bind(this)),t.on(o.a.SCTE35_METADATA_ARRIVED,y.bind(this)),t.on(o.a.PES_PRIVATE_DATA_DESCRIPTOR,v.bind(this)),t.on(o.a.PES_PRIVATE_DATA_ARRIVED,S.bind(this)),t.on(o.a.STATISTICS_INFO,b.bind(this)),t.on(o.a.RECOMMEND_SEEKPOINT,R.bind(this));break;case"destroy":t&&(t.destroy(),t=null),e.postMessage({msg:"destroyed"});break;case"start":t.start();break;case"stop":t.stop();break;case"seek":t.seek(a.data.param);break;case"pause":t.pause();break;case"resume":t.resume();break;case"logging_config":var T=a.data.param;n.a.applyConfig(T),!0===T.enableCallback?n.a.addLogListener(i):n.a.removeLogListener(i)}}))}},function(e,t,i){e.exports=i(25).default},function(e,t,i){(function(t,i){
/*!
 * @overview es6-promise - a tiny implementation of Promises/A+.
 * @copyright Copyright (c) 2014 Yehuda Katz, Tom Dale, Stefan Penner and contributors (Conversion to ES6 API by Jake Archibald)
 * @license   Licensed under MIT license
 *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE
 * @version   v4.2.8+1e68dce6
 */var n;n=function(){"use strict";function e(e){return"function"==typeof e}var n=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},a=0,r=void 0,o=void 0,s=function(e,t){f[a]=e,f[a+1]=t,2===(a+=2)&&(o?o(p):S())},d="undefined"!=typeof window?window:void 0,_=d||{},c=_.MutationObserver||_.WebKitMutationObserver,h="undefined"==typeof self&&void 0!==t&&"[object process]"==={}.toString.call(t),l="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel;function u(){var e=setTimeout;return function(){return e(p,1)}}var f=new Array(1e3);function p(){for(var e=0;e<a;e+=2)(0,f[e])(f[e+1]),f[e]=void 0,f[e+1]=void 0;a=0}var m,g,y,v,S=void 0;function b(e,t){var i=this,n=new this.constructor(R);void 0===n[A]&&I(n);var a=i._state;if(a){var r=arguments[a-1];s((function(){return O(a,n,r,i._result)}))}else M(i,n,e,t);return n}function E(e){if(e&&"object"==typeof e&&e.constructor===this)return e;var t=new this(R);return L(t,e),t}h?S=function(){return t.nextTick(p)}:c?(g=0,y=new c(p),v=document.createTextNode(""),y.observe(v,{characterData:!0}),S=function(){v.data=g=++g%2}):l?((m=new MessageChannel).port1.onmessage=p,S=function(){return m.port2.postMessage(0)}):S=void 0===d?function(){try{var e=Function("return this")().require("vertx");return void 0!==(r=e.runOnLoop||e.runOnContext)?function(){r(p)}:u()}catch(e){return u()}}():u();var A=Math.random().toString(36).substring(2);function R(){}function T(t,i,n){i.constructor===t.constructor&&n===b&&i.constructor.resolve===E?function(e,t){1===t._state?w(e,t._result):2===t._state?D(e,t._result):M(t,void 0,(function(t){return L(e,t)}),(function(t){return D(e,t)}))}(t,i):void 0===n?w(t,i):e(n)?function(e,t,i){s((function(e){var n=!1,a=function(e,t,i,n){try{e.call(t,i,n)}catch(e){return e}}(i,t,(function(i){n||(n=!0,t!==i?L(e,i):w(e,i))}),(function(t){n||(n=!0,D(e,t))}),e._label);!n&&a&&(n=!0,D(e,a))}),e)}(t,i,n):w(t,i)}function L(e,t){if(e===t)D(e,new TypeError("You cannot resolve a promise with itself"));else if(a=typeof(n=t),null===n||"object"!==a&&"function"!==a)w(e,t);else{var i=void 0;try{i=t.then}catch(t){return void D(e,t)}T(e,t,i)}var n,a}function k(e){e._onerror&&e._onerror(e._result),C(e)}function w(e,t){void 0===e._state&&(e._result=t,e._state=1,0!==e._subscribers.length&&s(C,e))}function D(e,t){void 0===e._state&&(e._state=2,e._result=t,s(k,e))}function M(e,t,i,n){var a=e._subscribers,r=a.length;e._onerror=null,a[r]=t,a[r+1]=i,a[r+2]=n,0===r&&e._state&&s(C,e)}function C(e){var t=e._subscribers,i=e._state;if(0!==t.length){for(var n=void 0,a=void 0,r=e._result,o=0;o<t.length;o+=3)n=t[o],a=t[o+i],n?O(i,n,a,r):a(r);e._subscribers.length=0}}function O(t,i,n,a){var r=e(n),o=void 0,s=void 0,d=!0;if(r){try{o=n(a)}catch(e){d=!1,s=e}if(i===o)return void D(i,new TypeError("A promises callback cannot return that same promise."))}else o=a;void 0!==i._state||(r&&d?L(i,o):!1===d?D(i,s):1===t?w(i,o):2===t&&D(i,o))}var B=0;function I(e){e[A]=B++,e._state=void 0,e._result=void 0,e._subscribers=[]}var P=function(){function e(e,t){this._instanceConstructor=e,this.promise=new e(R),this.promise[A]||I(this.promise),n(t)?(this.length=t.length,this._remaining=t.length,this._result=new Array(this.length),0===this.length?w(this.promise,this._result):(this.length=this.length||0,this._enumerate(t),0===this._remaining&&w(this.promise,this._result))):D(this.promise,new Error("Array Methods must be provided an Array"))}return e.prototype._enumerate=function(e){for(var t=0;void 0===this._state&&t<e.length;t++)this._eachEntry(e[t],t)},e.prototype._eachEntry=function(e,t){var i=this._instanceConstructor,n=i.resolve;if(n===E){var a=void 0,r=void 0,o=!1;try{a=e.then}catch(e){o=!0,r=e}if(a===b&&void 0!==e._state)this._settledAt(e._state,t,e._result);else if("function"!=typeof a)this._remaining--,this._result[t]=e;else if(i===x){var s=new i(R);o?D(s,r):T(s,e,a),this._willSettleAt(s,t)}else this._willSettleAt(new i((function(t){return t(e)})),t)}else this._willSettleAt(n(e),t)},e.prototype._settledAt=function(e,t,i){var n=this.promise;void 0===n._state&&(this._remaining--,2===e?D(n,i):this._result[t]=i),0===this._remaining&&w(n,this._result)},e.prototype._willSettleAt=function(e,t){var i=this;M(e,void 0,(function(e){return i._settledAt(1,t,e)}),(function(e){return i._settledAt(2,t,e)}))},e}(),x=function(){function t(e){this[A]=B++,this._result=this._state=void 0,this._subscribers=[],R!==e&&("function"!=typeof e&&function(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}(),this instanceof t?function(e,t){try{t((function(t){L(e,t)}),(function(t){D(e,t)}))}catch(t){D(e,t)}}(this,e):function(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}())}return t.prototype.catch=function(e){return this.then(null,e)},t.prototype.finally=function(t){var i=this.constructor;return e(t)?this.then((function(e){return i.resolve(t()).then((function(){return e}))}),(function(e){return i.resolve(t()).then((function(){throw e}))})):this.then(t,t)},t}();return x.prototype.then=b,x.all=function(e){return new P(this,e).promise},x.race=function(e){var t=this;return n(e)?new t((function(i,n){for(var a=e.length,r=0;r<a;r++)t.resolve(e[r]).then(i,n)})):new t((function(e,t){return t(new TypeError("You must pass an array to race."))}))},x.resolve=E,x.reject=function(e){var t=new this(R);return D(t,e),t},x._setScheduler=function(e){o=e},x._setAsap=function(e){s=e},x._asap=s,x.polyfill=function(){var e=void 0;if(void 0!==i)e=i;else if("undefined"!=typeof self)e=self;else try{e=Function("return this")()}catch(e){throw new Error("polyfill failed because global object is unavailable in this environment")}var t=e.Promise;if(t){var n=null;try{n=Object.prototype.toString.call(t.resolve())}catch(e){}if("[object Promise]"===n&&!t.cast)return}e.Promise=x},x.Promise=x,x},e.exports=n()}).call(this,i(22),i(23))},function(e,t){var i,n,a=e.exports={};function r(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function s(e){if(i===setTimeout)return setTimeout(e,0);if((i===r||!i)&&setTimeout)return i=setTimeout,setTimeout(e,0);try{return i(e,0)}catch(t){try{return i.call(null,e,0)}catch(t){return i.call(this,e,0)}}}!function(){try{i="function"==typeof setTimeout?setTimeout:r}catch(e){i=r}try{n="function"==typeof clearTimeout?clearTimeout:o}catch(e){n=o}}();var d,_=[],c=!1,h=-1;function l(){c&&d&&(c=!1,d.length?_=d.concat(_):h=-1,_.length&&u())}function u(){if(!c){var e=s(l);c=!0;for(var t=_.length;t;){for(d=_,_=[];++h<t;)d&&d[h].run();h=-1,t=_.length}d=null,c=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===o||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function f(e,t){this.fun=e,this.array=t}function p(){}a.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var i=1;i<arguments.length;i++)t[i-1]=arguments[i];_.push(new f(e,t)),1!==_.length||c||s(u)},f.prototype.run=function(){this.fun.apply(null,this.array)},a.title="browser",a.browser=!0,a.env={},a.argv=[],a.version="",a.versions={},a.on=p,a.addListener=p,a.once=p,a.off=p,a.removeListener=p,a.removeAllListeners=p,a.emit=p,a.prependListener=p,a.prependOnceListener=p,a.listeners=function(e){return[]},a.binding=function(e){throw new Error("process.binding is not supported")},a.cwd=function(){return"/"},a.chdir=function(e){throw new Error("process.chdir is not supported")},a.umask=function(){return 0}},function(e,t){var i;i=function(){return this}();try{i=i||new Function("return this")()}catch(e){"object"==typeof window&&(i=window)}e.exports=i},function(e,t,i){"use strict";i.r(t);var n=i(0),a=i(8),r=i(3),o=i(7),s=i(16),d=i(17),_=i(1),c=i(4),h=i(10);t.default=function(e){var t="PlayerEngineWorker",i=function(t,i){e.postMessage({msg:"logcat_callback",type:t,logcat:i})}.bind(void 0),l=null,u=null,f=null,p=null,m=!1,g=!1,y=0,v=0,S=!1;function b(){f&&(f.shutdown(),f.destroy(),f=null)}function E(){if(null==l||null==u)throw new r.a("Worker not initialized");if(p)throw new r.a("Transmuxer has been initialized");g||(!u.deferLoadAfterSourceOpen||m?((p=new d.a(l,u)).on(_.a.INIT_SEGMENT,(function(e,t){f.appendInitSegment(t)})),p.on(_.a.MEDIA_SEGMENT,(function(t,i){f.appendMediaSegment(i),e.postMessage({msg:"buffered_position_changed",buffered_position_milliseconds:i.info.endDts})})),p.on(_.a.LOADING_COMPLETE,(function(){f.endOfStream(),e.postMessage({msg:"player_event",event:c.a.LOADING_COMPLETE})})),p.on(_.a.RECOVERED_EARLY_EOF,(function(){e.postMessage({msg:"player_event",event:c.a.RECOVERED_EARLY_EOF})})),p.on(_.a.IO_ERROR,(function(t,i){e.postMessage({msg:"player_event",event:c.a.ERROR,error_type:h.b.NETWORK_ERROR,error_detail:t,info:i})})),p.on(_.a.DEMUX_ERROR,(function(t,i){e.postMessage({msg:"player_event",event:c.a.ERROR,error_type:h.b.MEDIA_ERROR,error_detail:t,info:i})})),p.on(_.a.MEDIA_INFO,(function(e){w(_.a.MEDIA_INFO,e)})),p.on(_.a.STATISTICS_INFO,(function(e){w(_.a.STATISTICS_INFO,e)})),p.on(_.a.RECOMMEND_SEEKPOINT,(function(t){!function(t){e.postMessage({msg:"transmuxing_event",event:_.a.RECOMMEND_SEEKPOINT,milliseconds:t})}(t)})),p.on(_.a.METADATA_ARRIVED,(function(e){D(c.a.METADATA_ARRIVED,e)})),p.on(_.a.SCRIPTDATA_ARRIVED,(function(e){D(c.a.SCRIPTDATA_ARRIVED,e)})),p.on(_.a.TIMED_ID3_METADATA_ARRIVED,(function(e){D(c.a.TIMED_ID3_METADATA_ARRIVED,e)})),p.on(_.a.SYNCHRONOUS_KLV_METADATA_ARRIVED,(function(e){D(c.a.SYNCHRONOUS_KLV_METADATA_ARRIVED,e)})),p.on(_.a.ASYNCHRONOUS_KLV_METADATA_ARRIVED,(function(e){D(c.a.ASYNCHRONOUS_KLV_METADATA_ARRIVED,e)})),p.on(_.a.SMPTE2038_METADATA_ARRIVED,(function(e){D(c.a.SMPTE2038_METADATA_ARRIVED,e)})),p.on(_.a.SCTE35_METADATA_ARRIVED,(function(e){D(c.a.SCTE35_METADATA_ARRIVED,e)})),p.on(_.a.PES_PRIVATE_DATA_DESCRIPTOR,(function(e){D(c.a.PES_PRIVATE_DATA_DESCRIPTOR,e)})),p.on(_.a.PES_PRIVATE_DATA_ARRIVED,(function(e){D(c.a.PES_PRIVATE_DATA_ARRIVED,e)})),p.open()):g=!0)}function A(){f&&f.flush(),p&&(p.close(),p.destroy(),p=null)}function R(){m=!0,g&&(g=!1,E())}function T(){e.postMessage({msg:"mse_event",event:o.a.UPDATE_END})}function L(){n.a.v(t,"MSE SourceBuffer is full, report to main thread"),e.postMessage({msg:"mse_event",event:o.a.BUFFER_FULL})}function k(t){e.postMessage({msg:"player_event",event:c.a.ERROR,error_type:h.b.MEDIA_ERROR,error_detail:h.b.MEDIA_MSE_ERROR,info:t})}function w(t,i){e.postMessage({msg:"transmuxing_event",event:t,info:i})}function D(t,i){e.postMessage({msg:"player_event",event:t,extraData:i})}e.addEventListener("message",(function(r){if(!S){var d=r.data;switch(d.cmd){case"logging_config":var _=d;a.a.applyConfig(_.logging_config),!0===_.logging_config.enableCallback?a.a.addLogListener(i):a.a.removeLogListener(i);break;case"init":l=(_=d).media_data_source,u=_.config;break;case"destroy":!function(){p&&A();f&&b();S=!0,e.postMessage({msg:"destroyed"})}();break;case"initialize_mse":!function(){n.a.v(t,"Initializing MediaSource in DedicatedWorker"),(f=new s.a(u)).on(o.a.SOURCE_OPEN,R.bind(this)),f.on(o.a.UPDATE_END,T.bind(this)),f.on(o.a.BUFFER_FULL,L.bind(this)),f.on(o.a.ERROR,k.bind(this)),f.initialize({getCurrentTime:function(){return y},getReadyState:function(){return v}});var i=f.getHandle();e.postMessage({msg:"mse_init",handle:i},[i])}();break;case"shutdown_mse":b();break;case"load":E();break;case"unload":A();break;case"unbuffered_seek":_=d;f.flush(),p.seek(_.milliseconds);break;case"timeupdate":y=(_=d).current_time;break;case"readystatechange":v=(_=d).ready_state;break;case"pause_transmuxer":p.pause();break;case"resume_transmuxer":p.resume()}}}))}},function(e,t,i){"use strict";i.r(t);var n=i(15),a=i(14),r={enableWorker:!1,enableWorkerForMSE:!1,enableStashBuffer:!0,stashInitialSize:void 0,isLive:!1,liveBufferLatencyChasing:!1,liveBufferLatencyMaxLatency:1.5,liveBufferLatencyMinRemain:.5,liveSync:!1,liveSyncMaxLatency:1.2,liveSyncTargetLatency:.8,liveSyncPlaybackRate:1.2,lazyLoad:!0,lazyLoadMaxDuration:180,lazyLoadRecoverDuration:30,deferLoadAfterSourceOpen:!0,autoCleanupMaxBackwardDuration:180,autoCleanupMinBackwardDuration:120,statisticsInfoReportInterval:600,fixAudioTimestampGap:!0,accurateSeek:!1,seekType:"range",seekParamStart:"bstart",seekParamEnd:"bend",rangeLoadZeroStart:!1,customSeekHandler:void 0,reuseRedirectedURL:!1,headers:void 0,customLoader:void 0};function o(){return Object.assign({},r)}var s=function(){function e(){}return e.supportMSEH264Playback=function(){var e=self.MediaSource&&self.MediaSource.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"'),t=self.ManagedMediaSource&&self.ManagedMediaSource.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"');return e||t},e.supportMSEH265Playback=function(){var e=self.MediaSource&&self.MediaSource.isTypeSupported('video/mp4; codecs="hvc1.1.6.L93.B0"'),t=self.ManagedMediaSource&&self.ManagedMediaSource.isTypeSupported('video/mp4; codecs="hvc1.1.6.L93.B0"');return e||t},e.supportNetworkStreamIO=function(){var e=new a.a({},o()),t=e.loaderType;return e.destroy(),"fetch-stream-loader"==t||"xhr-moz-chunked-loader"==t},e.getNetworkLoaderTypeName=function(){var e=new a.a({},o()),t=e.loaderType;return e.destroy(),t},e.supportNativeMediaPlayback=function(t){null==e.videoElement&&(e.videoElement=window.document.createElement("video"));var i=e.videoElement.canPlayType(t);return"probably"===i||"maybe"==i},e.getFeatureList=function(){var t={msePlayback:!1,mseLivePlayback:!1,mseH265Playback:!1,networkStreamIO:!1,networkLoaderName:"",nativeMP4H264Playback:!1,nativeMP4H265Playback:!1,nativeWebmVP8Playback:!1,nativeWebmVP9Playback:!1};return t.msePlayback=e.supportMSEH264Playback(),t.networkStreamIO=e.supportNetworkStreamIO(),t.networkLoaderName=e.getNetworkLoaderTypeName(),t.mseLivePlayback=t.msePlayback&&t.networkStreamIO,t.mseH265Playback=e.supportMSEH265Playback(),t.nativeMP4H264Playback=e.supportNativeMediaPlayback('video/mp4; codecs="avc1.42001E, mp4a.40.2"'),t.nativeMP4H265Playback=e.supportNativeMediaPlayback('video/mp4; codecs="hvc1.1.6.L93.B0"'),t.nativeWebmVP8Playback=e.supportNativeMediaPlayback('video/webm; codecs="vp8.0, vorbis"'),t.nativeWebmVP9Playback=e.supportNativeMediaPlayback('video/webm; codecs="vp9"'),t},e}(),d=i(2),_=i(0),c=i(9),h=i.n(c),l=i(16),u=i(4),f=i(17),p=i(7),m=i(10),g=i(3),y=i(1),v=i(5),S=i(11),b=function(){function e(e,t,i){this.TAG="SeekingHandler",this._config=null,this._media_element=null,this._always_seek_keyframe=!1,this._on_unbuffered_seek=null,this._request_set_current_time=!1,this._seek_request_record_clocktime=null,this._idr_sample_list=new S.a,this.e=null,this._config=e,this._media_element=t,this._on_unbuffered_seek=i,this.e={onMediaSeeking:this._onMediaSeeking.bind(this)};var n=v.a.chrome&&(v.a.version.major<50||50===v.a.version.major&&v.a.version.build<2661);this._always_seek_keyframe=!!(n||v.a.msedge||v.a.msie),this._always_seek_keyframe&&(this._config.accurateSeek=!1),this._media_element.addEventListener("seeking",this.e.onMediaSeeking)}return e.prototype.destroy=function(){this._idr_sample_list.clear(),this._idr_sample_list=null,this._media_element.removeEventListener("seeking",this.e.onMediaSeeking),this._media_element=null,this._on_unbuffered_seek=null},e.prototype.seek=function(e){var t=this._isPositionBuffered(e),i=!1;if(e<1&&this._media_element.buffered.length>0){var n=this._media_element.buffered.start(0);(n<1&&e<n||v.a.safari)&&(i=!0,e=v.a.safari?.1:n)}if(i)this.directSeek(e);else if(t)if(this._always_seek_keyframe){var a=this._getNearestKeyframe(Math.floor(1e3*e));null!=a&&(e=a.dts/1e3),this.directSeek(e)}else this.directSeek(e);else this._idr_sample_list.clear(),this._on_unbuffered_seek(Math.floor(1e3*e)),this._config.accurateSeek&&this.directSeek(e)},e.prototype.directSeek=function(e){this._request_set_current_time=!0,this._media_element.currentTime=e},e.prototype.appendSyncPoints=function(e){this._idr_sample_list.appendArray(e)},e.prototype._onMediaSeeking=function(t){if(this._request_set_current_time)this._request_set_current_time=!1;else{var i=this._media_element.currentTime,n=this._media_element.buffered;if(i<1&&n.length>0){var a=n.start(0);if(a<1&&i<a||v.a.safari){var r=v.a.safari?.1:a;return void this.directSeek(r)}}if(this._isPositionBuffered(i)){if(this._always_seek_keyframe){var o=this._getNearestKeyframe(Math.floor(1e3*i));null!=o&&(i=o.dts/1e3,this.directSeek(i))}}else this._seek_request_record_clocktime=e._getClockTime(),window.setTimeout(this._pollAndApplyUnbufferedSeek.bind(this),50)}},e.prototype._pollAndApplyUnbufferedSeek=function(){if(null!=this._seek_request_record_clocktime)if(this._seek_request_record_clocktime<=e._getClockTime()-100){var t=this._media_element.currentTime;this._seek_request_record_clocktime=null,this._isPositionBuffered(t)||(this._idr_sample_list.clear(),this._on_unbuffered_seek(Math.floor(1e3*t)),this._config.accurateSeek&&this.directSeek(t))}else window.setTimeout(this._pollAndApplyUnbufferedSeek.bind(this),50)},e.prototype._isPositionBuffered=function(e){for(var t=this._media_element.buffered,i=0;i<t.length;i++){var n=t.start(i),a=t.end(i);if(e>=n&&e<a)return!0}return!1},e.prototype._getNearestKeyframe=function(e){return this._idr_sample_list.getLastSyncPointBeforeDts(e)},e._getClockTime=function(){return self.performance&&self.performance.now?self.performance.now():Date.now()},e}(),E=function(){function e(e,t,i,n){this.TAG="LoadingController",this._config=null,this._media_element=null,this._on_pause_transmuxer=null,this._on_resume_transmuxer=null,this._paused=!1,this.e=null,this._config=e,this._media_element=t,this._on_pause_transmuxer=i,this._on_resume_transmuxer=n,this.e={onMediaTimeUpdate:this._onMediaTimeUpdate.bind(this)}}return e.prototype.destroy=function(){this._media_element.removeEventListener("timeupdate",this.e.onMediaTimeUpdate),this.e=null,this._media_element=null,this._config=null,this._on_pause_transmuxer=null,this._on_resume_transmuxer=null},e.prototype.notifyBufferedPositionChanged=function(e){!this._config.isLive&&this._config.lazyLoad&&(null==e?this._suspendTransmuxerIfNeeded():this._suspendTransmuxerIfBufferedPositionExceeded(e))},e.prototype._onMediaTimeUpdate=function(e){this._paused&&this._resumeTransmuxerIfNeeded()},e.prototype._suspendTransmuxerIfNeeded=function(){for(var e=this._media_element.buffered,t=this._media_element.currentTime,i=0,n=0;n<e.length;n++){var a=e.start(n),r=e.end(n);if(a<=t&&t<r){i=r;break}}i>0&&this._suspendTransmuxerIfBufferedPositionExceeded(i)},e.prototype._suspendTransmuxerIfBufferedPositionExceeded=function(e){e>=this._media_element.currentTime+this._config.lazyLoadMaxDuration&&!this._paused&&(_.a.v(this.TAG,"Maximum buffering duration exceeded, suspend transmuxing task"),this.suspendTransmuxer(),this._media_element.addEventListener("timeupdate",this.e.onMediaTimeUpdate))},e.prototype.suspendTransmuxer=function(){this._paused=!0,this._on_pause_transmuxer()},e.prototype._resumeTransmuxerIfNeeded=function(){for(var e=this._media_element.buffered,t=this._media_element.currentTime,i=this._config.lazyLoadRecoverDuration,n=!1,a=0;a<e.length;a++){var r=e.start(a),o=e.end(a);if(t>=r&&t<o){t>=o-i&&(n=!0);break}}n&&(_.a.v(this.TAG,"Continue loading from paused position"),this.resumeTransmuxer(),this._media_element.removeEventListener("timeupdate",this.e.onMediaTimeUpdate))},e.prototype.resumeTransmuxer=function(){this._paused=!1,this._on_resume_transmuxer()},e}(),A=function(){function e(e,t){this.TAG="StartupStallJumper",this._media_element=null,this._on_direct_seek=null,this._canplay_received=!1,this.e=null,this._media_element=e,this._on_direct_seek=t,this.e={onMediaCanPlay:this._onMediaCanPlay.bind(this),onMediaStalled:this._onMediaStalled.bind(this),onMediaProgress:this._onMediaProgress.bind(this)},this._media_element.addEventListener("canplay",this.e.onMediaCanPlay),this._media_element.addEventListener("stalled",this.e.onMediaStalled),this._media_element.addEventListener("progress",this.e.onMediaProgress)}return e.prototype.destroy=function(){this._media_element.removeEventListener("canplay",this.e.onMediaCanPlay),this._media_element.removeEventListener("stalled",this.e.onMediaStalled),this._media_element.removeEventListener("progress",this.e.onMediaProgress),this._media_element=null,this._on_direct_seek=null},e.prototype._onMediaCanPlay=function(e){this._canplay_received=!0,this._media_element.removeEventListener("canplay",this.e.onMediaCanPlay)},e.prototype._onMediaStalled=function(e){this._detectAndFixStuckPlayback(!0)},e.prototype._onMediaProgress=function(e){this._detectAndFixStuckPlayback()},e.prototype._detectAndFixStuckPlayback=function(e){var t=this._media_element,i=t.buffered;e||!this._canplay_received||t.readyState<2?i.length>0&&t.currentTime<i.start(0)&&(_.a.w(this.TAG,"Playback seems stuck at ".concat(t.currentTime,", seek to ").concat(i.start(0))),this._on_direct_seek(i.start(0)),this._media_element.removeEventListener("progress",this.e.onMediaProgress)):this._media_element.removeEventListener("progress",this.e.onMediaProgress)},e}(),R=function(){function e(e,t,i){this._config=null,this._media_element=null,this._on_direct_seek=null,this._config=e,this._media_element=t,this._on_direct_seek=i}return e.prototype.destroy=function(){this._on_direct_seek=null,this._media_element=null,this._config=null},e.prototype.notifyBufferedRangeUpdate=function(){this._chaseLiveLatency()},e.prototype._chaseLiveLatency=function(){var e=this._media_element.buffered,t=this._media_element.currentTime;if(this._config.isLive&&this._config.liveBufferLatencyChasing&&0!=e.length&&!this._media_element.paused){var i=e.end(e.length-1);if(i>this._config.liveBufferLatencyMaxLatency&&i-t>this._config.liveBufferLatencyMaxLatency){var n=i-this._config.liveBufferLatencyMinRemain;this._on_direct_seek(n)}}},e}(),T=function(){function e(e,t){this._config=null,this._media_element=null,this.e=null,this._config=e,this._media_element=t,this.e={onMediaTimeUpdate:this._onMediaTimeUpdate.bind(this)},this._media_element.addEventListener("timeupdate",this.e.onMediaTimeUpdate)}return e.prototype.destroy=function(){this._media_element.removeEventListener("timeupdate",this.e.onMediaTimeUpdate),this._media_element=null,this._config=null},e.prototype._onMediaTimeUpdate=function(e){if(this._config.isLive&&this._config.liveSync){var t=this._getCurrentLatency();if(t>this._config.liveSyncMaxLatency){var i=Math.min(2,Math.max(1,this._config.liveSyncPlaybackRate));this._media_element.playbackRate=i}else t>this._config.liveSyncTargetLatency||1!==this._media_element.playbackRate&&0!==this._media_element.playbackRate&&(this._media_element.playbackRate=1)}},e.prototype._getCurrentLatency=function(){if(!this._media_element)return 0;var e=this._media_element.buffered,t=this._media_element.currentTime;return 0==e.length?0:e.end(e.length-1)-t},e}(),L=function(){function e(e,t){this.TAG="PlayerEngineMainThread",this._emitter=new c,this._media_element=null,this._mse_controller=null,this._transmuxer=null,this._pending_seek_time=null,this._seeking_handler=null,this._loading_controller=null,this._startup_stall_jumper=null,this._live_latency_chaser=null,this._live_latency_synchronizer=null,this._mse_source_opened=!1,this._has_pending_load=!1,this._loaded_metadata_received=!1,this._media_info=null,this._statistics_info=null,this.e=null,this._media_data_source=e,this._config=o(),"object"==typeof t&&Object.assign(this._config,t),!0===e.isLive&&(this._config.isLive=!0),this.e={onMediaLoadedMetadata:this._onMediaLoadedMetadata.bind(this)}}return e.prototype.destroy=function(){this._emitter.emit(u.a.DESTROYING),this._transmuxer&&this.unload(),this._media_element&&this.detachMediaElement(),this.e=null,this._media_data_source=null,this._emitter.removeAllListeners(),this._emitter=null},e.prototype.on=function(e,t){var i=this;this._emitter.addListener(e,t),e===u.a.MEDIA_INFO&&this._media_info?Promise.resolve().then((function(){return i._emitter.emit(u.a.MEDIA_INFO,i.mediaInfo)})):e==u.a.STATISTICS_INFO&&this._statistics_info&&Promise.resolve().then((function(){return i._emitter.emit(u.a.STATISTICS_INFO,i.statisticsInfo)}))},e.prototype.off=function(e,t){this._emitter.removeListener(e,t)},e.prototype.attachMediaElement=function(e){var t=this;this._media_element=e,e.src="",e.removeAttribute("src"),e.srcObject=null,e.load(),e.addEventListener("loadedmetadata",this.e.onMediaLoadedMetadata),this._mse_controller=new l.a(this._config),this._mse_controller.on(p.a.UPDATE_END,this._onMSEUpdateEnd.bind(this)),this._mse_controller.on(p.a.BUFFER_FULL,this._onMSEBufferFull.bind(this)),this._mse_controller.on(p.a.SOURCE_OPEN,this._onMSESourceOpen.bind(this)),this._mse_controller.on(p.a.ERROR,this._onMSEError.bind(this)),this._mse_controller.on(p.a.START_STREAMING,this._onMSEStartStreaming.bind(this)),this._mse_controller.on(p.a.END_STREAMING,this._onMSEEndStreaming.bind(this)),this._mse_controller.initialize({getCurrentTime:function(){return t._media_element.currentTime},getReadyState:function(){return t._media_element.readyState}}),this._mse_controller.isManagedMediaSource()?(e.disableRemotePlayback=!0,e.srcObject=this._mse_controller.getObject()):e.src=this._mse_controller.getObjectURL()},e.prototype.detachMediaElement=function(){this._media_element&&(this._mse_controller.shutdown(),this._media_element.removeEventListener("loadedmetadata",this.e.onMediaLoadedMetadata),this._media_element.src="",this._media_element.removeAttribute("src"),this._media_element.srcObject=null,this._media_element.load(),this._media_element=null,this._mse_controller.revokeObjectURL()),this._mse_controller&&(this._mse_controller.destroy(),this._mse_controller=null)},e.prototype.load=function(){var e=this;if(!this._media_element)throw new g.a("HTMLMediaElement must be attached before load()!");if(this._transmuxer)throw new g.a("load() has been called, please call unload() first!");this._has_pending_load||(!this._config.deferLoadAfterSourceOpen||this._mse_source_opened?(this._transmuxer=new f.a(this._media_data_source,this._config),this._transmuxer.on(y.a.INIT_SEGMENT,(function(t,i){e._mse_controller.appendInitSegment(i)})),this._transmuxer.on(y.a.MEDIA_SEGMENT,(function(t,i){e._mse_controller.appendMediaSegment(i),!e._config.isLive&&"video"===t&&i.data&&i.data.byteLength>0&&"info"in i&&e._seeking_handler.appendSyncPoints(i.info.syncPoints),e._loading_controller.notifyBufferedPositionChanged(i.info.endDts/1e3)})),this._transmuxer.on(y.a.LOADING_COMPLETE,(function(){e._mse_controller.endOfStream(),e._emitter.emit(u.a.LOADING_COMPLETE)})),this._transmuxer.on(y.a.RECOVERED_EARLY_EOF,(function(){e._emitter.emit(u.a.RECOVERED_EARLY_EOF)})),this._transmuxer.on(y.a.IO_ERROR,(function(t,i){e._emitter.emit(u.a.ERROR,m.b.NETWORK_ERROR,t,i)})),this._transmuxer.on(y.a.DEMUX_ERROR,(function(t,i){e._emitter.emit(u.a.ERROR,m.b.MEDIA_ERROR,t,i)})),this._transmuxer.on(y.a.MEDIA_INFO,(function(t){e._media_info=t,e._emitter.emit(u.a.MEDIA_INFO,Object.assign({},t))})),this._transmuxer.on(y.a.STATISTICS_INFO,(function(t){e._statistics_info=e._fillStatisticsInfo(t),e._emitter.emit(u.a.STATISTICS_INFO,Object.assign({},t))})),this._transmuxer.on(y.a.RECOMMEND_SEEKPOINT,(function(t){e._media_element&&!e._config.accurateSeek&&e._seeking_handler.directSeek(t/1e3)})),this._transmuxer.on(y.a.METADATA_ARRIVED,(function(t){e._emitter.emit(u.a.METADATA_ARRIVED,t)})),this._transmuxer.on(y.a.SCRIPTDATA_ARRIVED,(function(t){e._emitter.emit(u.a.SCRIPTDATA_ARRIVED,t)})),this._transmuxer.on(y.a.TIMED_ID3_METADATA_ARRIVED,(function(t){e._emitter.emit(u.a.TIMED_ID3_METADATA_ARRIVED,t)})),this._transmuxer.on(y.a.SYNCHRONOUS_KLV_METADATA_ARRIVED,(function(t){e._emitter.emit(u.a.SYNCHRONOUS_KLV_METADATA_ARRIVED,t)})),this._transmuxer.on(y.a.ASYNCHRONOUS_KLV_METADATA_ARRIVED,(function(t){e._emitter.emit(u.a.ASYNCHRONOUS_KLV_METADATA_ARRIVED,t)})),this._transmuxer.on(y.a.SMPTE2038_METADATA_ARRIVED,(function(t){e._emitter.emit(u.a.SMPTE2038_METADATA_ARRIVED,t)})),this._transmuxer.on(y.a.SCTE35_METADATA_ARRIVED,(function(t){e._emitter.emit(u.a.SCTE35_METADATA_ARRIVED,t)})),this._transmuxer.on(y.a.PES_PRIVATE_DATA_DESCRIPTOR,(function(t){e._emitter.emit(u.a.PES_PRIVATE_DATA_DESCRIPTOR,t)})),this._transmuxer.on(y.a.PES_PRIVATE_DATA_ARRIVED,(function(t){e._emitter.emit(u.a.PES_PRIVATE_DATA_ARRIVED,t)})),this._seeking_handler=new b(this._config,this._media_element,this._onRequiredUnbufferedSeek.bind(this)),this._loading_controller=new E(this._config,this._media_element,this._onRequestPauseTransmuxer.bind(this),this._onRequestResumeTransmuxer.bind(this)),this._startup_stall_jumper=new A(this._media_element,this._onRequestDirectSeek.bind(this)),this._config.isLive&&this._config.liveBufferLatencyChasing&&(this._live_latency_chaser=new R(this._config,this._media_element,this._onRequestDirectSeek.bind(this))),this._config.isLive&&this._config.liveSync&&(this._live_latency_synchronizer=new T(this._config,this._media_element)),this._media_element.readyState>0&&this._seeking_handler.directSeek(0),this._transmuxer.open()):this._has_pending_load=!0)},e.prototype.unload=function(){var e,t,i,n,a,r,o,s,d;null===(e=this._media_element)||void 0===e||e.pause(),null===(t=this._live_latency_synchronizer)||void 0===t||t.destroy(),this._live_latency_synchronizer=null,null===(i=this._live_latency_chaser)||void 0===i||i.destroy(),this._live_latency_chaser=null,null===(n=this._startup_stall_jumper)||void 0===n||n.destroy(),this._startup_stall_jumper=null,null===(a=this._loading_controller)||void 0===a||a.destroy(),this._loading_controller=null,null===(r=this._seeking_handler)||void 0===r||r.destroy(),this._seeking_handler=null,null===(o=this._mse_controller)||void 0===o||o.flush(),null===(s=this._transmuxer)||void 0===s||s.close(),null===(d=this._transmuxer)||void 0===d||d.destroy(),this._transmuxer=null},e.prototype.play=function(){return this._media_element.play()},e.prototype.pause=function(){this._media_element.pause()},e.prototype.seek=function(e){this._media_element&&this._seeking_handler?this._seeking_handler.seek(e):this._pending_seek_time=e},Object.defineProperty(e.prototype,"mediaInfo",{get:function(){return Object.assign({},this._media_info)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"statisticsInfo",{get:function(){return Object.assign({},this._statistics_info)},enumerable:!1,configurable:!0}),e.prototype._onMSESourceOpen=function(){this._mse_source_opened=!0,this._has_pending_load&&(this._has_pending_load=!1,this.load())},e.prototype._onMSEUpdateEnd=function(){this._config.isLive&&this._config.liveBufferLatencyChasing&&this._live_latency_chaser&&this._live_latency_chaser.notifyBufferedRangeUpdate(),this._loading_controller.notifyBufferedPositionChanged()},e.prototype._onMSEBufferFull=function(){_.a.v(this.TAG,"MSE SourceBuffer is full, suspend transmuxing task"),this._loading_controller.suspendTransmuxer()},e.prototype._onMSEError=function(e){this._emitter.emit(u.a.ERROR,m.b.MEDIA_ERROR,m.a.MEDIA_MSE_ERROR,e)},e.prototype._onMSEStartStreaming=function(){this._loaded_metadata_received&&(this._config.isLive||(_.a.v(this.TAG,"Resume transmuxing task due to ManagedMediaSource onStartStreaming"),this._loading_controller.resumeTransmuxer()))},e.prototype._onMSEEndStreaming=function(){this._config.isLive||(_.a.v(this.TAG,"Suspend transmuxing task due to ManagedMediaSource onEndStreaming"),this._loading_controller.suspendTransmuxer())},e.prototype._onMediaLoadedMetadata=function(e){this._loaded_metadata_received=!0,null!=this._pending_seek_time&&(this._seeking_handler.seek(this._pending_seek_time),this._pending_seek_time=null)},e.prototype._onRequestDirectSeek=function(e){this._seeking_handler.directSeek(e)},e.prototype._onRequiredUnbufferedSeek=function(e){this._mse_controller.flush(),this._transmuxer.seek(e)},e.prototype._onRequestPauseTransmuxer=function(){this._transmuxer.pause()},e.prototype._onRequestResumeTransmuxer=function(){this._transmuxer.resume()},e.prototype._fillStatisticsInfo=function(e){if(e.playerType="MSEPlayer",!(this._media_element instanceof HTMLVideoElement))return e;var t=!0,i=0,n=0;if(this._media_element.getVideoPlaybackQuality){var a=this._media_element.getVideoPlaybackQuality();i=a.totalVideoFrames,n=a.droppedVideoFrames}else null!=this._media_element.webkitDecodedFrameCount?(i=this._media_element.webkitDecodedFrameCount,n=this._media_element.webkitDroppedFrameCount):t=!1;return t&&(e.decodedFrames=i,e.droppedFrames=n),e},e}(),k=i(18),w=i(8),D=function(){function e(e,t){this.TAG="PlayerEngineDedicatedThread",this._emitter=new c,this._media_element=null,this._worker_destroying=!1,this._seeking_handler=null,this._loading_controller=null,this._startup_stall_jumper=null,this._live_latency_chaser=null,this._live_latency_synchronizer=null,this._pending_seek_time=null,this._media_info=null,this._statistics_info=null,this.e=null,this._media_data_source=e,this._config=o(),"object"==typeof t&&Object.assign(this._config,t),!0===e.isLive&&(this._config.isLive=!0),this.e={onLoggingConfigChanged:this._onLoggingConfigChanged.bind(this),onMediaLoadedMetadata:this._onMediaLoadedMetadata.bind(this),onMediaTimeUpdate:this._onMediaTimeUpdate.bind(this),onMediaReadyStateChanged:this._onMediaReadyStateChange.bind(this)},w.a.registerListener(this.e.onLoggingConfigChanged),this._worker=k(24,{all:!0}),this._worker.addEventListener("message",this._onWorkerMessage.bind(this)),this._worker.postMessage({cmd:"init",media_data_source:this._media_data_source,config:this._config}),this._worker.postMessage({cmd:"logging_config",logging_config:w.a.getConfig()})}return e.isSupported=function(){return!!(self.Worker&&self.MediaSource&&"canConstructInDedicatedWorker"in self.MediaSource&&!0===self.MediaSource.canConstructInDedicatedWorker)},e.prototype.destroy=function(){this._emitter.emit(u.a.DESTROYING),this.unload(),this.detachMediaElement(),this._worker_destroying=!0,this._worker.postMessage({cmd:"destroy"}),w.a.removeListener(this.e.onLoggingConfigChanged),this.e=null,this._media_data_source=null,this._emitter.removeAllListeners(),this._emitter=null},e.prototype.on=function(e,t){var i=this;this._emitter.addListener(e,t),e===u.a.MEDIA_INFO&&this._media_info?Promise.resolve().then((function(){return i._emitter.emit(u.a.MEDIA_INFO,i.mediaInfo)})):e==u.a.STATISTICS_INFO&&this._statistics_info&&Promise.resolve().then((function(){return i._emitter.emit(u.a.STATISTICS_INFO,i.statisticsInfo)}))},e.prototype.off=function(e,t){this._emitter.removeListener(e,t)},e.prototype.attachMediaElement=function(e){this._media_element=e,this._media_element.src="",this._media_element.removeAttribute("src"),this._media_element.srcObject=null,this._media_element.load(),this._media_element.addEventListener("loadedmetadata",this.e.onMediaLoadedMetadata),this._media_element.addEventListener("timeupdate",this.e.onMediaTimeUpdate),this._media_element.addEventListener("readystatechange",this.e.onMediaReadyStateChanged),this._worker.postMessage({cmd:"initialize_mse"})},e.prototype.detachMediaElement=function(){this._worker.postMessage({cmd:"shutdown_mse"}),this._media_element&&(this._media_element.removeEventListener("loadedmetadata",this.e.onMediaLoadedMetadata),this._media_element.removeEventListener("timeupdate",this.e.onMediaTimeUpdate),this._media_element.removeEventListener("readystatechange",this.e.onMediaReadyStateChanged),this._media_element.src="",this._media_element.removeAttribute("src"),this._media_element.srcObject=null,this._media_element.load(),this._media_element=null)},e.prototype.load=function(){this._worker.postMessage({cmd:"load"}),this._seeking_handler=new b(this._config,this._media_element,this._onRequiredUnbufferedSeek.bind(this)),this._loading_controller=new E(this._config,this._media_element,this._onRequestPauseTransmuxer.bind(this),this._onRequestResumeTransmuxer.bind(this)),this._startup_stall_jumper=new A(this._media_element,this._onRequestDirectSeek.bind(this)),this._config.isLive&&this._config.liveBufferLatencyChasing&&(this._live_latency_chaser=new R(this._config,this._media_element,this._onRequestDirectSeek.bind(this))),this._config.isLive&&this._config.liveSync&&(this._live_latency_synchronizer=new T(this._config,this._media_element)),this._media_element.readyState>0&&this._seeking_handler.directSeek(0)},e.prototype.unload=function(){var e,t,i,n,a,r;null===(e=this._media_element)||void 0===e||e.pause(),this._worker.postMessage({cmd:"unload"}),null===(t=this._live_latency_synchronizer)||void 0===t||t.destroy(),this._live_latency_synchronizer=null,null===(i=this._live_latency_chaser)||void 0===i||i.destroy(),this._live_latency_chaser=null,null===(n=this._startup_stall_jumper)||void 0===n||n.destroy(),this._startup_stall_jumper=null,null===(a=this._loading_controller)||void 0===a||a.destroy(),this._loading_controller=null,null===(r=this._seeking_handler)||void 0===r||r.destroy(),this._seeking_handler=null},e.prototype.play=function(){return this._media_element.play()},e.prototype.pause=function(){this._media_element.pause()},e.prototype.seek=function(e){this._media_element&&this._seeking_handler?this._seeking_handler.seek(e):this._pending_seek_time=e},Object.defineProperty(e.prototype,"mediaInfo",{get:function(){return Object.assign({},this._media_info)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"statisticsInfo",{get:function(){return Object.assign({},this._statistics_info)},enumerable:!1,configurable:!0}),e.prototype._onLoggingConfigChanged=function(e){var t;null===(t=this._worker)||void 0===t||t.postMessage({cmd:"logging_config",logging_config:e})},e.prototype._onMSEUpdateEnd=function(){this._config.isLive&&this._config.liveBufferLatencyChasing&&this._live_latency_chaser&&this._live_latency_chaser.notifyBufferedRangeUpdate(),this._loading_controller.notifyBufferedPositionChanged()},e.prototype._onMSEBufferFull=function(){_.a.v(this.TAG,"MSE SourceBuffer is full, suspend transmuxing task"),this._loading_controller.suspendTransmuxer()},e.prototype._onMediaLoadedMetadata=function(e){null!=this._pending_seek_time&&(this._seeking_handler.seek(this._pending_seek_time),this._pending_seek_time=null)},e.prototype._onRequestDirectSeek=function(e){this._seeking_handler.directSeek(e)},e.prototype._onRequiredUnbufferedSeek=function(e){this._worker.postMessage({cmd:"unbuffered_seek",milliseconds:e})},e.prototype._onRequestPauseTransmuxer=function(){this._worker.postMessage({cmd:"pause_transmuxer"})},e.prototype._onRequestResumeTransmuxer=function(){this._worker.postMessage({cmd:"resume_transmuxer"})},e.prototype._onMediaTimeUpdate=function(e){this._worker.postMessage({cmd:"timeupdate",current_time:e.target.currentTime})},e.prototype._onMediaReadyStateChange=function(e){this._worker.postMessage({cmd:"readystatechange",ready_state:e.target.readyState})},e.prototype._onWorkerMessage=function(e){var t,i=e.data,n=i.msg;if("destroyed"==n||this._worker_destroying)return this._worker_destroying=!1,null===(t=this._worker)||void 0===t||t.terminate(),void(this._worker=null);switch(n){case"mse_init":var a=i;this._media_element.srcObject=a.handle;break;case"mse_event":(a=i).event==p.a.UPDATE_END?this._onMSEUpdateEnd():a.event==p.a.BUFFER_FULL&&this._onMSEBufferFull();break;case"transmuxing_event":if((a=i).event==y.a.MEDIA_INFO){var r=i;this._media_info=r.info,this._emitter.emit(u.a.MEDIA_INFO,Object.assign({},r.info))}else if(a.event==y.a.STATISTICS_INFO){var o=i;this._statistics_info=this._fillStatisticsInfo(o.info),this._emitter.emit(u.a.STATISTICS_INFO,Object.assign({},o.info))}else if(a.event==y.a.RECOMMEND_SEEKPOINT){var s=i;this._media_element&&!this._config.accurateSeek&&this._seeking_handler.directSeek(s.milliseconds/1e3)}break;case"player_event":if((a=i).event==u.a.ERROR){var d=i;this._emitter.emit(u.a.ERROR,d.error_type,d.error_detail,d.info)}else if("extraData"in a){var c=i;this._emitter.emit(c.event,c.extraData)}break;case"logcat_callback":a=i;_.a.emitter.emit("log",a.type,a.logcat);break;case"buffered_position_changed":a=i;this._loading_controller.notifyBufferedPositionChanged(a.buffered_position_milliseconds/1e3)}},e.prototype._fillStatisticsInfo=function(e){if(e.playerType="MSEPlayer",!(this._media_element instanceof HTMLVideoElement))return e;var t=!0,i=0,n=0;if(this._media_element.getVideoPlaybackQuality){var a=this._media_element.getVideoPlaybackQuality();i=a.totalVideoFrames,n=a.droppedVideoFrames}else null!=this._media_element.webkitDecodedFrameCount?(i=this._media_element.webkitDecodedFrameCount,n=this._media_element.webkitDroppedFrameCount):t=!1;return t&&(e.decodedFrames=i,e.droppedFrames=n),e},e}(),M=function(){function e(e,t){this.TAG="MSEPlayer",this._type="MSEPlayer",this._media_element=null,this._player_engine=null;var i=e.type.toLowerCase();if("mse"!==i&&"mpegts"!==i&&"m2ts"!==i&&"flv"!==i)throw new g.b("MSEPlayer requires an mpegts/m2ts/flv MediaDataSource input!");if(t&&t.enableWorkerForMSE&&D.isSupported())try{this._player_engine=new D(e,t)}catch(i){_.a.e(this.TAG,"Error while initializing PlayerEngineDedicatedThread, fallback to PlayerEngineMainThread"),this._player_engine=new L(e,t)}else this._player_engine=new L(e,t)}return e.prototype.destroy=function(){this._player_engine.destroy(),this._player_engine=null,this._media_element=null},e.prototype.on=function(e,t){this._player_engine.on(e,t)},e.prototype.off=function(e,t){this._player_engine.off(e,t)},e.prototype.attachMediaElement=function(e){this._media_element=e,this._player_engine.attachMediaElement(e)},e.prototype.detachMediaElement=function(){this._media_element=null,this._player_engine.detachMediaElement()},e.prototype.load=function(){this._player_engine.load()},e.prototype.unload=function(){this._player_engine.unload()},e.prototype.play=function(){return this._player_engine.play()},e.prototype.pause=function(){this._player_engine.pause()},Object.defineProperty(e.prototype,"type",{get:function(){return this._type},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"buffered",{get:function(){return this._media_element.buffered},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"duration",{get:function(){return this._media_element.duration},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"volume",{get:function(){return this._media_element.volume},set:function(e){this._media_element.volume=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"muted",{get:function(){return this._media_element.muted},set:function(e){this._media_element.muted=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentTime",{get:function(){return this._media_element?this._media_element.currentTime:0},set:function(e){this._player_engine.seek(e)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"mediaInfo",{get:function(){return this._player_engine.mediaInfo},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"statisticsInfo",{get:function(){return this._player_engine.statisticsInfo},enumerable:!1,configurable:!0}),e}(),C=function(){function e(e,t){this.TAG="NativePlayer",this._type="NativePlayer",this._emitter=new h.a,this._config=o(),"object"==typeof t&&Object.assign(this._config,t);var i=e.type.toLowerCase();if("mse"===i||"mpegts"===i||"m2ts"===i||"flv"===i)throw new g.b("NativePlayer does't support mse/mpegts/m2ts/flv MediaDataSource input!");if(e.hasOwnProperty("segments"))throw new g.b("NativePlayer(".concat(e.type,") doesn't support multipart playback!"));this.e={onvLoadedMetadata:this._onvLoadedMetadata.bind(this)},this._pendingSeekTime=null,this._statisticsReporter=null,this._mediaDataSource=e,this._mediaElement=null}return e.prototype.destroy=function(){this._emitter.emit(u.a.DESTROYING),this._mediaElement&&(this.unload(),this.detachMediaElement()),this.e=null,this._mediaDataSource=null,this._emitter.removeAllListeners(),this._emitter=null},e.prototype.on=function(e,t){var i=this;e===u.a.MEDIA_INFO?null!=this._mediaElement&&0!==this._mediaElement.readyState&&Promise.resolve().then((function(){i._emitter.emit(u.a.MEDIA_INFO,i.mediaInfo)})):e===u.a.STATISTICS_INFO&&null!=this._mediaElement&&0!==this._mediaElement.readyState&&Promise.resolve().then((function(){i._emitter.emit(u.a.STATISTICS_INFO,i.statisticsInfo)})),this._emitter.addListener(e,t)},e.prototype.off=function(e,t){this._emitter.removeListener(e,t)},e.prototype.attachMediaElement=function(e){if(this._mediaElement=e,e.addEventListener("loadedmetadata",this.e.onvLoadedMetadata),null!=this._pendingSeekTime)try{e.currentTime=this._pendingSeekTime,this._pendingSeekTime=null}catch(e){}},e.prototype.detachMediaElement=function(){this._mediaElement&&(this._mediaElement.src="",this._mediaElement.removeAttribute("src"),this._mediaElement.removeEventListener("loadedmetadata",this.e.onvLoadedMetadata),this._mediaElement=null),null!=this._statisticsReporter&&(window.clearInterval(this._statisticsReporter),this._statisticsReporter=null)},e.prototype.load=function(){if(!this._mediaElement)throw new g.a("HTMLMediaElement must be attached before load()!");this._mediaElement.src=this._mediaDataSource.url,this._mediaElement.readyState>0&&(this._mediaElement.currentTime=0),this._mediaElement.preload="auto",this._mediaElement.load(),this._statisticsReporter=window.setInterval(this._reportStatisticsInfo.bind(this),this._config.statisticsInfoReportInterval)},e.prototype.unload=function(){this._mediaElement&&(this._mediaElement.src="",this._mediaElement.removeAttribute("src")),null!=this._statisticsReporter&&(window.clearInterval(this._statisticsReporter),this._statisticsReporter=null)},e.prototype.play=function(){return this._mediaElement.play()},e.prototype.pause=function(){this._mediaElement.pause()},Object.defineProperty(e.prototype,"type",{get:function(){return this._type},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"buffered",{get:function(){return this._mediaElement.buffered},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"duration",{get:function(){return this._mediaElement.duration},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"volume",{get:function(){return this._mediaElement.volume},set:function(e){this._mediaElement.volume=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"muted",{get:function(){return this._mediaElement.muted},set:function(e){this._mediaElement.muted=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"currentTime",{get:function(){return this._mediaElement?this._mediaElement.currentTime:0},set:function(e){this._mediaElement?this._mediaElement.currentTime=e:this._pendingSeekTime=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"mediaInfo",{get:function(){var e={mimeType:(this._mediaElement instanceof HTMLAudioElement?"audio/":"video/")+this._mediaDataSource.type};return this._mediaElement&&(e.duration=Math.floor(1e3*this._mediaElement.duration),this._mediaElement instanceof HTMLVideoElement&&(e.width=this._mediaElement.videoWidth,e.height=this._mediaElement.videoHeight)),e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"statisticsInfo",{get:function(){var e={playerType:this._type,url:this._mediaDataSource.url};if(!(this._mediaElement instanceof HTMLVideoElement))return e;var t=!0,i=0,n=0;if(this._mediaElement.getVideoPlaybackQuality){var a=this._mediaElement.getVideoPlaybackQuality();i=a.totalVideoFrames,n=a.droppedVideoFrames}else null!=this._mediaElement.webkitDecodedFrameCount?(i=this._mediaElement.webkitDecodedFrameCount,n=this._mediaElement.webkitDroppedFrameCount):t=!1;return t&&(e.decodedFrames=i,e.droppedFrames=n),e},enumerable:!1,configurable:!0}),e.prototype._onvLoadedMetadata=function(e){null!=this._pendingSeekTime&&(this._mediaElement.currentTime=this._pendingSeekTime,this._pendingSeekTime=null),this._emitter.emit(u.a.MEDIA_INFO,this.mediaInfo)},e.prototype._reportStatisticsInfo=function(){this._emitter.emit(u.a.STATISTICS_INFO,this.statisticsInfo)},e}();n.a.install();var O={createPlayer:function(e,t){var i=e;if(null==i||"object"!=typeof i)throw new g.b("MediaDataSource must be an javascript object!");if(!i.hasOwnProperty("type"))throw new g.b("MediaDataSource must has type field to indicate video file type!");switch(i.type){case"mse":case"mpegts":case"m2ts":case"flv":return new M(i,t);default:return new C(i,t)}},isSupported:function(){return s.supportMSEH264Playback()},getFeatureList:function(){return s.getFeatureList()}};O.BaseLoader=d.a,O.LoaderStatus=d.c,O.LoaderErrors=d.b,O.Events=u.a,O.ErrorTypes=m.b,O.ErrorDetails=m.a,O.MSEPlayer=M,O.NativePlayer=C,O.LoggingControl=w.a,Object.defineProperty(O,"version",{enumerable:!0,get:function(){return"1.7.3"}});t.default=O}])}));
//# sourceMappingURL=mpegts.js.map