.dynamicList-container .pd-info-center {
  width: 100%;
  height: 100%;
}
.dynamicList-container .pd-info-center .y-center {
  position: absolute;
  top: 16px;
  left: 30px;
  width: 234px;
  height: 234px;
}
.dynamicList-container .pd-info-center .y-center > [class^='info-1'] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center center;
}
.dynamicList-container .pd-info-center .y-center .info-1-0 {
  background-image: url(../../../iframe/image/dynamicList/info-1-0.png);
  -webkit-animation-duration: 20s;
  -moz-animation-duration: 20s;
  -o-animation-duration: 20s;
  animation-duration: 20s;
}
.dynamicList-container .pd-info-center .y-center .info-1-1 {
  background-image: url(../../../iframe/image/dynamicList/info-1-1.png);
  -webkit-animation-duration: 20s;
  -moz-animation-duration: 20s;
  -o-animation-duration: 20s;
  animation-duration: 20s;
  -webkit-animation-delay: 3s;
  -moz-animation-delay: 3s;
  -o-animation-delay: 3s;
  animation-delay: 3s;
}
.dynamicList-container .pd-info-center .y-center .info-1-2 {
  background-image: url(../../../iframe/image/dynamicList/info-1-2.png);
  -webkit-animation-duration: 20s;
  -moz-animation-duration: 20s;
  -o-animation-duration: 20s;
  animation-duration: 20s;
  -webkit-animation-delay: 8s;
  -moz-animation-delay: 8s;
  -o-animation-delay: 8s;
  animation-delay: 8s;
}
.dynamicList-container .pd-info-center .y-center .info-1-3 {
  background-image: url(../../../iframe/image/dynamicList/info-1-3.png);
  -webkit-animation-duration: 10s;
  -moz-animation-duration: 10s;
  -o-animation-duration: 10s;
  animation-duration: 10s;
  -webkit-animation-delay: 5s;
  -moz-animation-delay: 5s;
  -o-animation-delay: 5s;
  animation-delay: 5s;
}
.dynamicList-container .pd-info-center .y-center .info-1-4 {
  background-image: url(../../../iframe/image/dynamicList/info-1-4.png);
  -webkit-animation-duration: 5s;
  -moz-animation-duration: 5s;
  -o-animation-duration: 5s;
  animation-duration: 5s;
}
.dynamicList-container .pd-info-center .y-number {
  position: absolute;
  left: 0;
  padding-left: 290px;
  -webkit-animation-timing-function: cubic-bezier(1, 0, 0.6, 0.6);
  -moz-animation-timing-function: cubic-bezier(1, 0, 0.6, 0.6);
  -o-animation-timing-function: cubic-bezier(1, 0, 0.6, 0.6);
  animation-timing-function: cubic-bezier(1, 0, 0.6, 0.6);
}
.dynamicList-container .pd-info-center .y-number .y-number-bg {
  position: absolute;
  top: 5px;
  width: 54px;
  height: 54px;
  -webkit-animation-duration: 5s;
  -moz-animation-duration: 5s;
  -o-animation-duration: 5s;
  animation-duration: 5s;
}
.dynamicList-container .pd-info-center .y-number .y-number-icon {
  position: absolute;
  top: 5px;
  width: 54px;
  height: 54px;
}
.dynamicList-container .pd-info-center .y-number .y-number-text {
  display: inline-block;
  padding: 10px 0 0 60px;
  color: #0072bc;
  font-size: 18px;
}
.dynamicList-container .pd-info-center .y-number .y-number-text > span {
  display: block;
}
.dynamicList-container .pd-info-center .y-number .y-number-text > span:nth-child(2) {
  padding: 0;
  color: #00aeef;
  font-size: 22px;
  font-family: 'DIGITALDREAMFAT';
  line-height: 28px;
  background: transparent;
}
.dynamicList-container .pd-info-center .y-number.y-number-0 {
  top: 15px;
  height: 60px;
  background: url(../../../iframe/image/dynamicList/info-line-01.png) no-repeat 180px center;
  -webkit-animation-delay: 1s;
  -moz-animation-delay: 1s;
  -o-animation-delay: 1s;
  animation-delay: 1s;
}
.dynamicList-container .pd-info-center .y-number.y-number-0 .y-number-bg {
  background: url(../../../iframe/image/dynamicList/info-bg-01.png) no-repeat 50% 50%;
  -webkit-animation-delay: 2s;
  -moz-animation-delay: 2s;
  -o-animation-delay: 2s;
  animation-delay: 2s;
}
.dynamicList-container .pd-info-center .y-number.y-number-0 .y-number-icon {
  background: url(../../../iframe/image/dynamicList/info-icon-1.png) no-repeat 50% 50%;
}
.dynamicList-container .pd-info-center .y-number.y-number-1 {
  top: 70px;
  height: 60px;
  padding-left: 350px;
  background: url(../../../iframe/image/dynamicList/info-line-02.png) no-repeat 180px center;
  -webkit-animation-delay: 1.25s;
  -moz-animation-delay: 1.25s;
  -o-animation-delay: 1.25s;
  animation-delay: 1.25s;
}
.dynamicList-container .pd-info-center .y-number.y-number-1 .y-number-bg {
  background: url(../../../iframe/image/dynamicList/info-bg-02.png) no-repeat 50% 50%;
  -webkit-animation-delay: 2.5s;
  -moz-animation-delay: 2.5s;
  -o-animation-delay: 2.5s;
  animation-delay: 2.5s;
}
.dynamicList-container .pd-info-center .y-number.y-number-1 .y-number-icon {
  background: url(../../../iframe/image/dynamicList/info-icon-2.png) no-repeat 50% 50%;
}
.dynamicList-container .pd-info-center .y-number.y-number-2 {
  top: 135px;
  height: 60px;
  padding-left: 350px;
  background: url(../../../iframe/image/dynamicList/info-line-02.png) no-repeat 180px center;
  -webkit-animation-delay: 1.5s;
  -moz-animation-delay: 1.5s;
  -o-animation-delay: 1.5s;
  animation-delay: 1.5s;
}
.dynamicList-container .pd-info-center .y-number.y-number-2 .y-number-bg {
  background: url(../../../iframe/image/dynamicList/info-bg-03.png) no-repeat 50% 50%;
  -webkit-animation-delay: 3s;
  -moz-animation-delay: 3s;
  -o-animation-delay: 3s;
  animation-delay: 3s;
}
.dynamicList-container .pd-info-center .y-number.y-number-2 .y-number-icon {
  background: url(../../../iframe/image/dynamicList/info-icon-3.png) no-repeat 50% 50%;
}
.dynamicList-container .pd-info-center .y-number.y-number-3 {
  top: 190px;
  height: 60px;
  background: url(../../../iframe/image/dynamicList/info-line-01.png) no-repeat 180px center;
  -webkit-animation-delay: 1.75s;
  -moz-animation-delay: 1.75s;
  -o-animation-delay: 1.75s;
  animation-delay: 1.75s;
}
.dynamicList-container .pd-info-center .y-number.y-number-3 .y-number-bg {
  background: url(../../../iframe/image/dynamicList/info-bg-04.png) no-repeat 50% 50%;
  -webkit-animation-delay: 3.5s;
  -moz-animation-delay: 3.5s;
  -o-animation-delay: 3.5s;
  animation-delay: 3.5s;
}
.dynamicList-container .pd-info-center .y-number.y-number-3 .y-number-icon {
  background: url(../../../iframe/image/dynamicList/info-icon-4.png) no-repeat 50% 50%;
}

.animated {
  -webkit-animation-duration: 1s;
  -moz-animation-duration: 1s;
  -o-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  -moz-animation-fill-mode: both;
  -o-animation-fill-mode: both;
  animation-fill-mode: both;
}

.animated.infinite {
  -webkit-animation-iteration-count: infinite;
  -moz-animation-iteration-count: infinite;
  -o-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}

@-webkit-keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transform-origin: center;
    transform-origin: center;
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
    -webkit-transform-origin: center;
    transform-origin: center;
  }
}
@-moz-keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transform-origin: center;
    -moz-transform-origin: center;
    transform-origin: center;
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    transform: rotate(360deg);
    -webkit-transform-origin: center;
    -moz-transform-origin: center;
    transform-origin: center;
  }
}
@-o-keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transform-origin: center;
    -o-transform-origin: center;
    transform-origin: center;
  }
  100% {
    -webkit-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
    -webkit-transform-origin: center;
    -o-transform-origin: center;
    transform-origin: center;
  }
}
@keyframes rotate {
  0% {
    -webkit-transform: rotate(0deg);
    -moz-transform: rotate(0deg);
    -o-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transform-origin: center;
    -moz-transform-origin: center;
    -o-transform-origin: center;
    transform-origin: center;
  }
  100% {
    -webkit-transform: rotate(360deg);
    -moz-transform: rotate(360deg);
    -o-transform: rotate(360deg);
    transform: rotate(360deg);
    -webkit-transform-origin: center;
    -moz-transform-origin: center;
    -o-transform-origin: center;
    transform-origin: center;
  }
}
.rotate {
  -webkit-animation-name: rotate;
  -moz-animation-name: rotate;
  -o-animation-name: rotate;
  animation-name: rotate;
  -webkit-animation-timing-function: linear;
  -moz-animation-timing-function: linear;
  -o-animation-timing-function: linear;
  animation-timing-function: linear;
}

@-webkit-keyframes rotateF {
  100% {
    -webkit-transform: rotate(-360deg);
    transform: rotate(-360deg);
    -webkit-transform-origin: center;
    transform-origin: center;
  }
}
@-moz-keyframes rotateF {
  100% {
    -webkit-transform: rotate(-360deg);
    -moz-transform: rotate(-360deg);
    transform: rotate(-360deg);
    -webkit-transform-origin: center;
    -moz-transform-origin: center;
    transform-origin: center;
  }
}
@-o-keyframes rotateF {
  100% {
    -webkit-transform: rotate(-360deg);
    -o-transform: rotate(-360deg);
    transform: rotate(-360deg);
    -webkit-transform-origin: center;
    -o-transform-origin: center;
    transform-origin: center;
  }
}
@keyframes rotateF {
  100% {
    -webkit-transform: rotate(-360deg);
    -moz-transform: rotate(-360deg);
    -o-transform: rotate(-360deg);
    transform: rotate(-360deg);
    -webkit-transform-origin: center;
    -moz-transform-origin: center;
    -o-transform-origin: center;
    transform-origin: center;
  }
}
.rotateF {
  -webkit-animation-name: rotateF;
  -moz-animation-name: rotateF;
  -o-animation-name: rotateF;
  animation-name: rotateF;
  -webkit-animation-timing-function: linear;
  -moz-animation-timing-function: linear;
  -o-animation-timing-function: linear;
  animation-timing-function: linear;
}

@-webkit-keyframes flashPD {
  0%,
  90% {
    opacity: 1;
  }
  92%,
  98% {
    opacity: 0;
  }
  96%,
  100% {
    opacity: 1;
  }
}
@-moz-keyframes flashPD {
  0%,
  90% {
    opacity: 1;
  }
  92%,
  98% {
    opacity: 0;
  }
  96%,
  100% {
    opacity: 1;
  }
}
@-o-keyframes flashPD {
  0%,
  90% {
    opacity: 1;
  }
  92%,
  98% {
    opacity: 0;
  }
  96%,
  100% {
    opacity: 1;
  }
}
@keyframes flashPD {
  0%,
  90% {
    opacity: 1;
  }
  92%,
  98% {
    opacity: 0;
  }
  96%,
  100% {
    opacity: 1;
  }
}
.flashPD {
  -webkit-animation-name: flashPD;
  -moz-animation-name: flashPD;
  -o-animation-name: flashPD;
  animation-name: flashPD;
}
