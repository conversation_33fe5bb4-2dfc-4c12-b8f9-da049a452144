/* x-noIndexList */


.noIndexList {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  transition: 1s;
}

.noIndexList li {
  padding: 20px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.noIndexList li .list-from {
  line-height: 400%;
}
.noIndexList li .list-from span:nth-of-type(2) {
  margin-left: 10%;
}

/* .myListBaseCss li div:nth-of-type(1) {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 32px;
  margin-right: 10px;
  margin-top: 10px;
} */

.noIndexList li div{
  width: 100%;
  box-sizing: border-box
}
.noIndexList li div> p{
  /* width:20%; */
  margin-right: 10px;
}
.noIndexList li div> p:nth-of-type(1) {
  display: block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  /* width:60%; */
  transition: 0.3s ease;
}
.hor-list-content>p:nth-of-type(1) {
  width: 60%;
}
.hor-list-content>p:nth-of-type(2) {
  width: 20%;
}
.hor-list-content>p:nth-of-type(3) {
  width: 20%;
}
.noIndexList .hor-list-content{
  display: flex;
  flex-direction: row;
  justify-content: space-between;

}