
.x-table *{
  font-size: 23px;
}
.x-table .table-header{
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.x-table .table-header .left-header,
.table-body-title
{ 
  flex: 2;
  height: 60px;
  /* padding:20px 10px; */
  border: 1px solid #BDD8FF;
  background-color:#ECF4FF;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.x-table .table-header .left-header::before{
  content: '';
  border-bottom: 3px solid #BDD8FF;
  width: 445px;
  height: 60px;
  transform: rotate(7deg);
  display: inline-block;
  position: absolute;
  z-index: 1;
  top: -30px;
}
.x-table .table-header .left-header span:nth-of-type(1){
  line-height: 60px;

}
.x-table .table-list{
  display: flex;
  flex-direction: row;
  flex: 3;
}
.x-table .table-list li{
  height: 60px;
  flex: 1;
  border: 1px solid #BDD8FF;
  display: flex;
  justify-content: center;
  align-items: center;
}

.table-body .table-body-title{
  height: 30px;
  font-size: 21px;
  font-weight: 700;
  padding-left: 20px;
  line-height: 30px;
}

.table-body .table-body-sub-title{
  border: 1px solid #BDD8FF;
  background-color:#fff;
  height: 30px;
  line-height: 30px;
  text-align: center;
}
.table-body .sub-ul li{
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
}
.sub-list{
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.sub-list span{
  text-align: center;
  border: 1px solid #BDD8FF;
  height: 30px;
  line-height: 30px;
}
.sub-list span.active{
  font-weight: 600;
}
.sub-ul{
  display: flex;
  flex-direction: column;
}
.sub-ul span:nth-of-type(1){
  flex:2;
}
.table-body-item .sub-list{
  flex:3;
}