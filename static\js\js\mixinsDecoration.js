// 全局混入 提取mixinsComponent文件
Vue.mixin({
    methods: {
        initDecoration() {
            for (let i = 1; i < 14; i++) {
                Vue.component(`x-border${i}`, {
                    props: {
                        option: {
                            type: Object,
                            default: () => {
                                return {};
                            },
                        },
                    },
                    watch: {
                        option: {
                            handler() {
                                this.init()
                            },
                        }
                    },
                    data() {
                        return {
                            color: [],
                            isResize: null
                        }
                    },
                    mounted() {
                        this.init()
                    },
                    methods: {
                        init() {
                            this.color = []
                            if (this.option.borderColor != '') {
                                this.color.border = this.option.borderColor
                            }
                            if (this.option.border1Color != '') {
                                this.color.border1 = this.option.border1Color
                            }
                            this.color = Object.values(this.color) // 对象转数组并去调空值
                            this.color = this.color.filter(item => { return item })
                        },
                        handleResize() {
                            this.isResize = vm.guid()
                        }
                    },
                    template: `<div class=""full>
      <dv-border-box-${i} :key="isResize"  style="box-show:'10px 10px 25px rgba(0, 0, 0, 0.5)'" :backgroundColor="option.backgroundColor" :dur="option.dur" :title="option.title" :titleWidth="option.titleWdith" :reverse="option.reverse" :color="color"></dv-border-box-${i}>
      <resize-observer @notify="handleResize" /></div>`
                });
            }
            for (let i = 14; i <= 20; i++) {
                Vue.component(`x-border${i}`, {
                    props: {
                        option: {
                            type: Object,
                            default: () => {
                                return {};
                            },
                        },
                    },
                    data() {
                        return {
                            i
                        }
                    },
                    template: `
              <div v-if="i==14" style="width:calc(100% - 20px);height:calc(100% - 20px)" :style="{backgroundColor:option.bgColor,border:''+option.borderFontSize+'px '+option.borderType+' '+option.borderColor+'', borderRadius:option.borderRadius+'px', boxShadow:'10px 10px 10px '+option.shadowColor+''}"></div>
              <svg v-else-if="i==15" t="1597288389492" preserveAspectRatio="none" width="100%" height="100%" class="icon" viewBox="0 0 1349 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6273"><path d="M731.428571 16.270222l32.507937 48.761905h568.888889v844.036063l-82.423873 82.440127H114.915556L32.507937 909.051937V16.270222h698.920634z m-682.666666 16.253968v869.798604L121.660952 975.238095h1122.011429L1316.571429 902.339048V81.269841H755.240635l-32.507937-48.761904H48.761905z" :fill="option.border1Color" p-id="6274"></path><path d="M655.506286 28.460698h69.404444l32.507937 48.761905h271.181206v-24.380952H770.454349l-32.507936-48.761905H655.52254zM1320.634921 77.222603v89.721905h24.380952v-114.102857H1121.28v24.380952zM21.130159 914.692063v-123.367619H11.377778v127.398604l93.427809 93.915428h112.103619v-9.752381H108.852825zM1339.326984 914.692063v-123.367619h9.752381v127.398604l-93.427809 93.915428h-112.103619v-9.752381h108.05638z" :fill="option.borderColor" p-id="6275"></path></svg>
              <svg v-else-if="i==16" t="1597288804010" preserveAspectRatio="none" width="100%" height="100%" class="icon" viewBox="0 0 1365 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6607"><path d="M1297.92 0L1365.333333 67.413333v889.173334L1297.92 1024H978.090667l-11.758934-17.0496H399.018667l-11.776 17.066667H67.413333L0 956.552533V67.4304L67.413333 0h319.778134l11.810133 17.083733H966.314667L978.107733 0H1297.92z m-7.082667 17.066667H987.067733l-11.776 17.083733H390.0416L378.248533 17.066667H74.478933L17.066667 74.496v875.008L74.478933 1006.933333h303.786667l11.776-17.0496h585.2672l11.741867 17.066667h303.803733l57.3952-57.429333V74.478933L1290.837333 17.066667z" :fill="option.borderColor" p-id="6608"></path><path d="M0 51.217067V27.5456L27.409067 0.017067H51.2zM0 972.817067v23.671466l27.409067 27.528534H51.2zM1365.333333 51.217067V27.5456L1337.924267 0.017067H1314.133333zM1365.333333 972.817067v23.671466l-27.409066 27.528534H1314.133333z"  :fill="option.border1Color" p-id="6609"></path></svg>
              <svg v-else-if="i==17" t="1597289241792" preserveAspectRatio="none" width="100%" height="100%" class="icon" viewBox="0 0 6488 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6749"><path d="M5313.155853 0H1179.483612L734.951171 307.542475v408.686733l444.532441 307.770792h4133.672241l444.532441-307.542475V307.542475z m421.70078 339.050167v365.306578l-429.006912 296.811594H1187.246377l-429.006912-296.811594V319.643255L1187.246377 22.831661h4118.831661l429.006912 296.811594v19.406912zM479.464883 306.400892v410.9699h22.831661v-410.9699h-22.831661z m-228.316611 296.811594h22.831661v-182.653289h-22.831661v182.653289z m-251.148272 0h22.831661v-182.653289H0v182.653289zM5986.233222 717.370792h22.831661v-410.9699h-22.831661v410.9699z m228.316611-114.158306h22.831661v-182.653289h-22.831661v182.653289z m251.148272-182.653289v182.653289h22.831661v-182.653289h-22.831661z" p-id="6750" :fill="option.borderColor"></path></svg>
              <svg v-else-if="i==18" t="1597289606359" preserveAspectRatio="none" width="100%" height="100%" class="icon" viewBox="0 0 2041 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="20692"><path d="M290.610777 0l-0.002116 5.003636 1207.063273 0.002116 31.65514 25.305917h353.370976l18.994776-25.305917h102.653885l32.300429 45.199868V749.127934H2041.652893v239.897388L1999.613884 1024H1663.665719v-3.939438H496.464397l-25.322843-22.142942H158.952727l-18.992661 25.308033H50.051174L5.005752 971.765421l-0.002116-676.284297H0V37.896463L51.750083 0h238.860694zM1496.732562 7.688463l-1206.123901-0.002116V12.694215H55.901091L12.694215 44.330314v251.152926l-5.007868-0.002116 0.002116 675.275107 43.579239 49.786711h87.348893l18.994777-25.305917h314.541488l25.316495 22.140826h1166.196364V1011.305785H1995.02281L2028.958678 983.06962V749.125818h5.005752V51.066711L2002.965157 7.688463l-42.483306-0.002116v197.380232l55.008265 64.744727V569.123967h-2.682711V270.797223l-55.008265-64.744727V7.686347l-54.767074 0.002116-18.990545 25.305917H1528.387702L1496.732562 7.688463zM30.96119 454.876033v298.326744l55.008265 64.744727V1017.652893h-2.682711v-198.719472l-55.008265-64.744727V454.876033h2.682711zM21.157025 935.140496l44.429752 53.248V1013.421488l-44.429752-53.248V935.140496z m0-42.31405l44.429752 53.248V971.107438l-44.429752-53.248V892.826446z m0-42.314049l44.429752 53.248V928.793388l-44.429752-53.248V850.512397z m0-42.31405l44.429752 53.248V886.479339l-44.429752-53.248V808.198347z m0-42.314049l44.429752 53.248V844.165289l-44.429752-53.248V765.884298zM1978.181818 181.950413l44.429752 53.248V260.231405l-44.429752-53.248V181.950413z m0-42.314049l44.429752 53.248V217.917355l-44.429752-53.248V139.636364z m0-42.31405l44.429752 53.248V175.603306l-44.429752-53.248V97.322314z m0-42.31405l44.429752 53.248V133.289256l-44.429752-53.248V55.008264z m0-42.314049l44.429752 53.248V90.975207l-44.429752-53.248V12.694215z" p-id="20693" :fill="option.borderColor"></path></svg>
              <svg v-else-if="i==19" t="1597289768200" preserveAspectRatio="none" width="100%" height="100%" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="21376"><path d="M365.100765 0h194.727869l-10.072131 10.072131h-194.727869zM216.830601 254.055519v-194.727869l10.072131 10.072131v194.727869z" :fill="option.borderColor" p-id="21377"></path><path d="M217.376175 62.251366L281.166339 0h13.429508L217.376175 77.947104v-15.695738zM311.382732 0h6.714754l-10.072131 10.072131h-6.714754zM216.830601 307.773552v-6.714754l10.072131 10.072131v6.714754zM351.671257 0h6.714754l-10.072131 10.072131h-6.714754zM216.830601 267.485027v-6.714754l10.072131 10.072131v6.714754zM338.241749 0h6.714754l-10.072131 10.072131h-6.714755zM216.830601 280.914536v-6.714755l10.072131 10.072132v6.714754zM324.81224 0h6.714755l-10.072132 10.072131h-6.714754zM216.830601 294.344044v-6.714754l10.072131 10.072131v6.714754z" :fill="option.border1Color" p-id="21378"></path><path d="M658.885246 1024H464.157377l10.072131-10.072131h194.727869zM807.15541 769.944481v194.727869l-10.072131-10.072131v-194.727869z" :fill="option.border2Color" p-id="21379"></path><path d="M806.609836 961.748634L742.819672 1024h-13.429508l77.219672-77.947104v15.695738zM712.603279 1024h-6.714754l10.072131-10.072131h6.714754zM807.15541 716.226448v6.714754l-10.072131-10.072131v-6.714754zM672.314754 1024h-6.714754l10.072131-10.072131h6.714754zM807.15541 756.514973v6.714754l-10.072131-10.072131v-6.714754zM685.744262 1024h-6.714754l10.072131-10.072131h6.714754zM807.15541 743.085464v6.714755l-10.072131-10.072132v-6.714754zM699.17377 1024H692.459016l10.072132-10.072131h6.714754zM807.15541 729.655956v6.714754l-10.072131-10.072131v-6.714754z" :fill="option.border3Color" p-id="21380"></path></svg>
              <svg v-else-if="i==20" t="1597290745357" preserveAspectRatio="none" width="100%" height="118%" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="30866"><path d="M1016.98508805 859.45397554A5.78793237 5.78793237 0 0 1 1011.58820978 863.1706467h-72.90839547a5.79953148 5.79953148 0 0 1 0-11.59906291H1009.10269631v-70.42288197a5.79953148 5.79953148 0 0 1 11.59906289 0v72.90839548a5.78793237 5.78793237 0 0 1-3.71667115 5.39687826zM1014.90222775 99.2895034a5.79953148 5.79953148 0 0 1-5.79953144-5.79953147V23.06708997h-70.422882a5.79953148 5.79953148 0 0 1 0-11.59906293h72.90839547a5.78958939 5.78958939 0 0 1 5.39687827 3.71501416A5.79953148 5.79953148 0 0 1 1020.7017592 20.58157648v72.90839545a5.79953148 5.79953148 0 0 1-5.79953145 5.79953147z m-929.58204206 763.8811433h-72.90839547a5.78958939 5.78958939 0 0 1-5.39687827-3.71667114A5.78793237 5.78793237 0 0 1 3.2982408 854.05709725v-72.90839543a5.79953148 5.79953148 0 1 1 11.59906289 0V851.57158379h70.422882a5.79953148 5.79953148 0 0 1 0 11.59906291z m0-840.10355673H14.89730369v70.42288196a5.79953148 5.79953148 0 1 1-11.59906289 0v-72.90839545a5.79953148 5.79953148 0 0 1 3.71501415-5.39853528A5.78958939 5.78958939 0 0 1 12.41179022 11.46802704h72.90839547a5.79953148 5.79953148 0 0 1 0 11.59906293z" :fill="option.borderColor" p-id="30867"></path></svg>
              `
                });
            }
            for (let i = 12; i <= 18; i++) {
                Vue.component(`x-decoration${i}`, {
                    props: {
                        option: {
                            type: Object,
                            default: () => {
                                return {};
                            },
                        },
                    },
                    data() {
                        return {
                            i
                        }
                    },
                    template: `
          <svg v-if="i===12" width="100%" height="100%" viewBox="0 0 1079 681" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg">
            <desc>Created with Lunacy</desc>
            <defs>
              <linearGradient x1="0.108657263" y1="0.3023702" x2="0.7431175" y2="0.7924229" id="gradient_1">
                <stop offset="0" stop-color="#386DF4" />
                <stop offset="1" stop-color="#000691" />
              </linearGradient>
              <linearGradient x1="0.5" y1="0" x2="0.5" y2="0.9790435" id="gradient_2">
                <stop offset="0" stop-color="#EEEEEE" />
                <stop offset="1" stop-color="#D2D2D2" stop-opacity="0" />
              </linearGradient>
              <radialGradient gradientUnits="objectBoundingBox" cx="50%" cy="46.35184%" fx="50%" fy="46.35184%" r="43.12863%" gradientTransform="translate(0.5,0.4635184),scale(0.7509826,1),rotate(-90),scale(1,0),translate(-0.5,-0.4635184)" id="gradient_3">
                <stop offset="0%" stop-color="#FE9A94" />
                <stop offset="100%" stop-color="#FFC0C8" />
              </radialGradient>
              <linearGradient x1="0.324416876" y1="0.6868707" x2="0.869264364" y2="0.0706165954" id="gradient_4">
                <stop offset="0" stop-color="#FFBAC2" />
                <stop offset="1" stop-color="#FFC4CC" />
              </linearGradient>
              <linearGradient x1="0.176164612" y1="0.5" x2="0.9343533" y2="0.3194449" id="gradient_5">
                <stop offset="0" stop-color="#FCDB00" />
                <stop offset="0.2787162" stop-color="#31FA00" />
                <stop offset="0.481366128" stop-color="#1ADB6B" />
                <stop offset="0.688872457" stop-color="#23DAFF" />
                <stop offset="1" stop-color="#8A34FF" />
              </linearGradient>
              <filter filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" id="filter_1">
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                <feOffset dx="0" dy="1" />
                <feGaussianBlur stdDeviation="38" />
                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
                <feColorMatrix type="matrix" values="0 0 0 0 0.5215687 0 0 0 0 0.4666667 0 0 0 0 1 0 0 0 1 0" />
                <feBlend mode="normal" in2="shape" result="effect0_innerShadow" />
              </filter>
              <filter filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB" id="filter_2">
                <feFlood flood-opacity="0" result="BackgroundImageFix" />
                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                <feOffset dx="0" dy="1" />
                <feGaussianBlur stdDeviation="38" />
                <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
                <feColorMatrix type="matrix" values="0 0 0 0 0.427451 0 0 0 0 0.427451 0 0 0 0 0.972549 0 0 0 1 0" />
                <feBlend mode="normal" in2="shape" result="effect0_innerShadow" />
              </filter>
              <path d="M510.5 1013.08C792.442 1013.08 1021 786.293 1021 506.539C1021 474.176 1017.94 442.521 1012.1 411.844C1006.69 383.503 998.913 355.996 988.967 329.537C927.858 166.971 785.008 43.9271 610.015 9.61884C592.651 6.2144 574.97 3.68372 557.02 2.07489C541.699 0.7016 526.182 0 510.5 0C443.378 0 379.281 12.8538 320.574 36.216C304.567 42.5859 288.961 49.7369 273.803 57.6216C111.041 142.287 0 311.546 0 506.539C0 562.786 9.23939 616.892 26.2944 667.444C40.0411 708.19 58.8654 746.626 82.0217 782.015C173.026 921.09 330.936 1013.08 510.5 1013.08Z" transform="translate(0.9990225 0)" id="path_1" />
              <path d="M510.5 1013.08C792.442 1013.08 1021 786.293 1021 506.539C1021 474.176 1017.94 442.521 1012.1 411.844C1006.69 383.503 998.913 355.996 988.967 329.537C927.858 166.971 785.008 43.9271 610.015 9.61884C592.651 6.2144 574.97 3.68372 557.02 2.07489C541.699 0.7016 526.182 0 510.5 0C443.378 0 379.281 12.8538 320.574 36.216C304.567 42.5859 288.961 49.7369 273.803 57.6216C111.041 142.287 0 311.546 0 506.539C0 562.786 9.23939 616.892 26.2944 667.444C40.0411 708.19 58.8654 746.626 82.0217 782.015C173.026 921.09 330.936 1013.08 510.5 1013.08Z" transform="translate(0 8.921387)" id="path_2" />
              <path d="M447.001 887.062C693.872 887.062 894.001 688.486 894.001 443.531C894.001 415.193 891.323 387.476 886.203 360.615C881.474 335.799 874.661 311.714 865.952 288.546C812.444 146.202 687.362 38.4631 534.137 8.42236C518.932 5.44139 503.45 3.2255 487.734 1.8168C474.318 0.614329 460.732 0 447.001 0C388.227 0 332.103 11.2549 280.698 31.7111C266.683 37.2886 253.018 43.5502 239.745 50.4541C97.229 124.588 0 272.793 0 443.531C0 492.781 8.09013 540.157 23.0237 584.421C35.0605 620.098 51.5433 653.754 71.8193 684.74C151.503 806.516 289.772 887.062 447.001 887.062Z" transform="translate(0.9985352 0)" id="path_3" />
              <path d="M447.001 887.062C693.872 887.062 894.001 688.486 894.001 443.531C894.001 415.193 891.323 387.476 886.203 360.615C881.474 335.799 874.661 311.714 865.952 288.546C812.444 146.202 687.362 38.4631 534.137 8.42236C518.932 5.44139 503.45 3.2255 487.734 1.8168C474.318 0.614329 460.732 0 447.001 0C388.227 0 332.103 11.2549 280.698 31.7111C266.683 37.2886 253.018 43.5502 239.745 50.4541C97.229 124.588 0 272.793 0 443.531C0 492.781 8.09013 540.157 23.0237 584.421C35.0605 620.098 51.5433 653.754 71.8193 684.74C151.503 806.516 289.772 887.062 447.001 887.062Z" transform="translate(0 7.937927)" id="path_4" />
              <path d="M347.5 695C539.419 695 695 539.419 695 347.5C695 325.298 692.918 303.582 688.938 282.536C685.261 263.093 679.965 244.223 673.194 226.071C631.597 114.547 534.358 30.1352 415.24 6.59879C403.42 4.26325 391.384 2.52713 379.166 1.42343C368.737 0.481317 358.175 0 347.5 0C301.809 0 258.178 8.81807 218.216 24.8452C207.32 29.2151 196.697 34.1209 186.379 39.53C75.5862 97.6128 0 213.729 0 347.5C0 386.087 6.2893 423.205 17.8987 457.885C27.2562 485.838 40.07 512.206 55.8326 536.484C117.779 631.893 225.27 695 347.5 695Z" id="path_5" />
              <path d="M2.59283 1.55725C4.02481 1.55728 5.18565 1.20869 5.18564 0.77867C5.18563 0.348647 4.02478 2.47135e-05 2.59281 -1.38312e-15C1.16083 -2.47135e-05 -7.39474e-06 0.348558 0 0.778581C7.39474e-06 1.2086 1.16086 1.55723 2.59283 1.55725Z" transform="matrix(0.9945219 0.1045285 -0.1045285 0.9945219 83.11719 77.37552)" id="path_6" />
              <path d="M3.28755 9.19375C5.01014 9.2529 6.33309 7.24304 6.24245 4.70461C6.1518 2.16618 4.68188 0.0604225 2.95929 0.00127279C1.2367 -0.0578769 -0.0862521 1.95198 0.00439342 4.49041C0.0950389 7.02884 1.56496 9.1346 3.28755 9.19375Z" transform="matrix(0.9335804 0.3583679 -0.3583679 0.9335804 3.294434 -1.192093E-07)" id="path_7" />
              <path d="M3.45489e-13 1.78225L6.0779 0L6.0779 28.1471L3.45489e-13 1.78225Z" transform="translate(-2.159573E-13 0.2247714)" id="path_8" />
              <path d="M22.2946 37.0127L8.5105 44.6414L11.1469 28.4906L1.12896e-12 17.056L15.4095 14.6957L22.3045 1.4373e-13L29.1916 14.692L44.5999 17.0442L33.4469 28.4847L36.0747 44.6341L22.2946 37.0127Z" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 248.0188 204.075)" id="path_9" />
              <path d="M22.2946 37.0127L8.51049 44.6414L11.1469 28.4906L1.13391e-12 17.056L15.4095 14.6957L22.3045 -1.28861e-13L29.1916 14.692L44.5999 17.0442L33.4469 28.4847L36.0747 44.6341L22.2946 37.0127Z" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 286.2063 251.3049)" id="path_10" />
              <path d="M43.6925 54.3802C43.6925 54.3802 97.4046 -16.0843 127.649 4.75736C157.894 25.5991 114.315 69.7573 114.315 69.7573C114.315 69.7573 78.7315 102.298 35.3855 118.008C-7.96046 133.719 1.79082 12.8641 1.79082 12.8641L43.6925 12.8641L43.6925 54.3802Z" transform="translate(0 3.051758E-05)" id="path_11" />
              <path d="M22.2979 33.4814L9.19152 40.3789L11.6946 25.7697L1.09134 15.4234L15.7447 13.2919L22.2979 0L28.8511 13.2919L43.5044 15.4234L32.9012 25.7697L35.4043 40.3789L22.2979 33.4814Z" transform="translate(99.23193 -3.685852)" id="path_12" />
              <path d="M22.2979 33.4814L9.19152 40.3789L11.6946 25.7697L1.09134 15.4234L15.7447 13.2919L22.2979 0L28.8511 13.2919L43.5044 15.4234L32.9012 25.7697L35.4043 40.3789L22.2979 33.4814Z" transform="translate(54.63574 29.53586)" id="path_13" />
              <path d="M22.2979 33.4814L9.19152 40.3789L11.6946 25.7697L1.09134 15.4234L15.7447 13.2919L22.2979 0L28.8511 13.2919L43.5044 15.4234L32.9012 25.7697L35.4043 40.3789L22.2979 33.4814Z" transform="translate(-12.77734 37.84125)" id="path_14" />
              <path d="M22.2979 33.4814L9.19152 40.3789L11.6946 25.7697L1.09134 15.4234L15.7447 13.2919L22.2979 0L28.8511 13.2919L43.5044 15.4234L32.9012 25.7697L35.4043 40.3789L22.2979 33.4814Z" transform="translate(43.22754 81.96399)" id="path_15" />
              <path d="M24 0L46.8254 16.5836L38.1068 43.4164L9.89315 43.4164L1.17464 16.5836L24 0Z" transform="matrix(0.7660444 0.6427876 -0.6427876 0.7660444 903.2993 520.188)" id="path_16" />
              <path d="M53.5 107C83.0472 107 107 83.0472 107 53.5C107 23.9528 83.0472 0 53.5 0C23.9528 0 0 23.9528 0 53.5C0 83.0472 23.9528 107 53.5 107Z" id="path_17" />
              <path d="M19.5 39C30.2696 39 39 30.2696 39 19.5C39 8.73045 30.2696 0 19.5 0C8.73045 0 0 8.73045 0 19.5C0 30.2696 8.73045 39 19.5 39Z" transform="translate(45.19086 53)" id="path_18" />
              <path d="M19.5 39C30.2696 39 39 30.2696 39 19.5C39 8.73045 30.2696 0 19.5 0C8.73045 0 0 8.73045 0 19.5C0 30.2696 8.73045 39 19.5 39Z" transform="translate(88.19086 53)" id="path_19" />
              <clipPath id="clip_1">
                <use xlink:href="#path_6" />
              </clipPath>
              <clipPath id="clip_2">
                <use xlink:href="#path_9" />
              </clipPath>
              <clipPath id="clip_3">
                <use xlink:href="#path_10" />
              </clipPath>
              <clipPath id="clip_4">
                <use xlink:href="#path_12" />
              </clipPath>
              <clipPath id="clip_5">
                <use xlink:href="#path_13" />
              </clipPath>
              <clipPath id="clip_6">
                <use xlink:href="#path_14" />
              </clipPath>
              <clipPath id="clip_7">
                <use xlink:href="#path_15" />
              </clipPath>
              <clipPath id="clip_8">
                <use xlink:href="#path_16" />
              </clipPath>
              <clipPath id="clip_9">
                <use xlink:href="#path_18" />
              </clipPath>
              <clipPath id="clip_10">
                <use xlink:href="#path_19" />
              </clipPath>
              <clipPath id="mask_1">
                <use xlink:href="#path_1" />
              </clipPath>
              <clipPath id="mask_2">
                <use xlink:href="#path_2" />
              </clipPath>
              <clipPath id="mask_3">
                <use xlink:href="#path_3" />
              </clipPath>
              <clipPath id="mask_4">
                <use xlink:href="#path_4" />
              </clipPath>
              <clipPath id="mask_5">
                <use xlink:href="#path_5" />
              </clipPath>
              <clipPath id="mask_6">
                <use xlink:href="#path_7" />
              </clipPath>
              <clipPath id="mask_7">
                <use xlink:href="#path_8" />
              </clipPath>
              <clipPath id="mask_8">
                <use xlink:href="#path_11" />
              </clipPath>
              <clipPath id="mask_9">
                <use xlink:href="#path_17" />
              </clipPath>
            </defs>
            <g id="Group-3" transform="translate(6 -403)">
              <g id="YaHong">
                <g id="yahong01" transform="translate(2.257324 0)">
                  <g id="编组-63" opacity="0.186802462">
                    <path d="M510.5 1013.08C792.442 1013.08 1021 786.293 1021 506.539C1021 474.176 1017.94 442.521 1012.1 411.844C1006.69 383.503 998.913 355.996 988.967 329.537C927.858 166.971 785.008 43.9271 610.015 9.61884C592.651 6.2144 574.97 3.68372 557.02 2.07489C541.699 0.7016 526.182 0 510.5 0C443.378 0 379.281 12.8538 320.574 36.216C304.567 42.5859 288.961 49.7369 273.803 57.6216C111.041 142.287 0 311.546 0 506.539C0 562.786 9.23939 616.892 26.2944 667.444C40.0411 708.19 58.8654 746.626 82.0217 782.015C173.026 921.09 330.936 1013.08 510.5 1013.08Z" transform="translate(0.9990225 0)" id="蒙版" fill="#06003B" stroke="none" />
                    <g filter="url(#filter_1)">
                      <path d="M510.5 1013.08C792.442 1013.08 1021 786.293 1021 506.539C1021 474.176 1017.94 442.521 1012.1 411.844C1006.69 383.503 998.913 355.996 988.967 329.537C927.858 166.971 785.008 43.9271 610.015 9.61884C592.651 6.2144 574.97 3.68372 557.02 2.07489C541.699 0.7016 526.182 0 510.5 0C443.378 0 379.281 12.8538 320.574 36.216C304.567 42.5859 288.961 49.7369 273.803 57.6216C111.041 142.287 0 311.546 0 506.539C0 562.786 9.23939 616.892 26.2944 667.444C40.0411 708.19 58.8654 746.626 82.0217 782.015C173.026 921.09 330.936 1013.08 510.5 1013.08Z" transform="translate(0 8.921387)" id="蒙版" fill="#7D73F5" stroke="none" />
                    </g>
                    <g clip-path="url(#mask_2)">
                      <path d="M134.868 267.643C209.354 267.643 269.736 207.729 269.736 133.822C269.736 59.9139 209.354 0 134.868 0C60.3825 0 0 59.9139 0 133.822C0 207.729 60.3825 267.643 134.868 267.643Z" transform="translate(119.8828 524.3821)" id="椭圆形备份-16" fill="#D8D8D8" fill-opacity="0.07454427" stroke="none" />
                      <path d="M134.868 267.643C209.354 267.643 269.736 207.729 269.736 133.822C269.736 59.9139 209.354 0 134.868 0C60.3825 0 0 59.9139 0 133.822C0 207.729 60.3825 267.643 134.868 267.643Z" transform="translate(917.1025 525.3734)" id="椭圆形备份-49" fill="#D8D8D8" fill-opacity="0.07454427" stroke="none" />
                      <path d="M71.9296 142.743C111.655 142.743 143.859 110.789 143.859 71.3715C143.859 47.2472 113.451 55.4672 94.984 42.5484C83.2772 34.3587 87.3423 0 71.9296 0C45.5196 0 62.843 34.7665 50.3341 55.8161C44.0272 66.4291 0 58.1592 0 71.3715C0 97.7644 28.8673 100.397 50.3341 112.746C60.9275 118.84 58.8033 142.743 71.9296 142.743Z" transform="translate(617.396 427.2377)" id="椭圆形备份-44" fill="#05002E" stroke="none" />
                      <path d="M71.9296 142.743C111.655 142.743 143.859 110.789 143.859 71.3715C143.859 47.2472 113.451 55.4672 94.984 42.5484C83.2772 34.3588 87.3423 0 71.9296 0C45.5196 0 62.843 34.7665 50.3341 55.8162C44.0272 66.4291 0 58.1592 0 71.3715C0 97.7644 28.8673 100.397 50.3341 112.746C60.9275 118.84 58.8033 142.743 71.9296 142.743Z" transform="translate(367.6401 734.5315)" id="椭圆形备份-48" fill="#05002E" stroke="none" />
                      <path d="M71.9296 142.743C111.655 142.743 143.859 110.789 143.859 71.3715C143.859 47.2472 113.451 55.4672 94.984 42.5484C83.2772 34.3588 87.3423 0 71.9296 0C45.5196 0 62.843 34.7665 50.3341 55.8162C44.0272 66.4291 0 58.1592 0 71.3715C0 97.7644 28.8673 100.397 50.3341 112.746C60.9275 118.84 58.8033 142.743 71.9296 142.743Z" transform="translate(399.6089 315.2241)" id="椭圆形备份-50" fill="#030022" stroke="none" />
                      <path d="M118.876 83.4657C158.592 83.4657 224.78 179.529 224.78 140.244C224.78 116.201 113.424 13.9029 94.9615 1.02743C83.2575 -7.13472 89.6354 36.015 74.2263 36.015C47.8225 36.015 62.8281 -6.72834 50.3222 14.2507C44.0168 24.828 0 16.5859 0 29.7538C0 56.0582 28.8605 58.6822 50.3222 70.9897C60.9131 77.0632 105.753 83.4657 118.876 83.4657Z" transform="translate(643.3706 276.5645)" id="椭圆形备份-51" fill="#030022" stroke="none" />
                      <path d="M73.3781 57.649C102.069 57.649 103.898 61.8861 103.898 33.2393C103.898 15.7068 86.7154 30.0976 73.3781 20.7088C64.9232 14.7569 54.5158 28.8647 43.3844 28.8647C24.3105 28.8647 31.9259 -11.8608 22.8917 3.43713C18.3368 11.1501 0 23.6372 0 33.2393C0 52.4205 20.8486 54.334 36.3524 63.3086C44.0032 67.7374 63.898 57.649 73.3781 57.649Z" transform="translate(271.7344 393.5344)" id="椭圆形备份-45" fill="#05002E" stroke="none" />
                      <path d="M73.3781 57.649C102.069 57.649 103.898 61.8861 103.898 33.2393C103.898 15.7068 86.7154 30.0976 73.3781 20.7088C64.9232 14.7569 54.5158 28.8647 43.3844 28.8647C24.3105 28.8647 31.9259 -11.8608 22.8917 3.43713C18.3368 11.1501 0 23.6372 0 33.2393C0 52.4205 20.8486 54.334 36.3524 63.3086C44.0032 67.7374 63.898 57.649 73.3781 57.649Z" transform="translate(-48.95215 360.8225)" id="椭圆形备份-47" fill="#05002E" fill-opacity="0.328962058" stroke="none" />
                      <path d="M50.901 27.0484C71.3156 27.0484 73.9277 52.261 73.9277 31.6104C73.9277 18.9718 71.8061 11.3088 62.3161 4.54065C56.3001 0.250134 44.8842 0 36.9638 0C23.392 0 25.6973 16.0116 19.2691 27.0395C16.0281 32.5995 0 24.6886 0 31.6104C0 45.4375 14.8346 46.8169 25.8661 53.2865C31.31 56.4791 44.1556 27.0484 50.901 27.0484Z" transform="translate(676.3384 779.1387)" id="椭圆形备份-46" fill="#05002E" stroke="none" />
                      <path d="M134.868 267.643C209.354 267.643 269.736 207.729 269.736 133.822C269.736 59.9139 209.354 0 134.868 0C60.3825 0 0 59.9139 0 133.822C0 207.729 60.3825 267.643 134.868 267.643Z" transform="translate(715.3003 159.5946)" id="椭圆形备份-43" fill="#D8D8D8" fill-opacity="0.04780506" stroke="none" />
                      <g id="编组-76" transform="translate(714.3013 158.6033)" />
                    </g>
                  </g>
                  <g id="编组-27" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 535.7764 309.8231)">
                    <g id="编组-46">
                      <g id="路径-280-+-路径-270-+-路径-269-+-路径-271-+-路径-272-+-路径-275-+-路径-273-+-路径-278-+-矩形-+-路径-283-+-路径-285-+-路径-284-+-路径-274-+-路径-277-+-路径-276-+-编组-+-编组备份-2-+-路径-279-+-椭圆形-+-路径-268-+-三角形-+-椭圆形-+-路径-281-+-路径-282-蒙版" transform="translate(0 176.0067)" />
                    </g>
                  </g>
                  <g id="编组-63" transform="translate(29 97)" opacity="0.3489816">
                    <path d="M447.001 887.062C693.872 887.062 894.001 688.486 894.001 443.531C894.001 415.193 891.323 387.476 886.203 360.615C881.474 335.799 874.661 311.714 865.952 288.546C812.444 146.202 687.362 38.4631 534.137 8.42236C518.932 5.44139 503.45 3.2255 487.734 1.8168C474.318 0.614329 460.732 0 447.001 0C388.227 0 332.103 11.2549 280.698 31.7111C266.683 37.2886 253.018 43.5502 239.745 50.4541C97.229 124.588 0 272.793 0 443.531C0 492.781 8.09013 540.157 23.0237 584.421C35.0605 620.098 51.5433 653.754 71.8193 684.74C151.503 806.516 289.772 887.062 447.001 887.062Z" transform="translate(0.9985352 0)" id="蒙版" fill="#06003B" stroke="none" />
                    <g filter="url(#filter_1)">
                      <path d="M447.001 887.062C693.872 887.062 894.001 688.486 894.001 443.531C894.001 415.193 891.323 387.476 886.203 360.615C881.474 335.799 874.661 311.714 865.952 288.546C812.444 146.202 687.362 38.4631 534.137 8.42236C518.932 5.44139 503.45 3.2255 487.734 1.8168C474.318 0.614329 460.732 0 447.001 0C388.227 0 332.103 11.2549 280.698 31.7111C266.683 37.2886 253.018 43.5502 239.745 50.4541C97.229 124.588 0 272.793 0 443.531C0 492.781 8.09013 540.157 23.0237 584.421C35.0605 620.098 51.5433 653.754 71.8193 684.74C151.503 806.516 289.772 887.062 447.001 887.062Z" transform="translate(0 7.937927)" id="蒙版" fill="#0455ED" stroke="none" />
                    </g>
                    <g clip-path="url(#mask_4)">
                      <path d="M117.868 234.169C182.965 234.169 235.737 181.748 235.737 117.084C235.737 52.4204 182.965 0 117.868 0C52.7714 0 0 52.4204 0 117.084C0 181.748 52.7714 234.169 117.868 234.169Z" transform="translate(105.8818 459.4069)" id="椭圆形备份-16" fill="#D8D8D8" fill-opacity="0.07454427" stroke="none" />
                      <path d="M117.868 234.169C182.965 234.169 235.737 181.748 235.737 117.084C235.737 52.4204 182.965 0 117.868 0C52.7714 0 0 52.4204 0 117.084C0 181.748 52.7714 234.169 117.868 234.169Z" transform="translate(803.1025 460.3991)" id="椭圆形备份-49" fill="#D8D8D8" fill-opacity="0.07454427" stroke="none" />
                      <path d="M62.9297 125.022C97.6848 125.022 125.859 97.035 125.859 62.5111C125.859 41.3817 99.2559 48.5812 83.0995 37.2662C72.8574 30.0933 76.4139 0 62.9297 0C39.8241 0 54.98 30.4504 44.0362 48.8869C38.5185 58.1823 0 50.9391 0 62.5111C0 85.6275 25.2554 87.9335 44.0362 98.7494C53.3042 104.087 51.4458 125.022 62.9297 125.022Z" transform="translate(541.395 374.0743)" id="椭圆形备份-44" fill="#05002E" stroke="none" />
                      <path d="M62.9297 125.022C97.6848 125.022 125.859 97.035 125.859 62.5111C125.859 41.3817 99.2559 48.5812 83.0995 37.2662C72.8574 30.0933 76.4139 0 62.9297 0C39.8241 0 54.98 30.4504 44.0362 48.8869C38.5185 58.1823 0 50.9391 0 62.5111C0 85.6275 25.2554 87.9335 44.0362 98.7494C53.3042 104.087 51.4458 125.022 62.9297 125.022Z" transform="translate(321.6406 643.9634)" id="椭圆形备份-48" fill="#05002E" stroke="none" />
                      <path d="M62.9297 125.022C97.6848 125.022 125.859 97.035 125.859 62.5111C125.859 41.3817 99.2559 48.5813 83.0995 37.2662C72.8574 30.0933 76.4139 0 62.9297 0C39.8241 0 54.98 30.4504 44.0362 48.8869C38.5185 58.1823 0 50.9391 0 62.5111C0 85.6275 25.2554 87.9335 44.0362 98.7494C53.3042 104.087 51.4458 125.022 62.9297 125.022Z" transform="translate(349.6094 276.8348)" id="椭圆形备份-50" fill="#030022" stroke="none" />
                      <path d="M104.068 73.0347C138.837 73.0347 196.78 157.093 196.78 122.717C196.78 101.679 99.2954 12.1654 83.1326 0.899029C72.8865 -6.24307 78.4699 31.5141 64.9803 31.5141C41.8655 31.5141 55.0019 -5.88747 44.0537 12.4697C38.5339 21.7252 0 14.5131 0 26.0354C0 49.0524 25.2655 51.3485 44.0537 62.1179C53.3254 67.4324 92.5796 73.0347 104.068 73.0347Z" transform="translate(563.3706 242.1064)" id="椭圆形备份-51" fill="#030022" stroke="none" />
                      <path d="M64.1969 50.6032C89.2978 50.6032 90.8984 54.3224 90.8984 29.1768C90.8984 13.7871 75.8654 26.4191 64.1969 18.1778C56.7999 12.9533 47.6947 25.3369 37.9561 25.3369C21.2687 25.3369 27.9313 -10.4112 20.0275 3.01704C16.0425 9.78737 0 20.7483 0 29.1768C0 46.0137 18.24 47.6933 31.8039 55.571C38.4974 59.4586 55.903 50.6032 64.1969 50.6032Z" transform="translate(237.7344 344.3071)" id="椭圆形备份-45" fill="#05002E" stroke="none" />
                      <path d="M64.1969 50.6032C89.2978 50.6032 90.8984 54.3224 90.8984 29.1768C90.8984 13.7871 75.8654 26.4191 64.1969 18.1778C56.7999 12.9533 47.6947 25.3369 37.9561 25.3369C21.2687 25.3369 27.9313 -10.4112 20.0275 3.01704C16.0425 9.78737 0 20.7483 0 29.1768C0 46.0137 18.24 47.6933 31.8039 55.571C38.4974 59.4586 55.903 50.6032 64.1969 50.6032Z" transform="translate(-42.95166 315.5322)" id="椭圆形备份-47" fill="#05002E" fill-opacity="0.328962058" stroke="none" />
                      <path d="M44.7041 23.5651C62.6333 23.5651 64.9274 45.5309 64.9274 27.5396C64.9274 16.5286 63.0641 9.85244 54.7295 3.95591C49.4459 0.217922 39.4199 0 32.4637 0C20.5442 0 22.5688 13.9497 16.9232 23.5574C14.0768 28.4014 0 21.5092 0 27.5396C0 39.5861 13.0286 40.7879 22.7171 46.4243C27.4982 49.2057 38.7799 23.5651 44.7041 23.5651Z" transform="translate(592.3379 682.6608)" id="椭圆形备份-46" fill="#05002E" stroke="none" />
                      <path d="M117.868 234.169C182.965 234.169 235.737 181.748 235.737 117.084C235.737 52.4204 182.965 0 117.868 0C52.7714 0 0 52.4204 0 117.084C0 181.748 52.7714 234.169 117.868 234.169Z" transform="translate(626.3003 140.898)" id="椭圆形备份-43" fill="#D8D8D8" fill-opacity="0.04780506" stroke="none" />
                      <g id="编组-76" transform="translate(625.3013 139.9058)" />
                    </g>
                  </g>
                  <g id="编组-63" transform="translate(146 212)" opacity="0.874697745">
                    <path d="M347.5 695C539.419 695 695 539.419 695 347.5C695 325.298 692.918 303.582 688.938 282.536C685.261 263.093 679.965 244.223 673.194 226.071C631.597 114.547 534.358 30.1352 415.24 6.59879C403.42 4.26325 391.384 2.52713 379.166 1.42343C368.737 0.481317 358.175 0 347.5 0C301.809 0 258.178 8.81807 218.216 24.8452C207.32 29.2151 196.697 34.1209 186.379 39.53C75.5862 97.6128 0 213.729 0 347.5C0 386.087 6.2893 423.205 17.8987 457.885C27.2562 485.838 40.07 512.206 55.8326 536.484C117.779 631.893 225.27 695 347.5 695Z" id="蒙版" fill="#06003B" stroke="none" />
                    <g filter="url(#filter_2)">
                      <path d="M347.5 695C539.419 695 695 539.419 695 347.5C695 325.298 692.918 303.582 688.938 282.536C685.261 263.093 679.965 244.223 673.194 226.071C631.597 114.547 534.358 30.1352 415.24 6.59879C403.42 4.26325 391.384 2.52713 379.166 1.42343C368.737 0.481317 358.175 0 347.5 0C301.809 0 258.178 8.81807 218.216 24.8452C207.32 29.2151 196.697 34.1209 186.379 39.53C75.5862 97.6128 0 213.729 0 347.5C0 386.087 6.2893 423.205 17.8987 457.885C27.2562 485.838 40.07 512.206 55.8326 536.484C117.779 631.893 225.27 695 347.5 695Z" id="蒙版" fill="url(#gradient_1)" stroke="none" />
                    </g>
                    <g clip-path="url(#mask_5)">
                      <path d="M69.5 139C107.884 139 139 107.884 139 69.5C139 31.1162 107.884 0 69.5 0C31.1162 0 0 31.1162 0 69.5C0 107.884 31.1162 139 69.5 139Z" transform="translate(81 404)" id="椭圆形备份-16" fill="url(#gradient_2)" fill-opacity="0.1046782" stroke="none" />
                      <path d="M91.5 183C142.034 183 183 142.034 183 91.5C183 40.9659 142.034 0 91.5 0C40.9659 0 0 40.9659 0 91.5C0 142.034 40.9659 183 91.5 183Z" transform="translate(407 609)" id="椭圆形备份-53" fill="url(#gradient_2)" fill-opacity="0.1046782" stroke="none" />
                      <path d="M91.5 183C142.034 183 183 142.034 183 91.5C183 40.9659 142.034 0 91.5 0C40.9659 0 0 40.9659 0 91.5C0 142.034 40.9659 183 91.5 183Z" transform="translate(385 110)" id="椭圆形备份-52" fill="url(#gradient_2)" fill-opacity="0.1046782" stroke="none" />
                      <path d="M91.5 183C142.034 183 183 142.034 183 91.5C183 40.9659 142.034 0 91.5 0C40.9659 0 0 40.9659 0 91.5C0 142.034 40.9659 183 91.5 183Z" transform="translate(624 361)" id="椭圆形备份-49" fill="#D8D8D8" fill-opacity="0.07454427" stroke="none" />
                      <path d="M49 98C76.062 98 98 76.062 98 49C98 32.4375 77.2853 38.081 64.7051 29.2115C56.7302 23.589 59.4995 0 49 0C31.0089 0 42.81 23.8689 34.2886 38.3205C29.9923 45.6068 0 39.9291 0 49C0 67.12 19.6651 68.9277 34.2886 77.4058C41.5051 81.5896 40.0581 98 49 98Z" transform="translate(249 504)" id="椭圆形备份-48" fill="#F1ECFF" fill-opacity="0.101960786" stroke="none" />
                      <path d="M49 98C76.062 98 98 76.062 98 49C98 32.4375 77.2853 38.081 64.7051 29.2115C56.7302 23.589 59.4995 0 49 0C31.0089 0 42.81 23.8689 34.2886 38.3205C29.9923 45.6068 0 39.9291 0 49C0 67.12 19.6651 68.9277 34.2886 77.4058C41.5051 81.5896 40.0581 98 49 98Z" transform="translate(271 216)" id="椭圆形备份-50" fill="#FFFFFF" fill-opacity="0.05673363" stroke="none" />
                      <path d="M1.0374 0C13.4725 -9.70443e-15 -4.64493 17.5216 9.28147 30C25.6625 44.6778 43.9003 39.7394 43.9003 25.1125C43.9003 8.55 -7.90452 0 1.0374 0Z" transform="translate(477.9497 284)" id="椭圆形备份-51" fill="#F1F0FF" fill-opacity="0.101960786" stroke="none" />
                      <path d="M50.1437 39.3676C69.7498 39.3676 71 42.261 71 22.6986C71 10.7259 59.2578 20.5532 50.1437 14.1417C44.3659 10.0773 37.2539 19.7113 29.6472 19.7113C16.6128 19.7113 21.8169 -8.09957 15.6433 2.34716C12.5306 7.61425 0 16.1415 0 22.6986C0 35.7971 14.2471 37.1038 24.8418 43.2324C30.07 46.2568 43.6653 39.3676 50.1437 39.3676Z" transform="translate(62 243)" id="椭圆形备份-45" fill="#EBE9F7" fill-opacity="0.121568628" stroke="none" />
                      <path d="M34.4262 18.6963C48.2333 18.6963 50 36.1238 50 21.8497C50 13.1137 48.5651 7.81684 42.1466 3.13858C38.0778 0.172897 30.3569 0 25 0C15.8209 0 17.38 11.0676 13.0324 18.6902C10.8404 22.5334 0 17.0652 0 21.8497C0 31.4073 10.0332 32.3607 17.4942 36.8326C21.1761 39.0394 29.864 18.6963 34.4262 18.6963Z" transform="translate(460 534)" id="椭圆形备份-46" fill="#05002E" stroke="none" />
                      <path d="M91.5 183C142.034 183 183 142.034 183 91.5C183 40.9659 142.034 0 91.5 0C40.9659 0 0 40.9659 0 91.5C0 142.034 40.9659 183 91.5 183Z" transform="translate(624 143)" id="椭圆形备份-43" fill="#D8D8D8" fill-opacity="0.04780506" stroke="none" />
                    </g>
                  </g>
                </g>
                <g id="编组-93" transform="matrix(0.9902681 0.1391731 -0.1391731 0.9902681 336.5151 461.1991)" opacity="0.900283635">
                  <g id="Group" transform="matrix(0.9902681 -0.1391731 0.1391731 0.9902681 304.7766 21.39869)">
                    <path d="M4.04577 3.56368L0 16.1025L16.8181 13.9328L14.1112 8.50728e-16L4.04577 3.56368Z" transform="matrix(0.9816272 -0.190809 0.190809 0.9816272 72.95557 93.74832)" id="路径-280" fill="#FFBAC2" stroke="none" />
                    <path d="M20.102 0C20.102 0 -6.67438 41.5942 2.21607 51.7367C5.78865 55.8125 15.746 56.253 20.1485 55.6994C24.7205 55.1244 33.9925 53.7001 33.9925 53.7001L43.8945 4.65915L20.102 0Z" transform="matrix(0.9816272 -0.190809 0.190809 0.9816272 46.40039 46.9278)" id="路径-270" fill="#FFC4CC" stroke="none" />
                    <path d="M-4.24195e-11 17.3024C-4.24195e-11 17.3024 0.249053 42.5487 17.3898 54.7595C34.5304 66.9703 53.1879 69.271 58.5616 53.2478C63.9353 37.2247 35.7545 -1.67904e-11 35.7545 -1.67904e-11L-4.24195e-11 17.3024Z" transform="matrix(0.9816272 -0.190809 0.190809 0.9816272 66.72949 27.66016)" id="路径-269" fill="#1F170F" stroke="none" />
                    <path d="M93.951 15.6325C93.951 15.6325 78.7651 21.4277 78.7651 36.1792C78.7651 50.9307 69.1919 47.4999 60.5303 47.4999C51.8687 47.4999 40.9429 55.0551 47.1048 71.597C53.2667 88.139 17.1015 86.4601 17.1015 86.4601C17.1015 86.4601 -8.55077 91.3103 4.27538 106.522C17.1015 121.734 88.6074 116.958 118.074 90.5987C147.54 64.2391 123.353 -1.8822e-11 123.353 -1.8822e-11L93.951 15.6325Z" transform="matrix(0.9816272 -0.190809 0.190809 0.9816272 4.768372E-06 72.49982)" id="路径-271" fill="#1F170F" stroke="none" />
                    <path d="M9.78634 -6.31501e-13C9.78634 -6.31501e-13 6.04835 1.62203 3.23591 6.42823C0.423469 11.2344 0 16.724 0 16.724L1.96611 17.3353C1.96611 17.3353 3.08361 15.1392 3.23591 15.2235C3.38821 15.3077 2.69805 17.5245 2.69805 17.5245C2.69805 17.5245 3.89493 17.9733 4.24156 18.0868C4.34105 18.1193 5.00682 16.5998 5.08491 16.6428C5.23227 16.724 4.76859 18.2669 4.8538 18.3011C5.30519 18.4825 5.97708 18.6548 6.40706 18.8131C6.45876 18.8321 7.33827 15.6829 7.47753 15.6829C7.6168 15.6829 6.62312 18.8834 6.6759 18.9031C7.09472 19.0595 7.50347 19.2041 7.90187 19.3372C7.95359 19.3544 8.33567 18.4826 8.38703 18.4995C8.43559 18.5154 8.07712 19.404 8.12529 19.4198C9.05286 19.7249 9.86098 20.036 10.6755 20.2161C10.7144 20.2247 11.2419 17.6696 11.3839 17.6696C11.5258 17.6696 10.9911 20.4131 11.0257 20.4301C13.2142 21.5064 16.0184 22.7128 17.1326 22.0097C22.2242 18.7968 11.587 0.37259 11.587 0.37259L9.78634 -6.31501e-13Z" transform="matrix(0.9816272 -0.190809 0.190809 0.9816272 56.02832 40.07684)" id="路径-272" fill="#1F170F" stroke="none" />
                    <path d="M5.31541e-15 14.9164C5.31541e-15 14.9164 13.3767 1.41322 31.1342 0.171736C48.8918 -1.06975 47.8154 6.66346 47.8154 6.66346L8.39213 21.3658L5.31541e-15 14.9164Z" transform="matrix(0.9816272 -0.190809 0.190809 0.9816272 59.14355 27.61048)" id="路径-279" fill="#1F170F" stroke="none" />
                    <path d="M10.5638 23.4915C16.2243 23.5958 17.0347 19.5889 17.2417 13.0359C17.4487 6.48279 16.2517 1.07331 10.087 0.0982071C3.92227 -0.876899 -1.13999 5.55435 0.223938 12.9492C1.58786 20.344 4.9032 23.3872 10.5638 23.4915Z" transform="matrix(0.9612617 -0.2756374 0.2756374 0.9612617 80.39404 4.758361)" id="椭圆形" fill="#1F170F" stroke="none" />
                    <path d="M0.0212457 1.4854C0.0212457 1.4854 2.60811 -1.37443 4.5199 1.27175C6.43168 3.91792 3.10967 10.1459 1.46658 8.67404C-0.176518 7.20215 0.0212457 1.4854 0.0212457 1.4854Z" transform="matrix(0.9925461 0.1218693 -0.1218693 0.9925461 84.50293 70.17618)" id="路径-268" fill="#FFAEB7" stroke="none" />
                    <path d="M1.55563 0L3.11139 3.63361L1.2712e-12 3.63355L1.55563 0Z" transform="matrix(0.9945219 0.1045285 -0.1045285 0.9945219 84.31201 75.93515)" id="三角形" fill="#F7FAFA" stroke="none" />
                    <g id="椭圆形">
                      <g clip-path="url(#clip_1)">
                        <use xlink:href="#path_6" fill="none" stroke="#D6E3E3" stroke-width="2" />
                      </g>
                    </g>
                    <path d="M11.3749 1.44844e-13C11.3749 1.44844e-13 0.000283306 6.879 -1.16184e-12 7.94631C-0.000283306 9.01362 5.94864 11.4442 5.94864 11.4442L11.3749 1.44844e-13Z" transform="matrix(0.9816272 -0.190809 0.190809 0.9816272 47.85107 66.6752)" id="路径-281" fill="#FFC4CC" stroke="none" />
                    <path d="M0.155674 0.00243823C0.155674 0.00243823 -1.2055 3.11707 9.33502 1.57867e-13" transform="matrix(0.9816272 -0.190809 0.190809 0.9816272 54.55274 86.74118)" id="路径-282" fill="#AF545B" stroke="none" />
                    <path d="M8.32616 12.5081C12.9255 12.5069 16.6547 9.70585 16.6556 6.25184C16.6566 2.79782 12.9288 -0.00122128 8.32948 3.99759e-07C3.73015 0.00122208 0.000917003 2.80224 1.69085e-07 6.25626C-0.000916665 9.71027 3.72683 12.5093 8.32616 12.5081Z" transform="matrix(0.9816272 -0.190809 0.190809 0.9816272 59.82861 70.88028)" id="椭圆形" fill="url(#gradient_3)" stroke="none" />
                    <g id="椭圆形" transform="matrix(0.9816272 -0.190809 0.190809 0.9816272 61.14209 59.51437)">
                      <path d="M3.28755 9.19375C5.01014 9.2529 6.33309 7.24304 6.24245 4.70461C6.1518 2.16618 4.68188 0.0604225 2.95929 0.00127279C1.2367 -0.0578769 -0.0862521 1.95198 0.00439342 4.49041C0.0950389 7.02884 1.56496 9.1346 3.28755 9.19375Z" transform="matrix(0.9335804 0.3583679 -0.3583679 0.9335804 3.294434 -1.192093E-07)" id="蒙版" fill="#FFFFFF" stroke="none" />
                      <g clip-path="url(#mask_6)">
                        <path d="M2.03342 4.24389C3.11634 4.28339 3.96228 3.36565 3.9229 2.19407C3.88351 1.02249 2.97371 0.0407245 1.89079 0.00122847C0.807882 -0.0382676 -0.0380644 0.879467 0.00132134 2.05105C0.0407071 3.22262 0.95051 4.20439 2.03342 4.24389Z" transform="matrix(0.9335804 0.3583679 -0.3583679 0.9335804 1.520997 3.826935)" id="椭圆形" fill="#15243B" stroke="none" />
                      </g>
                    </g>
                    <g id="路径-289-+-路径-291-+-路径-292-+-路径-293-+-路径-294-+-路径-295-+-路径-296-+-路径-297-+-路径-298-蒙版" transform="matrix(0.9816272 -0.190809 0.190809 0.9816272 77.08838 25.04486)">
                      <path d="M3.45489e-13 1.78225L6.0779 0L6.0779 28.1471L3.45489e-13 1.78225Z" transform="translate(-2.159573E-13 0.2247714)" id="蒙版" fill="#F55B48" stroke="none" />
                      <g clip-path="url(#mask_7)">
                        <path d="M0 0.259029L1.21744 0.259029" transform="translate(5.09308 24.112)" id="路径-289" fill="none" stroke="#FFFFFF" stroke-opacity="0.498039216" stroke-width="1" />
                        <path d="M0 0.259029L1.52568 0.259029" transform="translate(4.629723 21.69349)" id="路径-291" fill="none" stroke="#FFFFFF" stroke-opacity="0.498039216" stroke-width="1" />
                        <path d="M0 0.259029L2.05384 0.259029" transform="translate(4.101557 19.33338)" id="路径-292" fill="none" stroke="#FFFFFF" stroke-opacity="0.498039216" stroke-width="1" />
                        <path d="M0 0.259029L2.66315 0.259029" transform="translate(3.492249 17.1716)" id="路径-293" fill="none" stroke="#FFFFFF" stroke-opacity="0.498039216" stroke-width="1" />
                        <path d="M0 0.25903C0 0.25903 3.06351 0.25903 3.06351 0.25903" transform="translate(3.09189 15.13452)" id="路径-294" fill="none" stroke="#FFFFFF" stroke-opacity="0.498039216" stroke-width="1" />
                        <path d="M0 0.25903L3.51068 0.25903" transform="translate(2.644716 12.81034)" id="路径-295" fill="none" stroke="#FFFFFF" stroke-opacity="0.498039216" stroke-width="1" />
                        <path d="M0 0.25903L4.08775 0.25903" transform="translate(2.067652 10.1444)" id="路径-296" fill="none" stroke="#FFFFFF" stroke-opacity="0.498039216" stroke-width="1" />
                        <path d="M0 0.25903L4.58844 0.25903" transform="translate(1.566954 7.533111)" id="路径-297" fill="none" stroke="#FFFFFF" stroke-opacity="0.498039216" stroke-width="1" />
                        <path d="M0 0.25903L5.3263 0.25903" transform="translate(0.8291014 4.446508)" id="路径-298" fill="none" stroke="#FFFFFF" stroke-opacity="0.498039216" stroke-width="1" />
                      </g>
                    </g>
                    <path d="M8.37818e-13 18.7627L2.08694 2.37471e-13" transform="matrix(0.9816272 -0.190809 0.190809 0.9816272 77.71387 10.05804)" id="路径-288" fill="none" stroke="#DF503E" stroke-width="2" />
                    <path d="M9.01813 13.8955L0 -3.08542e-15" transform="matrix(0.9816272 -0.190809 0.190809 0.9816272 70.58447 20.6499)" id="路径-287" fill="none" stroke="#DF503E" stroke-width="2" />
                    <path d="M3.5 0L6.8287 2.41844L5.55725 6.33156L1.44275 6.33156L0.171302 2.41844L3.5 0Z" transform="matrix(0.9902681 0.1391731 -0.1391731 0.9902681 76.4126 4.304993)" id="多边形" fill="#DF503E" stroke="none" />
                    <path d="M3.5 0L6.8287 2.41844L5.55725 6.33156L1.44275 6.33156L0.171302 2.41844L3.5 0Z" transform="matrix(0.9902681 0.1391731 -0.1391731 0.9902681 66.4126 16.30682)" id="多边形备份-3" fill="#DF503E" stroke="none" />
                  </g>
                  <path d="M154.489 -5.84767e-11L-2.00947e-12 18.8961L-2.00947e-12 122.468C-2.00947e-12 122.468 2.51329 137.663 70.2736 124.451C138.034 111.24 159.132 84.0165 179.383 59.3596C199.633 34.7027 178.132 4.0483 178.132 4.0483L154.489 -5.84767e-11Z" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 236.7698 186.0534)" id="蒙版" fill="#FFD122" stroke="none" />
                  <g id="星形">
                    <g clip-path="url(#clip_2)">
                      <use xlink:href="#path_9" fill="none" stroke="#FFFFFF" stroke-opacity="0.384313732" stroke-width="4" />
                    </g>
                  </g>
                  <g id="星形备份-2">
                    <g clip-path="url(#clip_3)">
                      <use xlink:href="#path_10" fill="none" stroke="#FFFFFF" stroke-opacity="0.384313732" stroke-width="4" />
                    </g>
                  </g>
                  <g id="星形备份-3-+-星形备份-4-蒙版" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 284.3298 170.5664)">
                    <path d="M43.6925 54.3802C43.6925 54.3802 97.4046 -16.0843 127.649 4.75736C157.894 25.5991 114.315 69.7573 114.315 69.7573C114.315 69.7573 78.7315 102.298 35.3855 118.008C-7.96046 133.719 1.79082 12.8641 1.79082 12.8641L43.6925 12.8641L43.6925 54.3802Z" transform="translate(0 3.051758E-05)" id="蒙版" fill="#FFE379" stroke="none" />
                    <g clip-path="url(#mask_8)">
                      <g id="星形备份-3">
                        <g clip-path="url(#clip_4)">
                          <use xlink:href="#path_12" fill="none" stroke="#FFFFFF" stroke-opacity="0.3647059" stroke-width="4" />
                        </g>
                      </g>
                      <g id="星形备份-5">
                        <g clip-path="url(#clip_5)">
                          <use xlink:href="#path_13" fill="none" stroke="#FFFFFF" stroke-opacity="0.3647059" stroke-width="4" />
                        </g>
                      </g>
                      <g id="星形备份-6">
                        <g clip-path="url(#clip_6)">
                          <use xlink:href="#path_14" fill="none" stroke="#FFFFFF" stroke-opacity="0.3647059" stroke-width="4" />
                        </g>
                      </g>
                      <g id="星形备份-4">
                        <g clip-path="url(#clip_7)">
                          <use xlink:href="#path_15" fill="none" stroke="#FFFFFF" stroke-opacity="0.3647059" stroke-width="4" />
                        </g>
                      </g>
                    </g>
                  </g>
                  <path d="M0 20.7245L10.412 26.6004L21.9853 4.71071L12.2288 1.71288e-13L0 20.7245Z" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 94.39282 115.4191)" id="路径-278" fill="#FFC4CC" stroke="none" />
                  <path d="M0.00619987 0.00743859L28.0108 -1.47856e-13L28.0046 23.357L-1.55491e-15 23.3644L0.00619987 0.00743859Z" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 111.9333 183.993)" id="矩形" fill="#FFC4CC" stroke="none" />
                  <path d="M0.657416 37.8192L4.82338e-15 18.9143L12.7138 0.00588781L34.8801 -1.13367e-13C34.8801 -1.13367e-13 44.9835 8.1938 43.1688 10.4338C41.3542 12.6738 32.7814 7.27933 32.7814 7.27933" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 273.032 117.494)" id="路径-283" fill="#FFBAC2" stroke="none" />
                  <path d="M0.00811086 16.6781L0.011642 3.37533C0.011642 3.37533 0.052709 1.77535 0.497305 1.06409C0.941901 0.352817 1.65216 0.00110808 1.65216 0.00110808L5.82382 1.547e-13L5.81128 47.233L1.03452e-14 47.2345L0.00811086 16.6781Z" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 293.1165 92.46216)" id="路径-285" fill="#353535" stroke="none" />
                  <path d="M0.0125375 0.000275503L1.04974 3.04149e-13L1.03721 47.233L3.72944e-15 47.2332L0.0125375 0.000275503Z" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 297.6316 90.9071)" id="矩形" fill="#FDCD44" stroke="none" />
                  <path d="M22.3074 0.00072454L0.00204537 20.5819L1.41726e-12 28.2875L27.9333 28.2801L25.0352 1.88433e-13L22.3074 0.00072454Z" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 278.1316 125.818)" id="路径-299" fill="#FFBAC2" stroke="none" />
                  <path d="M11.9172 35.8717C11.9172 35.8717 17.0733 2.11833 14.1429 0.41752C11.2126 -1.28329 7.19477 2.28767 7.94042 9.75232C8.68606 17.217 -1.53953e-12 11.6987 -1.53953e-12 11.6987" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 290.6643 114.1714)" id="路径-284" fill="url(#gradient_4)" stroke="none" />
                  <path d="M4.65245e-15 29.5356L1.87005 0.0104771L41.3138 6.55823e-15L41.9055 29.5245L4.65245e-15 29.5356Z" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 278.6956 151.2253)" id="路径-274" fill="#FFE272" stroke="none" />
                  <path d="M0.0137488 0.018313L68.9578 -1.61072e-13L68.9441 51.7962L-9.56982e-16 51.8145L0.0137488 0.018313Z" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 116.406 160.3571)" id="路径-277" fill="#493028" stroke="none" />
                  <path d="M108.092 246.585C108.092 246.585 132.229 62.3408 122.918 45.5069C113.607 28.6731 23.251 -8.89734e-14 23.251 -8.89734e-14L-7.96003e-13 41.6696L58.8666 72.833C58.8666 72.833 50.4047 141.201 46.2329 166.528C42.0612 191.855 37.3869 210.053 50.3822 225.986C63.3775 241.918 108.092 246.585 108.092 246.585Z" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 94.12526 103.9224)" id="路径-276" fill="#593B31" stroke="none" />
                  <g id="编组" transform="matrix(0.4383711 -0.8987941 -0.8987941 -0.4383711 97.65407 167.476)">
                    <path d="M26.7215 9.09097L35.1432 -5.83709e-13L52.595 16.5364C52.595 16.5364 40.5966 29.7002 40.5966 29.7002C40.5966 29.7002 40.1247 33.9946 40.1206 39.0931C40.1166 44.1915 55.1014 63.7248 57.5106 73.9729C59.3273 81.7005 54.104 88.0736 51.4248 90.741C50.6424 91.5199 49.3868 91.3895 48.6948 90.5292L1.27005 31.5784Q1.20797 31.5012 1.15381 31.4183Q1.09966 31.3354 1.05397 31.2475Q1.00828 31.1596 0.971506 31.0676Q0.934728 30.9757 0.907223 30.8805Q0.879717 30.7854 0.861753 30.6879Q0.843789 30.5905 0.835543 30.4918Q0.827296 30.3931 0.828848 30.2941Q0.8304 30.195 0.841734 30.0966Q0.853069 29.9982 0.874076 29.9015Q0.895083 29.8047 0.925556 29.7104Q0.956029 29.6162 0.995669 29.5254Q1.03531 29.4346 1.08373 29.3482Q1.13215 29.2618 1.18887 29.1806Q1.2456 29.0994 1.31007 29.0242Q1.37455 28.949 1.44614 28.8806Q1.51773 28.8121 1.59574 28.7511L26.7215 9.09097Z" transform="matrix(0.8746197 -0.4848096 0.4848096 0.8746197 0.0002422333 28.10889)" id="路径-15" fill="#3D231A" stroke="none" />
                    <path d="M-7.15736e-16 5.31101e-13L17.1914 20.9813" transform="matrix(0.8746197 -0.4848096 0.4848096 0.8746197 26.62158 27.71558)" id="路径-16" fill="none" stroke="#D1D8E7" stroke-width="2" />
                    <path d="M-4.97842e-16 8.63188e-16L47.8311 62.1993" transform="matrix(0.8746197 -0.4848096 0.4848096 0.8746197 17.85498 48.95483)" id="路径-17" fill="none" stroke="#D1D8E7" stroke-width="2" />
                    <path d="M2.3846 1.62312e-15C2.3846 1.62312e-15 -2.37478 5.57842 2.365 12.8748C7.10479 20.1712 13.9925 18.1115 16.8637 22.289C19.7349 26.4665 14.7054 28.586 19.5487 36.3264C24.3921 44.0669 29.9313 41.7838 32.7065 47.6082C35.4817 53.4327 29.9059 58.4789 29.9059 58.4789" transform="matrix(0.8746197 -0.4848096 0.4848096 0.8746197 31.47607 41.53735)" id="路径-18" fill="none" stroke="#FFFFFF" stroke-opacity="0.3965774" stroke-width="3" />
                    <path d="M7.95085e-13 -9.88909e-16C8.15159e-13 -7.91127e-15 15.7223 2.22683 15.7223 2.22683" transform="matrix(0.8746197 -0.4848096 0.4848096 0.8746197 49.94824 47.90405)" id="路径-19" fill="none" stroke="#A0C4FF" stroke-width="3" />
                    <path d="M-1.87023e-15 1.31753e-15L11.2304 1.48341" transform="matrix(0.8746197 -0.4848096 0.4848096 0.8746197 61.20581 51.04126)" id="路径-20" fill="none" stroke="#A0C4FF" stroke-width="3" />
                    <path d="M-1.13959e-12 6.00793e-13L11.2303 1.48341" transform="matrix(0.8746197 -0.4848096 0.4848096 0.8746197 67.53027 56.20947)" id="路径-21" fill="none" stroke="#A0C4FF" stroke-width="3" />
                  </g>
                  <g id="编组备份-2" transform="matrix(0.03489946 -0.9993908 -0.9993908 -0.03489946 132.7292 235.1656)">
                    <path d="M25.3024 7.90752L34.7281 -1.09218e-12L49.7447 17.9087C49.7447 17.9087 38.4183 27.4193 38.4183 27.4193C38.4183 27.4193 37.9687 31.4874 37.9613 36.3168C37.9538 41.1462 52.1207 59.6379 54.3933 69.3435C56.0905 76.5912 51.2545 82.5855 48.6965 85.1567C47.9178 85.9393 46.6629 85.8091 45.9714 84.9485L1.26749 29.3093Q1.2055 29.2321 1.15143 29.1492Q1.09737 29.0663 1.05176 28.9785Q1.00614 28.8907 0.969431 28.7988Q0.932717 28.7068 0.905263 28.6118Q0.877808 28.5167 0.85988 28.4193Q0.841953 28.322 0.833727 28.2234Q0.825502 28.1247 0.82706 28.0258Q0.828617 27.9268 0.839942 27.8285Q0.851267 27.7302 0.872249 27.6335Q0.893231 27.5367 0.923664 27.4426Q0.954097 27.3484 0.993685 27.2577Q1.03327 27.167 1.08163 27.0806Q1.12998 26.9942 1.18663 26.9131Q1.24327 26.8319 1.30766 26.7568Q1.37205 26.6816 1.44355 26.6132Q1.51504 26.5447 1.59295 26.4837L25.3024 7.90752Z" transform="matrix(0.8746197 -0.4848096 0.4848096 0.8746197 -1.907349E-06 26.58448)" id="路径-15" fill="#3D231A" stroke="none" />
                    <path d="M8.23329e-13 1.65383e-15L16.254 19.8618" transform="matrix(0.8746197 -0.4848096 0.4848096 0.8746197 24.86902 25.60254)" id="路径-16" fill="none" stroke="#D1D8E7" stroke-width="2" />
                    <path d="M1.5689e-16 -7.4607e-13L45.2203 58.8828" transform="matrix(0.8746197 -0.4848096 0.4848096 0.8746197 16.57654 45.729)" id="路径-17" fill="none" stroke="#D1D8E7" stroke-width="2" />
                    <path d="M2.26092 7.0237e-13C2.26092 7.0237e-13 -2.24703 5.2874 2.23324 12.1954C6.71351 19.1033 13.2331 17.1474 15.9472 21.1024C18.6614 25.0575 13.9002 27.0686 18.4782 34.3971C23.0562 41.7257 28.2998 39.5591 30.9219 45.0742C33.544 50.5894 28.2639 55.3732 28.2639 55.3732" transform="matrix(0.8746197 -0.4848096 0.4848096 0.8746197 29.45667 38.7002)" id="路径-18" fill="none" stroke="#FFFFFF" stroke-opacity="0.3965774" stroke-width="3" />
                    <path d="M7.53166e-13 -8.5443e-13C7.5936e-13 -8.78656e-13 14.877 2.09815 14.877 2.09815" transform="matrix(0.8746197 -0.4848096 0.4848096 0.8746197 46.93518 44.7334)" id="路径-19" fill="none" stroke="#A0C4FF" stroke-width="3" />
                    <path d="M1.07597e-12 -1.13545e-12L10.6267 1.39716" transform="matrix(0.8746197 -0.4848096 0.4848096 0.8746197 57.58411 47.70557)" id="路径-20" fill="none" stroke="#A0C4FF" stroke-width="3" />
                    <path d="M5.37398e-13 -1.13669e-12L10.6267 1.39716" transform="matrix(0.8746197 -0.4848096 0.4848096 0.8746197 63.56641 52.604)" id="路径-21" fill="none" stroke="#A0C4FF" stroke-width="3" />
                  </g>
                  <path d="M65.3682 221.595C65.3682 221.595 92.0563 49.0425 85.2574 38.5985C78.4586 28.1545 0 3.07524e-15 0 3.07524e-15" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 112.1882 118.6592)" id="路径-275" fill="none" stroke="#FFFFFF" stroke-width="1" />
                  <path d="M0.208 0.0111895L42.334 6.19188e-15L42.3336 1.70017L9.40004e-15 3.48572L0.208 0.0111895Z" transform="matrix(0.9455186 -0.3255681 0.3255681 0.9455186 287.9397 179.0225)" id="路径-273" fill="#1F3528" stroke="none" />
                </g>
                <path d="M20.451 42.9301L18.7101 43.8454Q18.5764 43.9157 18.4382 43.9765Q18.3 44.0373 18.158 44.0883Q18.0159 44.1394 17.8706 44.1804Q17.7253 44.2215 17.5775 44.2524Q17.4298 44.2833 17.2802 44.3038Q17.1306 44.3243 16.98 44.3344Q16.8293 44.3445 16.6784 44.3441Q16.5274 44.3437 16.3768 44.3328Q16.2262 44.3219 16.0768 44.3006Q15.9273 44.2793 15.7797 44.2477Q15.6321 44.216 15.487 44.1742Q15.3419 44.1324 15.2001 44.0806Q15.0583 44.0288 14.9205 43.9672Q14.7826 43.9057 14.6493 43.8347Q14.5161 43.7638 14.3881 43.6837Q14.26 43.6037 14.1379 43.515Q14.0158 43.4262 13.9001 43.3292Q13.7844 43.2322 13.6757 43.1274Q13.5671 43.0226 13.466 42.9105Q13.3648 42.7984 13.2717 42.6795Q13.1787 42.5606 13.0941 42.4356Q13.0094 42.3106 12.9337 42.18Q12.858 42.0493 12.7916 41.9138Q12.7251 41.7782 12.6682 41.6384Q12.6113 41.4985 12.5643 41.3551Q12.5173 41.2116 12.4803 41.0652Q12.4434 40.9188 12.4167 40.7702Q12.39 40.6216 12.3737 40.4716Q12.3574 40.3215 12.3515 40.1706Q12.3457 40.0197 12.3503 39.8688Q12.355 39.7179 12.3701 39.5677Q12.3852 39.4175 12.4108 39.2687L12.7433 37.3301Q12.8097 36.9424 12.8527 36.5514Q12.8956 36.1604 12.9148 35.7675Q12.934 35.3746 12.9294 34.9813Q12.9249 34.588 12.8965 34.1956Q12.8682 33.8033 12.8161 33.4134Q12.7641 33.0235 12.6886 32.6375Q12.6131 32.2514 12.5144 31.8707Q12.4157 31.4899 12.2941 31.1158Q12.1726 30.7417 12.0286 30.3756Q11.8847 30.0095 11.7188 29.6528Q11.553 29.2961 11.3659 28.9501Q11.1788 28.6041 10.9712 28.27Q10.7635 27.936 10.536 27.6151Q10.3085 27.2942 10.0621 26.9876Q9.81558 26.681 9.55102 26.3899Q9.28646 26.0988 9.00478 25.8243L7.5963 24.4513Q7.48819 24.346 7.38766 24.2333Q7.28713 24.1207 7.19467 24.0013Q7.10221 23.882 7.01826 23.7565Q6.93431 23.631 6.85928 23.5Q6.78426 23.369 6.71851 23.2331Q6.65276 23.0972 6.59662 22.9571Q6.54047 22.8169 6.49419 22.6732Q6.44792 22.5295 6.41173 22.3829Q6.37555 22.2363 6.34964 22.0876Q6.32373 21.9389 6.30821 21.7887Q6.29269 21.6385 6.28764 21.4876Q6.2826 21.3367 6.28804 21.1859Q6.29349 21.035 6.3094 20.8849Q6.32531 20.7347 6.35161 20.5861Q6.37792 20.4374 6.41448 20.2909Q6.45105 20.1444 6.4977 20.0009Q6.54436 19.8573 6.60087 19.7173Q6.65739 19.5773 6.72349 19.4415Q6.78959 19.3058 6.86497 19.175Q6.94034 19.0442 7.02462 18.9189Q7.10889 18.7937 7.20167 18.6746Q7.29444 18.5555 7.39527 18.4431Q7.49609 18.3307 7.60448 18.2256Q7.71287 18.1205 7.82829 18.0232Q7.94372 17.9259 8.06562 17.8368Q8.18753 17.7478 8.31533 17.6674Q8.44313 17.587 8.5762 17.5157Q8.70928 17.4444 8.84698 17.3825Q8.98469 17.3206 9.12636 17.2684Q9.26803 17.2163 9.41298 17.1741Q9.55794 17.1319 9.70547 17.0998Q9.85301 17.0678 10.0024 17.0461L11.9489 16.7632Q12.3382 16.7067 12.7233 16.6267Q13.1084 16.5467 13.488 16.4435Q13.8676 16.3404 14.2403 16.2145Q14.6129 16.0886 14.9773 15.9404Q15.3417 15.7922 15.6964 15.6222Q16.0512 15.4523 16.395 15.2611Q16.7388 15.07 17.0704 14.8585Q17.4021 14.647 17.7203 14.4157Q18.0385 14.1845 18.3422 13.9345Q18.6459 13.6845 18.9338 13.4166Q19.2218 13.1486 19.4931 12.8638Q19.7644 12.5789 20.0179 12.2782Q20.2715 11.9774 20.5064 11.6619Q20.7413 11.3464 20.9567 11.0173Q21.1721 10.6881 21.3671 10.3466Q21.5622 10.005 21.7363 9.65225L22.6068 7.88844Q22.6736 7.75306 22.7497 7.62264Q22.8258 7.49223 22.9107 7.36741Q22.9956 7.2426 23.089 7.12398Q23.1824 7.00537 23.2838 6.89352Q23.3852 6.78168 23.4942 6.67716Q23.6031 6.57263 23.7191 6.47593Q23.835 6.37922 23.9574 6.2908Q24.0797 6.20238 24.208 6.12268Q24.3362 6.04297 24.4696 5.97237Q24.6031 5.90176 24.7411 5.8406Q24.8791 5.77943 25.0211 5.728Q25.163 5.67657 25.3082 5.63513Q25.4534 5.59369 25.6011 5.56243Q25.7488 5.53117 25.8983 5.51024Q26.0478 5.48932 26.1984 5.47883Q26.349 5.46834 26.5 5.46834Q26.651 5.46834 26.8016 5.47883Q26.9522 5.48932 27.1017 5.51024Q27.2512 5.53117 27.3989 5.56243Q27.5466 5.59369 27.6918 5.63513Q27.837 5.67658 27.9789 5.728Q28.1209 5.77943 28.2589 5.8406Q28.3969 5.90176 28.5304 5.97237Q28.6638 6.04297 28.792 6.12268Q28.9203 6.20238 29.0426 6.2908Q29.165 6.37922 29.2809 6.47593Q29.3969 6.57263 29.5058 6.67716Q29.6148 6.78168 29.7162 6.89352Q29.8176 7.00537 29.911 7.12398Q30.0044 7.2426 30.0893 7.36741Q30.1742 7.49223 30.2503 7.62265Q30.3264 7.75306 30.3932 7.88844L31.2637 9.65225Q31.4378 10.005 31.6329 10.3466Q31.8279 10.6881 32.0433 11.0173Q32.2587 11.3464 32.4936 11.6619Q32.7285 11.9774 32.9821 12.2782Q33.2356 12.5789 33.5069 12.8638Q33.7782 13.1486 34.0662 13.4166Q34.3542 13.6845 34.6578 13.9345Q34.9615 14.1845 35.2797 14.4157Q35.598 14.647 35.9296 14.8585Q36.2612 15.07 36.605 15.2611Q36.9488 15.4523 37.3036 15.6222Q37.6583 15.7922 38.0227 15.9404Q38.3871 16.0886 38.7597 16.2145Q39.1324 16.3404 39.512 16.4435Q39.8916 16.5467 40.2767 16.6267Q40.6618 16.7067 41.0511 16.7632L42.9976 17.0461Q43.147 17.0678 43.2945 17.0998Q43.4421 17.1319 43.587 17.1741Q43.732 17.2163 43.8736 17.2684Q44.0153 17.3206 44.153 17.3825Q44.2907 17.4444 44.4238 17.5157Q44.5569 17.587 44.6847 17.6674Q44.8125 17.7478 44.9344 17.8368Q45.0563 17.9259 45.1717 18.0232Q45.2871 18.1205 45.3955 18.2256Q45.5039 18.3307 45.6047 18.4431Q45.7056 18.5555 45.7983 18.6746Q45.8911 18.7937 45.9754 18.9189Q46.0597 19.0442 46.135 19.175Q46.2104 19.3058 46.2765 19.4415Q46.3426 19.5773 46.3991 19.7173Q46.4556 19.8573 46.5023 20.0009Q46.549 20.1444 46.5855 20.2909Q46.6221 20.4374 46.6484 20.5861Q46.6747 20.7347 46.6906 20.8849Q46.7065 21.035 46.712 21.1859Q46.7174 21.3367 46.7124 21.4876Q46.7073 21.6385 46.6918 21.7887Q46.6763 21.9389 46.6504 22.0876Q46.6244 22.2363 46.5883 22.3829Q46.5521 22.5295 46.5058 22.6732Q46.4595 22.8169 46.4034 22.9571Q46.3472 23.0972 46.2815 23.2331Q46.2157 23.369 46.1407 23.5Q46.0657 23.631 45.9817 23.7565Q45.8978 23.882 45.8053 24.0013Q45.7129 24.1207 45.6123 24.2333Q45.5118 24.346 45.4037 24.4513L43.9952 25.8243Q43.7135 26.0988 43.449 26.3899Q43.1844 26.681 42.938 26.9876Q42.6915 27.2942 42.464 27.6151Q42.2365 27.936 42.0288 28.27Q41.8212 28.6041 41.6341 28.9501Q41.447 29.2961 41.2812 29.6528Q41.1153 30.0095 40.9714 30.3756Q40.8274 30.7417 40.7059 31.1158Q40.5843 31.4899 40.4856 31.8707Q40.3869 32.2514 40.3114 32.6375Q40.2359 33.0235 40.1839 33.4134Q40.1318 33.8033 40.1035 34.1956Q40.0751 34.588 40.0706 34.9813Q40.066 35.3746 40.0852 35.7675Q40.1044 36.1604 40.1473 36.5514Q40.1903 36.9424 40.2567 37.3301L40.5892 39.2687Q40.6148 39.4175 40.6299 39.5677Q40.645 39.7179 40.6497 39.8688Q40.6543 40.0197 40.6485 40.1706Q40.6426 40.3215 40.6263 40.4716Q40.61 40.6216 40.5833 40.7702Q40.5566 40.9188 40.5197 41.0652Q40.4827 41.2116 40.4357 41.3551Q40.3887 41.4985 40.3318 41.6384Q40.2749 41.7782 40.2084 41.9138Q40.142 42.0493 40.0663 42.18Q39.9906 42.3106 39.9059 42.4356Q39.8213 42.5606 39.7283 42.6795Q39.6352 42.7984 39.534 42.9105Q39.4329 43.0226 39.3243 43.1274Q39.2156 43.2322 39.0999 43.3292Q38.9842 43.4262 38.8621 43.515Q38.74 43.6037 38.6119 43.6837Q38.4839 43.7638 38.3507 43.8347Q38.2174 43.9057 38.0796 43.9672Q37.9417 44.0288 37.7999 44.0806Q37.6581 44.1324 37.513 44.1742Q37.3679 44.216 37.2203 44.2477Q37.0727 44.2793 36.9232 44.3006Q36.7738 44.3219 36.6232 44.3328Q36.4726 44.3437 36.3216 44.3441Q36.1707 44.3445 36.02 44.3344Q35.8694 44.3243 35.7198 44.3038Q35.5702 44.2833 35.4225 44.2524Q35.2747 44.2215 35.1294 44.1804Q34.9841 44.1394 34.842 44.0883Q34.7 44.0373 34.5618 43.9765Q34.4236 43.9157 34.2899 43.8454L32.549 42.9301Q32.2008 42.7471 31.8422 42.5854Q31.4836 42.4238 31.1159 42.2841Q30.7482 42.1444 30.3727 42.0272Q29.9972 41.91 29.6153 41.8158Q29.2334 41.7215 28.8465 41.6505Q28.4596 41.5795 28.0691 41.532Q27.6786 41.4845 27.286 41.4607Q26.8934 41.437 26.5 41.437Q26.1066 41.437 25.714 41.4607Q25.3214 41.4845 24.9309 41.532Q24.5404 41.5795 24.1535 41.6505Q23.7666 41.7215 23.3847 41.8158Q23.0028 41.91 22.6273 42.0272Q22.2519 42.1444 21.8841 42.2841Q21.5164 42.4238 21.1578 42.5854Q20.7992 42.7471 20.451 42.9301L20.451 42.9301Z" transform="translate(209.2573 818)" id="星形" fill="#FFFFFF" stroke="none" />
                <path d="M66.84 0.363281C24.1629 21.602 -0.000244141 45.8099 -0.000244141 71.5017C-0.000244141 141.59 179.825 200.634 424.783 218.48M764.029 216.166C994.146 195.937 1160 138.812 1160 71.5021C1160 45.6635 1135.56 21.3258 1092.43 0" transform="matrix(0.809017 -0.5877852 0.5877852 0.809017 1.144409E-05 776.5691)" id="形状" fill="none" stroke="#FFFFFF" stroke-width="3" />
                <g id="多边形">
                  <g clip-path="url(#clip_8)">
                    <use xlink:href="#path_16" fill="none" stroke="#FFFFFF" stroke-opacity="0.4745098" stroke-width="6" />
                  </g>
                </g>
                <path d="M0 0C0 0 17.9502 12.8179 27.0348 12.8179C36.1193 12.8179 52 0 52 0C52 0 39.1691 17.2958 39.1691 26.1969C39.1691 35.0979 52 50 52 50C52 50 33.316 39.96 24.6059 39.96C15.8958 39.96 0 50 0 50C0 50 12.0872 34.8092 12.0872 26.1782C12.0872 17.5472 0 0 0 0Z" transform="matrix(0.9396926 0.3420201 -0.3420201 0.9396926 267.376 447.6152)" id="矩形备份-74" fill="#FFFFFF" stroke="none" />
              </g>
              <g id="编组-81备份" transform="matrix(0.8829476 0.4694716 -0.4694716 0.8829476 136.4097 852.8827)">
                <path d="M7.999 -3.49237e-12L23.4409 -3.49237e-12Q23.6302 -3.49237e-12 23.8193 0.00895416Q24.0084 0.0179083 24.1968 0.0357966Q24.3853 0.0536848 24.5727 0.0804672Q24.7601 0.107249 24.946 0.142866Q25.1319 0.178482 25.3159 0.222853Q25.4999 0.267224 25.6816 0.32025Q25.8634 0.373276 26.0424 0.434838Q26.2214 0.4964 26.3973 0.56636Q26.5732 0.636321 26.7455 0.714523Q26.9179 0.792726 27.0864 0.878995Q27.2549 0.965264 27.4191 1.05941Q27.5834 1.15355 27.7429 1.25536Q27.9025 1.35716 28.0571 1.4664Q28.2117 1.57564 28.361 1.69207Q28.5102 1.8085 28.6538 1.93187Q28.7974 2.05523 28.9349 2.18524Q29.0725 2.31525 29.2038 2.45163Q29.3351 2.58801 29.4598 2.73044Q29.5844 2.87288 29.7022 3.02105Q29.82 3.16922 29.9307 3.3228Q30.0414 3.47638 30.1446 3.63502Q30.2479 3.79366 30.3436 3.95701Q30.4392 4.12036 30.527 4.28805Q30.6149 4.45574 30.6946 4.62739Q30.7744 4.79905 30.846 4.97429Q30.9176 5.14953 30.9808 5.32796Q31.044 5.50639 31.0987 5.6876Q31.1534 5.86882 31.1995 6.05243Q31.2455 6.23603 31.2829 6.42161Q31.3202 6.60718 31.3487 6.79432Q31.3772 6.98145 31.3968 7.16973Q31.4165 7.358 31.4272 7.54699L32.6298 28.7957Q32.6402 28.9804 32.6421 29.1654Q32.6441 29.3503 32.6374 29.5352Q32.6308 29.72 32.6156 29.9044Q32.6004 30.0887 32.5767 30.2722Q32.553 30.4556 32.5209 30.6378Q32.4887 30.8199 32.4482 31.0004Q32.4077 31.1809 32.3588 31.3593Q32.31 31.5377 32.253 31.7137Q32.196 31.8896 32.1309 32.0627Q32.0658 32.2359 31.9928 32.4058Q31.9197 32.5758 31.8389 32.7422Q31.7581 32.9085 31.6697 33.071Q31.5813 33.2335 31.4855 33.3917Q31.3896 33.5499 31.2866 33.7035Q31.1836 33.8572 31.0735 34.0058Q30.9635 34.1545 30.8467 34.298Q30.7299 34.4414 30.6066 34.5793Q30.4834 34.7172 30.3538 34.8492Q30.2243 34.9813 30.0888 35.1072Q29.9533 35.2331 29.8121 35.3526Q29.671 35.4722 29.5244 35.585Q29.3779 35.6979 29.2263 35.8039Q29.0747 35.9098 28.9183 36.0087Q28.762 36.1075 28.6012 36.1991Q28.4405 36.2906 28.2757 36.3746Q28.1109 36.4585 27.9424 36.5348Q27.7739 36.6111 27.602 36.6795Q27.4302 36.7479 27.2553 36.8083Q27.0805 36.8687 26.9031 36.921Q26.7256 36.9732 26.546 37.0172Q26.3663 37.0612 26.1848 37.0968Q26.0033 37.1325 25.8203 37.1597L16.5201 38.5429Q16.1375 38.5998 15.7524 38.6361Q15.3673 38.6724 14.9809 38.688Q14.5944 38.7035 14.2076 38.6983Q13.8209 38.6931 13.435 38.6672Q13.0491 38.6412 12.6651 38.5946Q12.2812 38.548 11.9003 38.4808Q11.5194 38.4136 11.1426 38.3261Q10.7659 38.2386 10.3944 38.1309Q10.0228 38.0233 9.65765 37.8959Q9.29245 37.7685 8.93462 37.6217Q8.57679 37.4749 8.22736 37.3091Q7.87792 37.1432 7.53789 36.9589Q7.19786 36.7746 6.86822 36.5722Q6.53857 36.3699 6.22026 36.1502Q5.90195 35.9305 5.59589 35.694Q5.28983 35.4575 4.99691 35.2049Q4.70399 34.9523 4.42504 34.6844Q4.1461 34.4165 3.88194 34.1339Q3.61778 33.8514 3.36917 33.5551Q3.12055 33.2588 2.8882 32.9496Q2.65585 32.6404 2.44043 32.3192Q2.22501 31.9979 2.02714 31.6656Q1.82928 31.3333 1.64953 30.9908Q1.46979 30.6483 1.30869 30.2967Q1.14759 29.945 1.00559 29.5853Q0.863599 29.2255 0.74112 28.8586Q0.618641 28.4917 0.516031 28.1188Q0.413421 27.7459 0.330976 27.368Q0.24853 26.9901 0.186487 26.6083Q0.124445 26.2266 0.082983 25.842Q0.0415214 25.4575 0.0207607 25.0712Q-2.75325e-12 24.685 -2.75325e-12 24.2982L-2.75325e-12 7.999Q-2.75325e-12 7.80264 0.00963514 7.60651Q0.0192703 7.41038 0.0385174 7.21496Q0.0577644 7.01954 0.0865771 6.8253Q0.11539 6.63106 0.153699 6.43847Q0.192007 6.24588 0.23972 6.0554Q0.287433 5.86492 0.344434 5.67701Q0.401436 5.4891 0.467589 5.30422Q0.533742 5.11933 0.608888 4.93791Q0.684033 4.7565 0.76799 4.57899Q0.851946 4.40148 0.944512 4.2283Q1.03708 4.05512 1.13803 3.88669Q1.23898 3.71826 1.34807 3.55499Q1.45717 3.39172 1.57414 3.234Q1.69112 3.07628 1.81569 2.92449Q1.94026 2.7727 2.07213 2.6272Q2.204 2.4817 2.34285 2.34285Q2.4817 2.204 2.6272 2.07213Q2.7727 1.94026 2.92449 1.81569Q3.07628 1.69112 3.234 1.57414Q3.39172 1.45717 3.55499 1.34807Q3.71826 1.23898 3.88669 1.13803Q4.05512 1.03708 4.2283 0.944512Q4.40148 0.851946 4.57899 0.76799Q4.7565 0.684033 4.93791 0.608888Q5.11933 0.533742 5.30422 0.467589Q5.4891 0.401436 5.67701 0.344434Q5.86492 0.287433 6.0554 0.23972Q6.24588 0.192007 6.43847 0.153699Q6.63106 0.11539 6.8253 0.0865771Q7.01954 0.0577644 7.21496 0.0385174Q7.41038 0.0192703 7.60651 0.00963514Q7.80264 -3.49237e-12 7.999 -3.49237e-12L7.999 -3.49237e-12Z" transform="matrix(0.9510565 -0.309017 0.309017 0.9510565 38.66461 114.1087)" id="矩形" fill="#16A8E2" stroke="none" />
                <path d="M7.999 -3.49237e-12L23.4409 -3.49237e-12Q23.6302 -3.49237e-12 23.8193 0.00895416Q24.0084 0.0179083 24.1968 0.0357966Q24.3853 0.0536848 24.5727 0.0804672Q24.7601 0.107249 24.946 0.142866Q25.1319 0.178482 25.3159 0.222853Q25.4999 0.267224 25.6816 0.32025Q25.8634 0.373276 26.0424 0.434838Q26.2214 0.4964 26.3973 0.56636Q26.5732 0.636321 26.7455 0.714523Q26.9179 0.792726 27.0864 0.878995Q27.2549 0.965264 27.4191 1.05941Q27.5834 1.15355 27.7429 1.25536Q27.9025 1.35716 28.0571 1.4664Q28.2117 1.57564 28.361 1.69207Q28.5102 1.8085 28.6538 1.93187Q28.7974 2.05523 28.9349 2.18524Q29.0725 2.31525 29.2038 2.45163Q29.3351 2.58801 29.4598 2.73044Q29.5844 2.87288 29.7022 3.02105Q29.82 3.16922 29.9307 3.3228Q30.0414 3.47638 30.1446 3.63502Q30.2479 3.79366 30.3436 3.95701Q30.4392 4.12036 30.527 4.28805Q30.6149 4.45574 30.6946 4.62739Q30.7744 4.79905 30.846 4.97429Q30.9176 5.14953 30.9808 5.32796Q31.044 5.50639 31.0987 5.6876Q31.1534 5.86882 31.1995 6.05243Q31.2455 6.23603 31.2829 6.42161Q31.3202 6.60718 31.3487 6.79432Q31.3772 6.98145 31.3968 7.16973Q31.4165 7.358 31.4272 7.54699L32.6298 28.7957Q32.6402 28.9804 32.6421 29.1654Q32.6441 29.3503 32.6374 29.5352Q32.6308 29.72 32.6156 29.9044Q32.6004 30.0887 32.5767 30.2722Q32.553 30.4556 32.5209 30.6378Q32.4887 30.8199 32.4482 31.0004Q32.4077 31.1809 32.3588 31.3593Q32.31 31.5377 32.253 31.7137Q32.196 31.8896 32.1309 32.0627Q32.0658 32.2359 31.9928 32.4058Q31.9197 32.5758 31.8389 32.7422Q31.7581 32.9085 31.6697 33.071Q31.5813 33.2335 31.4855 33.3917Q31.3896 33.5499 31.2866 33.7035Q31.1836 33.8572 31.0735 34.0058Q30.9635 34.1545 30.8467 34.298Q30.7299 34.4414 30.6066 34.5793Q30.4834 34.7172 30.3538 34.8492Q30.2243 34.9813 30.0888 35.1072Q29.9533 35.2331 29.8121 35.3526Q29.671 35.4722 29.5244 35.585Q29.3779 35.6979 29.2263 35.8039Q29.0747 35.9098 28.9183 36.0087Q28.762 36.1075 28.6012 36.1991Q28.4405 36.2906 28.2757 36.3746Q28.1109 36.4585 27.9424 36.5348Q27.7739 36.6111 27.602 36.6795Q27.4302 36.7479 27.2553 36.8083Q27.0805 36.8687 26.9031 36.921Q26.7256 36.9732 26.546 37.0172Q26.3663 37.0612 26.1848 37.0968Q26.0033 37.1325 25.8203 37.1597L16.5201 38.5429Q16.1375 38.5998 15.7524 38.6361Q15.3673 38.6724 14.9809 38.688Q14.5944 38.7035 14.2076 38.6983Q13.8209 38.6931 13.435 38.6672Q13.0491 38.6412 12.6651 38.5946Q12.2812 38.548 11.9003 38.4808Q11.5194 38.4136 11.1426 38.3261Q10.7659 38.2386 10.3944 38.1309Q10.0228 38.0233 9.65765 37.8959Q9.29245 37.7685 8.93462 37.6217Q8.57679 37.4749 8.22736 37.3091Q7.87792 37.1432 7.53789 36.9589Q7.19786 36.7746 6.86822 36.5722Q6.53857 36.3699 6.22026 36.1502Q5.90195 35.9305 5.59589 35.694Q5.28983 35.4575 4.99691 35.2049Q4.70399 34.9523 4.42504 34.6844Q4.1461 34.4165 3.88194 34.1339Q3.61778 33.8514 3.36917 33.5551Q3.12055 33.2588 2.8882 32.9496Q2.65585 32.6404 2.44043 32.3192Q2.22501 31.9979 2.02714 31.6656Q1.82928 31.3333 1.64953 30.9908Q1.46979 30.6483 1.30869 30.2967Q1.14759 29.945 1.00559 29.5853Q0.863599 29.2255 0.74112 28.8586Q0.618641 28.4917 0.516031 28.1188Q0.413421 27.7459 0.330976 27.368Q0.24853 26.9901 0.186487 26.6083Q0.124445 26.2266 0.082983 25.842Q0.0415214 25.4575 0.0207607 25.0712Q-2.75325e-12 24.685 -2.75325e-12 24.2982L-2.75325e-12 7.999Q-2.75325e-12 7.80264 0.00963514 7.60651Q0.0192703 7.41038 0.0385174 7.21496Q0.0577644 7.01954 0.0865771 6.8253Q0.11539 6.63106 0.153699 6.43847Q0.192007 6.24588 0.23972 6.0554Q0.287433 5.86492 0.344434 5.67701Q0.401436 5.4891 0.467589 5.30422Q0.533742 5.11933 0.608888 4.93791Q0.684033 4.7565 0.76799 4.57899Q0.851946 4.40148 0.944512 4.2283Q1.03708 4.05512 1.13803 3.88669Q1.23898 3.71826 1.34807 3.55499Q1.45717 3.39172 1.57414 3.234Q1.69112 3.07628 1.81569 2.92449Q1.94026 2.7727 2.07213 2.6272Q2.204 2.4817 2.34285 2.34285Q2.4817 2.204 2.6272 2.07213Q2.7727 1.94026 2.92449 1.81569Q3.07628 1.69112 3.234 1.57414Q3.39172 1.45717 3.55499 1.34807Q3.71826 1.23898 3.88669 1.13803Q4.05512 1.03708 4.2283 0.944512Q4.40148 0.851946 4.57899 0.76799Q4.7565 0.684033 4.93791 0.608888Q5.11933 0.533742 5.30422 0.467589Q5.4891 0.401436 5.67701 0.344434Q5.86492 0.287433 6.0554 0.23972Q6.24588 0.192007 6.43847 0.153699Q6.63106 0.11539 6.8253 0.0865771Q7.01954 0.0577644 7.21496 0.0385174Q7.41038 0.0192703 7.60651 0.00963514Q7.80264 -3.49237e-12 7.999 -3.49237e-12L7.999 -3.49237e-12Z" transform="matrix(-0.9510565 -0.309017 -0.309017 0.9510565 126.7595 114.1087)" id="矩形备份-72" fill="#16A8E2" stroke="none" />
                <g id="椭圆形" transform="translate(29.19086 29)">
                  <path d="M53.5 107C83.0472 107 107 83.0472 107 53.5C107 23.9528 83.0472 0 53.5 0C23.9528 0 0 23.9528 0 53.5C0 83.0472 23.9528 107 53.5 107Z" id="蒙版" fill="#32C5FF" stroke="none" />
                  <g clip-path="url(#mask_9)">
                    <path d="M17 34C26.3888 34 34 26.3888 34 17C34 7.61116 26.3888 0 17 0C7.61116 0 0 7.61116 0 17C0 26.3888 7.61116 34 17 34Z" transform="translate(-11 51)" id="椭圆形" fill="#FFFFFF" fill-opacity="0.29703775" stroke="none" />
                    <path d="M17 34C26.3888 34 34 26.3888 34 17C34 7.61116 26.3888 0 17 0C7.61116 0 0 7.61116 0 17C0 26.3888 7.61116 34 17 34Z" transform="translate(87 59)" id="椭圆形备份-33" fill="#FFFFFF" fill-opacity="0.29703775" stroke="none" />
                  </g>
                </g>
                <path d="M11.5 23C17.8513 23 23 17.8513 23 11.5C23 5.14873 17.8513 0 11.5 0C5.14873 0 0 5.14873 0 11.5C0 17.8513 5.14873 23 11.5 23Z" transform="translate(55.19086 62)" id="椭圆形" fill="#FFFFFF" stroke="none" />
                <path d="M12.5 25C19.4036 25 25 19.4036 25 12.5C25 5.59644 19.4036 0 12.5 0C5.59644 0 0 5.59644 0 12.5C0 19.4036 5.59644 25 12.5 25Z" transform="translate(90.19086 60)" id="椭圆形备份-30" fill="#FFFFFF" stroke="none" />
                <path d="M20.6905 22C20.6905 22 10.0768 18.2459 1.44128 9.90902C-7.19422 1.57211 35.9104 15.9545 35.9104 15.9545L58 -1.25056e-12L53.2364 17.3957C53.2364 17.3957 48.8273 17.597 46.4692 17.0848C43.1708 16.3683 36.3704 17.9222 34.2258 18.2894C25.7746 19.7364 20.6905 22 20.6905 22Z" transform="matrix(0.9135454 0.4067366 -0.4067366 0.9135454 70.17214 0.1556396)" id="路径-436" fill="url(#gradient_5)" stroke="none" />
                <g id="椭圆形">
                  <g clip-path="url(#clip_9)">
                    <use xlink:href="#path_18" fill="none" stroke="#FFFFFF" stroke-width="2" />
                  </g>
                </g>
                <g id="椭圆形备份-31">
                  <g clip-path="url(#clip_10)">
                    <use xlink:href="#path_19" fill="none" stroke="#FFFFFF" stroke-width="2" />
                  </g>
                </g>
                <path d="M5.5 11C8.53757 11 11 8.53757 11 5.5C11 2.46243 8.53757 0 5.5 0C2.46243 0 0 2.46243 0 5.5C0 8.53757 2.46243 11 5.5 11Z" transform="translate(65.19086 71)" id="椭圆形" fill="#FA6400" stroke="none" />
                <path d="M5.5 11C8.53757 11 11 8.53757 11 5.5C11 2.46243 8.53757 0 5.5 0C2.46243 0 0 2.46243 0 5.5C0 8.53757 2.46243 11 5.5 11Z" transform="translate(99.19086 71)" id="椭圆形备份-32" fill="#FA6400" stroke="none" />
                <path d="M29.1909 0L0 67.1737" transform="translate(0 83)" id="路径-437" fill="none" stroke="#EAC852" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M1.61174e-12 0L5.44508 21.8941Q5.56441 22.3739 5.71854 22.8437Q5.87268 23.3135 6.06079 23.7708Q6.2489 24.228 6.46998 24.6703Q6.69105 25.1126 6.94391 25.5375Q7.19677 25.9623 7.48005 26.3676Q7.76333 26.7728 8.07552 27.1563Q8.3877 27.5397 8.72712 27.8992Q9.06653 28.2587 9.43135 28.5925Q9.79617 28.9262 10.1844 29.2323Q10.5727 29.5385 10.9824 29.8154Q11.392 30.0922 11.8208 30.3384Q12.2496 30.5845 12.6953 30.7987Q13.141 31.0128 13.6011 31.1937Q14.0613 31.3746 14.5334 31.5213Q15.0056 31.6681 15.4872 31.7799Q15.9689 31.8916 16.4574 31.9679L16.4586 31.9681Q16.97 32.0479 17.4852 32.0973Q18.0005 32.1467 18.5177 32.1656Q19.035 32.1846 19.5525 32.1729Q20.07 32.1612 20.5858 32.1189Q21.1017 32.0766 21.6142 32.004Q22.1267 31.9313 22.634 31.8285Q23.1413 31.7257 23.6416 31.593Q24.1419 31.4604 24.6336 31.2985Q25.1252 31.1365 25.6064 30.9458Q26.0876 30.755 26.5566 30.5362Q27.0257 30.3174 27.481 30.0712Q27.9364 29.825 28.3763 29.5524Q28.8163 29.2797 29.2394 28.9815Q29.6625 28.6834 30.0672 28.3607Q30.472 28.038 30.857 27.692Q31.2419 27.346 31.6058 26.9779L50.6183 7.74001" transform="translate(137.3817 86)" id="路径-437备份" fill="none" stroke="#FF9B9B" stroke-width="5" stroke-linecap="round" stroke-linejoin="round" />
                <path d="M0 0L13 0L13 2.501Q13 2.62377 12.988 2.74594Q12.9759 2.86812 12.952 2.98853Q12.928 3.10894 12.8924 3.22642Q12.8568 3.3439 12.8098 3.45733Q12.7628 3.57075 12.7049 3.67902Q12.647 3.78729 12.5788 3.88937Q12.5106 3.99145 12.4328 4.08635Q12.3549 4.18125 12.2681 4.26806Q12.1812 4.35487 12.0863 4.43275Q11.9914 4.51064 11.8894 4.57884Q11.7873 4.64705 11.679 4.70492Q11.5707 4.76279 11.4573 4.80977Q11.3439 4.85676 11.2264 4.89239Q11.1089 4.92803 10.9885 4.95198Q10.8681 4.97593 10.7459 4.98797Q10.6238 5 10.501 5L2.499 5Q2.37623 5 2.25406 4.98797Q2.13188 4.97593 2.01147 4.95198Q1.89106 4.92803 1.77358 4.89239Q1.6561 4.85676 1.54267 4.80977Q1.42925 4.76279 1.32098 4.70492Q1.21271 4.64705 1.11063 4.57884Q1.00855 4.51064 0.913651 4.43275Q0.81875 4.35487 0.73194 4.26806Q0.64513 4.18125 0.567247 4.08635Q0.489364 3.99145 0.421157 3.88937Q0.352951 3.78729 0.295079 3.67902Q0.237206 3.57075 0.190225 3.45733Q0.143244 3.3439 0.107606 3.22642Q0.0719684 3.10894 0.0480176 2.98853Q0.0240667 2.86812 0.0120334 2.74594Q0 2.62377 0 2.501L0 0Z" transform="translate(79.19086 96)" id="矩形" fill="#FFFFFF" stroke="none" />
              </g>
            </g>
          </svg>

        <svg v-else-if="i==13" version="1.1" id="&#x56FE;&#x5C42;_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px"
          y="0px" viewBox="0 0 1000 1000" style="enable-background:new 0 0 1000 1000;" xml:space="preserve">
        <g>
          <defs>
            <path id="XMLID_14_" d="M306.585,279.471c0,0-14.233,29.06-24.316,34.991c-10.082,5.931-13.64,21.35-13.047,49.224
              c0.593,27.874-40.328,58.12-87.773,87.18c0,0-14.827,7.117,0,24.909c14.827,17.792,28.17,48.631,1.779,106.158
              s1.779,67.016,30.839,74.726c29.06,7.71,96.669-4.151,106.751,78.284c0,0,26.984,67.485,14.975,107.431h188.149
              c0,0-52.486-57.613-21.647-140.642c0,0,8.896-23.722,68.795,9.489c59.899,33.211,78.877-8.303,94.297-14.233
              s23.129,16.606,47.445-12.454c24.315-29.06,36.77-100.227,24.315-128.101c0,0-3.558-14.234,11.861,8.896
              c15.42,23.129,42.256,23.871,54.043,16.087s27.577-15.123,30.691-25.576c3.114-10.453-6.005-23.574,19.126-28.689
              c25.131-5.115,59.825-63.828,36.696-104.75c0,0-12.01-22.462,1.112-37.808c13.121-15.345,15.79-54.043-66.275-90.071
              s-68.054,10.008-90.071,9.341c-22.017-0.667-21.795-8.08-35.139-24.39c-13.344-16.309-35.584-28.319-50.262-36.77
              c-14.678-8.451-26.688-44.48-65.385-47.593c-38.697-3.114-52.486-25.798-77.839-34.249
              C480.352,152.408,329.418,151.221,306.585,279.471z"/>
          </defs>
          <linearGradient id="XMLID_2_" gradientUnits="userSpaceOnUse" x1="535.1869" y1="877.1912" x2="535.1869" y2="178.0314">
            <stop  offset="0" style="stop-color:#251017"/>
            <stop  offset="0.0045" style="stop-color:#2A1219"/>
            <stop  offset="0.08" style="stop-color:#7F3A41"/>
            <stop  offset="0.1333" style="stop-color:#B45359"/>
            <stop  offset="0.1592" style="stop-color:#C85D63"/>
            <stop  offset="0.3547" style="stop-color:#E6625E"/>
            <stop  offset="0.3821" style="stop-color:#E87160"/>
            <stop  offset="0.4434" style="stop-color:#ED8D65"/>
            <stop  offset="0.497" style="stop-color:#F09F68"/>
            <stop  offset="0.5363" style="stop-color:#F1A569"/>
            <stop  offset="0.6508" style="stop-color:#E55F5C"/>
            <stop  offset="0.7626" style="stop-color:#BC4E5A"/>
            <stop  offset="0.8464" style="stop-color:#5D233C"/>
            <stop  offset="1" style="stop-color:#321420"/>
          </linearGradient>
          <use xlink:href="#XMLID_14_"  style="overflow:visible;fill:url(#XMLID_2_);"/>
          <clipPath id="XMLID_3_">
            <use xlink:href="#XMLID_14_"  style="overflow:visible;"/>
          </clipPath>
          <path style="opacity:0.21;clip-path:url(#XMLID_3_);fill:#FFF5F5;" d="M346.063,551.076
            c27.595,57.291,86.207,96.828,154.054,96.828c67.847,0,126.459-39.537,154.054-96.828H346.063z"/>
          
            <linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="781.5439" y1="100.3345" x2="776.971" y2="218.7169" gradientTransform="matrix(1 0 0 -1 0 851.1053)">
            <stop  offset="0" style="stop-color:#FFFAE6;stop-opacity:0.7"/>
            <stop  offset="0.3209" style="stop-color:#FFFCEF;stop-opacity:0.4696"/>
            <stop  offset="0.9749" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_1_);" d="M899.058,799.807c-29.394,0-53.222-23.828-53.222-53.222
            c0-2.198,0.149-4.361,0.408-6.489c-3.499,0.864-7.155,1.331-10.921,1.331c-9.881,0-19.017-3.167-26.47-8.528
            c-9.336,4.9-19.956,7.687-31.232,7.687c-24.842,0-46.526-13.467-58.19-33.489c-4.783,3.524-10.69,5.61-17.086,5.61
            c-12.556,0-23.233-8.027-27.192-19.228c-3.395,0.613-6.887,0.951-10.459,0.951c-32.297,0-58.478-26.182-58.478-58.479
            c0-32.297,26.182-58.478,58.478-58.478c31.57,0,57.281,25.021,58.424,56.311c12.234-16.857,32.082-27.829,54.503-27.829
            c29.378,0,54.345,18.827,63.534,45.068c22.314,2.864,39.562,21.916,39.562,45.009c0,0.195-0.012,0.387-0.015,0.581
            c5.722-2.102,11.904-3.251,18.355-3.251c29.394,0,53.222,23.828,53.222,53.222C952.28,775.978,928.451,799.807,899.058,799.807z"/>
          
            <linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="243.9041" y1="93.5675" x2="249.7565" y2="213.5365" gradientTransform="matrix(1 0 0 -1 0 851.1053)">
            <stop  offset="0.4017" style="stop-color:#FFFAE6;stop-opacity:0.7"/>
            <stop  offset="0.5986" style="stop-color:#FFFCEF;stop-opacity:0.4696"/>
            <stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_2_);" d="M295.793,704.122c-4.003,0-7.925-0.37-11.738-1.073
            c-8.017,35.491-38.148,61.877-74.1,61.877c-42.082,0-76.195-36.148-76.195-80.739c0-44.591,34.114-80.739,76.195-80.739
            c7.704,0,15.14,1.216,22.15,3.47c9.638-27.351,34.515-46.829,63.688-46.829c37.536,0,67.964,32.243,67.964,72.017
            C363.757,671.879,333.328,704.122,295.793,704.122z"/>
          <linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="749.5723" y1="376.6403" x2="801.1652" y2="483.2867">
            <stop  offset="0" style="stop-color:#FFFAE6;stop-opacity:0.7"/>
            <stop  offset="0.3209" style="stop-color:#FFFCEF;stop-opacity:0.4696"/>
            <stop  offset="0.9749" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_3_);" d="M899.058,328.839c-29.394,0-53.222,23.828-53.222,53.222
            c0,2.198,0.149,4.361,0.408,6.489c-3.499-0.864-7.155-1.331-10.921-1.331c-9.881,0-19.017,3.167-26.47,8.528
            c-9.336-4.9-19.956-7.687-31.232-7.687c-24.842,0-46.526,13.467-58.19,33.489c-4.783-3.524-10.69-5.61-17.086-5.61
            c-12.556,0-23.233,8.027-27.192,19.228c-3.395-0.613-6.887-0.951-10.459-0.951c-32.297,0-58.478,26.182-58.478,58.479
            s26.182,58.479,58.478,58.479c31.57,0,57.281-25.021,58.424-56.311c12.234,16.857,32.082,27.829,54.503,27.829
            c29.378,0,54.345-18.827,63.534-45.068c22.314-2.864,39.562-21.916,39.562-45.009c0-0.195-0.012-0.387-0.015-0.581
            c5.722,2.102,11.904,3.251,18.355,3.251c29.394,0,53.222-23.828,53.222-53.222C952.28,352.668,928.451,328.839,899.058,328.839z"/>
          
            <linearGradient id="SVGID_4_" gradientUnits="userSpaceOnUse" x1="323.977" y1="97.165" x2="323.2867" y2="254.081" gradientTransform="matrix(0.9033 0.4291 -0.4291 0.9033 138.4575 43.7274)">
            <stop  offset="0" style="stop-color:#FFFAE6;stop-opacity:0.7"/>
            <stop  offset="0.3209" style="stop-color:#FFFCEF;stop-opacity:0.4696"/>
            <stop  offset="0.9749" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_4_);" d="M433.999,408.354c-9.865-4.686-20.785-4.49-30.049-0.4
            c-3.287-15.137-13.293-28.642-28.366-35.803c-8.343-3.963-17.211-5.485-25.811-4.876c-2.859-11.291-10.542-21.27-21.884-26.658
            c-3.412-1.621-6.937-2.711-10.486-3.325c-2.702-17.334-13.646-33.075-30.687-41.17c-27.379-13.006-60.117-1.354-73.122,26.025
            c-13.006,27.379-1.354,60.116,26.025,73.122c11.928,5.666,24.873,6.649,36.76,3.693c3.949,6.205,9.627,11.415,16.762,14.804
            c2.78,1.321,5.636,2.284,8.518,2.933c-0.846,20.126,10.17,39.758,29.513,48.946c18.998,9.024,40.722,5.447,55.766-7.353
            c3.519,6.413,9.007,11.822,16.119,15.2c17.988,8.545,39.497,0.889,48.042-17.099C459.642,438.407,451.987,416.898,433.999,408.354z
            "/>
          <linearGradient id="SVGID_5_" gradientUnits="userSpaceOnUse" x1="671.0007" y1="270.2607" x2="763.4581" y2="550.2796">
            <stop  offset="0" style="stop-color:#FFFAE6;stop-opacity:0.7"/>
            <stop  offset="0.1586" style="stop-color:#FFFCEF;stop-opacity:0.4696"/>
            <stop  offset="0.4818" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_5_);" d="M864.002,267.522c-34.406,0-63.758,21.574-75.308,51.926
            c-7.308-4.06-15.716-6.38-24.669-6.38c-14.555,0-27.673,6.121-36.947,15.919c-11.702-7.454-25.583-11.79-40.485-11.79
            c-21.187,0-40.323,8.741-54.031,22.799c-16.748-12.992-37.774-20.73-60.611-20.73c-54.664,0-98.977,44.314-98.977,98.977
            c0,54.664,44.314,98.977,98.977,98.977c38.416,0,71.719-21.886,88.124-53.869c8.252,3.098,17.185,4.804,26.519,4.804
            c34.07,0,62.857-22.578,72.242-53.585c1.706,0.173,3.438,0.262,5.19,0.262c14.705,0,27.946-6.246,37.235-16.22
            c14.769,18.318,37.38,30.047,62.742,30.047c44.496,0,80.568-36.071,80.568-80.568C944.57,303.594,908.499,267.522,864.002,267.522z
            "/>
          <linearGradient id="SVGID_6_" gradientUnits="userSpaceOnUse" x1="331.5464" y1="366.8328" x2="255.8754" y2="460.1105">
            <stop  offset="0.4017" style="stop-color:#FFFAE6;stop-opacity:0.7"/>
            <stop  offset="0.5986" style="stop-color:#FFFCEF;stop-opacity:0.4696"/>
            <stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_6_);" d="M295.793,424.524c-4.003,0-7.925,0.37-11.738,1.073
            c-8.017-35.491-38.148-61.877-74.1-61.877c-42.082,0-76.195,36.148-76.195,80.739c0,44.591,34.114,80.739,76.195,80.739
            c7.704,0,15.14-1.216,22.15-3.47c9.638,27.351,34.515,46.829,63.688,46.829c37.536,0,67.964-32.243,67.964-72.017
            S333.328,424.524,295.793,424.524z"/>
          
            <radialGradient id="SVGID_7_" cx="500.0425" cy="479.0114" r="200.6271" fx="501.7923" fy="481.4102" gradientUnits="userSpaceOnUse">
            <stop  offset="0.6532" style="stop-color:#FFFFFF;stop-opacity:0.7"/>
            <stop  offset="0.948" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </radialGradient>
          <circle style="clip-path:url(#XMLID_3_);fill:url(#SVGID_7_);" cx="498.871" cy="477.473" r="187.451"/>
          <path style="clip-path:url(#XMLID_3_);fill:#FFF5F5;" d="M654.172,551.076c10.793-22.408,16.842-47.531,16.842-74.068
            c0-94.383-76.513-170.896-170.896-170.896s-170.896,76.513-170.896,170.896c0,26.536,6.049,51.66,16.842,74.068H654.172z"/>
          <path style="clip-path:url(#XMLID_3_);fill:#E29B71;" d="M173.353,527.109c0,0,71.255-6.478,106.883-3.239
            c0,0,5.182,0.864,24.615-4.966s20.081,2.159,30.661,4.103c10.58,1.943,57.436-4.103,70.175-4.966
            c12.74-0.864,36.059,10.796,48.799,12.74c12.74,1.943,91.336,11.228,110.769,4.318c19.433-6.91,27.638-2.159,34.548-2.591
            c6.91-0.432,14.683-11.228,32.389-10.364c17.706,0.864,30.229,10.364,50.958,10.364l6.046,24.266l-529.447-7.158L173.353,527.109z"
            />
          <path style="clip-path:url(#XMLID_3_);fill:#F5B460;" d="M179.831,532.291c0,0,53.981,3.455,74.71,3.239
            c0,0,37.571,0.442,47.935-1.615c10.364-2.056,21.592-1.976,45.344,1.654c23.752,3.631,80.108-0.688,82.699-3.279
            c2.591-2.591,24.184-10.659,63.482,2.336c39.298,12.995,122.213,6.218,128.691,2.697s37.571-6.976,56.14-7.408
            c0,0,4.318-10.472,38.003-9.555c33.684,0.918,43.185,1.349,53.117-4.265c9.932-5.614,25.911-1.296,49.231,8.421
            c23.32,9.717,25.263-0.648,25.263-0.648l8.421,38.036l-693.117-5.134L179.831,532.291z"/>
          <path style="clip-path:url(#XMLID_3_);fill:#AD446C;" d="M181.774,537.473c0,0,276.599-0.534,525.344,6.858
            c0,0,4.534-8.171,31.093-6.867l106.235-0.141l-2.375,24.583l-677.139-2.409L181.774,537.473z"/>
          <path style="clip-path:url(#XMLID_3_);fill:#502038;" d="M170.114,548.553c0,0,85.506-3.819,237.085,0
            c0,0,25.911,1.207,39.514-1.697c13.603-2.905,40.162-2.92,62.834,1.946c22.672,4.866,79.028-1.627,88.097,0
            c9.069,1.627,91.984,1.412,118.542,1.844c26.559,0.431,17.49-12.091,83.563,0c0,0,41.457-12.026,49.879-2.883v21.451H164.932
            L170.114,548.553z"/>
          <path style="clip-path:url(#XMLID_3_);fill:#050000;" d="M176.16,551.076c0,0,32.821-8.637,48.367,6.046s14.683,6.046,18.138,0
            c0,0,151.147-12.575,193.468-0.242c42.321,12.333,158.92,4.077,177.058,0s75.142,2.349,106.235,0
            c31.093-2.349,60.459-12.334,81.188,0c0,0,26.775-8.879,41.457,0v18.379H170.114L176.16,551.076z"/>
          
            <linearGradient id="SVGID_8_" gradientUnits="userSpaceOnUse" x1="665.3583" y1="122.6966" x2="664.0892" y2="5.6642" gradientTransform="matrix(0.6222 0.7828 -0.7828 0.6222 393.9006 -221.668)">
            <stop  offset="0.6532" style="stop-color:#E5A25E"/>
            <stop  offset="0.7253" style="stop-color:#EBB985;stop-opacity:0.7919"/>
            <stop  offset="0.856" style="stop-color:#F6DEC6;stop-opacity:0.4153"/>
            <stop  offset="0.9511" style="stop-color:#FCF6EF;stop-opacity:0.141"/>
            <stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_8_);" d="M708.303,378.21L708.303,378.21
            c-0.914-1.15-0.721-2.838,0.429-3.752l124.244-96.022c1.15-0.914,2.257,0.48,1.108,1.394l-122.029,98.809
            C710.905,379.553,709.217,379.36,708.303,378.21z"/>
          
            <linearGradient id="SVGID_9_" gradientUnits="userSpaceOnUse" x1="406.2467" y1="466.5096" x2="405.3817" y2="386.7447" gradientTransform="matrix(0.6222 0.7828 -0.7828 0.6222 393.9006 -221.668)">
            <stop  offset="0.6532" style="stop-color:#E5A25E"/>
            <stop  offset="0.7253" style="stop-color:#EBB985;stop-opacity:0.7919"/>
            <stop  offset="0.856" style="stop-color:#F6DEC6;stop-opacity:0.4153"/>
            <stop  offset="0.9511" style="stop-color:#FCF6EF;stop-opacity:0.141"/>
            <stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_9_);" d="M279.055,388.444L279.055,388.444
            c-0.623-0.784-0.491-1.934,0.292-2.557l84.68-65.445c0.784-0.623,1.539,0.327,0.755,0.95l-83.17,67.345
            C280.828,389.36,279.678,389.228,279.055,388.444z"/>
          <linearGradient id="SVGID_10_" gradientUnits="userSpaceOnUse" x1="499.1334" y1="162.974" x2="585.5845" y2="162.974">
            <stop  offset="0.6532" style="stop-color:#FFFAE6;stop-opacity:0.7"/>
            <stop  offset="0.7673" style="stop-color:#FFFCEF;stop-opacity:0.4696"/>
            <stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_10_);" d="M499.527,196.734L499.527,196.734
            c-0.623-0.784-0.491-1.934,0.292-2.557l84.68-65.445c0.784-0.623,1.539,0.327,0.755,0.95l-83.17,67.345
            C501.3,197.649,500.149,197.518,499.527,196.734z"/>
          <g style="clip-path:url(#XMLID_3_);">
            <circle style="fill:#FFFFFF;" cx="261.576" cy="500" r="1.561"/>
            <circle style="fill:#FFFFFF;" cx="764.294" cy="385.083" r="1.78"/>
            <circle style="fill:#FFFFFF;" cx="835.669" cy="397.188" r="0.663"/>
            <circle style="fill:#FFFFFF;" cx="840.179" cy="497.114" r="2.205"/>
            <circle style="opacity:0.58;fill:#FFFFFF;" cx="824.582" cy="338.086" r="2.433"/>
            <circle style="opacity:0.52;fill:#FFFFFF;" cx="721.333" cy="481.212" r="2.077"/>
            <circle style="fill:#FFFFFF;" cx="786.605" cy="450.593" r="0.482"/>
            <circle style="fill:#FFFFFF;" cx="668.64" cy="376.538" r="1.899"/>
            <circle style="fill:#FFFFFF;" cx="709.465" cy="333.102" r="2.136"/>
            <circle style="fill:#FFFFFF;" cx="553.523" cy="256.911" r="0.475"/>
            <circle style="fill:#FFFFFF;" cx="621.644" cy="301.059" r="1.543"/>
            <circle style="fill:#FFFFFF;" cx="576.309" cy="222.732" r="0.949"/>
            <circle style="fill:#FFFFFF;" cx="521.48" cy="280.172" r="1.661"/>
            <circle style="fill:#FFFFFF;" cx="241.638" cy="457.476" r="1.78"/>
            <circle style="opacity:0.63;fill:#FFFFFF;" cx="287.922" cy="443.709" r="1.899"/>
            <circle style="fill:#FFFFFF;" cx="325.424" cy="361.584" r="1.721"/>
            <circle style="opacity:0.21;fill:#FFFFFF;" cx="426.063" cy="226.055" r="2.463"/>
            <circle style="fill:#FFFFFF;" cx="369.097" cy="243.144" r="1.661"/>
            <circle style="fill:#FFFFFF;" cx="639.683" cy="251.926" r="1.78"/>
            <circle style="fill:#FFFFFF;" cx="446.001" cy="274" r="0.712"/>
            <circle style="fill:#FFFFFF;" cx="431.759" cy="184.043" r="1.187"/>
            <circle style="opacity:0.62;fill:#FFFFFF;" cx="494.184" cy="245.518" r="2.136"/>
            <circle style="fill:#FFFFFF;" cx="393.07" cy="284.681" r="1.187"/>
            <circle style="fill:#FFFFFF;" cx="324.712" cy="316.012" r="0.831"/>
          </g>
          <g style="opacity:0.4;clip-path:url(#XMLID_3_);">
            <circle style="fill:#FFFFFF;" cx="764.294" cy="635.123" r="1.78"/>
            <circle style="fill:#FFFFFF;" cx="835.669" cy="623.018" r="0.663"/>
            <circle style="opacity:0.58;fill:#FFFFFF;" cx="824.582" cy="682.12" r="2.433"/>
            <circle style="fill:#FFFFFF;" cx="786.605" cy="569.613" r="0.482"/>
            <circle style="fill:#FFFFFF;" cx="668.64" cy="643.668" r="1.899"/>
            <circle style="fill:#FFFFFF;" cx="709.465" cy="687.104" r="2.136"/>
            <circle style="fill:#FFFFFF;" cx="553.523" cy="763.295" r="0.475"/>
            <circle style="fill:#FFFFFF;" cx="621.644" cy="719.147" r="1.543"/>
            <circle style="fill:#FFFFFF;" cx="576.309" cy="797.474" r="0.949"/>
            <circle style="fill:#FFFFFF;" cx="521.48" cy="740.034" r="1.661"/>
            <circle style="fill:#FFFFFF;" cx="325.424" cy="658.621" r="1.721"/>
            <circle style="opacity:0.21;fill:#FFFFFF;" cx="426.063" cy="794.151" r="2.463"/>
            <circle style="fill:#FFFFFF;" cx="369.097" cy="777.062" r="1.661"/>
            <circle style="fill:#FFFFFF;" cx="639.683" cy="768.28" r="1.78"/>
            <circle style="fill:#FFFFFF;" cx="446.001" cy="746.206" r="0.712"/>
            <circle style="fill:#FFFFFF;" cx="431.759" cy="836.163" r="1.187"/>
            <circle style="opacity:0.62;fill:#FFFFFF;" cx="494.184" cy="774.688" r="2.136"/>
            <circle style="fill:#FFFFFF;" cx="393.07" cy="735.525" r="1.187"/>
            <circle style="fill:#FFFFFF;" cx="324.712" cy="704.194" r="0.831"/>
          </g>
          <circle style="clip-path:url(#XMLID_3_);fill:#EFF0E2;" cx="387.997" cy="443.739" r="31.717"/>
          <circle style="clip-path:url(#XMLID_3_);fill:#EFF0E2;" cx="441.016" cy="382.114" r="16.378"/>
          <circle style="clip-path:url(#XMLID_3_);fill:#EFF0E2;" cx="434.923" cy="502.338" r="14.241"/>
        </g>
        </svg>


      <svg v-else-if="i===14" version="1.1" id="&#x56FE;&#x5C42;_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px"
        y="0px" viewBox="0 0 1000 1000" style="enable-background:new 0 0 1000 1000;" xml:space="preserve">
      <g>
        <defs>
          <path id="XMLID_74_" d="M288.249,628.77c0,0-3.798-13.767-21.362-15.665c-17.564-1.899-22.311-9.019-10.444-28.483
            c11.868-19.463,25.634-55.188,5.222-69.368c-20.413-14.181-70.732-55.481-61.238-100.103
            c9.494-44.623,45.572-67.884,65.035-99.689s28.008-34.179,40.35-37.027c12.342-2.848,18.751-8.307,25.16-18.988
            c6.409-10.681,29.907-34.179,74.055-44.86s65.51-46.996,127.46-36.315c61.95,10.681,108.234,14.241,125.324,65.51
            c0,0,21.362,27.771,7.833,73.343c0,0,55.541,17.756,61.238,85.425c0,0,47.708,68.381-10.681,126.058
            c0,0,14.953-27.771,2.136-37.027c0,0-11.393,44.148-46.284,55.541c0,0-17.802,17.09-35.603,12.817c0,0-9.257,17.802-23.498,29.195
            C598.71,600.525,640.01,681.7,778.863,816.281H498.309c0,0-13.529-146.686-95.417-118.915c0,0-59.814,42.724-74.055,9.257
            c0,0-6.409-27.059-15.665-34.535S302.795,642.926,288.249,628.77z"/>
        </defs>
        <linearGradient id="XMLID_2_" gradientUnits="userSpaceOnUse" x1="489.0552" y1="816.2806" x2="489.0552" y2="176.3252">
          <stop  offset="0.296" style="stop-color:#FCF6EC"/>
          <stop  offset="0.4898" style="stop-color:#D2CAD2"/>
          <stop  offset="0.9257" style="stop-color:#685D91"/>
          <stop  offset="1" style="stop-color:#554A86"/>
        </linearGradient>
        <use xlink:href="#XMLID_74_"  style="overflow:visible;fill:url(#XMLID_2_);"/>
        <clipPath id="XMLID_3_">
          <use xlink:href="#XMLID_74_"  style="overflow:visible;"/>
        </clipPath>
        <g style="clip-path:url(#XMLID_3_);">
          <g>
            <radialGradient id="SVGID_1_" cx="581.5397" cy="492.7833" r="180.7954" gradientUnits="userSpaceOnUse">
              <stop  offset="0" style="stop-color:#FFFFFF;stop-opacity:0.4"/>
              <stop  offset="0.8587" style="stop-color:#FFFFFF;stop-opacity:0.0995"/>
              <stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0.05"/>
            </radialGradient>
            <circle style="fill:url(#SVGID_1_);" cx="581.54" cy="492.783" r="180.795"/>
            <circle style="fill:#FFFFFF;" cx="581.54" cy="492.783" r="141.07"/>
            <circle style="fill:#EFF0E2;" cx="612.951" cy="420.372" r="14.241"/>
            <circle style="fill:#EFF0E2;" cx="672.053" cy="450.991" r="22.786"/>
            <circle style="fill:#EFF0E2;" cx="631.109" cy="514.461" r="18.158"/>
          </g>
          <path style="fill:#997CA2;" d="M143.852,520.969c0,0,95.679,16.223,135.044,8.62c39.365-7.603,49.753-9.663,65.609,2.028
            c15.855,11.692,17.496-3.204,22.963-5.324c5.467-2.12,7.108,14.282,19.683,12.642c12.575-1.64,30.752-19.136,42.986-13.668
            c12.234,5.467,30.824,18.042,42.852,16.949c0,0,10.935-9.295,19.683-6.561c8.748,2.734,31.711,16.949,47.02,18.589l2.734,27.884
            H137.291L143.852,520.969z"/>
          <linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="495.4043" y1="615.478" x2="495.4043" y2="769.9928">
            <stop  offset="0" style="stop-color:#DEC1CF"/>
            <stop  offset="0.755" style="stop-color:#E0C6A4"/>
            <stop  offset="0.7678" style="stop-color:#E0C6A3"/>
          </linearGradient>
          <polygon style="fill:url(#SVGID_2_);" points="130.183,615.478 860.626,615.478 845.317,749.878 311.386,769.993 		"/>
          <linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="475.9548" y1="632.4269" x2="475.9548" y2="462.3401">
            <stop  offset="0" style="stop-color:#3B2D62"/>
            <stop  offset="0.6107" style="stop-color:#664866"/>
            <stop  offset="1" style="stop-color:#845A69"/>
          </linearGradient>
          <path style="fill:url(#SVGID_3_);" d="M130.183,525.89c0,0,81.191-0.897,105.794,20.426
            c24.603,21.323,41.005,13.122,51.667,10.661c10.661-2.46,40.185-6.561,44.286-0.82c4.101,5.741,20.503,9.841,31.984,8.201
            c11.482-1.64,0-4.101,12.302-10.661c0,0,0,4.921,13.942,7.381s32.392,8.93,39.979,3.645s1.846-16.767,13.328-2.825
            c11.482,13.942,21.17,3.28,29.038,0c7.867-3.28,18.529-8.201,35.751-1.64c17.222,6.561,55.767-24.603,70.529-30.344
            c0,0,7.928,12.848,16.675,13.395c0,0,2.187-19.802,11.208-17.419c0,0,6.834,11.131,16.129,7.577
            c9.294-3.554,68.342-17.075,92.945-39.018c24.603-21.943,16.402-26.864,36.085-13.742c0,0,10.661-23.783,27.064-17.222
            c16.402,6.561,31.711,16.402,37.998,19.683c6.288,3.28,4.647,149.26,4.647,149.26h-683.97L130.183,525.89z"/>
          
            <linearGradient id="SVGID_4_" gradientUnits="userSpaceOnUse" x1="475.9548" y1="3515.9495" x2="475.9548" y2="3345.8625" gradientTransform="matrix(1 0 0 -1 0 4147.9932)">
            <stop  offset="0" style="stop-color:#3B2D62;stop-opacity:0.4"/>
            <stop  offset="0.3036" style="stop-color:#664866;stop-opacity:0.1863"/>
            <stop  offset="0.4971" style="stop-color:#845A69;stop-opacity:0.05"/>
          </linearGradient>
          <path style="fill:url(#SVGID_4_);" d="M130.183,738.581c0,0,81.191,0.897,105.794-20.426
            c24.603-21.323,41.005-13.122,51.667-10.661c10.661,2.46,40.185,6.561,44.286,0.82c4.101-5.741,20.503-9.841,31.984-8.201
            c11.482,1.64,0,4.101,12.302,10.661c0,0,0-4.921,13.942-7.381s32.392-8.93,39.979-3.645s1.846,16.767,13.328,2.825
            c11.482-13.942,21.17-3.28,29.038,0c7.867,3.28,18.529,8.201,35.751,1.64c17.222-6.561,55.767,24.603,70.529,30.344
            c0,0,7.928-12.848,16.675-13.395c0,0,2.187,19.802,11.208,17.419c0,0,6.834-11.131,16.129-7.577
            c9.294,3.554,68.342,17.075,92.945,39.018c24.603,21.943,16.402,26.864,36.085,13.742c0,0,10.661,23.783,27.064,17.222
            s31.711-16.402,37.998-19.683c6.288-3.28,4.647-149.26,4.647-149.26h-683.97L130.183,738.581z"/>
          <linearGradient id="SVGID_5_" gradientUnits="userSpaceOnUse" x1="372.5381" y1="567.9651" x2="372.5381" y2="820.0607">
            <stop  offset="0.296" style="stop-color:#3B2D62"/>
            <stop  offset="1" style="stop-color:#634B71"/>
          </linearGradient>
          <polygon style="fill:url(#SVGID_5_);" points="99.865,567.965 645.211,820.061 113.781,820.061 		"/>
          <polygon style="fill:#301F53;" points="816.887,684.367 43.386,815.389 808.139,815.389 		"/>
        </g>
        <linearGradient id="SVGID_6_" gradientUnits="userSpaceOnUse" x1="551.7582" y1="648.769" x2="776.0143" y2="648.769">
          <stop  offset="0.296" style="stop-color:#301F53"/>
          <stop  offset="0.8985" style="stop-color:#5B446C"/>
          <stop  offset="1" style="stop-color:#634B71"/>
        </linearGradient>
        <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_6_);" d="M567.432,727.309c0.681-2.726,20.444-62.046,20.444-62.046
          l9.541,53.001l5.452-93.857l16.741,88.592l9.836-164.236l14.992,139.021l28.622-161.51l102.953,244.99H551.758L567.432,727.309z"/>
        <linearGradient id="SVGID_7_" gradientUnits="userSpaceOnUse" x1="312.2937" y1="732.1221" x2="312.2937" y2="505.6954">
          <stop  offset="0.296" style="stop-color:#3B2D62"/>
          <stop  offset="1" style="stop-color:#634B71"/>
        </linearGradient>
        <polygon style="clip-path:url(#XMLID_3_);fill:url(#SVGID_7_);" points="254.066,630.352 299.992,505.695 324.595,599.188 
          332.796,525.378 370.521,732.122 254.066,725.484 	"/>
        
          <linearGradient id="SVGID_8_" gradientUnits="userSpaceOnUse" x1="557.0518" y1="3640.3105" x2="557.0518" y2="3600.4473" gradientTransform="matrix(1 0 0 -1 0 4272.8711)">
          <stop  offset="0" style="stop-color:#3F2F62"/>
          <stop  offset="0.6439" style="stop-color:#6A4A66"/>
          <stop  offset="1" style="stop-color:#845A69"/>
        </linearGradient>
        <polygon style="opacity:0.31;clip-path:url(#XMLID_3_);fill:url(#SVGID_8_);" points="483.379,636.114 499.781,661.762 
          514.543,632.56 532.585,661.762 543.93,632.56 549.808,638.799 557.744,632.56 567.85,665.863 581.177,632.56 589.993,652.741 
          598.194,632.56 613.776,672.424 631.818,632.56 482.285,632.56 	"/>
        <linearGradient id="SVGID_9_" gradientUnits="userSpaceOnUse" x1="557.0518" y1="632.4269" x2="557.0518" y2="579.6667">
          <stop  offset="0" style="stop-color:#3F2F62"/>
          <stop  offset="0.6439" style="stop-color:#6A4A66"/>
          <stop  offset="1" style="stop-color:#845A69"/>
        </linearGradient>
        <polygon style="clip-path:url(#XMLID_3_);fill:url(#SVGID_9_);" points="483.379,628.873 499.781,590.328 514.543,632.427 
          532.585,590.328 543.93,632.427 549.808,613.291 557.744,632.427 567.85,586.228 581.177,632.427 589.993,599.349 598.194,632.427 
          613.776,579.667 631.818,632.427 482.285,632.427 	"/>
        
          <linearGradient id="SVGID_10_" gradientUnits="userSpaceOnUse" x1="-744.4966" y1="497.3826" x2="-744.4966" y2="398.6962" gradientTransform="matrix(0.7071 0.7071 -0.7071 0.7071 1247.0066 440.6955)">
          <stop  offset="0" style="stop-color:#AD99FF"/>
          <stop  offset="0.1826" style="stop-color:#B09DFF;stop-opacity:0.7836"/>
          <stop  offset="0.3465" style="stop-color:#B9A7FF;stop-opacity:0.5896"/>
          <stop  offset="0.5031" style="stop-color:#C7BAFF;stop-opacity:0.4041"/>
          <stop  offset="0.6551" style="stop-color:#DCD3FF;stop-opacity:0.224"/>
          <stop  offset="0.8027" style="stop-color:#F6F4FF;stop-opacity:0.0492"/>
          <stop  offset="0.8442" style="stop-color:#FFFFFF;stop-opacity:0"/>
        </linearGradient>
        <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_10_);" d="M368.886,265.98L368.886,265.98
          c-1.292-1.292-0.6-2.715,0.692-4.007l67.218-65.708c1.292-1.292,3.055,0.47,1.762,1.762l-65.708,67.218
          C371.559,266.538,370.179,267.273,368.886,265.98z"/>
        
          <linearGradient id="SVGID_11_" gradientUnits="userSpaceOnUse" x1="-631.6718" y1="392.0847" x2="-631.6718" y2="328.2376" gradientTransform="matrix(0.7071 0.7071 -0.7071 0.7071 1247.0066 440.6955)">
          <stop  offset="0" style="stop-color:#AD99FF"/>
          <stop  offset="0.1826" style="stop-color:#B09DFF;stop-opacity:0.7836"/>
          <stop  offset="0.3465" style="stop-color:#B9A7FF;stop-opacity:0.5896"/>
          <stop  offset="0.5031" style="stop-color:#C7BAFF;stop-opacity:0.4041"/>
          <stop  offset="0.6551" style="stop-color:#DCD3FF;stop-opacity:0.224"/>
          <stop  offset="0.8027" style="stop-color:#F6F4FF;stop-opacity:0.0492"/>
          <stop  offset="0.8442" style="stop-color:#FFFFFF;stop-opacity:0"/>
        </linearGradient>
        <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_11_);" d="M523.115,271.295L523.115,271.295
          c-0.836-0.836-0.388-1.756,0.448-2.592l43.488-42.511c0.836-0.836,1.976,0.304,1.14,1.14L525.68,270.82
          C524.844,271.656,523.951,272.131,523.115,271.295z"/>
        
          <linearGradient id="SVGID_12_" gradientUnits="userSpaceOnUse" x1="-675.8812" y1="709.3518" x2="-675.8812" y2="634.6215" gradientTransform="matrix(0.7071 0.7071 -0.7071 0.7071 1247.0066 440.6955)">
          <stop  offset="0" style="stop-color:#AD99FF"/>
          <stop  offset="0.1826" style="stop-color:#B09DFF;stop-opacity:0.7836"/>
          <stop  offset="0.3465" style="stop-color:#B9A7FF;stop-opacity:0.5896"/>
          <stop  offset="0.5031" style="stop-color:#C7BAFF;stop-opacity:0.4041"/>
          <stop  offset="0.6551" style="stop-color:#DCD3FF;stop-opacity:0.224"/>
          <stop  offset="0.8027" style="stop-color:#F6F4FF;stop-opacity:0.0492"/>
          <stop  offset="0.8442" style="stop-color:#FFFFFF;stop-opacity:0"/>
        </linearGradient>
        <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_12_);" d="M267.515,464.379L267.515,464.379
          c-0.979-0.979-0.454-2.056,0.524-3.034l50.901-49.757c0.979-0.979,2.313,0.356,1.335,1.334l-49.757,50.901
          C269.539,464.801,268.493,465.357,267.515,464.379z"/>
        <g style="clip-path:url(#XMLID_3_);">
          <circle style="opacity:0.43;fill:#FFFFFF;" cx="352.78" cy="302.97" r="3.115"/>
          <circle style="fill:#FFFFFF;" cx="372.807" cy="239.151" r="0.86"/>
          <circle style="fill:#FFFFFF;" cx="402.447" cy="504.581" r="1.157"/>
          <circle style="opacity:0.63;fill:#FFFFFF;" cx="223.899" cy="431.943" r="1.332"/>
          <circle style="fill:#FFFFFF;" cx="331.418" cy="369.726" r="1.216"/>
          <circle style="fill:#FFFFFF;" cx="394.169" cy="345.922" r="1.029"/>
          <circle style="fill:#FFFFFF;" cx="661.728" cy="325.667" r="2.077"/>
          <circle style="opacity:0.53;fill:#FFFFFF;" cx="614.731" cy="239.151" r="0.871"/>
          <circle style="fill:#FFFFFF;" cx="402.447" cy="256.508" r="0.89"/>
          <circle style="fill:#FFFFFF;" cx="476.313" cy="239.151" r="0.871"/>
          <circle style="fill:#FFFFFF;" cx="279.081" cy="369.726" r="1.098"/>
          <circle style="fill:#FFFFFF;" cx="550.645" cy="325.667" r="1.424"/>
          <circle style="fill:#FFFFFF;" cx="434.757" cy="317.389" r="1.765"/>
          <circle style="opacity:0.57;fill:#FFFFFF;" cx="519.671" cy="264.786" r="1.78"/>
          <circle style="fill:#FFFFFF;" cx="372.807" cy="386.282" r="0.621"/>
          <circle style="opacity:0.56;fill:#FFFFFF;" cx="364.529" cy="456.776" r="2.492"/>
          <circle style="fill:#FFFFFF;" cx="295.637" cy="448.499" r="1.869"/>
          <circle style="fill:#FFFFFF;" cx="240.455" cy="386.282" r="0.621"/>
        </g>
      </g>
      </svg>
      <svg v-else-if="i===15" version="1.1" id="&#x56FE;&#x5C42;_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px"
         y="0px" viewBox="0 0 1000 1000" style="enable-background:new 0 0 1000 1000;" xml:space="preserve">
      <g>
        <defs>
          <path id="XMLID_18_" d="M195.906,518.153c0,0-40.465-56.406,0-142.241c0,0-55.18-63.763-23.298-114.038
            s148.279-151.707,361.733-52.727c0,0,95.645,55.18,148.372,100.549s67.442-20.846,74.799-33.108c0,0,79.704,20.846,23.298,84.609
            c0,0-40.465,30.655-96.871,0c0,0,30.564,80.93,117.058,55.18c0,0,20.278,36.786-72.914,58.858c0,0,89.473,55.18,74.779,94.418
            c0,0-61.29-18.393-74.779,0c0,0,38.013,12.262,53.953,45.37c0,0-14.715,34.334-28.203,67.442c0,0-17.167-53.953-60.084-56.406
            c0,0-26.977-2.452-24.524,7.357c0,0,49.048,33.108,44.144,53.953c0,0-45.37,41.691-145.919,11.036
            c0,0-6.131,141.014,42.917,212.135H388.421c0,0,64.989-212.135-35.56-180.253s-82.156,34.334-104.228,17.167
            c-22.072-17.167-33.108-92.318-47.822-132.431c0,0-53.953,3.679-53.953-9.81S195.906,518.153,195.906,518.153z"/>
        </defs>
        <linearGradient id="XMLID_2_" gradientUnits="userSpaceOnUse" x1="475.8706" y1="616.2743" x2="473.0099" y2="129.649">
          <stop  offset="0" style="stop-color:#FFCDD6"/>
          <stop  offset="0.3975" style="stop-color:#6D8DAF"/>
          <stop  offset="1" style="stop-color:#253A59"/>
        </linearGradient>
        <use xlink:href="#XMLID_18_"  style="overflow:visible;fill:url(#XMLID_2_);"/>
        <clipPath id="XMLID_3_">
          <use xlink:href="#XMLID_18_"  style="overflow:visible;"/>
        </clipPath>
        <g style="clip-path:url(#XMLID_3_);">
          <defs>
            <polygon id="XMLID_54_" points="310.309,600.344 441.685,600.344 500.431,546.939 582.674,640.932 533.542,732.789 
              322.763,719.897 			"/>
          </defs>
          <linearGradient id="XMLID_4_" gradientUnits="userSpaceOnUse" x1="449.8146" y1="583.4114" x2="435.4871" y2="743.9322">
            <stop  offset="0.3464" style="stop-color:#E7A9B6"/>
            <stop  offset="0.3968" style="stop-color:#DFA7B5"/>
            <stop  offset="0.5606" style="stop-color:#CBA2B3"/>
            <stop  offset="0.7244" style="stop-color:#BF9FB2"/>
            <stop  offset="0.887" style="stop-color:#BB9EB2"/>
          </linearGradient>
          <use xlink:href="#XMLID_54_"  style="overflow:visible;fill:url(#XMLID_4_);"/>
          <clipPath id="XMLID_5_">
            <use xlink:href="#XMLID_54_"  style="overflow:visible;"/>
          </clipPath>
          <linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="503.043" y1="536.8331" x2="496.1307" y2="614.2761">
            <stop  offset="0" style="stop-color:#E7A9B6"/>
            <stop  offset="0.2045" style="stop-color:#F0C2CC"/>
            <stop  offset="0.4326" style="stop-color:#F7D6DE"/>
            <stop  offset="0.6606" style="stop-color:#FCE2E8"/>
            <stop  offset="0.887" style="stop-color:#FDE6EC"/>
          </linearGradient>
          <polygon style="clip-path:url(#XMLID_5_);fill:url(#SVGID_1_);" points="446.492,563.154 468.388,594.128 481.205,591.992 
            489.75,603.741 501.499,608.014 513.248,586.993 552.768,586.993 492.954,516.157 		"/>
        </g>
        <g style="clip-path:url(#XMLID_3_);">
          <defs>
            <polygon id="XMLID_55_" points="673.943,579.971 731.62,506.886 830.251,657.035 			"/>
          </defs>
          <linearGradient id="XMLID_6_" gradientUnits="userSpaceOnUse" x1="757.1794" y1="559.6225" x2="737.6806" y2="785.517">
            <stop  offset="0" style="stop-color:#E7A9B6"/>
            <stop  offset="0.0827" style="stop-color:#DFA7B5"/>
            <stop  offset="0.3515" style="stop-color:#CBA2B3"/>
            <stop  offset="0.6202" style="stop-color:#BF9FB2"/>
            <stop  offset="0.887" style="stop-color:#BB9EB2"/>
          </linearGradient>
          <use xlink:href="#XMLID_55_"  style="overflow:visible;fill:url(#XMLID_6_);"/>
          <clipPath id="XMLID_7_">
            <use xlink:href="#XMLID_55_"  style="overflow:visible;"/>
          </clipPath>
          <linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="735.5176" y1="499.1807" x2="725.1649" y2="619.1182">
            <stop  offset="0" style="stop-color:#E7A9B6"/>
            <stop  offset="0.2045" style="stop-color:#F0C2CC"/>
            <stop  offset="0.4326" style="stop-color:#F7D6DE"/>
            <stop  offset="0.6606" style="stop-color:#FCE2E8"/>
            <stop  offset="0.887" style="stop-color:#FDE6EC"/>
          </linearGradient>
          <polygon style="clip-path:url(#XMLID_7_);fill:url(#SVGID_2_);" points="695.305,531.645 728.416,546.939 743.369,546.939 
            771.14,519.896 729.484,471.831 		"/>
        </g>
        <g style="clip-path:url(#XMLID_3_);">
          <defs>
            <polygon id="XMLID_28_" points="390.534,727.659 446.006,664.535 528.259,727.659 575.011,892.165 361.841,892.165 			"/>
          </defs>
          <linearGradient id="XMLID_8_" gradientUnits="userSpaceOnUse" x1="467.8975" y1="802.2943" x2="467.0201" y2="653.0363">
            <stop  offset="0" style="stop-color:#FFCDD6"/>
            <stop  offset="0.6486" style="stop-color:#3B4A56"/>
          </linearGradient>
          <use xlink:href="#XMLID_28_"  style="overflow:visible;fill:url(#XMLID_8_);"/>
          <clipPath id="XMLID_9_">
            <use xlink:href="#XMLID_28_"  style="overflow:visible;"/>
          </clipPath>
          <linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="459.32" y1="718.8841" x2="465.2173" y2="616.6317">
            <stop  offset="0" style="stop-color:#E6FAFF"/>
            <stop  offset="0.7014" style="stop-color:#F3C6D3"/>
          </linearGradient>
          <polygon style="clip-path:url(#XMLID_9_);fill:url(#SVGID_3_);" points="375.241,622.144 454.37,726.887 554.681,620.008 		"/>
        </g>
        <g style="clip-path:url(#XMLID_3_);">
          <defs>
            <polygon id="XMLID_40_" points="107.431,714.269 258.547,505.554 539.736,892.165 197.59,850.082 			"/>
          </defs>
          <linearGradient id="XMLID_10_" gradientUnits="userSpaceOnUse" x1="323.7108" y1="824.8422" x2="322.2191" y2="571.1006">
            <stop  offset="0" style="stop-color:#FFCDD6"/>
            <stop  offset="0.0834" style="stop-color:#CCABB5"/>
            <stop  offset="0.1686" style="stop-color:#A08E98"/>
            <stop  offset="0.2548" style="stop-color:#7C7580"/>
            <stop  offset="0.3407" style="stop-color:#5F626E"/>
            <stop  offset="0.4264" style="stop-color:#4B5561"/>
            <stop  offset="0.5118" style="stop-color:#3F4D59"/>
            <stop  offset="0.5965" style="stop-color:#3B4A56"/>
          </linearGradient>
          <use xlink:href="#XMLID_40_"  style="overflow:visible;fill:url(#XMLID_10_);"/>
          <clipPath id="XMLID_11_">
            <use xlink:href="#XMLID_40_"  style="overflow:visible;"/>
          </clipPath>
          <linearGradient id="SVGID_4_" gradientUnits="userSpaceOnUse" x1="240.634" y1="636.3158" x2="246.0242" y2="455.6764">
            <stop  offset="0" style="stop-color:#E6FAFF"/>
            <stop  offset="0.7014" style="stop-color:#F3C6D3"/>
          </linearGradient>
          <polygon style="clip-path:url(#XMLID_11_);fill:url(#SVGID_4_);" points="92.55,602.918 230.691,575.375 259.174,631.401 
            279.112,594.373 329.698,662.732 334.267,634.249 389.571,641.113 273.415,462.878 		"/>
        </g>
        <g style="clip-path:url(#XMLID_3_);">
          <defs>
            <polygon id="XMLID_41_" points="495.74,735.311 662.159,505.554 920.394,769.742 667.897,949.55 346.538,949.55 			"/>
          </defs>
          <linearGradient id="XMLID_12_" gradientUnits="userSpaceOnUse" x1="633.8513" y1="925.2321" x2="631.6611" y2="552.6646">
            <stop  offset="0" style="stop-color:#FFCDD6"/>
            <stop  offset="0.0834" style="stop-color:#CCABB5"/>
            <stop  offset="0.1686" style="stop-color:#A08E98"/>
            <stop  offset="0.2548" style="stop-color:#7C7580"/>
            <stop  offset="0.3407" style="stop-color:#5F626E"/>
            <stop  offset="0.4264" style="stop-color:#4B5561"/>
            <stop  offset="0.5118" style="stop-color:#3F4D59"/>
            <stop  offset="0.5965" style="stop-color:#3B4A56"/>
          </linearGradient>
          <use xlink:href="#XMLID_41_"  style="overflow:visible;fill:url(#XMLID_12_);"/>
          <clipPath id="XMLID_13_">
            <use xlink:href="#XMLID_41_"  style="overflow:visible;"/>
          </clipPath>
          <linearGradient id="SVGID_5_" gradientUnits="userSpaceOnUse" x1="660.5321" y1="612.9142" x2="659.8223" y2="492.1754">
            <stop  offset="0" style="stop-color:#E6FAFF"/>
            <stop  offset="0.7014" style="stop-color:#F3C6D3"/>
          </linearGradient>
          <polygon style="clip-path:url(#XMLID_13_);fill:url(#SVGID_5_);" points="533.319,598.646 625.176,594.373 637.993,647.778 
            659.355,590.101 676.445,585.828 699.943,609.327 702.079,575.375 787.527,590.101 663.627,462.878 		"/>
        </g>
        
          <linearGradient id="SVGID_6_" gradientUnits="userSpaceOnUse" x1="1742.5769" y1="441.008" x2="1794.1698" y2="547.6545" gradientTransform="matrix(-1 0 0 1 2037.8346 0)">
          <stop  offset="0" style="stop-color:#FFFAE6;stop-opacity:0.7"/>
          <stop  offset="0.3209" style="stop-color:#FFFCEF;stop-opacity:0.4696"/>
          <stop  offset="0.9749" style="stop-color:#FFFFFF;stop-opacity:0"/>
        </linearGradient>
        <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_6_);" d="M145.772,393.207c29.394,0,53.222,23.828,53.222,53.222
          c0,2.198-0.149,4.361-0.408,6.489c3.499-0.864,7.155-1.331,10.921-1.331c9.881,0,19.017,3.167,26.47,8.528
          c9.336-4.9,19.956-7.687,31.232-7.687c24.842,0,46.526,13.467,58.19,33.489c4.783-3.524,10.69-5.61,17.086-5.61
          c12.556,0,23.233,8.027,27.192,19.228c3.395-0.613,6.887-0.951,10.459-0.951c32.297,0,58.478,26.182,58.478,58.478
          c0,32.297-26.182,58.479-58.478,58.479c-31.57,0-57.282-25.021-58.424-56.311c-12.234,16.857-32.082,27.829-54.503,27.829
          c-29.378,0-54.345-18.827-63.534-45.068c-22.314-2.864-39.562-21.916-39.562-45.008c0-0.195,0.012-0.387,0.015-0.581
          c-5.722,2.102-11.904,3.251-18.355,3.251c-29.394,0-53.222-23.828-53.222-53.222C92.55,417.035,116.379,393.207,145.772,393.207z"
          />
        
          <linearGradient id="SVGID_7_" gradientUnits="userSpaceOnUse" x1="1277.152" y1="-386.4847" x2="1276.4617" y2="-229.5686" gradientTransform="matrix(-0.9033 0.4291 0.4291 0.9033 1899.3772 43.7274)">
          <stop  offset="0" style="stop-color:#FFFAE6;stop-opacity:0.7"/>
          <stop  offset="0.3209" style="stop-color:#FFFCEF;stop-opacity:0.4696"/>
          <stop  offset="0.9749" style="stop-color:#FFFFFF;stop-opacity:0"/>
        </linearGradient>
        <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_7_);" d="M535.341,380.472c9.865-4.686,20.785-4.49,30.049-0.4
          c3.287-15.137,13.293-28.642,28.366-35.803c8.343-3.963,17.211-5.485,25.811-4.876c2.859-11.291,10.542-21.27,21.884-26.658
          c3.412-1.621,6.937-2.711,10.486-3.325c2.702-17.334,13.646-33.075,30.687-41.17c27.379-13.006,60.117-1.354,73.122,26.025
          c13.006,27.379,1.354,60.116-26.025,73.122c-11.928,5.666-24.873,6.649-36.76,3.693c-3.949,6.205-9.627,11.415-16.762,14.804
          c-2.78,1.321-5.636,2.284-8.518,2.933c0.846,20.126-10.17,39.758-29.513,48.946c-18.998,9.024-40.722,5.447-55.766-7.353
          c-3.519,6.413-9.007,11.822-16.119,15.2c-17.988,8.545-39.497,0.889-48.042-17.099S517.353,389.016,535.341,380.472z"/>
        
          <linearGradient id="SVGID_8_" gradientUnits="userSpaceOnUse" x1="1708.9547" y1="358.5402" x2="1762.6028" y2="521.0202" gradientTransform="matrix(-1 0 0 1 2037.8346 0)">
          <stop  offset="0" style="stop-color:#FFFAE6;stop-opacity:0.7"/>
          <stop  offset="0.1586" style="stop-color:#FFFCEF;stop-opacity:0.4696"/>
          <stop  offset="0.4818" style="stop-color:#FFFFFF;stop-opacity:0"/>
        </linearGradient>
        <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_8_);" d="M216.892,356.951c19.964,0,36.995,12.518,43.697,30.13
          c4.241-2.356,9.119-3.702,14.314-3.702c8.446,0,16.057,3.552,21.438,9.237c6.79-4.325,14.844-6.841,23.491-6.841
          c12.294,0,23.397,5.072,31.352,13.229c9.718-7.539,21.918-12.029,35.169-12.029c31.718,0,57.431,25.713,57.431,57.431
          c0,31.718-25.713,57.431-57.431,57.431c-22.291,0-41.615-12.699-51.133-31.257c-4.788,1.798-9.971,2.787-15.388,2.787
          c-19.769,0-36.473-13.101-41.918-31.093c-0.99,0.1-1.995,0.152-3.011,0.152c-8.533,0-16.215-3.624-21.605-9.412
          c-8.569,10.629-21.69,17.435-36.406,17.435c-25.819,0-46.749-20.93-46.749-46.749C170.142,377.882,191.073,356.951,216.892,356.951
          z"/>
        
          <linearGradient id="SVGID_9_" gradientUnits="userSpaceOnUse" x1="1457.6002" y1="405.8765" x2="1381.9292" y2="499.1541" gradientTransform="matrix(-1 0 0 1 2037.8346 0)">
          <stop  offset="0.4017" style="stop-color:#FFFAE6;stop-opacity:0.7"/>
          <stop  offset="0.5986" style="stop-color:#FFFCEF;stop-opacity:0.4696"/>
          <stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
        </linearGradient>
        <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_9_);" d="M615.988,463.568c4.003,0,7.925,0.37,11.738,1.073
          c8.017-35.491,38.148-61.877,74.1-61.877c42.082,0,76.195,36.148,76.195,80.739c0,44.591-34.114,80.739-76.195,80.739
          c-7.704,0-15.14-1.216-22.15-3.47c-9.638,27.351-34.515,46.829-63.688,46.829c-37.536,0-67.964-32.243-67.964-72.017
          S578.453,463.568,615.988,463.568z"/>
        <radialGradient id="SVGID_10_" cx="363.5223" cy="368.1141" r="112.1515" gradientUnits="userSpaceOnUse">
          <stop  offset="0" style="stop-color:#FFFFFF"/>
          <stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
        </radialGradient>
        <circle style="clip-path:url(#XMLID_3_);fill:url(#SVGID_10_);" cx="363.522" cy="368.114" r="112.152"/>
        <circle style="clip-path:url(#XMLID_3_);fill:#FFFFFF;" cx="363.522" cy="368.114" r="90.848"/>
        <circle style="clip-path:url(#XMLID_3_);fill:#EFF0E2;" cx="298.723" cy="361.528" r="18.158"/>
        <circle style="clip-path:url(#XMLID_3_);fill:#EFF0E2;" cx="337.649" cy="378.825" r="10.889"/>
        <circle style="clip-path:url(#XMLID_3_);fill:#EFF0E2;" cx="340.202" cy="422.201" r="13.677"/>
        
          <linearGradient id="SVGID_11_" gradientUnits="userSpaceOnUse" x1="751.922" y1="759.4001" x2="751.922" y2="639.8343" gradientTransform="matrix(0.5526 0.8334 -0.8334 0.5526 704.0733 -795.8789)">
          <stop  offset="0" style="stop-color:#FFC5D3"/>
          <stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
        </linearGradient>
        <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_11_);" d="M464.694,265.057L464.694,265.057
          c-1.299-1.958-0.759-4.623,1.2-5.922l136.708-87.231c1.958-1.299,3.729,1.372,1.771,2.671l-133.757,91.682
          C468.658,267.555,465.993,267.015,464.694,265.057z"/>
        
          <linearGradient id="SVGID_12_" gradientUnits="userSpaceOnUse" x1="626.8152" y1="975.358" x2="626.8152" y2="901.6899" gradientTransform="matrix(0.5526 0.8334 -0.8334 0.5526 704.0733 -795.8789)">
          <stop  offset="0" style="stop-color:#FFC5D3"/>
          <stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
        </linearGradient>
        <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_12_);" d="M224.021,274.532L224.021,274.532
          c-0.8-1.207-0.467-2.849,0.739-3.649l84.23-53.746c1.207-0.8,2.298,0.845,1.091,1.645l-82.411,56.488
          C226.463,276.071,224.821,275.738,224.021,274.532z"/>
        
          <linearGradient id="SVGID_13_" gradientUnits="userSpaceOnUse" x1="847.0363" y1="943.5185" x2="847.0363" y2="861.0972" gradientTransform="matrix(0.4328 0.6527 -0.5438 0.3606 802.3822 -554.7216)">
          <stop  offset="0" style="stop-color:#FFC5D3"/>
          <stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
        </linearGradient>
        <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_13_);" d="M646.016,344.945L646.016,344.945
          c-0.701-1.057-0.554-2.4,0.327-2.984l61.63-39.021c0.881-0.584,1.837,0.858,0.956,1.442l-60.037,41.424
          C648.011,346.389,646.717,346.002,646.016,344.945z"/>
        
          <linearGradient id="SVGID_14_" gradientUnits="userSpaceOnUse" x1="920.4477" y1="997.7446" x2="920.4477" y2="802.566" gradientTransform="matrix(0.5526 0.8334 -0.8334 0.5526 704.0733 -795.8789)">
          <stop  offset="0" style="stop-color:#FFC5D3"/>
          <stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
        </linearGradient>
        <path style="clip-path:url(#XMLID_3_);fill:url(#SVGID_14_);" d="M345.264,546.457L345.264,546.457
          c-2.12-3.197-1.239-7.547,1.958-9.667l223.161-142.396c3.197-2.12,6.088,2.24,2.891,4.359L354.931,548.416
          C351.734,550.535,347.384,549.654,345.264,546.457z"/>
        <g style="clip-path:url(#XMLID_3_);">
          <circle style="fill:#FFFFFF;" cx="354.55" cy="215.007" r="1.411"/>
          <circle style="fill:#FFFFFF;" cx="574.665" cy="574.601" r="2.301"/>
          <circle style="fill:#FFFFFF;" cx="375.431" cy="574.601" r="2.301"/>
          <circle style="opacity:0.46;fill:#FFFFFF;" cx="223.585" cy="497.633" r="2.864"/>
          <circle style="fill:#FFFFFF;" cx="771.936" cy="541.049" r="2.139"/>
          <circle style="fill:#FFFFFF;" cx="511.599" cy="408.524" r="1.009"/>
          <circle style="fill:#FFFFFF;" cx="292.973" cy="266.964" r="2.219"/>
          <circle style="fill:#FFFFFF;" cx="272.675" cy="198.87" r="1.641"/>
          <circle style="fill:#FFFFFF;" cx="418.636" cy="256.523" r="0.561"/>
          <circle style="opacity:0.47;fill:#FFFFFF;" cx="433.488" cy="204.566" r="3.072"/>
          <circle style="fill:#FFFFFF;" cx="740.614" cy="432.642" r="1.963"/>
          <circle style="fill:#FFFFFF;" cx="454.37" cy="541.049" r="1.063"/>
          <circle style="fill:#FFFFFF;" cx="475.674" cy="361.528" r="1.527"/>
          <circle style="fill:#FFFFFF;" cx="761.496" cy="324.44" r="0.416"/>
          <circle style="fill:#FFFFFF;" cx="600.61" cy="297.442" r="1.424"/>
          <circle style="fill:#FFFFFF;" cx="558.126" cy="508.074" r="1.235"/>
          <circle style="fill:#FFFFFF;" cx="334.267" cy="497.633" r="0.86"/>
          <circle style="fill:#FFFFFF;" cx="227.843" cy="422.201" r="1.394"/>
          <circle style="opacity:0.38;fill:#FFFFFF;" cx="615.665" cy="446.319" r="3.236"/>
          <circle style="fill:#FFFFFF;" cx="511.599" cy="266.964" r="2.219"/>
          <circle style="opacity:0.64;fill:#FFFFFF;" cx="585.106" cy="361.528" r="2.589"/>
          <circle style="fill:#FFFFFF;" cx="234.026" cy="313.999" r="2.599"/>
        </g>
      </g>
      </svg>
      <svg v-else-if="i===16" version="1.1" id="&#x56FE;&#x5C42;_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px"
        y="0px" viewBox="0 0 1000 1000" style="enable-background:new 0 0 1000 1000;" xml:space="preserve">
      <g>
        <g>
          <defs>
            <path id="XMLID_1_" d="M173.359,547.33c0,0,37.74-54.117,49.845-112.506c12.105-58.389-16.479-232.609,241.289-279.606
              c0,0,90.534-20.53,163.521,45.336c8.721,7.87,21.718,13.173,39.52,14.241c0,0-2.492,7.833-37.027,3.204
              c0,0,105.386,49.845,114.643,158.435c0,0,4.628,55.897,63.73,42.368c0,0,9.85-2.136,15.547-3.679s31.331-8.782,35.366,17.327
              l-12.342,17.09c0,0-1.661-20.887-47.471-6.171c0,0-22.549,6.883-36.79-5.934c0,0,18.751,31.568,67.409,30.144l-31.806,44.385
              c0,0-21.125,3.56-32.755-9.494c0,0-2.848,19.938,1.424,31.331l-130.071,68.358c0,0-46.522,49.845,24.685,85.448
              c0,0,48.42-81.176,130.071-74.53c0,0-29.432,28.957-0.949,74.53c28.483,45.572,1.899,75.954-19.938,79.751H424.719
              c0,0,1.78-64.976-47.352-73.521c0,0-93.546,13.924-113.1,0.554c0,0-29.728-16.041-31.556-71.583c0,0-3.795-45.15-11.642-54.829
              C214.212,559.524,193.944,557.29,173.359,547.33z"/>
          </defs>
          <linearGradient id="XMLID_11_" gradientUnits="userSpaceOnUse" x1="516.575" y1="767.3582" x2="516.575" y2="152.1903">
            <stop  offset="0" style="stop-color:#86E3EA"/>
            <stop  offset="0.0164" style="stop-color:#85DBE7"/>
            <stop  offset="0.1281" style="stop-color:#7DA8D2"/>
            <stop  offset="0.2126" style="stop-color:#7988C5"/>
            <stop  offset="0.2593" style="stop-color:#777CC0"/>
            <stop  offset="0.3408" style="stop-color:#7679BD"/>
            <stop  offset="0.4113" style="stop-color:#7271B2"/>
            <stop  offset="0.4776" style="stop-color:#6C63A0"/>
            <stop  offset="0.5414" style="stop-color:#634F87"/>
            <stop  offset="0.6029" style="stop-color:#583667"/>
            <stop  offset="0.6117" style="stop-color:#563262"/>
            <stop  offset="0.6839" style="stop-color:#4A2D57"/>
            <stop  offset="0.8146" style="stop-color:#291F3B"/>
            <stop  offset="0.8996" style="stop-color:#111425"/>
          </linearGradient>
          <use xlink:href="#XMLID_1_"  style="overflow:visible;fill:url(#XMLID_11_);"/>
          <clipPath id="XMLID_12_">
            <use xlink:href="#XMLID_1_"  style="overflow:visible;"/>
          </clipPath>
          <linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="323.9896" y1="627.2883" x2="248.3412" y2="698.994">
            <stop  offset="0" style="stop-color:#FFFFFF"/>
            <stop  offset="0.9262" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="display:none;clip-path:url(#XMLID_12_);fill:url(#SVGID_1_);" d="M196.543,591.01c0,0,70.495-2.848,74.055,81.176
            c0,0,39.164-12.817,56.253,42.724c0,0,10.681,17.09,3.56,63.374l-118.203-35.603L196.543,591.01z"/>
          <linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="605.5555" y1="635.199" x2="752.7689" y2="763.5152">
            <stop  offset="0" style="stop-color:#FFFFFF"/>
            <stop  offset="0.2444" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="display:none;clip-path:url(#XMLID_12_);fill:url(#SVGID_2_);" d="M560.408,782.556c0,0-9.257-86.16,48.421-92.569
            c0,0,4.272-52.693,56.965-55.541c0,0-25.634-63.211,46.996-92.487l9.257,14.872l-27.771,127.81l-77.615,75.841L560.408,782.556z"
            />
          <linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="523.0341" y1="531.1534" x2="807.8984" y2="757.0997">
            <stop  offset="0" style="stop-color:#FFFFFF;stop-opacity:0.6"/>
            <stop  offset="0.325" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="display:none;clip-path:url(#XMLID_12_);fill:url(#SVGID_3_);" d="M725.21,448.353c0,0-68.673,5.941-72.233,60.058
            c0,0-56.253-9.257-61.95,40.588c0,0-75.479,9.969-73.343,81.176c0,0-61.238,141.701,39.164,158.079
            c0,0,160.215-126.036,165.199-234.982L725.21,448.353z"/>
          <linearGradient id="SVGID_4_" gradientUnits="userSpaceOnUse" x1="399.771" y1="524.9507" x2="182.7013" y2="770.9656">
            <stop  offset="0" style="stop-color:#FFFFFF;stop-opacity:0.7"/>
            <stop  offset="0.4407" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="display:none;clip-path:url(#XMLID_12_);fill:url(#SVGID_4_);" d="M188.71,469.247c0,0,27.059-14.241,33.467,34.891
            c0,0,47.708-14.953,59.814,42.724c0,0,60.526-27.059,71.919,56.253c0,0,70.809-8.789,89.323,27.526
            c0,0,57.363,89.253-36.63,180.397c0,0-210.772-59.101-224.301-237.118C182.301,573.921,170.908,490.609,188.71,469.247z"/>
          <linearGradient id="SVGID_5_" gradientUnits="userSpaceOnUse" x1="414.4122" y1="427.1346" x2="230.1549" y2="626.3482">
            <stop  offset="0.0097" style="stop-color:#455FAF"/>
            <stop  offset="0.0639" style="stop-color:#4B64B5;stop-opacity:0.9316"/>
            <stop  offset="0.3578" style="stop-color:#677CD0;stop-opacity:0.5609"/>
            <stop  offset="0.614" style="stop-color:#798AE1;stop-opacity:0.2377"/>
            <stop  offset="0.8024" style="stop-color:#7F8FE7;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_12_);fill:url(#SVGID_5_);" d="M370.242,486.241c-0.538-51.416-42.365-92.935-93.907-92.935
            c-51.878,0-93.933,42.055-93.933,93.933c0,50.603,40.019,91.84,90.132,93.836c2.331,47.999,41.981,86.201,90.558,86.201
            c50.076,0,90.671-40.595,90.671-90.671C453.763,528.938,416.972,489.89,370.242,486.241z"/>
          <linearGradient id="SVGID_6_" gradientUnits="userSpaceOnUse" x1="534.9168" y1="316.276" x2="484.9213" y2="700.2502">
            <stop  offset="0" style="stop-color:#344C90"/>
            <stop  offset="0.0698" style="stop-color:#435AA3;stop-opacity:0.8185"/>
            <stop  offset="0.195" style="stop-color:#5A6FBF;stop-opacity:0.4929"/>
            <stop  offset="0.3042" style="stop-color:#687CD1;stop-opacity:0.2089"/>
            <stop  offset="0.3846" style="stop-color:#6D81D7;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_12_);fill:url(#SVGID_6_);" d="M762.08,345.639c-32.26,0-60.102,18.89-73.087,46.209
            c-11.08-6.556-23.999-10.332-37.806-10.332c-19.13,0-36.568,7.229-49.745,19.097c-20.605-20.279-48.871-32.796-80.065-32.796
            c-36.226,0-68.507,16.878-89.42,43.196c-13.361-32.974-45.676-56.242-83.443-56.242c-4.595,0-9.108,0.349-13.517,1.013
            c-2.03-33.631-29.935-60.282-64.076-60.282c-35.461,0-64.207,28.747-64.207,64.207c0,32.13,23.601,58.748,54.413,63.464
            c-1.708,6.927-2.631,14.163-2.631,21.617c0,49.716,40.303,90.019,90.019,90.019c24.111,0,45.99-9.498,62.149-24.932
            c12.451,49.556,57.294,86.25,110.713,86.25c47.404,0,88.056-28.896,105.311-70.034c7.673,2.677,15.912,4.15,24.498,4.15
            c27.629,0,51.721-15.08,64.542-37.446c13.129,9.2,29.103,14.615,46.351,14.615c44.672,0,80.886-36.214,80.886-80.886
            C842.966,381.853,806.752,345.639,762.08,345.639z"/>
          <linearGradient id="SVGID_7_" gradientUnits="userSpaceOnUse" x1="543.4095" y1="281.716" x2="446.3935" y2="590.8045">
            <stop  offset="0" style="stop-color:#314975"/>
            <stop  offset="0.1151" style="stop-color:#344F76;stop-opacity:0.6517"/>
            <stop  offset="0.3304" style="stop-color:#375577;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_12_);fill:url(#SVGID_7_);" d="M729.918,253.918c-40.693,0-75.025,27.147-85.931,64.312
            c-2.935-0.297-5.911-0.45-8.924-0.45c-11.229,0-21.954,2.135-31.821,5.983c-14.678-12.449-33.664-19.972-54.418-19.972
            c-26.944,0-50.913,12.671-66.325,32.362c-6.554-2.298-13.595-3.561-20.933-3.561c-7.447,0-14.589,1.297-21.225,3.66
            c-5.816-28.831-29.666-51.113-59.272-54.606c-11.739-26.104-37.953-44.291-68.431-44.291c-41.43,0-75.016,33.586-75.016,75.016
            c0,41.43,33.586,75.016,75.016,75.016c0.868,0,1.728-0.036,2.589-0.066c12.26,18.879,33.516,31.376,57.704,31.376
            c9.855,0,19.218-2.088,27.692-5.822c7.432,26.681,31.892,46.265,60.942,46.265c11.381,0,22.05-3.02,31.277-8.279
            c14.879,13.258,34.484,21.325,55.981,21.325c8.798,0,17.279-1.354,25.251-3.858c15.777,15.298,37.275,24.732,60.988,24.732
            c39.024,0,72.076-25.513,83.425-60.764c3.746,0.478,7.555,0.751,11.43,0.751c49.466,0,89.565-40.1,89.565-89.565
            C819.483,294.018,779.383,253.918,729.918,253.918z"/>
          <linearGradient id="SVGID_8_" gradientUnits="userSpaceOnUse" x1="399.297" y1="595.9878" x2="135.2136" y2="806.1765">
            <stop  offset="0" style="stop-color:#FFFFFF"/>
            <stop  offset="0.4798" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_12_);fill:url(#SVGID_8_);" d="M318.027,659.681c-1.135,0-2.265,0.024-3.392,0.058
            c0.121-2.141,0.188-4.296,0.188-6.467c0-62.529-50.69-113.219-113.219-113.219S88.386,590.744,88.386,653.273
            c0,62.529,50.69,113.219,113.219,113.219c2.881,0,5.736-0.109,8.562-0.321c-0.006,0.463-0.018,0.924-0.018,1.389
            c0,59.579,48.299,107.878,107.878,107.878c59.579,0,107.878-48.299,107.878-107.878
            C425.905,707.98,377.607,659.681,318.027,659.681z"/>
          <linearGradient id="SVGID_9_" gradientUnits="userSpaceOnUse" x1="521.5445" y1="581.7819" x2="781.5144" y2="771.5405">
            <stop  offset="0" style="stop-color:#FFFFFF"/>
            <stop  offset="0.6289" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_12_);fill:url(#SVGID_9_);" d="M748.708,530.952c-53.335,0-96.949,41.589-100.199,94.106
            c-22.203,14.475-38.147,37.744-42.887,64.868c-44.289,6.213-78.371,44.249-78.371,90.251c0,50.339,40.808,91.146,91.147,91.146
            c41.997,0,77.353-28.405,87.921-67.05c47.827-2.224,86.651-38.885,92.247-85.755c30.199-17.311,50.544-49.861,50.544-87.165
            C849.11,575.904,804.159,530.952,748.708,530.952z"/>
          <linearGradient id="SVGID_10_" gradientUnits="userSpaceOnUse" x1="495.7218" y1="495.5315" x2="663.3544" y2="633.6132">
            <stop  offset="0" style="stop-color:#FFFFFF"/>
            <stop  offset="0.8017" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_12_);fill:url(#SVGID_10_);" d="M715.597,451.913c-33.989,0-62.325,24.178-68.762,56.272
            c-29.088,0.309-53.945,18.225-64.419,43.604c-38.337,10.643-66.884,44.807-69.121,85.975
            c-14.305,15.277-23.067,35.807-23.067,58.387c0,47.192,38.256,85.448,85.448,85.448c37.373,0,69.137-23.996,80.739-57.419
            c27.562-16.542,46.009-46.714,46.009-81.199c0-6.155-0.595-12.17-1.717-17.997c8-9.164,13.668-20.409,16.076-32.825
            c38.187-0.636,68.952-31.77,68.952-70.109C785.736,483.315,754.334,451.913,715.597,451.913z"/>
          <linearGradient id="SVGID_11_" gradientUnits="userSpaceOnUse" x1="373.7831" y1="486.5111" x2="156.5213" y2="752.207">
            <stop  offset="0" style="stop-color:#697DD3"/>
            <stop  offset="0.0143" style="stop-color:#7083D5;stop-opacity:0.9679"/>
            <stop  offset="0.1199" style="stop-color:#A3AFE4;stop-opacity:0.7312"/>
            <stop  offset="0.2194" style="stop-color:#CBD2F0;stop-opacity:0.5083"/>
            <stop  offset="0.3098" style="stop-color:#E7EBF8;stop-opacity:0.3055"/>
            <stop  offset="0.3884" style="stop-color:#F9FAFD;stop-opacity:0.1294"/>
            <stop  offset="0.4461" style="stop-color:#FFFFFF;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_12_);fill:url(#SVGID_11_);" d="M377.01,600.023c-7.382,0-14.546,0.93-21.384,2.676
            c-0.458-37.099-29.561-67.289-66.221-69.471c0.005-0.284,0.021-0.566,0.021-0.851c0-26.742-21.679-48.421-48.42-48.421
            c-1.805,0-3.584,0.107-5.337,0.299c0.227-2.087,0.353-4.204,0.353-6.352c0-32.051-25.982-58.033-58.033-58.033
            c-32.051,0-58.033,25.982-58.033,58.033c0,32.051,25.982,58.033,58.033,58.033c5.058,0,9.963-0.65,14.64-1.866
            c0.64,18.574,11.731,34.491,27.585,42.042c-3.576,8.442-5.554,17.725-5.554,27.471c0,38.933,31.561,70.495,70.495,70.495
            c2.224,0,4.42-0.116,6.591-0.317c-0.586,4.057-0.895,8.203-0.895,12.422c0,47.585,38.575,86.16,86.16,86.16
            c47.585,0,86.16-38.575,86.16-86.16S424.595,600.023,377.01,600.023z"/>
          <path style="display:none;clip-path:url(#XMLID_12_);fill:#777CC0;" d="M402.135,732.335c0,0,112.15-66.222,200.803-40.588
            c0,0,30.975,17.09,90.788,0c59.814-17.09,138.853,28.839,138.853,28.839l-8.545,81.176L392.855,791.08L402.135,732.335z"/>
          <path style="display:none;clip-path:url(#XMLID_12_);fill:#86E3EA;" d="M418.512,762.954c0,0,59.102-44.86,145.974-42.724
            c0,0,76.191,10.681,103.25,4.984c27.059-5.697,98.977-20.798,159.503-5.058L807.3,782.179H411.391L418.512,762.954z"/>
          <path style="display:none;opacity:0.39;clip-path:url(#XMLID_12_);fill:#FFFFFF;" d="M515.574,265.832
            c0-3.355,0.092-6.688,0.266-10c-4.217-0.291-8.471-0.45-12.762-0.45c-101.069,0-183.001,81.932-183.001,183.001
            s81.932,183.001,183.001,183.001c96.217,0,175.088-74.257,182.438-168.587C590.173,443.794,515.574,363.531,515.574,265.832z"/>
          <linearGradient id="SVGID_12_" gradientUnits="userSpaceOnUse" x1="285.6288" y1="302.685" x2="350.4787" y2="401.7539">
            <stop  offset="0" style="stop-color:#B5FF85"/>
            <stop  offset="0.322" style="stop-color:#5296BB"/>
            <stop  offset="0.4482" style="stop-color:#466BB3;stop-opacity:0.642"/>
            <stop  offset="0.6041" style="stop-color:#3939AA;stop-opacity:0.2"/>
          </linearGradient>
          <circle style="opacity:0.49;clip-path:url(#XMLID_12_);fill:url(#SVGID_12_);" cx="318.054" cy="352.219" r="59.194"/>
          
            <linearGradient id="SVGID_13_" gradientUnits="userSpaceOnUse" x1="1493.7837" y1="-804.7325" x2="1599.4481" y2="-987.7488" gradientTransform="matrix(-0.4507 0.8927 -0.8927 -0.4507 234.7928 -1402.3431)">
            <stop  offset="0" style="stop-color:#B5FF85"/>
            <stop  offset="0.322" style="stop-color:#5296BB"/>
            <stop  offset="0.4482" style="stop-color:#466BB3;stop-opacity:0.642"/>
            <stop  offset="0.6041" style="stop-color:#3939AA;stop-opacity:0.2"/>
          </linearGradient>
          <circle style="clip-path:url(#XMLID_12_);fill:url(#SVGID_13_);" cx="318.054" cy="352.219" r="54.117"/>
          
            <linearGradient id="SVGID_14_" gradientUnits="userSpaceOnUse" x1="470.6286" y1="-18.3345" x2="468.525" y2="-301.3485" gradientTransform="matrix(1.0502 1.2393 -0.7629 0.6465 -87.0896 -261.7044)">
            <stop  offset="0" style="stop-color:#B5FF85"/>
            <stop  offset="0.362" style="stop-color:#5296BB"/>
            <stop  offset="0.5374" style="stop-color:#466BB3;stop-opacity:0.5525"/>
            <stop  offset="0.7539" style="stop-color:#3939AA;stop-opacity:0"/>
          </linearGradient>
          <path style="opacity:0.36;clip-path:url(#XMLID_12_);fill:url(#SVGID_14_);" d="M422.566,308.92l-2.602-3.071
            c-2.969-3.503-2.532-8.799,0.972-11.767L618.66,126.522c3.503-2.969,8.799-2.532,11.767,0.972l2.602,3.071
            c2.969,3.503,2.532,8.799-0.972,11.767L434.334,309.892C430.83,312.861,425.535,312.423,422.566,308.92z"/>
          
            <linearGradient id="SVGID_15_" gradientUnits="userSpaceOnUse" x1="1081.1057" y1="-26.3008" x2="1079.2272" y2="-279.0536" gradientTransform="matrix(0.6465 0.7629 -0.7629 0.6465 -292.8698 -504.5275)">
            <stop  offset="0" style="stop-color:#B5FF85"/>
            <stop  offset="0.362" style="stop-color:#5296BB"/>
            <stop  offset="0.5374" style="stop-color:#466BB3;stop-opacity:0.5525"/>
            <stop  offset="0.7539" style="stop-color:#3939AA;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_12_);fill:url(#SVGID_15_);" d="M427.33,302.478l-1.431-1.688
            c-1.632-1.926-1.392-4.837,0.534-6.469L607.389,140.97c1.926-1.632,4.837-1.392,6.469,0.534l1.431,1.688
            c1.632,1.926,1.392,4.837-0.534,6.469L433.8,303.013C431.874,304.645,428.963,304.404,427.33,302.478z"/>
          
            <linearGradient id="SVGID_16_" gradientUnits="userSpaceOnUse" x1="570.2747" y1="122.6335" x2="568.2377" y2="-151.4308" gradientTransform="matrix(1.0489 1.2377 -0.7629 0.6465 -128.8416 -310.9723)">
            <stop  offset="0" style="stop-color:#B5FF85"/>
            <stop  offset="0.362" style="stop-color:#5296BB"/>
            <stop  offset="0.5374" style="stop-color:#466BB3;stop-opacity:0.5525"/>
            <stop  offset="0.7539" style="stop-color:#3939AA;stop-opacity:0"/>
          </linearGradient>
          <path style="opacity:0.54;clip-path:url(#XMLID_12_);fill:url(#SVGID_16_);" d="M377.114,473.404l-2.517-2.97
            c-2.871-3.388-2.448-8.509,0.94-11.381l191.487-162.275c3.388-2.871,8.509-2.448,11.381,0.94l2.517,2.97
            c2.871,3.388,2.448,8.509-0.94,11.381L388.495,474.344C385.107,477.215,379.985,476.793,377.114,473.404z"/>
          
            <linearGradient id="SVGID_17_" gradientUnits="userSpaceOnUse" x1="1178.3345" y1="117.8437" x2="1176.4558" y2="-134.9091" gradientTransform="matrix(0.6465 0.7629 -0.7629 0.6465 -292.8698 -504.5275)">
            <stop  offset="0" style="stop-color:#B5FF85"/>
            <stop  offset="0.362" style="stop-color:#5296BB"/>
            <stop  offset="0.5374" style="stop-color:#466BB3;stop-opacity:0.5525"/>
            <stop  offset="0.7539" style="stop-color:#3939AA;stop-opacity:0"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_12_);fill:url(#SVGID_17_);" d="M380.223,469.846l-1.431-1.688
            c-1.632-1.926-1.392-4.837,0.534-6.469l180.955-153.35c1.926-1.632,4.837-1.392,6.469,0.534l1.431,1.688
            c1.632,1.926,1.392,4.837-0.534,6.469L386.692,470.38C384.766,472.012,381.855,471.772,380.223,469.846z"/>
          <g style="clip-path:url(#XMLID_12_);">
            <radialGradient id="SVGID_18_" cx="467.6689" cy="534.7138" r="102.0879" gradientUnits="userSpaceOnUse">
              <stop  offset="0" style="stop-color:#FFFFFF"/>
              <stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
            </radialGradient>
            <circle style="fill:url(#SVGID_18_);" cx="467.669" cy="534.714" r="102.088"/>
            <circle style="fill:#FFFFFF;" cx="467.669" cy="534.714" r="81.048"/>
            <circle style="fill:#EFF0E2;" cx="484.267" cy="483.44" r="13.093"/>
            <circle style="fill:#EFF0E2;" cx="515.717" cy="504.225" r="8.082"/>
            <circle style="fill:#EFF0E2;" cx="510.591" cy="552.314" r="19.766"/>
          </g>
          <g style="display:none;clip-path:url(#XMLID_12_);">
            <radialGradient id="SVGID_19_" cx="465.3467" cy="532.9011" r="90.0811" gradientUnits="userSpaceOnUse">
              <stop  offset="0.2999" style="stop-color:#FED593;stop-opacity:0.6"/>
              <stop  offset="0.6618" style="stop-color:#FED393;stop-opacity:0.2899"/>
              <stop  offset="0.7922" style="stop-color:#FDCC95;stop-opacity:0.1781"/>
              <stop  offset="0.8851" style="stop-color:#FDC198;stop-opacity:0.0985"/>
              <stop  offset="0.9597" style="stop-color:#FCB09C;stop-opacity:0.0346"/>
              <stop  offset="1" style="stop-color:#FBA39F;stop-opacity:0"/>
            </radialGradient>
            <circle style="display:inline;fill:url(#SVGID_19_);" cx="465.347" cy="532.901" r="90.081"/>
            <linearGradient id="SVGID_20_" gradientUnits="userSpaceOnUse" x1="465.8964" y1="606.8533" x2="464.7812" y2="456.8195">
              <stop  offset="0" style="stop-color:#FED593"/>
              <stop  offset="1" style="stop-color:#FBA39F"/>
            </linearGradient>
            <circle style="display:inline;fill:url(#SVGID_20_);" cx="465.347" cy="532.901" r="73.127"/>
            <path style="display:inline;fill:#F9A396;" d="M503.357,579.059c-41.595,0-75.315-33.72-75.315-75.315
              c0-11.696,2.666-22.769,7.423-32.645c-21.746,11.471-36.576,34.29-36.576,60.585c0,37.813,30.654,68.467,68.467,68.467
              c20.146,0,38.257-8.703,50.786-22.552C513.36,578.551,508.418,579.059,503.357,579.059z"/>
            <path style="display:inline;fill:#FF6B81;" d="M442.161,484.947l0.49,4.818c0.055,0.54,0.372,1.019,0.848,1.28l4.247,2.327
              c1.199,0.657,1.124,2.404-0.127,2.956l-4.431,1.955c-0.497,0.219-0.854,0.669-0.955,1.202l-0.901,4.758
              c-0.254,1.343-1.939,1.812-2.85,0.793l-3.228-3.61c-0.362-0.405-0.9-0.605-1.438-0.537l-4.804,0.613
              c-1.356,0.173-2.322-1.284-1.635-2.466l2.435-4.186c0.273-0.469,0.298-1.043,0.066-1.534l-2.068-4.379
              c-0.584-1.236,0.504-2.605,1.84-2.317l4.733,1.023c0.531,0.115,1.084-0.039,1.479-0.411l3.526-3.32
              C440.385,482.976,442.023,483.587,442.161,484.947z"/>
            <path style="display:inline;fill:#FF6B81;" d="M467.891,551.85l0.302,2.972c0.034,0.333,0.23,0.628,0.523,0.789l2.62,1.436
              c0.74,0.405,0.693,1.483-0.078,1.823l-2.733,1.206c-0.306,0.135-0.527,0.413-0.589,0.742l-0.556,2.935
              c-0.157,0.829-1.196,1.118-1.758,0.489l-1.992-2.227c-0.223-0.25-0.555-0.373-0.887-0.331l-2.963,0.378
              c-0.837,0.107-1.433-0.792-1.008-1.521l1.502-2.582c0.168-0.289,0.184-0.643,0.041-0.946l-1.276-2.701
              c-0.36-0.763,0.311-1.607,1.135-1.429l2.92,0.631c0.327,0.071,0.669-0.024,0.912-0.254l2.175-2.048
              C466.795,550.634,467.805,551.011,467.891,551.85z"/>
            <path style="display:inline;fill:#FF6B81;" d="M504.023,507.747l3.128,2.55c0.351,0.286,0.82,0.381,1.254,0.254l3.874-1.131
              c1.094-0.319,2.079,0.754,1.667,1.817L512.487,515c-0.164,0.422-0.109,0.897,0.146,1.271l2.273,3.335
              c0.642,0.942-0.075,2.21-1.212,2.147l-4.029-0.225c-0.452-0.025-0.887,0.174-1.164,0.531l-2.469,3.192
              c-0.697,0.901-2.125,0.612-2.416-0.49l-1.032-3.901c-0.116-0.437-0.439-0.79-0.865-0.943l-3.799-1.362
              c-1.073-0.385-1.239-1.832-0.281-2.449l3.392-2.187c0.38-0.245,0.616-0.662,0.629-1.114l0.121-4.034
              C501.815,507.632,503.14,507.027,504.023,507.747z"/>
          </g>
          
            <linearGradient id="SVGID_21_" gradientUnits="userSpaceOnUse" x1="-567.549" y1="511.2258" x2="-568.1494" y2="430.4504" gradientTransform="matrix(0.8216 -0.5701 0.5701 0.8216 677.1215 -177.5552)">
            <stop  offset="0" style="stop-color:#FED593"/>
            <stop  offset="1" style="stop-color:#FBA39F"/>
          </linearGradient>
          <path style="opacity:0.24;clip-path:url(#XMLID_12_);fill:url(#SVGID_21_);" d="M364.782,630.182
            c-8.958,0-15.268-2.597-18.874-7.794c-8.49-12.236,1.643-33.308,30.118-62.63c2.694-2.774,7.126-2.838,9.898-0.146
            c2.773,2.693,2.838,7.125,0.146,9.898c-28.477,29.324-30.275,42.569-28.659,44.897c1.371,1.976,12.208,4.871,42.977-7.321
            c27.154-10.759,60.398-29.374,93.608-52.416c33.209-23.042,62.284-47.666,81.868-69.335c22.191-24.555,23.273-35.72,21.902-37.695
            c-2.713-3.911-25.425-3.727-75.241,22.297c-3.426,1.789-7.655,0.463-9.446-2.963c-1.79-3.427-0.463-7.656,2.963-9.446
            c21.904-11.443,41.567-19.478,56.861-23.237c18.765-4.612,30.66-2.855,36.366,5.369c7.707,11.108-0.038,29.634-23.019,55.063
            c-20.281,22.441-50.21,47.816-84.274,71.451c-34.064,23.635-68.311,42.787-96.432,53.929
            C388.588,626.821,374.972,630.182,364.782,630.182z"/>
          
            <linearGradient id="SVGID_22_" gradientUnits="userSpaceOnUse" x1="-567.5787" y1="507.2247" x2="-568.1196" y2="434.4509" gradientTransform="matrix(0.8216 -0.5701 0.5701 0.8216 677.1215 -177.5552)">
            <stop  offset="0" style="stop-color:#FED593"/>
            <stop  offset="1" style="stop-color:#FBA39F"/>
          </linearGradient>
          <path style="clip-path:url(#XMLID_12_);fill:url(#SVGID_22_);" d="M364.732,626.211c-6.894,0-12.493-1.715-15.537-6.103
            c-9.024-13.006,11.171-38.482,29.701-57.563c1.154-1.189,3.053-1.217,4.242-0.063c1.189,1.154,1.217,3.054,0.063,4.242
            c-27.412,28.228-32.994,44.318-29.076,49.964c3.429,4.942,17.771,5.991,47.737-5.882c27.43-10.868,60.96-29.637,94.415-52.848
            c72.759-50.483,113.616-99.858,104.777-112.597c-5.311-7.657-32.227-4.123-80.38,21.032c-1.468,0.765-3.281,0.198-4.048-1.27
            c-0.767-1.469-0.198-3.281,1.27-4.048c22.368-11.686,75.731-36.946,88.088-19.133c14.359,20.696-44.044,77.761-106.287,120.947
            c-33.82,23.465-67.78,42.464-95.625,53.497C391.234,621.47,376.192,626.211,364.732,626.211z"/>
          <g style="display:none;clip-path:url(#XMLID_12_);">
            <path style="display:inline;fill:#ED9685;" d="M400.617,306.862c0,0-14.419,10.503-9.435,19.226c0,0,2.466,3.491,9.435-6.053
              c0,0-2.67,5.163,0,6.676c0,0,3.541,5.963,19.75-12.194l-3.016-9.435L400.617,306.862z"/>
            <path style="display:inline;fill:#FFBFB0;" d="M457.391,320.345c0,0-21.111-28.971-37.964-28.971
              c-16.852,0-27.711,0.712-35.841,25.634c0,0-2.848,8.07,10.918-0.237c0,0,21.625-14.901,21.625-0.182
              c0,5.109-2.716,9.841-6.984,12.649c-0.933,0.614-2.773,1.418-4.197,2.783c0,0-5.398,6.634,3.001,6.202
              c6.232-0.321,12.238,2.438,15.738,7.604c0.061,0.091,0.123,0.182,0.185,0.275L457.391,320.345z"/>
            <g style="display:inline;">
              <defs>
                <path id="SVGID_23_" d="M570.748,375.43l-25.157,28.89c-4.005,4.599-11.044,5.085-15.643,1.081l-0.749-0.652l1.676-1.676
                  c2.542-2.542,2.542-6.701,0-9.243l-17.474-17.474c0.011-0.013,0.019-0.026,0.031-0.039l25.157-28.89
                  c4.005-4.599,11.044-5.085,15.643-1.081l15.436,13.441C574.267,363.792,574.753,370.831,570.748,375.43z"/>
              </defs>
              <defs>
                <path id="SVGID_24_" d="M513.402,376.356l-58.628-58.628c-2.542-2.542-6.701-2.542-9.243,0l-20.576,20.576
                  c-2.542,2.542-2.542,6.701,0,9.243l76.102,76.102c2.542,2.542,6.701,2.542,9.243,0l18.9-18.9l-14.687-12.789
                  C509.926,387.966,509.433,380.956,513.402,376.356z"/>
              </defs>
              <defs>
                <path id="SVGID_25_" d="M530.876,393.83l-17.474-17.474c-3.969,4.6-3.475,11.61,1.111,15.604l14.687,12.789l1.676-1.676
                  C533.418,400.531,533.418,396.372,530.876,393.83z"/>
              </defs>
              <use xlink:href="#SVGID_23_"  style="overflow:visible;fill:#FFBFB0;"/>
              <use xlink:href="#SVGID_24_"  style="overflow:visible;fill:#FFBFB0;"/>
              <use xlink:href="#SVGID_25_"  style="overflow:visible;fill:#FFBFB0;"/>
              <clipPath id="SVGID_26_">
                <use xlink:href="#SVGID_23_"  style="overflow:visible;"/>
              </clipPath>
              <clipPath id="SVGID_27_" style="clip-path:url(#SVGID_26_);">
                <use xlink:href="#SVGID_24_"  style="overflow:visible;"/>
              </clipPath>
              <clipPath id="SVGID_28_" style="clip-path:url(#SVGID_27_);">
                <use xlink:href="#SVGID_25_"  style="overflow:visible;"/>
              </clipPath>
              <polygon style="clip-path:url(#SVGID_28_);fill:#ED9685;" points="516.506,359.733 544.988,381.807 544.988,385.011 
                452.42,385.011 456.198,398.036 564.362,392.488 556.738,360.801 523.27,351.188 				"/>
            </g>
            <g style="display:inline;">
              <defs>
                <path id="SVGID_29_" d="M732.499,459.897l84.024-65.129l79.277,13.346c0,0-17.386,3.6-22.015,10.187l-106.988,91.322
                  L732.499,459.897z"/>
              </defs>
              <use xlink:href="#SVGID_29_"  style="overflow:visible;fill:#FFBFB0;"/>
              <clipPath id="SVGID_30_">
                <use xlink:href="#SVGID_29_"  style="overflow:visible;"/>
              </clipPath>
              <polygon style="clip-path:url(#SVGID_30_);fill:#4848DC;" points="764.305,419.072 820.083,484.107 904.344,404.831 
                814.149,382.519 				"/>
              <path style="clip-path:url(#SVGID_30_);fill:#F74D4D;" d="M799.908,401.745c0,0,13.885,16.021,32.755-1.78l-29.195-8.901
                L799.908,401.745z"/>
              <polygon style="clip-path:url(#SVGID_30_);fill:#FFFFFF;" points="768.933,402.101 833.375,477.224 836.935,474.376 
                772.138,398.897 				"/>
              <polygon style="clip-path:url(#SVGID_30_);fill:#FFFFFF;" points="805.961,396.76 903.514,416.342 910.278,399.253 
                809.877,389.016 				"/>
              <polygon style="clip-path:url(#SVGID_30_);fill:#ED9685;" points="732.499,435.924 787.091,518.88 759.567,531.99 
                711.256,452.196 				"/>
            </g>
            <g style="display:inline;">
              <defs>
                <path id="SVGID_31_" d="M586.763,683.96l19.397,107.285c0,0,1.847,24.804-2.426,30.975l65.154-26.228l-21.362-124.374
                  L586.763,683.96z"/>
              </defs>
              <use xlink:href="#SVGID_31_"  style="overflow:visible;fill:#FFBFB0;"/>
              <clipPath id="SVGID_32_">
                <use xlink:href="#SVGID_31_"  style="overflow:visible;"/>
              </clipPath>
              <polygon style="clip-path:url(#SVGID_32_);fill:#4848DC;" points="571.572,754.218 677.433,717.19 685.503,822.22 
                588.662,837.292 				"/>
              <path style="clip-path:url(#SVGID_32_);fill:#F74D4D;" d="M658.881,796.942c0,0-9.931-19.226,10.007-26.346l5.222,19.226
                L658.881,796.942z"/>
              <polygon style="clip-path:url(#SVGID_32_);fill:#FFFFFF;" points="564.362,768.459 687.468,724.311 687.607,730.719 
                564.362,774.155 				"/>
              <polygon style="clip-path:url(#SVGID_32_);fill:#FFFFFF;" points="573.461,825.424 683.129,781.988 683.129,799.79 
                586.763,836.105 				"/>
              <path style="clip-path:url(#SVGID_32_);fill:#ED9685;" d="M564.362,700.341c0,0,46.848,31.802,117.343-23.739l-7.595-19.226
                L568.879,685.12L564.362,700.341z"/>
            </g>
            <g style="display:inline;">
              <defs>
                <path id="SVGID_33_" d="M550.21,621.061l-6.779,68.358c0,0,35.024,29.907,125.456-9.257c0,0-12.817-63.374-26.346-78.327
                  L550.21,621.061z"/>
              </defs>
              <use xlink:href="#SVGID_33_"  style="overflow:visible;fill:#FF6D00;"/>
              <clipPath id="SVGID_34_">
                <use xlink:href="#SVGID_33_"  style="overflow:visible;"/>
              </clipPath>
              <path style="clip-path:url(#SVGID_34_);fill:none;stroke:#FFFFFF;stroke-width:4;stroke-miterlimit:10;" d="M521.752,653.579
                c0,0,48.871,40.35,152.358-4.747"/>
              <path style="clip-path:url(#SVGID_34_);fill:none;stroke:#FFFFFF;stroke-width:4;stroke-miterlimit:10;" d="M524.576,668.77
                c0,0,56.016,43.673,152.857-5.697"/>
              <path style="clip-path:url(#SVGID_34_);fill:#AA5318;" d="M529.2,640.287c0,0,44.746,38.926,139.688-11.393l-12.342-29.432
                l-123.763,28.483L529.2,640.287z"/>
            </g>
            <g style="display:inline;">
              <defs>
                <path id="XMLID_7_" d="M558.518,424.919L493.72,527.781c0,0-32.043,49.133,23.498,96.129s133.156,0,133.156,0L776.41,517.812
                  l-51.981-81.176l-70.495,54.829c0,0,46.759-34.115,20.175-66.546H558.518z"/>
              </defs>
              <linearGradient id="XMLID_13_" gradientUnits="userSpaceOnUse" x1="610.1702" y1="412.8804" x2="653.6985" y2="691.0949">
                <stop  offset="0.2808" style="stop-color:#FF6D00"/>
                <stop  offset="0.4604" style="stop-color:#FF8015"/>
                <stop  offset="0.6689" style="stop-color:#FF8F26"/>
                <stop  offset="0.8227" style="stop-color:#FF952C"/>
              </linearGradient>
              <use xlink:href="#XMLID_7_"  style="overflow:visible;fill:url(#XMLID_13_);"/>
              <clipPath id="XMLID_14_">
                <use xlink:href="#XMLID_7_"  style="overflow:visible;"/>
              </clipPath>
              <path style="clip-path:url(#XMLID_14_);fill:none;stroke:#FFFFFF;stroke-width:4;stroke-miterlimit:10;" d="M627.825,427.617
                l-83.78,126.002c-6.267,9.425-7.028,21.645-1.468,31.504c6.893,12.223,26.13,32.087,63.582,3.421l121.847-96.252"/>
              <g style="clip-path:url(#XMLID_14_);">
                <path style="fill:#AA5318;" d="M658.881,487.477c-5.183,6.498-10.649,12.72-16.214,18.846
                  c-2.756,3.089-5.615,6.077-8.425,9.113l-8.585,8.957l-8.739,8.807c-2.968,2.882-5.881,5.817-8.902,8.647
                  c-5.985,5.715-12.073,11.332-18.444,16.67c5.18-6.501,10.644-12.725,16.21-18.85c2.755-3.09,5.617-6.075,8.425-9.113
                  l8.588-8.953l8.743-8.803c2.965-2.884,5.883-5.816,8.902-8.647C646.427,498.437,652.513,492.819,658.881,487.477z"/>
              </g>
              
                <line style="clip-path:url(#XMLID_14_);fill:none;stroke:#FFFFFF;stroke-width:4;stroke-linecap:round;stroke-miterlimit:10;" x1="710.544" y1="438.772" x2="771.425" y2="532.053"/>
              
                <line style="clip-path:url(#XMLID_14_);fill:none;stroke:#FFFFFF;stroke-width:4;stroke-linecap:round;stroke-miterlimit:10;" x1="696.302" y1="444.469" x2="760.032" y2="540.598"/>
            </g>
            <path style="display:inline;fill:#4848DC;" d="M557.567,381.243l-27.393-20.704c-2.453-1.854-2.943-5.378-1.089-7.831
              l27.981-37.02c5.899-7.804,17.11-9.364,24.915-3.465l6.84,5.17c7.804,5.899,9.364,17.11,3.465,24.915l-28.807,38.113
              C562.079,382.273,559.419,382.643,557.567,381.243z"/>
            <linearGradient id="SVGID_35_" gradientUnits="userSpaceOnUse" x1="615.4909" y1="288.6176" x2="609.4825" y2="412.1881">
              <stop  offset="0.2808" style="stop-color:#4848DC"/>
              <stop  offset="0.2884" style="stop-color:#4949DC"/>
              <stop  offset="0.596" style="stop-color:#5A5AE0"/>
              <stop  offset="0.8227" style="stop-color:#6060E1"/>
            </linearGradient>
            <path style="display:inline;fill:url(#SVGID_35_);" d="M637.11,427.617H587.21c-20.35,0-37-16.65-37-37v-66.989
              c0-20.35,16.65-37,37-37h49.899c20.35,0,37,16.65,37,37v66.989C674.11,410.967,657.46,427.617,637.11,427.617z"/>
            <g style="display:inline;">
              <path style="fill:#381F1F;" d="M597.666,228.42c0,0,6.157-22.315,13.01-28.635c0,0,18.87-3.827,24.388,14.864l-12.105,30.619
                l-6.594,6.053l-14.412-11.393L597.666,228.42z"/>
              <g>
                <g>
                  <defs>
                    <path id="XMLID_21_" d="M588.602,290.841l7.121-22.786l23.854-13.974l-6.409,39.787
                      C613.169,293.867,599.64,309.71,588.602,290.841z"/>
                  </defs>
                  <clipPath id="XMLID_17_">
                    <use xlink:href="#XMLID_21_"  style="overflow:visible;"/>
                  </clipPath>
                </g>
                <g>
                  <defs>
                    <path id="XMLID_20_" d="M588.602,290.841l7.121-22.786l23.854-13.974l-6.409,39.787
                      C613.169,293.867,599.64,309.71,588.602,290.841z"/>
                  </defs>
                  <use xlink:href="#XMLID_20_"  style="overflow:visible;fill:#FFBFB0;"/>
                  <clipPath id="XMLID_22_">
                    <use xlink:href="#XMLID_20_"  style="overflow:visible;"/>
                  </clipPath>
                  <path style="clip-path:url(#XMLID_22_);fill:#FFBFB0;" d="M592.934,271.081c0,0,10.711,9.969,29.432-10.598l-2.018-8.39
                    l-30.025,16.971L592.934,271.081z"/>
                </g>
                <g>
                  <defs>
                    <path id="XMLID_18_" d="M588.602,290.841l7.121-22.786l23.854-13.974l-6.409,39.787
                      C613.169,293.867,599.64,309.71,588.602,290.841z"/>
                  </defs>
                  <clipPath id="XMLID_23_">
                    <use xlink:href="#XMLID_18_"  style="overflow:visible;"/>
                  </clipPath>
                </g>
              </g>
              <path style="fill:#FFBFB0;" d="M598.037,227.467c0,0-4.094,10.325-5.34,16.199c0,0-6.765,5.697-6.231,9.435
                c0,0,3.62,2.492,4.628,2.73c0,0-2.492,11.334,4.272,13.292c0,0,14.33,7.388,24.21-15.042c0,0,7.299,0.089,5.964-9.969
                c0,0-0.979-4.361-6.587,3.649c0,0-3.204,3.568-4.539,1.117c0,0-1.335-1.473-0.534-7.258c0,0-7.121,1.869-6.676-3.916
                c0,0-0.267-5.964,0.623-7.388C607.828,230.315,603.912,225.865,598.037,227.467z"/>
              <path style="fill:#020101;" d="M597.666,236.813c0,0,5.178-2.225,5.979,4.717c0,0-0.148,1.276-0.712,0
                c-0.564-1.276-2.374-4.747-5.267-4.094C597.666,237.436,596.731,237.406,597.666,236.813z"/>
              
                <ellipse transform="matrix(0.3419 -0.9397 0.9397 0.3419 167.0484 721.9498)" style="fill:#020101;" cx="598.972" cy="241.708" rx="1.513" ry="0.668"/>
              <path style="fill:#020101;" d="M592.638,258.916c0,0,4.45,1.78,7.18-1.424c0,0,1.758-0.519-0.142,1.632
                c0,0-2.907,2.477-7.038,0.741C592.638,259.866,591.154,258.976,592.638,258.916z"/>
              <path style="fill:none;stroke:#282828;stroke-linecap:round;stroke-miterlimit:10;" d="M619.577,250.253
                c0,0,1.513,0.534,4.005-4.539"/>
            </g>
            <g style="display:inline;">
              <defs>
                <path id="SVGID_36_" d="M676.452,419.696l-16.24,2.173c-6.856,0.917-13.216-3.941-14.133-10.797l-5.195-38.825
                  c-0.917-6.856,3.941-13.216,10.797-14.133l16.24-2.173c6.856-0.917,13.216,3.941,14.133,10.797l5.195,38.825
                  C688.167,412.418,683.308,418.778,676.452,419.696z"/>
              </defs>
              <use xlink:href="#SVGID_36_"  style="overflow:visible;fill:#FFBFB0;"/>
              <clipPath id="SVGID_37_">
                <use xlink:href="#SVGID_36_"  style="overflow:visible;"/>
              </clipPath>
              <path style="clip-path:url(#SVGID_37_);fill:#ED9685;" d="M641.29,382.132l53.227-8.189l-1.068-25.278l-48.777,8.545
                c0,0-11.571,9.435-11.037,9.791C634.169,367.357,641.29,382.132,641.29,382.132z"/>
            </g>
            <path style="display:inline;fill:#6060E1;" d="M682.285,365.429l-38.963,5.213c-2.857,0.382-5.508-1.643-5.89-4.5l-6.914-49.599
              c-1.843-13.772,7.918-26.548,21.69-28.391h0c13.772-1.843,26.548,7.918,28.391,21.69l6.817,48.872
              C687.851,361.971,685.542,364.993,682.285,365.429z"/>
            <path style="display:inline;fill:#FFBFB0;" d="M670.824,430.672H478.061c-4.787,0-8.703-3.916-8.703-8.703v-24.106
              c0-4.866,3.981-8.847,8.847-8.847h192.618c9.231,0,16.783,7.552,16.783,16.783v8.09
              C687.607,423.119,680.055,430.672,670.824,430.672z"/>
            <path style="display:inline;fill:#FFBFB0;" d="M476.485,420.106h-40.574c-2.549,0-4.635-2.086-4.635-4.635v-2.234
              c0-2.549,2.086-4.635,4.635-4.635h40.574c2.549,0,4.635,2.086,4.635,4.635v2.234C481.12,418.02,479.034,420.106,476.485,420.106z
              "/>
            <path style="display:inline;fill:#FFBFB0;" d="M482.478,409.541h-40.574c-2.549,0-4.635-2.086-4.635-4.635v-2.234
              c0-2.549,2.086-4.635,4.635-4.635h40.574c2.549,0,4.635,2.086,4.635,4.635v2.234
              C487.113,407.455,485.027,409.541,482.478,409.541z"/>
            <path style="display:inline;fill:#FFBFB0;" d="M485.01,430.672h-40.574c-2.549,0-4.635-2.086-4.635-4.635v-2.234
              c0-2.549,2.086-4.635,4.635-4.635h40.574c2.549,0,4.635,2.086,4.635,4.635v2.234C489.645,428.586,487.559,430.672,485.01,430.672
              z"/>
            <path style="display:inline;fill:#FFBFB0;" d="M493.614,400.521H453.04c-2.549,0-4.635-2.086-4.635-4.635v-2.234
              c0-2.549,2.086-4.635,4.635-4.635h40.574c2.549,0,4.635,2.086,4.635,4.635v2.234
              C498.249,398.435,496.163,400.521,493.614,400.521z"/>
          </g>
        </g>
        <path style="opacity:0.33;fill:#FADD97;" d="M294.226,537.686l4.94,9.032c0.434,0.794,1.087,1.447,1.881,1.881l9.032,4.94
          c3.282,1.795,3.282,6.509,0,8.304l-9.032,4.94c-0.794,0.434-1.447,1.087-1.881,1.881l-4.94,9.032c-1.795,3.282-6.509,3.282-8.304,0
          l-4.94-9.032c-0.434-0.794-1.087-1.447-1.881-1.881l-9.032-4.94c-3.282-1.795-3.282-6.509,0-8.304l9.032-4.94
          c0.794-0.434,1.447-1.087,1.881-1.881l4.94-9.032C287.717,534.404,292.431,534.404,294.226,537.686z"/>
        <path style="fill:#FADD97;" d="M293.333,541.987l3.878,7.09c0.341,0.623,0.853,1.136,1.477,1.477l7.09,3.878
          c2.577,1.409,2.577,5.11,0,6.519l-7.09,3.878c-0.623,0.341-1.136,0.853-1.477,1.477l-3.878,7.09c-1.409,2.577-5.11,2.577-6.519,0
          l-3.878-7.09c-0.341-0.623-0.853-1.136-1.477-1.477l-7.09-3.878c-2.577-1.409-2.577-5.11,0-6.519l7.09-3.878
          c0.623-0.341,1.136-0.853,1.477-1.477l3.878-7.09C288.224,539.41,291.924,539.41,293.333,541.987z"/>
        <path style="opacity:0.42;fill:#86E3EA;" d="M403.642,398.07l-5.837-4.741c-0.371-0.302-0.821-0.49-1.297-0.544l-7.473-0.843
          c-1.965-0.222-2.908-2.527-1.661-4.062l4.741-5.837c0.302-0.371,0.49-0.821,0.544-1.297l0.843-7.473
          c0.222-1.965,2.527-2.908,4.062-1.661l5.837,4.741c0.371,0.302,0.821,0.49,1.297,0.544l7.473,0.843
          c1.965,0.222,2.908,2.527,1.661,4.062l-4.741,5.837c-0.302,0.371-0.49,0.821-0.544,1.297l-0.843,7.473
          C407.483,398.374,405.177,399.317,403.642,398.07z"/>
        <path style="fill:#86E3EA;" d="M403.047,395.478l-4.693-3.812c-0.299-0.242-0.66-0.394-1.042-0.437l-6.008-0.678
          c-1.58-0.178-2.338-2.032-1.336-3.266l3.812-4.693c0.242-0.299,0.394-0.66,0.437-1.042l0.678-6.008
          c0.178-1.58,2.032-2.338,3.266-1.336l4.693,3.812c0.299,0.242,0.66,0.394,1.042,0.437l6.008,0.678
          c1.58,0.178,2.338,2.032,1.336,3.266l-3.812,4.693c-0.242,0.299-0.394,0.66-0.437,1.042l-0.678,6.008
          C406.135,395.722,404.28,396.48,403.047,395.478z"/>
        <circle style="fill:#FFFFFF;" cx="391.299" cy="204.873" r="2.561"/>
        <g>
          <circle style="fill:#FFFFFF;" cx="516.575" cy="263.777" r="1.431"/>
          <circle style="fill:#FFFFFF;" cx="718.068" cy="700.63" r="1.301"/>
          <circle style="fill:#FFFFFF;" cx="411.692" cy="653.633" r="4.52"/>
          <circle style="fill:#FFFFFF;" cx="459.588" cy="195.569" r="1.683"/>
          <circle style="opacity:0.47;fill:#FFFFFF;" cx="280.77" cy="456.486" r="1.606"/>
          <circle style="opacity:0.47;fill:#FFFFFF;" cx="740.164" cy="649.834" r="3.662"/>
          <circle style="fill:#FFFFFF;" cx="464.179" cy="734.522" r="2.167"/>
          <circle style="fill:#FFFFFF;" cx="478.196" cy="631.225" r="4.652"/>
          <circle style="fill:#FFFFFF;" cx="497.966" cy="447.182" r="1.282"/>
          <circle style="fill:#FFFFFF;" cx="236.674" cy="530.75" r="3.964"/>
          <circle style="fill:#FFFFFF;" cx="290.074" cy="640.529" r="2.958"/>
          <circle style="fill:#FFFFFF;" cx="497.966" cy="691.325" r="1.551"/>
          <circle style="fill:#FFFFFF;" cx="356.301" cy="418.982" r="2.955"/>
          <circle style="fill:#FFFFFF;" cx="497.966" cy="326.508" r="2.653"/>
          <circle style="fill:#FFFFFF;" cx="580.808" cy="214.178" r="1.099"/>
          <circle style="fill:#FFFFFF;" cx="323.498" cy="493.132" r="3.904"/>
          <circle style="opacity:0.22;fill:#FFFFFF;" cx="708.764" cy="375.537" r="4.487"/>
          <circle style="fill:#FFFFFF;" cx="332.803" cy="273.081" r="1.521"/>
          <circle style="fill:#FFFFFF;" cx="758.772" cy="738.923" r="2.234"/>
          <circle style="fill:#FFFFFF;" cx="535.184" cy="734.522" r="2.167"/>
          <circle style="fill:#FFFFFF;" cx="464.179" cy="240.892" r="3.49"/>
          <circle style="fill:#FFFFFF;" cx="694.948" cy="299.483" r="1.78"/>
          <circle style="fill:#FFFFFF;" cx="507.271" cy="176.961" r="3.394"/>
          <circle style="fill:#FFFFFF;" cx="255.282" cy="400.373" r="1.863"/>
          <circle style="fill:#FFFFFF;" cx="342.107" cy="551.092" r="1.116"/>
          <circle style="fill:#FFFFFF;" cx="372.69" cy="240.892" r="1.116"/>
          <circle style="fill:#FFFFFF;" cx="356.301" cy="505.472" r="2.955"/>
          <circle style="opacity:0.54;fill:#FFFFFF;" cx="420.996" cy="250.196" r="1.998"/>
          <circle style="fill:#FFFFFF;" cx="650.908" cy="245.169" r="2.048"/>
          <circle style="fill:#FFFFFF;" cx="330.078" cy="209.932" r="3.002"/>
          <circle style="fill:#FFFFFF;" cx="367.663" cy="329.161" r="3.625"/>
          <circle style="fill:#FFFFFF;" cx="543.93" cy="306.063" r="2.196"/>
          <circle style="fill:#FFFFFF;" cx="349.722" cy="456.486" r="1.096"/>
          <circle style="opacity:0.37;fill:#FFFFFF;" cx="232.71" cy="631.225" r="1.829"/>
          <circle style="fill:#FFFFFF;" cx="316.919" cy="691.325" r="3.066"/>
          <circle style="fill:#FFFFFF;" cx="745.843" cy="425.561" r="2.379"/>
          <circle style="fill:#FFFFFF;" cx="681.789" cy="732.846" r="2.674"/>
          <circle style="opacity:0.18;fill:#FFFFFF;" cx="525.5" cy="658.435" r="6.579"/>
          <circle style="fill:#FFFFFF;" cx="296.653" cy="412.403" r="1.059"/>
          <circle style="opacity:0.53;fill:#FFFFFF;" cx="555.999" cy="245.92" r="3.505"/>
        </g>
      </g>
      </svg>
      <img class='full' :src="vm.staticUrl+'/image/decoration/decoration17.png'" v-else-if="i===17"/>
      <img class='full' :src="vm.staticUrl+'/image/decoration/decoration18.png'" v-else-if="i===18"/>
              `
                });
            }
            for (let i = 1; i < 12; i++) {
                Vue.component(`x-decoration${i}`, {
                    props: {
                        option: {
                            type: Object,
                            default: () => {
                                return {};
                            },
                        },
                    },
                    watch: {
                        option: {
                            handler() {
                                this.init()
                            },
                        }
                    },
                    data() {
                        return {
                            color: {
                                border: '',
                                border1: ''
                            },
                            isResize: false
                        }
                    },
                    mounted() {
                        this.init()
                    },
                    methods: {
                        init() {
                            this.color = []
                            if (this.option.borderColor != '') {
                                this.color.border = this.option.borderColor
                            }
                            if (this.option.border1Color != '') {
                                this.color.border1 = this.option.border1Color
                            }
                            this.color = Object.values(this.color) // 对象转数组并去调空值
                            this.color = this.color.filter(item => { return item })
                        },
                        handleResize() {
                            this.isResize = vm.guid()
                        }

                    },
                    template: `<div class="full" >
          <dv-decoration-${i} :key="isResize" :color="color" >{{option.data}}</dv-decoration-${i}>
          <resize-observer @notify="handleResize" /></div>`,
                });
            }
        }
    }
})